const buttonPopup = {
  data() {
    return {
      showButton: false, //是否展示button弹框
    };
  },
  methods: {
    //按钮设置提交
    buttonSubmit(params) {
      if (!this.buttonInfo.isInsert) {
        if (this.curClickInfo) {
          const { viewPath, row, element } = this.curClickInfo;
          let index;
          for (let i = 0; i < row.children.length; i++) {
            const char = row.children[i];
            if (char === element) {
              index = i;
            }
          }
          let path = this.editor.viewPath2ModelPath(viewPath);
          if (index) {
            path[path.length - 1] = index + 1;
          }
          this.editor.selection.setCursorPosition(path);
          this.editor.delete_backward();
          this.editor.update();
          this.editor.render();
        }
      }

      this.editor.insertButton(
        params.text,
        params.width,
        params.height,
        params.color
      );

      this.editor.focus();
      this.showButton = false;
    },
    //关闭按钮设置弹窗
    closeButton() {
      this.showButton = false;
    },
    initButtonDefault() {
      return {
        field_id: null,
        value: "",
        field_position: "normal",
        height: null,
        width: null,
        color: "rgb(78,169,252)",
        pointer_in: false,
      };
    },
    //打开按钮弹窗
    insertButtonPopup() {
      this.buttonInfo = this.initButtonDefault();
      this.editor._curButtonInfo = this.buttonInfo;
      this.buttonInfo.isInsert = true;
      this.showButton = true;
    },
    //编辑按钮
    buttonEdit() {
      if (this.curClickInfo && this.curClickInfo.element) {
        if (!this.buttonInfo) {
          this.buttonInfo = this.curClickInfo.element;
        }
        this.editor._curButtonInfo = this.buttonInfo;
        this.buttonInfo.isInsert = false;
        this.showButton = true;
      }
    },
  },
};
export default buttonPopup;
