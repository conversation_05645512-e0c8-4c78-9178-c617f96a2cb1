import Editor from "./Editor";
import WaterMark from "./WaterMark";
export default class MarkInput {
  dom: HTMLElement;
  timer: any = null;
  hold_mouse_move: boolean = false;
  pointer_down_cursor: string = "";
  moveList: any = [];
  in_move_area: boolean = false;
  before_location: any = { x: 0, y: 0 };
  constructor (dom: HTMLElement) {
    this.dom = dom;
  }

  pointer_down (event: PointerEvent) {
    const target = event.target as HTMLCanvasElement;
    target.setPointerCapture(event.pointerId);
    if (this.in_move_area) {
      this.hold_mouse_move = true;
      this.pointer_down_cursor = this.dom.style.cursor = "move";
    } else {
      this.hold_mouse_move = false;
      this.pointer_down_cursor = "";
      this.dom.style.cursor = "text";
    }
    this.before_location = { x: event.offsetX, y: event.offsetY };
  }

  pointer_up (event: PointerEvent, editor:Editor) {
    this.dom.focus();
    this.hold_mouse_move = false;
    this.pointer_down_cursor = "";
    const mark = WaterMark.isSameMarkId(editor.internal.markId, editor);
    if (mark) {
      const font_top = mark.params.fontStyle.height * 0.1;
      // 6是input框距文字的距离
      const { x, y } = editor.getNeedXYbyXY(this.dom.offsetLeft - editor.init_canvas.offsetLeft + 6, this.dom.offsetTop - editor.init_canvas.offsetTop + font_top + 6);
      let input_y = editor.getSpacingPageTopByY(y + editor.scroll_top);
      if (mark.mode === "single") {
        input_y = y + editor.scroll_top;
      }
      mark.start = { x: x - editor.page_left, y: Number(input_y) };
      editor.internal.is_drag_mark = false;
      editor.render();
    }
    const target = event.target as HTMLCanvasElement;
    target.releasePointerCapture(event.pointerId);
  }

  pointer_move (event: PointerEvent, editor:Editor) {
    event.preventDefault();
    const x = event.offsetX;
    const y = event.offsetY;
    this.moveList = [
      { x: [0, this.dom.offsetWidth], y: [0, 5] },
      { x: [0, this.dom.offsetWidth], y: [this.dom.offsetHeight - 7, this.dom.offsetHeight] },
      { x: [0, 5], y: [0, this.dom.offsetHeight] },
      { x: [this.dom.offsetWidth - 7, this.dom.offsetWidth], y: [0, this.dom.offsetHeight] }
    ];
    if (this.pointer_down_cursor === "") {
      for (let i = 0; i < this.moveList.length; i++) {
        const move = this.moveList[i];
        if (move.x[0] <= x && x <= move.x[1] && move.y[0] <= y && y <= (move.y[1])) {
          this.dom.style.cursor = "move";
          this.in_move_area = true;
          break;
        } else {
          this.in_move_area = false;
          this.dom.style.cursor = "text";
        }
      }
    } else {
      const move_x = x + this.dom.offsetLeft + 1 - this.before_location.x;
      const move_y = y + this.dom.offsetTop + 1 - this.before_location.y;

      this.dom.style.left = move_x + "px";
      this.dom.style.top = move_y + "px";
      editor.internal.is_drag_mark = true;
      editor.render();
    }
  }

  // 水印文字进入编辑模式,展示水印文字input框
  static displayMarkInput (re_x:number, re_y:number, editor: Editor, mark:WaterMark|null) {
    const dom: HTMLElement = editor.internal.markInput;
    let font_style = editor.markInputStyle;
    if (mark) {
      font_style = mark.params.fontStyle;
    } else {
      if (!editor.markInputStyle) {
        font_style = editor.contextState.getFontState();
      }
    }
    editor.markInputStyle = font_style;
    const { x, y } = editor.getNeedXYbyXY(re_x, re_y, 2);
    dom.style.left = x + editor.init_canvas.offsetLeft - 6 + "px";
    dom.style.display = "block";
    dom.style.font = `${font_style.italic ? "italic " : ""}${font_style.bold ? 700 : 400} ${font_style.height}px ${font_style.family}`;
    dom.style.color = font_style.color;
    dom.style.lineHeight = font_style.height * 1.2 + "px";
    dom.style.top = y - font_style.height * 0.1 - parseInt(dom.style.border) - parseInt(dom.style.padding) + editor.init_canvas.offsetTop + "px";
    dom.focus();
  }
}
