module.exports = {
  root: true,
  env: {
    browser: true,
    es6: true,
    jest: true,
    node: true
  },
  // "plugin:@typescript-eslint/recommended"
  extends: ["eslint:recommended"],
  globals: {
    Atomics: "readonly",
    SharedArrayBuffer: "readonly"
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: "module",
    parser: "babel-eslint"
  },
  rules: {
    "no-useless-escape": "warn",
    "no-undef": "off",
    camelcase: "off",
    semi: ["off", "never"],
    quotes: [
      "off",
      "single",
      {
        avoidEscape: true,
        allowTemplateLiterals: true
      }
    ],
    indent: [
      2,
      2,
      {
        SwitchCase: 1
      }
    ],
    "node/no-callback-literal": "off",
    "no-unused-vars": "off",
    "no-use-before-define": "off",
    "object-curly-spacing": ["error", "always"],
    "no-constant-condition": "off",
    "prefer-const": "off",
    "for-direction": "off",
    // '@typescript-eslint/no-unused-vars': 'error',
    "@typescript-eslint/no-inferrable-types": "off",
    "@typescript-eslint/no-empty-function": "off",
    "@typescript-eslint/no-non-null-assertion": "off",
    "@typescript-eslint/ban-ts-comment": "off",
    "@typescript-eslint/no-namespace": "off" // TODO WAIT FOR DELETE
  },
  overrides: [
    {
      files: ["**/__tests__/**", "test-dts/**"],
      rules: {
        "no-restricted-globals": "off",
        "no-restricted-syntax": "off"
      }
    },
    {
      files: ["**/Sheet.ts"],
      rules: {
        "no-unused-vars": "off",
        "@typescript-eslint/no-unused-vars": "off" // TODO WAIT FOR DELETE
      }
    }
  ]
};
