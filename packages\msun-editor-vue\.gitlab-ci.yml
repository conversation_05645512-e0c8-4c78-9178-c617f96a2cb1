stages:
  - build
  - tag
  - deploy-prod

build_job:
  stage: build
  tags:
    - msun-sheet
  only:
    refs:
      - master
    changes:
      - package.json
  artifacts:
    paths:
      - dist/
    expire_in: 1 hr
  script:
    - git checkout master
    - git pull --tags -f
    - npm install
    - npm run build

put-tag:
  stage: tag
  tags:
    - msun-sheet
  only:
    refs:
      - master
    changes:
      - package.json
  script:
    - git remote set-url origin https://gitlab-ci-token:$MY_CI_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git
    - git checkout master
    - git pull --tags -f
    - VERSION=$(node -p "require('./package.json').version")
    - git tag "tag-release-V${VERSION}"
    - git push --tags

publish-dev:
  stage: deploy-prod
  tags:
    - msun-sheet
  only:
    refs:
      - master
    changes:
      - package.json
  dependencies:
    - build_job
  script:
    - npm publish --registry=$REGISTRY_DEV

publish-test:
  stage: deploy-prod
  tags:
    - msun-sheet
  only:
    refs:
      - master
    changes:
      - package.json
  dependencies:
    - build_job
  script:
    - npm publish --registry=$REGISTRY_TEST

publish-prod:
  stage: deploy-prod
  tags:
    - msun-sheet
  only:
    refs:
      - master
    changes:
      - package.json
  dependencies:
    - build_job
  script:
    - npm publish --registry=$REGISTRY_PROD
