<template>
  <div>
    <a-modal
      width="1000px"
      :bodyStyle="{
        height: '550px',
      }"
      v-model="visible"
      title="数据集管理(只做临时使用，页面刷新后修改的内容将丢失)"
      @ok="handleOk"
      @cancel="cancel"
    >
      <div id="components-layout-editor-basic">
        <a-layout>
          <a-layout-sider>
            <a-input
              v-for="(item, index) in dataSetList"
              :ref="'dataSetInput' + index"
              @click="
                () => {
                  dataSetClick(item, index);
                }
              "
              :key="index"
              style="width: 95%"
              :class="dataSetClass(index, item)"
              placeholder="数据集名称"
              v-model="item.name"
            />
            <a-button type="dashed" style="width: 75%" @click="addDataSet">
              <a-icon type="plus" /> 临时数据集
            </a-button>
          </a-layout-sider>
          <a-layout-content>
            <a-textarea
              v-for="(item, index) in dataSetList"
              :key="index"
              v-show="selectIndex === index"
              v-model="item.value"
              placeholder="请输入JSON格式的数据集"
              :rows="24"
              @keydown="
                ($event) => {
                  dataSetSaveLocal($event, item.value);
                }
              "
            />
          </a-layout-content>
        </a-layout>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { h } from "vue";
export default {
  name: "dataSetModal",
  data() {
    return {
      visible: false,
      dataSetList: [],
      selectIndex: 0,
      jsonDataStr: "",
      errorDataSet: {},
      localDataSet: {},
    };
  },
  props: ["show", "oriDataSet"],
  watch: {
    show: {
      handler(val) {
        this.visible = val;
        this.errorDataSet = {};
        const local = localStorage.getItem("EditorLocalDataSet");
        try {
          if (local) {
            this.localDataSet = JSON.parse(local);
          }
        } catch (e) {}
      },
      immediate: true,
    },
    oriDataSet: {
      handler(val) {
        const newData = [];
        for (const key in val) {
          newData.push({ name: key, value: JSON.stringify(val[key], null, 4) });
        }
        this.dataSetList = newData;
        if (!this.dataSetList.length) {
          this.dataSetList.push({ name: "", value: "" });
        }
      },
      immediate: true,
    },
  },
  methods: {
    dataSetClass(index, item) {
      let localClass = "";
      if (this.localDataSet[item.name]) {
        localClass = " localDataSet";
      }

      if (this.errorDataSet[index]) {
        return "errorDataSet" + localClass;
      }
      if (index === this.selectIndex) {
        return "selectDataSet" + localClass;
      }
      return localClass;
    },
    cancel() {
      this.$emit("cancel");
    },
    handleOk() {
      this.validateData();
      if (this.isRight()) {
        const newDateSet = {};
        for (let i = 0, len = this.dataSetList.length; i < len; i++) {
          const item = this.dataSetList[i];
          if (!item.value) {
            continue;
          }
          newDateSet[item.name] = JSON.parse(item.value);
          if (this.localDataSet[item.name]) {
            this.localDataSet[item.name] = newDateSet[item.name];
          }
        }
        this.saveLocalDataSet();
        this.$emit("submit", newDateSet);
      }
    },
    isRight() {
      if (Object.keys(this.errorDataSet).length === 0) {
        return true;
      } else {
        this.$editor.error("存在错误格式或空的数据集，请检查");
        return false;
      }
    },
    validateData() {
      this.errorDataSet = {};
      for (let i = 0, len = this.dataSetList.length; i < len; i++) {
        const item = this.dataSetList[i];
        if (!item.name && item.value) {
          this.errorDataSet[i] = true;
          continue;
        }
        if (item.value) {
          try {
            const json = JSON.parse(item.value);
            if (item.name.endsWith("-dict")) {
              if (!Array.isArray(json)) {
                this.errorDataSet[i] = true;
                const example = `[{"name":"监测项目","label":"尿量","value":"0"},{"name":"监测项目","label":"体温","value":"1"},{"name":"麻醉药品","label":"丙泊酚","value":"0"},{"name":"麻醉药品","label":"依托咪酯","value":"1"}]`;
                const msg =
                  "字典数据集必须是规定结构的JSON数组，例如：\n" +
                  JSON.stringify(JSON.parse(example), null, 4);
                this.openNotification(msg);
              }
            }
          } catch (e) {
            this.errorDataSet[i] = true;
          }
        }
      }
    },
    openNotification(msg) {
      this.$notification.error({
        message: "数据结构提示",
        description: () => {
          return h("pre", {}, msg);
        },
        style: {
          width: "500px",
          marginLeft: `${335 - 500}px`,
        },
      });
    },
    addDataSet() {
      this.dataSetList.push({ name: "", value: "" });
      this.selectIndex = this.dataSetList.length - 1;
      this.$nextTick(() => {
        this.$refs["dataSetInput" + this.selectIndex][0].$el.focus();
      });
    },
    dataSetClick(item, index) {
      this.selectIndex = index;
      this.validateData();
    },
    dataSetSaveLocal(event, value) {
      if (event.ctrlKey) {
        const dataSetName = this.dataSetList[this.selectIndex].name;
        if (event.key === "s") {
          event.preventDefault();
          this.localDataSet[dataSetName] = JSON.parse(value);
          this.saveLocalDataSet();
          this.$editor.info("已保存至本地,删除使用ctrl+d");
        }
        if (event.key === "d") {
          event.preventDefault();
          delete this.localDataSet[dataSetName];
          this.saveLocalDataSet();
          this.$editor.info("已删除本地" + dataSetName + ",刷新页面后生效");
        }
      }
    },
    saveLocalDataSet() {
      localStorage.setItem(
        "EditorLocalDataSet",
        JSON.stringify(this.localDataSet)
      );
    },
  },
};
</script>

<style scoped>
#components-layout-editor-basic {
  text-align: center;
  width: 100%;
  height: 100%;
}
#components-layout-editor-basic .ant-layout-sider {
  background: #f5f5f5;
  color: #fff;
  line-height: 40px;
  height: 500px;
  overflow: auto;
}
.selectDataSet {
  background: #e6f7ff;
}
.dataSetName {
}
.errorDataSet {
  border: 1px #f83131 solid;
}
.localDataSet {
  background-color: rgba(133, 94, 66, 0.1);
}
</style>
