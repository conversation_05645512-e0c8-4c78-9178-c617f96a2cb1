const Config = {
  formula_font: 24,
  img_devicePixelRatio: 2,
  // 打印插件配置
  BasePrint: {
    name: "BasePrint",
    pluginName: "BasePrintPlugin",
    pluginVersion: "1.0.0.1",
    pluginRemark: "打印插件",
    dllFile: "plugins/BasePrintPlugin/BasePrintPlugin.dll",
    socketUrl: "ws://127.0.0.1:37211",
    socketOptions: {
      reconnectEnabled: true, // 允许重连
      reconnectAttempts: 3, // 企图重连
      reconnectInterval: 1000, // 多久连接一次？
    },
    reportServiceApi: "http://demo-.msunhis.com/basereport",
  },
};
export default Config;
