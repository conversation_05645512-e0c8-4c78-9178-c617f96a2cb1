import { isFunction } from "../assets/js/utils";

export default class WebsocketClient {
  constructor(url, opts = {}) {
    this.instance = null;
    this.url = url;
    this.options = opts;

    this.reconnectEnabled = this.options.reconnectEnabled || false;
    this.reconnectAttempts = this.options.reconnectAttempts || Infinity;
    this.reconnectInterval = this.options.reconnectInterval || 1000;
    this.reconnectCount = 0;
    this.reconnectTimeoutId = null;

    // These methods should be defined by caller
    this.onOpen = null;
    this.onMessage = null;
    this.onClose = null;
    this.onError = null;
  }

  /** socket连接 */
  connect() {
    this.instance = new WebSocket(this.url);

    // Socket event listeners
    this.instance.onopen = (event) => {
      event.reconnectCount = this.reconnectCount;
      isFunction(this.onOpen) && this.onOpen(event);

      if (this.reconnectEnabled) {
        this.reconnectCount = 0;
        clearTimeout(this.reconnectTimeoutId);
      }
    };

    this.instance.onmessage = (event) => {
      isFunction(this.onMessage) && this.onMessage(event);
    };

    this.instance.onclose = (event) => {
      isFunction(this.onClose) && this.onClose(event);

      if (!event.wasClean && this.reconnectEnabled) {
        this.reconnect();
      }
    };

    this.instance.onerror = (event) => {
      isFunction(this.onError) && this.onError(event);
    };
  }

  /** 发送数据 */
  send(data) {
    this.waitForConnection(() => {
      this.instance.send(data);
    });
  }

  /** 发送数据，数据是object类型 */
  sendObj(data) {
    this.send(JSON.stringify(data));
  }

  /** 等待连接成功回调，轮询监听连接状态 */
  waitForConnection(callback, interval = 1000) {
    if (this.instance.readyState === 1) {
      callback();
    } else if (this.instance.readyState === 0) {
      setTimeout(() => {
        this.waitForConnection(callback, interval);
      }, interval);
    }
  }

  /** socket重连 */
  reconnect() {
    if (this.reconnectCount < this.reconnectAttempts) {
      this.reconnectCount++;
      clearTimeout(this.reconnectTimeoutId);

      this.reconnectTimeoutId = setTimeout(() => {
        this.connect();
      }, this.reconnectInterval);
    } else {
      // 重连错误事件
      const event = new Event("error");
      event.currentTarget = this.instance;
      event.reason = "reconnect_error";

      isFunction(this.onError) && this.onError(event);
    }
  }
}
