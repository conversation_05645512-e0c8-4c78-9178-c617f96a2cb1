/**
 * 文本域相关逻辑混入
 * @type {{data(): {}, method: {}}}
 */
import medicalFormulaList from "../components/medicalFormulaList.vue";
import medicalFormula from "../components/medicalFormula.vue";
import Config from "../config";
const formulaMixIn = {
  data() {
    return {
      showMedicalFormula: false,
      showMedicalFormulaList: false,
      formulaType: null,
      formulaSrcList: [],
      meta: {
        formula_type: 0,
        params: [],
      },
    };
  },
  components: {
    medicalFormula,
    medicalFormulaList,
  },
  methods: {
    showEditMedicalFormulaWin() {
      if (
        !this.curClickInfo.element ||
        !this.curClickInfo.element.meta ||
        Number(this.curClickInfo.element.meta.formula_type) < 0 ||
        JSON.stringify(this.curClickInfo.element.meta) === "{}" ||
        (!this.curClickInfo.field && this.editor.view_mode === "form") ||
        (this.curClickInfo.field && this.curClickInfo.field.isReadonly) ||
        this.editor.selection.getFocusGroup()?.lock
      ) {
        return;
      }
      if (
        this.curClickInfo.element &&
        this.curClickInfo.element.meta &&
        Number(this.curClickInfo.element.meta.formula_type) >= 0
      ) {
        this.formulaType = Number(this.curClickInfo.element.meta.formula_type);
        this.meta = JSON.parse(JSON.stringify(this.curClickInfo.element.meta));
        this.showMedicalFormula = true;
      } else {
        this.showMedicalFormula = false;
      }
    },
    insertFormula() {
      this.formulaSrcList = this.instance.config.getConfig().formulaImage;
      this.showMedicalFormulaList = true;
    },
    choiceFormula() {
      this.meta.params = [];
      this.formulaType = this.$refs.formulaList.formulaType;
      this.meta.formula_type = this.formulaType;
      this.showMedicalFormulaList = false;
      this.showMedicalFormula = true;
      this.editor.focus();
    },
    cancelFormula() {
      // console.log(11111111);
      // const params = {
      //   formula_type: 11,
      //   params: [
      //     { line: 0, clickList: ["8", "4"] },
      //     { line: 3, clickList: ["D", "C", "B"] },
      //   ],
      // };
      // const fields = this.editor.getFieldsByName("aa");
      // this.insertFormulaImageByData(params, fields);
      // const rawData = this.editor.getRawData();
      // console.log(rawData);
      this.showMedicalFormulaList = false;
      this.editor.focus();
    },
    insertFormulaImageByFields(params, fields) {
      const image = this.drawImage(params);
      const meta = {};
      meta.params = JSON.parse(JSON.stringify(params.params));
      meta.formula_type = JSON.parse(JSON.stringify(params.formula_type));
      if (!this.editor.selection.isCollapsed) {
        this.editor.delete_backward();
      }
      const image_info = {
        src: image.src,
        meta: meta,
        width: image.width / Config.img_devicePixelRatio,
        height: image.height / Config.img_devicePixelRatio,
      };
      this.editor.replaceFieldsImage(fields, image_info);
    },
    insertFormulaImage(params) {
      const image = this.drawImage(params);
      const meta = {};
      meta.params = JSON.parse(JSON.stringify(params.params));
      meta.formula_type = JSON.parse(JSON.stringify(params.formula_type));
      if (!this.editor.selection.isCollapsed) {
        this.editor.delete_backward();
      }
      this.editor.insertImage(image.src, {
        meta: meta,
        devicePixelRatio: Config.img_devicePixelRatio,
      });
      this.showMedicalFormula = false;
      this.editor.focus();
    },
    // params={formula_type:11,params:[{line:0,clickList:[]}]}
    insertFormulaImageByData(params, fields) {
      if (params.formula_type === 11) {
        const data = [
          [
            { value: 8, click: false },
            { value: 7, click: false },
            { value: 6, click: false },
            { value: 5, click: false },
            { value: 4, click: false },
            { value: 3, click: false },
            { value: 2, click: false },
            { value: 1, click: false },
          ],
          [
            { value: "E", click: false },
            { value: "D", click: false },
            { value: "C", click: false },
            { value: "B", click: false },
            { value: "A", click: false },
          ],
          [
            { value: 1, click: false },
            { value: 2, click: false },
            { value: 3, click: false },
            { value: 4, click: false },
            { value: 5, click: false },
            { value: 6, click: false },
            { value: 7, click: false },
            { value: 8, click: false },
          ],
          [
            { value: "A", click: false },
            { value: "B", click: false },
            { value: "C", click: false },
            { value: "D", click: false },
            { value: "E", click: false },
          ],
          [
            { value: 8, click: false },
            { value: 7, click: false },
            { value: 6, click: false },
            { value: 5, click: false },
            { value: 4, click: false },
            { value: 3, click: false },
            { value: 2, click: false },
            { value: 1, click: false },
          ],
          [
            { value: "E", click: false },
            { value: "D", click: false },
            { value: "C", click: false },
            { value: "B", click: false },
            { value: "A", click: false },
          ],
          [
            { value: 1, click: false },
            { value: 2, click: false },
            { value: 3, click: false },
            { value: 4, click: false },
            { value: 5, click: false },
            { value: 6, click: false },
            { value: 7, click: false },
            { value: 8, click: false },
          ],
          [
            { value: "A", click: false },
            { value: "B", click: false },
            { value: "C", click: false },
            { value: "D", click: false },
            { value: "E", click: false },
          ],
        ];
        for (let i = 0; i < params.params.length; i++) {
          const param = params.params[i];
          for (let j = 0; j < param.clickList.length; j++) {
            const val = String(param.clickList[j]);
            for (let k = 0; k < data[Number(param.line)].length; k++) {
              const element = data[Number(param.line)][k];
              if (String(element.value) === val) {
                element.click = true;
              }
            }
          }
        }
        this.insertFormulaImageByFields(
          {
            formula_type: 11,
            params: data,
            width: params.width,
            height: params.height,
          },
          fields
        );
      }
      //  else if (params.formula_type === 12) {
      // }
    },
    //绘制医学表达式图片
    drawImage(params) {
      return this.instance.medicalFormula.getMedicalFormulaImage(params);
    },
    cancelFormulaImage() {
      this.showMedicalFormula = false;
      this.editor.focus();
    },
  },
};
export default formulaMixIn;
