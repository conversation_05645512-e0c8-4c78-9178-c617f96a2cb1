<template>
  <div class="date_picker_editor" v-if="show">
    <div class="field-date-picker-input" :id="'date' + editorId">
      <a-input
        v-if="!showHHmm"
        type="text"
        class="date-input-css"
        v-model="getYearMonthDay"
        @change="changeYearMonthDay"
        @pressEnter="getYearMonthDayKeyDown"
      >
        <a-icon slot="prefix" type="schedule"
      /></a-input>
      <a-input
        type="text"
        v-if="showHourMinuteSecond"
        v-model="getHourMinuteSecond"
        @change="changeHourMinuteSecond"
        ref="hourMinuteSecond"
        @pressEnter="getHourMinuteSecondKeyDown"
      >
        <a-icon slot="prefix" type="schedule" />
        /></a-input
      >
    </div>
    <template v-if="showHHmm">
      <a-time-picker
        :value="
          moment(
            field_date_text.split(' ')[1]
              ? field_date_text.split(' ')[1]
              : field_date_text.split(' ')[0],
            'HH:mm'
          )
        "
        popupClassName="editorDatePickPanelClassField"
        format="HH:mm"
        :open="true"
        @change="panelChange"
      />
    </template>
    <template v-else>
      <a-date-picker
        style="visibility: hidden"
        :open="focusShow"
        :format="dateFormat"
        ref="field_date_picker"
        :disabledDate="disabledDate"
        dropdownClassName="editorDatePickPanelClassField"
        :value="moment(field_date_text, valueFormat)"
        @change="panelChange"
        @panelChange="dateChange"
        :showTime="timeFormat"
        @ok="submit"
        :valueFormat="valueFormat"
        :getPopupContainer="
          (triggerNode) => {
            return triggerNode.parentNode || document.body;
          }
        "
      />
    </template>
  </div>
</template>

<script>
import moment from "moment";
// 因为vite只支持ES modules,所以需要改下zh-cn的引入方式才能让国际化生效
import "../../assets/js/zh-cn";
import { formatDateTime } from "../../assets/js/utils";
// import BUS from "@/assets/js/eventBus";
const default_date_format = "YYYY-MM-DD HH:mm:ss";
export default {
  name: "datePicker",
  data() {
    return {
      showHourMinuteSecond: false,
      getYearMonthDay: null,
      getHourMinuteSecond: null,
      showHHmm: false,
      // choiceDate: "",
      clickNow: false, // 是否点击在了此刻上
      moment,
      timeFormatDict: {
        1: "HH:mm",
        2: "HH:mm:ss",
        3: "HH",
      },
      focusShow: false,
      timeFormat: false,
      valueFormat: default_date_format,
      dateFormat: default_date_format,
      field_date_text: formatDateTime(new Date(), default_date_format),
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          const fieldInfo = this.editor.editor._curFieldInfo;
          const format = fieldInfo.replace_format;
          this.getYearMonthDay = null;
          this.getHourMinuteSecond = null;
          //根据不同类型时间格式展示不同默认值
          if (format === 8 || format === 9) {
            this.valueFormat = "HH:mm";
            this.showHHmm = true;

            this.$nextTick(() => {
              const dom = document.getElementById("date" + this.editorId);
              dom.style.width = 134 + "px";
              dom.style.top = "50px";
              if (fieldInfo.text) {
                if (format === 8) {
                  this.getHourMinuteSecond =
                    fieldInfo.text.split(":")[0] + fieldInfo.text.split(":")[1];
                } else {
                  const h = fieldInfo.text.split("时");
                  if (h[1]) {
                    this.getHourMinuteSecond = h[0] + h[1].split("分")[0];
                  }
                }
              } else {
                if (this.field_date_text.split(" ")[1]) {
                  this.getHourMinuteSecond = this.field_date_text.substring(
                    11,
                    16
                  );
                }
              }
            });
          } else {
            this.valueFormat = default_date_format;
            this.showHHmm = false;
            let num_time = moment(fieldInfo.text, this.valueFormat).format(
              "YYYYMMDD HHmmss"
            );

            if ((format === 6 || format === 11) && fieldInfo.text) {
              const year = new Date().getFullYear();
              num_time = moment(
                year + "-" + fieldInfo.text,
                this.valueFormat
              ).format("YYYYMMDD HHmmss");
            } else if (format === 7 && fieldInfo.text) {
              const year = new Date().getFullYear();
              num_time = moment(
                year + "年" + fieldInfo.text,
                this.valueFormat
              ).format("YYYYMMDD HHmmss");
            }
            if (num_time !== "Invalid date") {
              if ((0 <= format && format <= 5) || format === 10) {
                this.getYearMonthDay = num_time.split(" ")[0];
              } else if (format === 6 || format === 7 || format === 11) {
                const ymd = num_time.split(" ")[0];
                this.getYearMonthDay = ymd.substring(4, 8);
              }
            }
          }
          //判断日期类型是否带时分秒，带时分秒的显示时分秒input框
          const format_list = [1, 2, 4, 5, 8, 9, 10, 11];
          if (format_list.indexOf(format) !== -1) {
            this.showHourMinuteSecond = true;

            let num_time = moment(fieldInfo.text, this.valueFormat).format(
              "HHmmss"
            );
            if (format === 11 && fieldInfo.text) {
              const year = new Date().getFullYear();
              num_time = moment(
                year + "-" + fieldInfo.text,
                this.valueFormat
              ).format("HHmm");
            }
            if (num_time !== "Invalid date") {
              if (format === 1) {
                this.getHourMinuteSecond = num_time.substring(0, 4);
              } else if (format === 2 || format === 10 || format === 11) {
                this.getHourMinuteSecond = num_time;
              } else if (format === 4) {
                this.getHourMinuteSecond = num_time.substring(0, 2);
              } else if (format === 5) {
                this.getHourMinuteSecond = num_time.substring(0, 4);
              }
            }
          } else {
            this.showHourMinuteSecond = false;
          }

          if (format === 4) {
            this.timeFormat = { format: "HH" };
          } else if (
            format === 1 ||
            format === 5 ||
            format === 8 ||
            format === 9 ||
            format === 11
          ) {
            this.timeFormat = { format: "HH:mm" };
          } else if (format === 2 || format === 10) {
            this.timeFormat = { format: "HH:mm:ss" };
          } else {
            this.timeFormat = false;
          }
          const fieldText = fieldInfo.text;
          if (this.validFieldDateText(fieldText)) {
            this.field_date_text = fieldText;
            if (format === 6) {
              const year = new Date().getFullYear();
              this.field_date_text = year + "-" + fieldInfo.text;
            } else if (format === 7) {
              const year = new Date().getFullYear();
              this.field_date_text = year + "年" + fieldInfo.text;
            } else if (format === 11) {
              const year = new Date().getFullYear();
              this.field_date_text = year + "-" + fieldInfo.text;
            }
          } else {
            this.field_date_text = formatDateTime(
              new Date(),
              default_date_format
            );
          }
        }
        this.focusShow = val;
      },
    },
  },
  methods: {
    // 当点击年和月的时候 就要实时在文本域中展示日期 点击确定或者点此刻是不走这儿的
    dateChange(momentVal) {
      const val = momentVal.format(this.valueFormat);
      this.$emit("submit", val, "date", true);
      this.field_date_text = val;
    },
    panelChange(val) {
      let text = val;
      const fieldInfo = this.editor.editor._curFieldInfo;
      const isClickNow = event.target.innerHTML === "此刻";
      if (!this.timeFormat || isClickNow) {
        this.$emit("submit", text, isClickNow ? "dateNow" : "date");
      } else {
        text = moment(val).format(default_date_format);
        if (
          (fieldInfo.replace_format === 8 || fieldInfo.replace_format === 9) &&
          val
        ) {
          const hm = text.split(" ")[1].split("00")[0].split(":");
          this.getHourMinuteSecond = hm[0] + hm[1];
        } else {
          let ymd = moment(val).format("YYYYMMDD");
          let sfm = moment(val).format("HHmmss");
          switch (fieldInfo.replace_format) {
            case 1:
              sfm = moment(val).format("HHmm");
              break;
            case 4:
              sfm = moment(val).format("HH");
              break;
            case 5:
              sfm = moment(val).format("HHmm");
              break;
            case 11:
              ymd = moment(val).format("MMDD");
              sfm = moment(val).format("HHmm");
              break;
          }

          this.getYearMonthDay = ymd;
          this.getHourMinuteSecond = sfm;
        }
        this.$emit("submit", text, "date", true);
      }
      this.field_date_text = text;
    },
    submit(val) {
      if (val !== "Invalid date") {
        let year = "";
        const fieldInfo = this.editor.editor._curFieldInfo;
        if (fieldInfo.replace_format === 11) {
          year = new Date().getFullYear();
        }
        let num_time = moment(
          year + this.getYearMonthDay + this.getHourMinuteSecond,
          this.valueFormat
        ).format("YYYYMMDD HHmmss");

        if (
          num_time == "Invalid date" &&
          (this.getYearMonthDay || this.getHourMinuteSecond)
        ) {
          this.$editor.error("请输入正确的日期");
        }
        this.$emit("submit", val, "date");
      } else {
        this.$editor.error("请输入正确的日期");
      }
    },
    validFieldDateText(text) {
      return this.editor.utils.valid.date_valid({ value: text });
    },
    disabledDate(val) {
      const fieldInfo = this.editor.editor._curFieldInfo;
      const fieldStart = fieldInfo.forbidden?.start;
      const fieldEnd = fieldInfo.forbidden?.end;
      const start = moment(fieldStart);
      const end = moment(fieldEnd);
      if (fieldStart && fieldEnd) {
        return val.valueOf() < start.valueOf() || val.valueOf() > end.valueOf();
      } else if (fieldStart && !fieldEnd) {
        return val.valueOf() < start.valueOf();
      } else if (!fieldStart && fieldEnd) {
        return val.valueOf() > end.valueOf();
      }
    },
    changeYearMonthDay() {
      if (this.getYearMonthDay.match(/\d+/g)) {
        this.getYearMonthDay = this.getYearMonthDay.match(/\d+/g).join("");
      } else {
        this.getYearMonthDay = null;
      }
      const fieldInfo = this.editor.editor._curFieldInfo;
      const format = fieldInfo.replace_format;
      if (
        (format === 0 || format === 3) &&
        this.getYearMonthDay &&
        this.getYearMonthDay.length === 8
      ) {
        const date = this.getYearMonthDay.replace(
          /(\d{4})(\d{2})(\d{2})/g,
          "$1-$2-$3 01:01:01"
        );
        this.field_date_text = date;
      } else if (
        (format === 6 || format === 7 || format === 11) &&
        this.getYearMonthDay &&
        this.getYearMonthDay.length === 4
      ) {
        const date = (new Date().getFullYear() + this.getYearMonthDay).replace(
          /(\d{4})(\d{2})(\d{2})/g,
          "$1-$2-$3 01:01:01"
        );
        this.field_date_text = date;
      } else if (
        (format === 1 ||
          format === 2 ||
          format === 4 ||
          format === 5 ||
          format === 10) &&
        this.getYearMonthDay &&
        this.getYearMonthDay.length === 8
      ) {
        const date = this.getYearMonthDay.replace(
          /(\d{4})(\d{2})(\d{2})/g,
          "$1-$2-$3"
        );
        const hms = formatDateTime(new Date(this.field_date_text), "HH:mm:ss");
        if (this.validFieldDateText(date)) {
          this.field_date_text = date + " " + hms;
        }
        this.$nextTick(() => {
          this.$refs.hourMinuteSecond.focus();
        });
      }
    },

    changeHourMinuteSecond() {
      if (this.getHourMinuteSecond.match(/\d+/g)) {
        this.getHourMinuteSecond = this.getHourMinuteSecond
          .match(/\d+/g)
          .join("");
      } else {
        this.getHourMinuteSecond = "";
      }
      const fieldInfo = this.editor.editor._curFieldInfo;
      const format = fieldInfo.replace_format;

      if (this.getYearMonthDay && this.getYearMonthDay.length === 8) {
        if (
          (format === 1 || format === 5) &&
          this.getHourMinuteSecond &&
          this.getHourMinuteSecond.length === 4
        ) {
          const date = (
            this.getYearMonthDay +
            (this.getHourMinuteSecond + "")
          ).replace(
            /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/g,
            "$1-$2-$3 $4:$5:01"
          );
          this.field_date_text = date;
        } else if (
          (format === 2 || format === 10) &&
          this.getHourMinuteSecond &&
          this.getHourMinuteSecond.length === 6
        ) {
          const date = (
            this.getYearMonthDay +
            (this.getHourMinuteSecond + "")
          ).replace(
            /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g,
            "$1-$2-$3 $4:$5:$6"
          );
          this.field_date_text = date;
        } else if (
          format === 4 &&
          this.getHourMinuteSecond &&
          this.getHourMinuteSecond.length === 2
        ) {
          const date = (
            this.getYearMonthDay +
            (this.getHourMinuteSecond + "")
          ).replace(/(\d{4})(\d{2})(\d{2})(\d{2})/g, "$1-$2-$3 $4:01:01");
          this.field_date_text = date;
        }
      } else {
        const yearMonthDayDate = formatDateTime(
          new Date(this.field_date_text),
          "YYYYMMDD"
        );
        if (
          (format === 1 || format === 5 || format === 8 || format === 9) &&
          this.getHourMinuteSecond &&
          this.getHourMinuteSecond.length === 4
        ) {
          const date = (
            yearMonthDayDate +
            (this.getHourMinuteSecond + "")
          ).replace(
            /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/g,
            "$1-$2-$3 $4:$5:01"
          );
          this.field_date_text = date;
        } else if (
          (format === 2 || format === 10) &&
          this.getHourMinuteSecond &&
          this.getHourMinuteSecond.length === 6
        ) {
          const date = (
            yearMonthDayDate +
            (this.getHourMinuteSecond + "")
          ).replace(
            /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g,
            "$1-$2-$3 $4:$5:$6"
          );
          this.field_date_text = date;
        } else if (
          format === 4 &&
          this.getHourMinuteSecond &&
          this.getHourMinuteSecond.length === 2
        ) {
          const date = (
            yearMonthDayDate +
            (this.getHourMinuteSecond + "")
          ).replace(/(\d{4})(\d{2})(\d{2})(\d{2})/g, "$1-$2-$3 $4:01:01");
          this.field_date_text = date;
        }
      }
    },

    getYearMonthDayKeyDown() {
      const fieldInfo = this.editor.editor._curFieldInfo;
      const format = fieldInfo.replace_format;
      let adjustDate = "";
      let hms = "";
      if (this.getHourMinuteSecond) {
        hms = this.getHourMinuteSecond;
      }
      if (format === 6 || format === 7 || format === 11) {
        const date = this.matchingDate(
          this.getYearMonthDay,
          4,
          "up",
          "monthDay"
        );
        adjustDate = (new Date().getFullYear() + date + hms).replace(
          /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/g,
          "$1-$2-$3 $4:$5:01"
        );
      } else if (format === 0 || format === 3) {
        const date = this.matchingDate(this.getYearMonthDay, 8);
        adjustDate = date.replace(
          /(\d{4})(\d{2})(\d{2})/g,
          "$1-$2-$3 01:01:01"
        );
      } else if (format === 1 || format === 5) {
        const date = this.getInputDateNum(12, "yearMonthDay");
        adjustDate = date.replace(
          /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/g,
          "$1-$2-$3 $4:$5:01"
        );
      } else if (format === 2 || format === 10) {
        const date = this.getInputDateNum(14, "yearMonthDay");
        adjustDate = date.replace(
          /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g,
          "$1-$2-$3 $4:$5:$6"
        );
      } else if (format === 4) {
        const date = this.getInputDateNum(10, "yearMonthDay");
        adjustDate = date.replace(
          /(\d{4})(\d{2})(\d{2})(\d{2})/g,
          "$1-$2-$3 $4:01:01"
        );
      }
      const is_real = moment(adjustDate).format(default_date_format);
      if (is_real !== "Invalid date") {
        this.$emit("submit", adjustDate, "date");
      } else {
        this.$editor.error("请输入正确的日期");
      }
    },
    //   0: "YYYY-MM-DD",
    //   1: "YYYY-MM-DD HH:mm",
    //   2: "YYYY-MM-DD HH:mm:ss",
    //   3: "YYYY年MM月DD日",
    //   4: "YYYY年MM月DD日 HH时",
    //   5: "YYYY年MM月DD日 HH时mm分",
    //   6: "MM-DD",
    //   7: "MM月DD日",
    //   8: "HH：mm"
    //   9: "HH时：mm分"
    //   10: "YYYY年MM月DD日 HH时mm分ss秒",
    //   11: "MM-DD HH:mm",
    getHourMinuteSecondKeyDown() {
      const fieldInfo = this.editor.editor._curFieldInfo;
      const format = fieldInfo.replace_format;
      let adjustDate = "";
      if (
        format === 1 ||
        format === 5 ||
        format === 8 ||
        format === 9 ||
        format === 11
      ) {
        const date = this.getInputDateNum(12, "HourMinuteSecond");
        adjustDate = date.replace(
          /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})/g,
          "$1-$2-$3 $4:$5:01"
        );
      } else if (format === 2 || format === 10) {
        const date = this.getInputDateNum(14, "HourMinuteSecond");
        adjustDate = date.replace(
          /(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g,
          "$1-$2-$3 $4:$5:$6"
        );
      } else if (format === 4) {
        const date = this.getInputDateNum(10, "HourMinuteSecond");
        adjustDate = date.replace(
          /(\d{4})(\d{2})(\d{2})(\d{2})/g,
          "$1-$2-$3 $4:01:01"
        );
      }

      const is_real = moment(adjustDate).format(default_date_format);
      if (is_real !== "Invalid date") {
        this.$emit("submit", adjustDate, "date");
      } else {
        this.$editor.error("请输入正确的日期");
      }
    },
    getInputDateNum(num, type = "yearMonthDay") {
      const fieldInfo = this.editor.editor._curFieldInfo;
      const format = fieldInfo.replace_format;
      let date = "";
      const dateYearMonthDay = this.matchingDate(this.getYearMonthDay, 8);
      if (type === "yearMonthDay") {
        if (!this.getHourMinuteSecond) {
          let timeFormat = "HHmmss";
          if (format === 1 || format === 5) {
            timeFormat = "HHmm";
          } else if (format === 4) {
            timeFormat = "HH";
          }
          const hourMinuteSecond = formatDateTime(
            new Date(this.field_date_text),
            timeFormat
          );
          date = this.matchingDate(dateYearMonthDay + hourMinuteSecond, num);
        } else {
          date = this.matchingDate(
            dateYearMonthDay + this.getHourMinuteSecond,
            num
          );
        }
      } else {
        let timeFormat = "HHmmss";
        if (format === 1 || format === 5 || format === 8 || format === 9) {
          timeFormat = "HHmm";
        } else if (format === 4) {
          timeFormat = "HH";
        }
        if (!this.getYearMonthDay && !this.getHourMinuteSecond) {
          date = this.matchingDate(null, num);
        } else if (!this.getYearMonthDay && this.getHourMinuteSecond) {
          const yearMonthDayDate = formatDateTime(
            new Date(this.field_date_text),
            "YYYYMMDD"
          );
          date = this.matchingDate(
            yearMonthDayDate + this.getHourMinuteSecond,
            num
          );
        } else if (this.getYearMonthDay && !this.getHourMinuteSecond) {
          if (this.getYearMonthDay.length === 8) {
            const hourMinuteSecond = formatDateTime(
              new Date(this.field_date_text),
              timeFormat
            );
            date = this.matchingDate(
              this.getYearMonthDay + hourMinuteSecond,
              num
            );
          } else {
            const hourMinuteSecond = formatDateTime(
              new Date(this.field_date_text),
              "YYYYMMDD" + timeFormat
            );
            date = this.matchingDate(hourMinuteSecond, num);
          }
        } else {
          if (this.getYearMonthDay.length === 8) {
            date = this.matchingDate(
              this.getYearMonthDay + this.getHourMinuteSecond,
              num
            );
          } else {
            const yearMonthDayDate = formatDateTime(
              new Date(this.field_date_text),
              "YYYYMMDD"
            );
            date = this.matchingDate(
              yearMonthDayDate + this.getHourMinuteSecond,
              num
            );
          }
        }
      }

      return date;
    },

    //input框输入校验，传入字符串数字和限制的长度补全或切除多余部分
    matchingDate(dateNum, size, dateType = "up", type = "normal") {
      if (dateNum) {
        if (dateNum.length > size) {
          dateNum = dateNum.slice(0, size);
        } else if (dateNum.length < size) {
          if (dateNum.length % 2 === 0) {
            dateNum = String(dateNum).padEnd(size, "01");
          } else {
            dateNum = String(dateNum).padEnd(size, "10");
          }
        }
      } else {
        if (dateType === "up") {
          if (type === "monthDay") {
            dateNum = formatDateTime(new Date(), "MMDD");
          } else {
            dateNum = formatDateTime(new Date(), "YYYYMMDD");
          }
        } else {
          dateNum = formatDateTime(new Date(), "HHmmss");
        }
      }

      return dateNum;
    },
  },
};
</script>
<style>
/*//隐藏输入框*/
.editorDatePickPanelClassField .ant-calendar-input,
.editorDatePickPanelClassField .ant-calendar-picker,
.editorDatePickPanelClassField .ant-calendar-input-wrap {
  display: none;
}
.editorDatePickPanelClassField .ant-calendar-date:hover,
/*.datePickPanelClass .ant-calendar-today .ant-calendar-date,*/
/*.datePickPanelClass .ant-calendar-selected-day .ant-calendar-date,*/
.editorDatePickPanelClassField .ant-calendar-time-picker-select li:hover {
  background: #eeeeee;
}

.editorDatePickPanelClassField {
  z-index: 99999 !important;
}
.field-date-picker-input {
  display: flex;
  height: 38px;
  width: 282px;
  margin-left: -2px;
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(238, 238, 238);
  box-shadow: 0 0 10px rgb(238, 238, 238);
}
.ant-input-prefix .anticon {
  margin-top: -4px;
  color: rgba(0, 0, 0, 0.3);
}

.editorDatePickPanelClassField .ant-time-picker-panel-input-wrap {
  max-width: 132px;
}
.editorDatePickPanelClassField .ant-time-picker-panel-select {
  width: 50%;
}
.editorDatePickPanelClassField .ant-time-picker-panel-select ul {
  width: 100%;
}
/*.datePickPanelClass .ant-calendar-selected-day .ant-calendar-date,*/
/*.datePickPanelClass .ant-calendar-today .ant-calendar-date {*/
/*  color: #6f6f64;*/
/*  border-color: #6f6f64;*/
/*}*/
/*.datePickPanelClass .ant-calendar .ant-calendar-today-btn,*/
/*.datePickPanelClass .ant-calendar .ant-calendar-clear-btn,*/
/*.datePickPanelClass*/
/*  .ant-calendar-time*/
/*  .ant-calendar-footer*/
/*  .ant-calendar-time-picker-btn,*/
/*.datePickPanelClass .ant-calendar-time-picker-select li:focus {*/
/*  color: #6f6f64;*/
/*}*/
</style>
