/**
 * 批注功能相关方法
 */
const commentMixIn = {
  data() {
    return {
      selectCommentId: "-1", //选中的批注id
      isShowCommentList: false, //是否展示批注列表
      isOpenCommentEditModal: false, //是否打开批注编辑窗口
      curComment: null, //当前激活的批注
      comments: [], //批注列表内容
    };
  },
  methods: {
    refreshCommentList() {
      //表示从未添加过批注信息
      if (!this.editor.document_meta.commentsIDSet) {
        this.comments = [];
        return;
      }
      //展示所有的批注信息
      this.comments = Object.values(
        this.editor.document_meta.commentsIDSet
      ).flat();
      this.editor.render();
    },
    //打开批注列表
    openCommentList() {
      this.editor.internal.toggleCommentMode(true);
      this.refreshCommentList();
      if (!this.comments.length) {
        this.$editor.info("当前文档不存在批注信息！");
        return;
      }
      // if (this.instance.localTest.transUse) {
      //   this.isShowCommentList = true;
      // }
      this.editor.updateCanvasSize();
    },
    testComment() {
      const options = {
        hideDeleteOperationBtn: true,
        hideReplaceOperationBtn: true,
        hideDate: true,
        useNewTitle: "无敌批注",
        hideCloseButton: true,
      };
      this.openCommentList(options);
    },
    //关闭批注列表
    closeCommentList() {
      this.editor.internal.toggleCommentMode(false);
      this.isShowCommentList = false;
      this.editor.updateCanvasSize();
    },
    //打开批注编辑窗口，根据item判断是修改还是添加
    openCommentEditModal(item) {
      if (item) {
        this.curComment = item;
      }
      this.isOpenCommentEditModal = true;
    },
    //关闭批注编辑窗口
    closeCommentEditorModal() {
      this.isOpenCommentEditModal = false;
      this.curComment = null;
    },
    // 批注修改完成确定后
    submitComment(value, isEdit, commentStayTop) {
      let readonlyMark = false;
      if (this.editor.readonly) {
        this.editor.setReadonly(false);
        readonlyMark = true;
      }
      if (!value) {
        return this.$editor.warning("内容为空！");
      }
      if (!isEdit) {
        const selectedChars =
          this.editor.selection.selected_fields_chars.all_chars;
        if (
          !selectedChars ||
          !selectedChars.find((char) => char.field_position === "normal")
        ) {
          return this.$editor.warning("请先选中需要添加批注的文本！");
        }
      }
      if (this.curComment) {
        this.curComment.value = value;
        //更新批注内容
        this.editor.internal.updateComments(this.curComment.id, value);
        this.editor.update();
        if (this.editor.useNewVersionCommentList) {
          this.editor.newVersionOpenCommentMap.set(this.curComment.id, true);
          F: for (const page of this.editor.pages) {
            for (const box of page.commentBox) {
              if (box.comment.id === this.curComment.id) {
                page.currentComment = box.comment;
                page.setCommentBox();
                break F;
              }
            }
          }
        } else {
          for (const box of this.editor.commentBox) {
            if (box.comment.id === this.curComment.id) {
              this.editor.internal.currentComment = box.comment;
            }
          }
        }
        this.editor.render();
      } else {
        //新增批注
        this.editor.internal.toggleCommentMode(true);
        this.selectCommentId = this.editor.addComment({
          value,
        });
        this.refreshCommentList();
      }
      if (!commentStayTop) {
        this.isOpenCommentEditModal = false;
      }
      this.curComment = null;
      if (readonlyMark) {
        this.editor.setReadonly(true);
      }
    },
    //删除批注内容
    deleteComment(item) {
      let readonlyMark;
      if (this.editor.readonly) {
        this.editor.setReadonly(false);
        readonlyMark = true;
      }
      this.editor.uncomments(item.id);
      this.refreshCommentList();
      if (readonlyMark) {
        this.editor.setReadonly(true);
      }
    },
    //从右侧批注列表中选中一条批注
    selectComment(item) {
      this.selectCommentId = item.id;
      //激活对应的字符
      const res = this.editor.internal.addHightLighterByCommentID(
        this.selectCommentId
      );
      //res 为 true 代表找到了对应的字符并设置好了样式 ， 为false 代表未从文档中找到对应字符
      if (res) {
        this.editor.render();
      } else {
        this.refreshCommentList();
        this.$editor.warning("文档中对应内容已删除！列表已刷新");
      }
    },
    //光标点击事件触发的激活右侧批注信息
    activeSelectComment(curClickInfo) {
      if (!this.isShowCommentList) {
        return;
      }
      if (curClickInfo.element && curClickInfo.element.comment_id) {
        this.selectCommentId = curClickInfo.element.comment_id;
      } else {
        this.selectCommentId = "-1";
      }
    },

    replaceContent(item) {
      const id = item.id;
      const elements = this.editor.internal.getInfoByCommentID(id);
      const paraPath = elements.para_path;
      const endPath = elements.end_path;
      if (paraPath.length && endPath.length) {
        this.editor.selection.setSelectionByPath(
          paraPath,
          endPath,
          "para_path"
        );
        this.editor.delete_backward();
        // this.editor.deleteContentByPath(paraPath, endPath);
        const modelPath = this.editor.paraPath2ModelPath(paraPath);
        this.editor.selection.setCursorPosition(modelPath);
        this.editor.insertText(item.value);
        this.editor.update();
        this.editor.render();
      }
    },
  },
};
export default commentMixIn;
