import Editor from "./Editor";
import { Config } from "./Config";
import { isEmptyObj } from "./Utils";
import EditorLocalTest from "../../localtest";
import { isUseCurrentLogic } from "./Helper";
const err_src = Config.error_src;

export default class ImageMap {
  imageMap = new Map();

  editor: Editor;

  constructor (editor:Editor) {
    this.editor = editor;
  }

  get () {
    return this.imageMap;
  }

  /**
   *
   * @param img modelData 中的 image
   * @returns
   */
  addOnload(img: any) {
    let src = img.src;
    if (!src.startsWith("http")) {
      if (!src.startsWith("data") && !src.startsWith("image")) {
        src = "data:image/png;base64," + src;
        img.src = src;
      }
    }
    if (this.imageMap.has(src)) return;
    if (!isEmptyObj(Config.configFontJson)) {
      this.imageMap.set(src, "");
      return;
    }
    const image = new Image();
    let url = src;
    if(isUseCurrentLogic(this.editor)){
      url = (img.url && img.url.startsWith("http")) ? img.url : src;
    }

    if(this.editor.isMobileTerminal() && url.startsWith("http")){
      const key = ".msunhis.com/msun-middle-base-common"
      if(url){
        const urlSplit =  url.split(key);
        if(urlSplit.length === 2 && !urlSplit[0].endsWith("-net")){
          url = urlSplit[0] + "-net" + key + urlSplit[1]
        }
      }
    }

    image.src = url;

    image.crossOrigin = "Anonymous";
    const map_value = {
      data: image,
      isLoaded: false
    };

    let retryCount = 0;
    const maxRetries = 3;
    const retryInterval = 500;

    const loadImage = () => {
      image.onload = () => {
        map_value.isLoaded = true;
        this.editor.render();
      };

      image.onerror = () => {
        if (retryCount < maxRetries) {
          retryCount++;
          setTimeout(() => {
            image.src = url; // 重新设置 src 以触发加载
          }, retryInterval);
        } else {
          image.src = err_src;
          map_value.data = image;
          map_value.isLoaded = true;
        }
      };
    };

    loadImage();
    this.imageMap.set(img.src, map_value);
  }


  refreshImage (key:string) {
    if (EditorLocalTest.useLocal) {
      // TODO 解决缩放后出现波纹的问题
      const image = this.imageMap.get(key);
      if (!image) { return; }
      if (image.data.src.startsWith("http")) {
        if (image.data.src.indexOf("?") > -1) {
          image.data.src = image.data.src + "&a=" + Math.random();
        } else {
          image.data.src = image.data.src + "?a=" + Math.random();
        }
      } else {
        image.data.src = this.getBlobUrlByBase64(key);
      }
    }
  }

  getBlobUrlByBase64 (base64Src:string) {
    const base64Arr = base64Src.split(",");
    let base64 = base64Arr[1];
    if (!base64) {
      base64 = base64Src;
    }
    // 将Base64字符串转换为Blob对象
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: "image/png" }); // 根据实际图片类型设置type

    // 将Blob对象转换为Blob URL
    const src = URL.createObjectURL(blob);
    return src;
  }

  remove (key:string) {
    this.imageMap.delete(key);
  }

  clear () {
    this.imageMap.clear();
  }
}
