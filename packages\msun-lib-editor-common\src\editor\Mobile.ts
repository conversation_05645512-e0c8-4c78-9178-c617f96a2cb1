import Editor from "./Editor";
import EditorHelper from "./EditorHelper";

//是否点击在选区两个小水滴上
export function isInMobileEdit(x: number, y: number, editor: Editor) {
  const selection = editor.selection.selected_areas
  if (selection.length) {
    const startPath = selection[0].start_para_path
    const endPath = selection[selection.length - 1].end_para_path
    const startModelPath = editor.paraPath2ModelPath(startPath)
    const endModelPath = editor.paraPath2ModelPath(endPath)
    const startInfo = editor.getElementByModelPath([...startModelPath], editor)
    const endInfo = editor.getElementByModelPath([...endModelPath], editor)
    if (startInfo.row && endInfo.row) {
      const startViewPath = editor.modelPath2viewPath(startModelPath)
      const endViewPath = editor.modelPath2viewPath(endModelPath)
      const startXY = editor.getElementAbsolutePositionByViewPath(startViewPath)
      const endXY = editor.getElementAbsolutePositionByViewPath(endViewPath)
      const startHeight = startInfo.row.height
      const endHeight = endInfo.row.height
      const startX = startXY.x
      const startY = startXY.y + startHeight
      const endX = endXY.x
      const endY = endXY.y + endHeight
      const radius = editor.internal.mobile_radius * 3
      if (startX - radius <= x && x <= startX && startY <= y && y <= startY + radius) {
        return "start"
      } else if (endX <= x && x <= endX + radius && endY <= y && y <= endY + radius) {
        return "end"
      }
    }
  }
  if (editor.selection.selected_cells_path.length) {
    const anchorXY = { x: 0, y: 0, height: 0 }
    const focusXY = { x: 0, y: 0, height: 0 }
    const compareResult = EditorHelper.compareAnchorAndFocusIsBig(editor.selection.anchor, editor.selection.focus)
    if (editor.selection.anchor.length === 4) {
      const anchorTable = editor.getElementByModelPath([...editor.selection.anchor], editor).table;
      if (anchorTable) {
        anchorXY.x = editor.config.page_padding_left + editor.page_left
        anchorXY.height = anchorTable.children[0].children[0].height
        const viewPath = editor.modelPath2viewPath(anchorTable.start_path)
        anchorXY.y = editor.getElementAbsolutePositionByViewPath([...viewPath]).y
        if (compareResult === "anchor") {
          anchorXY.x = editor.page_left + editor.page_size.width - editor.config.page_padding_right
          anchorXY.y = editor.getElementAbsolutePositionByViewPath([...viewPath]).y + anchorTable.height - anchorXY.height
        }
      }
    }
    if (editor.selection.focus.length === 4) {
      const focusTable = editor.getElementByModelPath([...editor.selection.focus], editor).table;
      if (focusTable) {
        focusXY.x = editor.page_left + editor.page_size.width - editor.config.page_padding_right
        focusXY.height = focusTable.children[0].children[0].height
        const viewPath = editor.modelPath2viewPath(focusTable.start_path)
        focusXY.y = editor.getElementAbsolutePositionByViewPath([...viewPath]).y + focusTable.height - focusXY.height
        if (compareResult === "anchor") {
          focusXY.x = editor.config.page_padding_left + editor.page_left
          focusXY.y = editor.getElementAbsolutePositionByViewPath([...viewPath]).y
        }
      }
    }
    const radius = editor.internal.mobile_radius * 3
    if (anchorXY.y + anchorXY.height <= y && y <= anchorXY.y + anchorXY.height + radius) {
      if (compareResult === "focus") {
        if (anchorXY.x - radius <= x && x <= anchorXY.x) {
          return "start"
        }
      } else if (compareResult === "anchor") {
        if (anchorXY.x <= x && x <= anchorXY.x + radius) {
          return "end"
        }
      }
    } else if (focusXY.y + focusXY.height <= y && y <= focusXY.y + focusXY.height + radius) {
      if (compareResult === "focus") {
        if (focusXY.x <= x && x <= focusXY.x + radius) {
          return "end"
        }
      } else if (compareResult === "anchor") {
        if (focusXY.x - radius <= x && x <= focusXY.x) {
          return "start"
        }
      }
    }
  }

  return false
}
//是否点击在非选区小水滴上
export function isInPointerDownMobileEdit(x: number, y: number, editor: Editor) {
  const ele_x = editor.caret.x
  const ele_y = Math.round(editor.caret.y + editor.caret.height) 
  const radius = editor.internal.mobile_radius * 3
  if (ele_x - radius <= x && x <= ele_x + radius && ele_y <= y && y <= ele_y + radius) {

    return true
  }
  return false
}
export function setMobileSelection(info: any, editor: Editor) {
  if (!info.element) return
  const index = info.row.children.indexOf(info.element)
  const startPath = [...info.model_path]
  const endPath = [...startPath]
  startPath[startPath.length - 1] = index
  endPath[endPath.length - 1] = index + 1
  editor.internal.VL.is_mobile_selection = true
  editor.selection.setSelectionByPath(
    startPath,
    endPath,
    "model_path"
  );
  editor.render()
}