<template>
  <div class="input-select msun-editor-forms-scrollbar">
    <div
      v-for="(item, i) in transformList"
      :key="i"
      :title="selectComSentencesList[i].name"
      :id="'selectList' + editorId + i"
      :class="index === i ? 'input-select-selected' : 'input-select-unselected'"
      @click="clickSelect(i)"
    >
      <div v-html="transformList[i]"></div>
    </div>
  </div>
</template>

<script>
export default {
  name: "inputSelect",
  data() {
    return {
      style: {
        position: "fixed",
        left: 0,
        top: 0,
      },
      transformList: [],
    };
  },
  props: {
    index: {
      type: Number,
      default: 0,
    },
    selectComSentencesList: {
      type: Array,
      default: () => [],
    },
    editorId: {
      type: String,
      default: "",
    },
    selectText: {
      type: String,
      default: "",
    },
    showInputSelect: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    selectComSentencesList(val) {
      this.transformList = [];
      const allList = [];
      for (let i = 0; i < val.length; i++) {
        let sentence = val[i].name;
        let keywords = [];
        if (val[i].keyword) {
          keywords = val[i].keyword.split(/[，,]/); // 仅使用中文逗号分隔
        }
        if (this.selectText !== "") {
          const searchText = this.selectText.toLowerCase(); // 将搜索文本转换为小写，以便不区分大小写匹配
          if (
            sentence.toLowerCase().includes(searchText) ||
            keywords.some((keyword) =>
              keyword.toLowerCase().includes(searchText)
            )
          ) {
            const highlightedSentence = sentence.replace(
              new RegExp(searchText, "gi"),
              "<span style='font-weight: 900; color: #000'>$&</span>"
            );
            allList.push(highlightedSentence);
          }
        } else {
          allList.push(sentence);
        }
      }
      this.transformList = allList;
    },
    immediate: true,
  },
  mounted() {},
  methods: {
    clickSelect(val) {
      this.$emit("changeSelectIndex", val);
    },
  },
};
</script>

<style lang="less" scoped>
.input-select {
  background-color: #fff;
  color: black;
  z-index: 8;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(100, 100, 100, 0.4);
  max-height: 155px;
  // padding-top: 5px;
  // padding-bottom: 5px;
  overflow-y: auto;
}
.input-select-selected {
  background-color: #e6f7ff;
  padding: 0 15px;
  cursor: pointer;
  max-width: 600px;
  line-height: 31px;
  display: -webkit-box;
  overflow: hidden;
  /* 可以显示的行数，超出部分用...表示*/
  -webkit-box-orient: vertical;
  /*控制显示行数*/
  -webkit-line-clamp: 1;
}
.input-select-unselected {
  background-color: rgb(255, 255, 255);
  padding: 0 15px;
  cursor: pointer;
  max-width: 600px;
  line-height: 31px;
  display: -webkit-box;
  overflow: hidden;
  /* 可以显示的行数，超出部分用...表示*/
  -webkit-box-orient: vertical;
  /*控制显示行数*/
  -webkit-line-clamp: 1;
}
.input-select-unselected:hover {
  background-color: #e6f7ff;
  padding: 0 15px;
  cursor: pointer;
  line-height: 31px;
  max-width: 600px;

  display: -webkit-box;
  overflow: hidden;
  /* 可以显示的行数，超出部分用...表示*/
  -webkit-box-orient: vertical;
  /*控制显示行数*/
  -webkit-line-clamp: 1;
}

.msun-editor-forms-scrollbar {
  &::-webkit-scrollbar {
    width: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #cccccc;
    border-radius: 6px;
  }
  /* 鼠标悬停时滑块样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #bbbbbb;
  }

  &::-webkit-scrollbar-track {
    background-color: white;
    -webkit-box-shadow: inset 0 0 4px rgba(100, 100, 100, 0.01);
  }
}
</style>
