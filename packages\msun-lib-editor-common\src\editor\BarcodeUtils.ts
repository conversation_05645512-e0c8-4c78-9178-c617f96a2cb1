
// @ts-ignore
import _QRCode from "jr-qrcode";
import JsBarcode from "jsbarcode";

// 用于检查文本是否合法
// @ts-ignore
import Barcodes from "jsbarcode/src/barcodes";
// @ts-ignore
import defaultBarcodeOptions from "jsbarcode/src/options/defaults";

const QRCode = _QRCode as JrQrcode;
export enum BarcodeType {
  /** 二维码 */
  qrcode = 'qrcode',
  /** 条形码 */
  barcode = 'barcode'
}
type JrQrcode = {
  QRErrorCorrectLevel: {
    L: 1,
    M: 0,
    Q: 3,
    H: 2
  }
  options: {
    /**
     * 二维码四边空白
     * @default 10
     */
    padding?: number,
    /**
     * 二维码图片宽度（默认为256px）
     * @default 256
     */
    width?: number,
    /**
     * 二维码图片高度（默认为256px）
     * @default 256
     */
    height?: number,
    /**
     * 二维码容错level（默认为高）
     * @default 2
     */
    correctLevel?: JrQrcode["QRErrorCorrectLevel"][keyof JrQrcode["QRErrorCorrectLevel"]],
    /**
     * 反色二维码，二维码颜色为上层容器的背景颜色
     * @default false
     */
    reverse?: boolean,
    /**
     * 二维码背景颜色（默认白色）
     * @default "#ffffff"
     */
    background?: string,
    /**
     * 二维码前景颜色（默认黑色）
     * @default "#000000"
     */
    foreground?: string
  }
  getQrBase64: (text: string, options?: JrQrcode["options"]) => string
}

// 方便外面引用
export type QrcodeOptions = JrQrcode["options"]
export type BarcodeOptions = JsBarcode.Options & { format: BarcodeFormat }

/**
 * 这个BarcodeInfo统指二维码和条形码
 */
export type BarcodeInfo = {
  type: BarcodeType.barcode,
  options?: BarcodeOptions
} | {
  type: BarcodeType.qrcode,
  options?: QrcodeOptions
}

/**
 * 检验文本是否为合法的二维码文本
 */
export function validateQRCodeText (text: string): boolean {
  if (!text) { return false; }

  return text.length <= 1200; // 简单粗暴
}


export function getBarcodeSrc(text: string,barcodeInfo:BarcodeInfo): string {

  if (barcodeInfo?.type === BarcodeType.qrcode) {
    return textToQRCodeSrc(text);
  }
  if (barcodeInfo?.type === BarcodeType.barcode) {
    return textToBarCodeSrc(text,barcodeInfo.options);
  }
  throw new Error('不支持的条码类型');
}

/**
 * 通过string生成png二维码的base64 data url
 */
export function textToQRCodeImage (text: string, options?: QrcodeOptions): HTMLImageElement {
  const img = document.createElement("img");
  img.setAttribute("src", textToQRCodeSrc(text, options));
  return img;
}

const defaultQRCodeCacheMap = new Map<string, string>();

export function textToQRCodeSrc (text: string, options?: QrcodeOptions): string {
  // 优先使用缓存
  const cacheKey = JSON.stringify({ text, options });
  const cachedSrc = defaultQRCodeCacheMap.get(cacheKey);
  if (cachedSrc) {
    return cachedSrc;
  }
  const base64Src = QRCode.getQrBase64(text, options);
  defaultQRCodeCacheMap.set(cacheKey, base64Src);
  return base64Src;
}

const AVAILABLE_BARCODE_FORMATS = [
  "CODE39", "CODE128",
  // "CODE128A", "CODE128B", "CODE128C",
  "EAN13", "EAN8",
  // "EAN5", "EAN2",
  // "UPC",
  "UPCE",
  // "ITF14",
  "ITF"
  // "MSI", "MSI10", "MSI11", "MSI1010", "MSI1110",
  // "pharmacode",
  // "codabar",
  // "GenericBarcode"
] as const;

export type BarcodeFormat =
  | typeof AVAILABLE_BARCODE_FORMATS[number]
  | "auto" // 缺省值

/**
 * 检查文本是否为合法的条形码文本
 */
export function validateBarcodeText (text: string, options?: BarcodeOptions): boolean {
  if (!text) { return false; }
  options = { ...defaultBarcodeOptions as BarcodeOptions, ...options };
  if (options.format === "auto") {
    options.format = "CODE128";
  }
  return new Barcodes[options.format](text, options).valid();
}

// const defaultBarcodeCacheMap = new Map<string, HTMLImageElement>();

// export function textToBarCodeImage (text: string, options?: BarcodeOptions): HTMLImageElement {
//   // 优先使用缓存
//   const cacheKey = JSON.stringify({ text, options });
//   const cachedImg = defaultBarcodeCacheMap.get(cacheKey);
//   if (cachedImg) {
//     return cachedImg;
//   }
//   const img = document.createElement("img");

//   JsBarcode(img, text, { ...options });
//   defaultBarcodeCacheMap.set(cacheKey, img);
//   return img;
// }

// export function textToBarCodeSrc (text: string, options?: BarcodeOptions): string {
//   const img = textToBarCodeImage(text, options);
//   return img.src;
// }
const defaultBarcodeCacheMap = new Map<string, string>()
const barcodeCanvas = document.createElement('canvas')

export function textToBarCodeImage(text: string, options?: BarcodeOptions): HTMLImageElement {
  const img = document.createElement('img')
  img.setAttribute('src', textToBarCodeSrc(text, options))
  return img
}

export function textToBarCodeSrc(text: string, options?: BarcodeOptions): string {
  // 优先使用缓存
  const cacheKey = JSON.stringify({ text, options })
  const cachedSrc = defaultBarcodeCacheMap.get(cacheKey)
  if (cachedSrc) {
    return cachedSrc
  }

  JsBarcode(barcodeCanvas, text, options)
  const imgSrc = barcodeCanvas.toDataURL()
  defaultBarcodeCacheMap.set(cacheKey, imgSrc)
  return imgSrc

}
// 通用方法
export function validateTextByInfo(
  text: string,
  barcodeInfo: BarcodeInfo
): boolean {
  const { type, options } = barcodeInfo

  switch (type) {
    case BarcodeType.barcode: {
      
      return validateBarcodeText(text, options)
    }
    case BarcodeType.qrcode: {
      return validateQRCodeText(text)
    }
    default: {
      return false
    }
  }
}

/**
 * setBarcodeInfo
 *
 * 获得文本 和 条宽信息， cell bounding信息
 *
 * ## 二维码
 *
 * 文本 -> 二维码
 *
 * -> 计算范围
 * -> 渲染
 *
 * ## 条形码
 *
 * 1. 固定高度：
 * 条宽, cell高, 文本 -> 条形码
 *
 * -> 计算范围
 * -> 渲染
 *
 * 2. 固定宽高比
 * 默认条宽，文本 -> 条形码
 *
 * -> 计算范围
 * -> 渲染
 */
