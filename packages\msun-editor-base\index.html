<!doctype html>
<html style="width: 100%; height: 100%" lang="en">
<head>
  <meta charset="utf-8">
  <meta>
  <title></title>
  <style>
    html,body{
      margin:0px;
      width: 100%;
      height: 100%;
      overflow-Y: hidden;
    }
    .button_list{
      width: 350px;
      position: fixed;
      background: white;
      padding: 10px;
      height: calc(100% - 40px);
      overflow-Y: scroll;
      margin: 0 0 0 10px;
      top: 10px;
      border: 1px solid rgb(203,203,203);
    }
    .content{
      /* height: 930px;
      width: 1920px; */
      display: flex;
      width: 100%;
      height: 100%;
      margin:0 0 0 0px;
      overflow-Y: hidden;
    }
    .fieldset{
      border: 1px solid rgb(24, 144, 255);
    }
    .fieldset-color{
      font-family: "微软雅黑";
      color: red;
      font-weight: 600;
    }
    #btn-show-hide {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 99999;
    }
    #testChangeFontSize {
      width: 140px;
      height: 40px;
      position: fixed;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #ccc;
      text-align: center;
      line-height: 40px;
      display: none;
    }
  </style>
</head>
<body style="width: 100%; height: 100%" >
  <button id="btn-show-hide" onclick="setBtnListShowHide()">展示/隐藏</button>
  <button id="testChangeFontSize"></button>
  <div style="width: 100%; height: 100%" id="changeCanvas" >
    <div class="button_list" style="margin-left: 0px;line-height: 28px;">
      <div ondragstart="handler_drag_start(event)" draggable="true">这是一段测试拖拽的文字</div>
      <div id="innerForm"></div>
    </div>
    <div style="width: 100%; height: 100%;display: flex;">
      <div style="height: 100%;width: 100px;"></div>
      <div class="content" id="content"></div>
    </div>
</body>
<script src="./src/rawData.js"> </script>
<script src="./src/ActionButton.js"> </script>
<script>
   function setBtnListShowHide () {
      const btnListDiv = document.querySelector(".button_list")
      const display = btnListDiv.style.display;
      if (!display || display === "block") {
        btnListDiv.style.display = "none";
      } else {
        btnListDiv.style.display = "block";
      }
    }
    function handler_drag_start (e) {
      const flag = "$$drag_innerHTML$$"
      let drag_str = flag + e.target.innerHTML;
      const config = {
        placeholder: "placeholder"
      }
      drag_str += "$$dragConfig$$" + JSON.stringify(config) + "$$dragConfig$$";
      e.dataTransfer.setData("text/plain", drag_str);
    }
  </script>
  <script type="module">
    import initEditor from "./src/index.ts"

     let localStorage_rawData=localStorage.getItem("rawData")
       if (!localStorage_rawData) {
            localStorage_rawData=rawData
        }else{
         localStorage_rawData = localStorage_rawData.replace(/^"|"$/g, '');
       }
      const Editor = initEditor("#content")
      const {editor} = Editor;
      editor.reInitRaw(localStorage_rawData)
      editor.refreshDocument();
      Editor.rawData = rawData; //还原操作使用
      const allBtn = actionButton(Editor);
      const innerForm = document.getElementById("innerForm")

      Editor.editor.event.on("beforeKeydown",(event)=> {
        if (event.ctrlKey || event.metaKey) {
          if (event.key.toLocaleLowerCase() === "s") { // 不知道 阻止默认行为有没有额外的损失就先 在不是 ctrl + v 的时候阻止吧 否则就把 paste 事件也给阻止掉了
            const rawData = Editor.editor.getRawData();
            localStorage.setItem("rawData", JSON.stringify(rawData));
            // const titleMsgDom = document.getElementById("titleMsg")
            // titleMsgDom.innerText = "已保存！！！！！！！！！！！！！！！"
            // setTimeout(()=>{
              // titleMsgDom.innerText = ""
            // },1000)
          }
        }
      })

      for (let index = 0; index < allBtn.length; index++) {
          const groupObj = allBtn[index];
          const form = document.createElement("form")
          const fieldset = document.createElement("fieldset")
          fieldset.className = "fieldset"
          form.append(fieldset)
         const legend = document.createElement("legend")
         legend.className = "fieldset-color"
         legend.innerText = groupObj.title;
         fieldset.append(legend)
          if(groupObj.children.length){
            for (let i = 0; i < groupObj.children.length; i++) {
              const btn = groupObj.children[i];
              if(btn.type === "input"){

              }else{
                const buttonDom = document.createElement("button")
                buttonDom.type = "button"
                buttonDom.innerText = btn.title;
                fieldset.append(buttonDom)
                buttonDom.onmousedown = (e)=>{ beforeExeFun(e,btn) }
              }
            }
          }
        innerForm.append(form)
    }
    function beforeExeFun(e,btn){
        let args = []
        if(btn.input === "prompt"){
          const res =  prompt("请需要的参数,多个使用逗号隔开", "arg1,arg2,arg3");
          args = res.split(/[\u002C|\uFF0C]/g)
        }
        btn.exe(e,args)
    }

</script>
</html>
