import { isFunction, getUUID } from "../assets/js/utils";

// import dataList from "../assets/js/test";
import DEFAULTS from "../config";
import WebSocketClient from "./websocket";
import jsPDF from "jspdf";
import { predownloadOnlineImageForPrintJson } from "../assets/js/predownloadOnlineImage";

/** 创建Deferred对象 */
const createDeferred = () => {
  const deferred = {};
  deferred.promise = new Promise((resolve, reject) => {
    Object.assign(deferred, { resolve, reject });
  });
  return deferred;
};

const printMixin = {
  data() {
    return {
      pdfPrintDataQuene: [], // pdf打印队列
      fontBase64Map: {}, // 字体文件base64
      fontLoaded: false, // 字体文件加载是否完成
      pdfFontFileLoading: false, //字体文件正在加载状态
      localFontFilePath:
        "C:/Msunsoft/busielectron/extlibs/staticLibs/editorPrint/V1.0/",
      printerList: [],
      srcs: [],
      showSystemPrint: false,
      defaultOpts: DEFAULTS.BasePrint,
      socket: null,
      _pluginStatus: "",
      _subscribers: "",
      name: DEFAULTS.BasePrint.name,
      pageTypes: [
        {
          pageSizeName: "A4",
          pageSizeWidth: 827,
          pageSizeHeight: 1169,
        },
        {
          pageSizeName: "A5",
          pageSizeWidth: 585,
          pageSizeHeight: 827,
        },
        {
          pageSizeName: "B5",
          pageSizeWidth: 686,
          pageSizeHeight: 974,
        },
        {
          pageSizeName: "B5(JIS)",
          pageSizeWidth: 710,
          pageSizeHeight: 1003,
        },
        {
          pageSizeName: "16K",
          pageSizeWidth: 768,
          pageSizeHeight: 1063,
        },
        {
          pageSizeName: "16K(BIG)",
          pageSizeWidth: 768,
          pageSizeHeight: 1067,
        },
      ],
      keepPrint: 0,
      print_plugin_version: 0, // 托盘版本
      isElectron: false, // 是否在众阳浏览器里
      electronVersion: 0, // 众阳浏览器版本
      isStartUpSocket: true,
    };
  },
  components: {},
  mounted() {
    this.judgeIsElectron();
    if (this.isElectron) {
      // 获取打印机版本
      this.getElectronVersion();
    } else {
      this.initSocket();
    }
  },
  methods: {
    judgeIsElectron() {
      const userAgent = navigator.userAgent.toLowerCase();
      if (
        userAgent.indexOf("electron/") > -1 ||
        (this.$workEnv &&
          this.$workEnv.isElectronEnv &&
          this.$workEnv.isElectronEnv())
      ) {
        this.isElectron = true;
      } else {
        this.isElectron = false;
      }
    },
    /** 初始化socket */
    initSocket() {
      const isMobile = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      if (isMobile) return;
      const { socketUrl, socketOptions } = this.defaultOpts; // 这些都是直接从配置当中取的 写死的
      if (this.socket) {
        if (this.socket.instance.readyState === 1) {
          this.socket.onMessage = null;
          this.socket.onClose = null;
          this.socket.onError = null;
          // 关闭先前的连接
          this.socket.instance.close();
          this.socket = null;
        }
      }

      this.socket = new WebSocketClient(socketUrl, socketOptions); // 自己创建的一个类
      this.bindSocketEvent();

      // socket连接
      this.socket.connect();

      const result_connect = new Promise((resolve) => {
        this.socket.waitForConnection(resolve);
      });
      // 初始化打印数据
      this.request(this.print_parameter);
      return result_connect;
    },

    /** 绑定socket事件 */
    bindSocketEvent() {
      this.socket.onMessage = this.handleMessage.bind(this); // TODO 都是同一个 this 为什么要用 bind 替换掉 this 重新生成一个函数呢

      this.socket.onClose = (event) => {
        // 连接意外关闭后，重置状态
        this._initialized = false;

        // 连接不应被关闭，触发错误
        const reason = event.wasClean ? "aborted" : "aborted_noclean";
        isFunction(this.onError) && this.onError(reason);
      };

      this.socket.onError = (event) => {
        // 代表没启动socket
        this.isStartUpSocket = false;
        this.printerList = [];
        // 如果检测到托盘出问题 第一时间清空,让用户明确知道托盘未正常启动
        // this.$editor.error("打印托盘未正常启动，不能正常打印！");
        // if (localStorage.getItem("editorSavePrinterConfig")) {
        //   localStorage.removeItem("editorSavePrinterConfig");
        // }
        isFunction(this.onError) && this.onError(event.reason || "error");
      };
    },

    /**
     * 处理托盘版本并保存
     * @param {string} version 托盘版本
     */
    handleVersion(version) {
      console.log("version :>> ", version);
      if (!version) return;
      const version_parts = version.split(".");
      let result = "";
      if (version_parts.length > 1) {
        version_parts.forEach((part, index) => {
          result += index > 0 ? part : part + ".";
        });
      } else {
        result = version_parts[0];
      }
      return result * 1;
    },
    /** 接收并处理消息 */
    handleMessage(event) {
      // 代表启动了socket
      this.isStartUpSocket = true;
      const data = JSON.parse(event.data) || {};
      if (data.keyword === "getPrinterListForWebResult") {
        const selectPrinter = data.content.filter((item) => item.isDefault)[0];
        const params = {
          printerList: data.content,
          selectPrinter: selectPrinter && selectPrinter.printerName,
        };
        this.localPrintConfigOption("set", params);
      }
      const { application, keyword, content } = data;
      if (application !== this.name) {
        return;
      }

      // 状态监测消息
      if (keyword === "getPluginStatusResult") {
        this._pluginStatus = content.result;
        return;
      }

      // 打印业务消息
      if (
        keyword === "printPluginForWebResult" ||
        keyword === "printPluginForWebResultError"
      ) {
        // 执行订阅接收
        this._subscribers.slice().forEach((sub) => sub(data));
      }
    },

    /** 发送请求 */
    request(content) {
      const body = this.normalizeRequestBody(content);
      //   const { printRecordId } = body.content;

      const deferred = createDeferred();
      // 发送请求消息
      this.socket.sendObj(body);
      // 等待插件就绪
      return deferred.promise;
    },
    /** 等待插件准备就绪, 轮询监听初始化状态 */
    waitForPluginReady(callback, interval = 1000) {
      if (this._initialized) {
        if (this._pluginStatus === 0) {
          callback();
        } else {
          this.initPluginOnFault().then(callback);
        }
      } else if (this._initializing) {
        setTimeout(() => {
          this.waitForPluginReady(callback, interval);
        }, interval);
      }
    },
    /** 规范请求消息数据 */
    normalizeRequestBody(content) {
      let body = null;
      if (content.application && content.keyword && content.content) {
        body = content;
      } else {
        body = {
          type: 0,
          application: this.name,
          keyword: "printPluginForWeb",
          content,
        };
      }
      // printRecordId 设置
      body.content.printRecordId =
        body.content.printRecordId || this.generateRecordId();
      return body;
    },
    /** 生成 printRecordId */
    generateRecordId() {
      return Date.now() + Math.random().toString(16).substr(1);
    },
    /**
     * 打开系统打印预览页面
     * @param {function} afterPrint 回调函数
     * @param {string} printViewType 打印类型
     * @returns 展示预览页面
     */
    openSystemPrint(afterPrint, printViewType = "json") {
      // 不是在新浏览器中，可以设置预览模式：pdf预览和系统打印的预览页面
      if (printViewType === "pdf" && !this.isElectron) {
        // pdf预览页面
        this.convertPDF("print", afterPrint);
        return;
      }
      this.$refs.system_print.afterPrint = afterPrint;
      this.$refs.system_print.printType = printViewType;
      const { newPrintEditor } = this.editor.event.emit("beforePrintView");
      if (newPrintEditor) {
        this.newPrintEditor = newPrintEditor;
      }
      this.setPrinterList();
      this.showSystemPrint = true;
    },
    // 获取参数
    // 众阳浏览器打印需要的参数
    async setElectronParameter(printerName, extraInfo, printMultiple, pages) {
      const editor = this.newPrintEditor ?? this.instance.editor;
      editor.print_mode = true;
      let jsonData = editor.assemblePageJson(extraInfo, pages);
      jsonData = await predownloadOnlineImageForPrintJson(jsonData);
      // 每次使用完接着清除
      this.newPrintEditor = null;
      // 打印机名称
      jsonData.pageSetting.printerName = printerName;
      // 打印份数
      jsonData.pageSetting.pageCopies =
        printMultiple && printMultiple > 1 ? printMultiple : 1;
      // const printConfig = this.getPrintConfig();
      // // 合并本地配置，优先级低于产品设置
      Object.assign(jsonData.pageSetting, extraInfo);
      editor.event.emit("beforePrint", jsonData.pageSetting);
      // 获取外部配置的左边距和上边距
      // jsonData.pageSetting.marginLeft = this.print_pic.content.marginLeft;
      // jsonData.pageSetting.marginTop = this.print_pic.content.marginTop;
      editor.print_mode = false;
      return JSON.stringify(jsonData);
    },
    // 托盘打印需要的参数
    setPluginParameter(printerName, printMultiple) {
      const { editor } = this.instance;
      const page_type = editor.config.getPageType();
      const page_direction = editor.config.getDirection();
      // 获取页面高度，在托盘打印中用到
      const page_info = editor.config.getPageSize();
      this.printerName = printerName;
      this.print_pic.content.printerName = printerName;
      if (printMultiple) {
        this.print_pic.content.pageCopies = printMultiple;
      }
      this.print_pic.content.pageSizeName = page_type;
      if (page_direction === "horizontal") {
        this.print_pic.content.landscape = true;
      } else {
        this.print_pic.content.landscape = false;
      }
      this.print_pic.content.pageSizeWidth = page_info.width;
      this.print_pic.content.pageSizeHeight = page_info.height;
    },
    // 直接打印
    // 众阳浏览器直接打印
    // 原版
    electronImmediatelyPrint(printerName, afterPrint, pages, extraInfo) {
      if (pages && pages.length && extraInfo && extraInfo.sort === 2) {
        pages.reverse();
      }
      this.setPdfPrintQueneData(this.editor, { type: "base64", pages: pages });
      this.jsPdfFontLoading().then(() => {
        const base64 = this.jsPdfOutput({ type: "base64", pages: pages });
        const content = { imageBase64: base64, printerName: printerName };
        const electron = window.electron ?? window.top.electron;
        electron.hdApi.print({
          busiVersion: "PDF_PRINT_V1.0",
          content: content,
        });
        this.editor.printContinue(false);
        const params = {
          printerName: printerName,
        };
        if (extraInfo && extraInfo.sort) {
          params.sort = extraInfo.sort;
        }
        afterPrint && afterPrint(params);
      });
    },

    // 托盘图片版本打印
    pluginPicturePrint(printerName, afterPrint, pages, printMultiple) {
      this.$editor.warning("当前打印方式已停止维护，请及时升级打印托盘");
      this.setPluginParameter(printerName);
      this.print_pic.content.jsonData = null;
      const printParams = {
        pages: pages,
        printRatio: 4,
        sendPrintRequest: (src) => {
          this.systemPrintBySrc(src);
        },
      };
      if (this.upConfig?.systemConfig) {
        printParams.printRatio = this.upConfig?.systemConfig.printRatio;
      }
      printMultiple = printMultiple && printMultiple > 0 ? printMultiple : 1;
      for (let i = 0; i < printMultiple; i++) {
        this.editor.print(printParams);
      }
      const params = {
        printerName: printerName,
      };
      afterPrint && afterPrint(params);
      this.editor.printContinue(false);
      this.editor.focus();
    },
    // c++版本
    async electronImmediatelyPrint_CPP(
      printerName,
      afterPrint,
      extraInfo,
      printMultiple,
      pages
    ) {
      const jsonData = await this.setElectronParameter(
        printerName,
        extraInfo,
        printMultiple,
        pages
      );
      const electron = window.electron ?? window.top.electron;
      const needHandleJsonData = JSON.parse(jsonData);
      const pageData = needHandleJsonData.pageData;

      const print = (data) => {
        electron.hdApi.print({
          busiVersion: "EDITOR_PRINT_V1.0",
          requestContent: JSON.stringify(data),
        });
      };
      if (extraInfo.doublePrint === 3 && pageData.length > 1) {
        this.printByOddOrEven(needHandleJsonData, pageData, print);
      } else {
        print(needHandleJsonData);
      }
      this.editor.printContinue(false);
      const params = {
        printerName: printerName,
      };
      if (extraInfo && extraInfo.sort) {
        params.sort = extraInfo.sort;
      }
      afterPrint && afterPrint(params);
    },
    // 托盘打印C++版本
    async pluginPrint_CPP(
      printerName,
      printMultiple,
      pages,
      afterPrint,
      extraInfo = {}
    ) {
      const jsonData = await this.setElectronParameter(
        printerName,
        extraInfo,
        printMultiple,
        pages
      );
      const needHandleJsonData = JSON.parse(jsonData);
      const pageData = needHandleJsonData.pageData;

      const print = (data) => {
        const printParams = {
          content: { jsonData: JSON.stringify(data) },
          type: 0,
          application: "RawPrintPlugin",
          keyword: "printCpp",
        };
        this.request(printParams);
        this.editor.printContinue(false);
        const params = { printerName };
        if (extraInfo.sort) {
          params.sort = extraInfo.sort;
        }
        if (afterPrint) {
          afterPrint(params);
        }
      };

      if (extraInfo.doublePrint === 3 && pageData.length > 1) {
        this.printByOddOrEven(needHandleJsonData, pageData, print);
      } else {
        print(needHandleJsonData);
      }
    },
    printByOddOrEven(needHandleJsonData, pageData, print) {
      let oddData = pageData.filter((_, index) => index % 2 === 0);
      // 处理奇数页

      const oddPrintData = {
        fontStyles: needHandleJsonData.fontStyles,
        pageSetting: needHandleJsonData.pageSetting,
        pageData: oddData,
      };

      let evenData = pageData.filter((_, index) => index % 2 !== 0);
      if (pageData.length % 2 !== 0) {
        evenData.push([]); // 添加一个空页用于最后一页是奇数页的情况
      }
      evenData.reverse();

      const evenPrintData = {
        fontStyles: needHandleJsonData.fontStyles,
        pageSetting: needHandleJsonData.pageSetting,
        pageData: evenData,
      };

      // 打印奇数页
      print(evenPrintData);

      // 等待用户翻转纸张
      setTimeout(() => {
        if (
          confirm(
            "打印完成后，取出纸张，旋转纸张方向重新插入纸盘，点击“确定”继续打印"
          )
        ) {
          print(oddPrintData);
        }
      }, 2000); // 2秒延时
    },
    /**
     * 获取默认打印机列表
     * @returns 默认打印机列表名称
     */
    async getPrinterName() {
      let selectedPrintName = "";
      let printList = [];
      printList = await this.getPrintList();
      // 是一个这种对象的数组：
      // {
      //   isDefault: false,
      //   printerName: "Microsoft Print to PDF",
      //   version:"1.1.0.12"
      // }
      if (!printList.length) return null;
      // 查找默认打印机
      for (let i = 0; i < printList.length; i++) {
        const printer = printList[i];
        if (printer.isDefault) {
          selectedPrintName = printList[i].printerName;
        }
      }

      return selectedPrintName;
    },
    /**
     * 获取打印机列表
     * @returns 所有打印机列表
     */
    async getPrintList() {
      let promise = new Promise((resolve, reject) => {
        if (this.isElectron) {
          const electron = window.electron ?? window.top.electron;
          // 在众阳浏览器中
          electron.hdApi
            .getPrinterList({
              busiVersion: "PDF_PRINT_V1.0",
            })
            .then((result) => {
              if (result?.data?.length) {
                const printerList = result.data.map((item) => {
                  item.printerName = item.name;
                  delete item.name;
                  return item;
                });
                resolve(printerList);
              } else {
                reject([]);
              }
            });
        } else {
          // 在谷歌浏览器中
          if (this.isStartUpSocket) {
            // 打印的时候会将打印机列表存到 localStorage 里边，如果执行过打印，可能就有了，所以先取 看看有没有取到
            this.socket.sendObj({
              type: 0,
              application: "RawPrintPlugin",
              keyword: "getPrinterList",
              content: "",
            });
            let time = 0;
            const getPrintInterval = setInterval(() => {
              if (this.printerList.length) {
                clearInterval(getPrintInterval);
                resolve(this.printerList);
              }
              if (time > 3) {
                clearInterval(getPrintInterval);
                reject("获取打印机列表超时");
              }
              time++;
            }, 1000);
          } else {
            reject(console.log("托盘未正确启动"));
          }
        }
      });
      let result = await promise;
      return result;
    },

    /**
     * 获取众阳浏览器版本
     */
    async getElectronVersion() {
      const userAgent = navigator.userAgent.split("cloud-health/")[1];
      if (userAgent) {
        this.electronVersion = userAgent.split(" ")[0];
      } else {
        const electron = window.electron ?? window.top.electron;
        if (!electron.hcInfos || !electron.hcInfos.hcVersion) {
          this.electronVersion = 0;
        } else {
          const electron_version_p = await electron.hcInfos.hcVersion();
          this.electronVersion = electron_version_p.data.hcversion;
        }
      }
    },

    /**
     * 获取托盘版本参数
     */
    getPluginVersion() {
      const version = this.getPrintList()
        .then((e) => {
          this.print_plugin_version = e[0].version;
          return this.print_plugin_version;
        })
        .catch(() => {
          return 0;
        });
      return version;
    },
    /**
     * 打印当前页
     */
    async printCurrentPage(printerName, afterPrint, printType = "json") {
      const page = this.editor.printCurrentPage();
      if (!this.isStartUpSocket && !this.isElectron) {
        // 没有启动托盘 并且不是在众阳浏览器中 isStartUpSocket 是否启动了websocket 就是是否启动了托盘 isElectron 是否在众阳浏览器中
        this.$editor.error("托盘未正确启动");
        this.editor.printContinue(false);
        return;
      }
      printerName = this.judgePrinterNameExist(printerName);
      if (!printerName) {
        this.$editor.error("获取打印机列表失败");
        console.log("打印中断 :>> ");
        this.editor.printContinue(false);
        return;
      }
      this.previewSystemPrint(
        1,
        [page],
        printerName,
        { sort: 1 },
        afterPrint, // 打印回调
        printType
      );
    },
    /**
     * 直接打印(右键菜单中的直接打印就走该方法) 不弹出打印预览页面
     * @param {string} printerName 打印机名称
     * @param {function} afterPrint 打印回调函数
     * @param {string} printType 打印类型 pdf / image
     * @param {object} extraInfo 传入的额外信息
     */
    //奇数页打印
    printOddPages(printerName, afterPrint, printType = "json") {
      const allPages = this.editor.pages;
      const pages = [];
      //TODO改进一下
      let totalList = new Array(allPages.length)
        .fill(0)
        .map((val, index) => index);
      totalList.forEach((e) => {
        if (e % 2 === 0) {
          pages.push(e);
        }
      });
      const extraInfo = {};
      extraInfo.pages = pages;
      this.immediatelyPrint(printerName, afterPrint, printType, extraInfo);
    },
    //偶数页打印
    printEvenPages(printerName, afterPrint, printType = "json") {
      const allPages = this.editor.pages;
      const pages = [];
      //
      let totalList = new Array(allPages.length)
        .fill(0)
        .map((val, index) => index);
      totalList.forEach((e) => {
        if (e % 2 !== 0) {
          pages.push(e);
        }
      });
      const extraInfo = {};
      extraInfo.pages = pages;
      this.immediatelyPrint(printerName, afterPrint, printType, extraInfo);
    },
    isObjectEmpty(obj) {
      return Object.keys(obj).length === 0 && obj.constructor === Object;
    },
    async immediatelyPrint(
      printerName,
      afterPrint,
      printType = "json",
      extraInfo
    ) {
      const sort = this.isObjectEmpty(this.getPrintConfig("sortValue"))
        ? 1
        : this.getPrintConfig("sortValue");
      const doublePrint = this.isObjectEmpty(this.getPrintConfig("doublePrint"))
        ? 2
        : this.getPrintConfig("doublePrint");
      const color = this.isObjectEmpty(this.getPrintConfig("color"))
        ? 1
        : this.getPrintConfig("color");
      const duplexFlip = this.isObjectEmpty(this.getPrintConfig("duplexFlip"))
        ? 3
        : this.getPrintConfig("duplexFlip");
      let isDuplex;
      if (doublePrint === 1) {
        isDuplex = true;
      } else {
        isDuplex = false;
      }
      extraInfo = extraInfo || {
        sort,
        duplexFlip,
        isDuplex,
        color,
        doublePrint,
      };
      let printMultiple = 1;
      if (
        this.editor.document_meta &&
        this.editor.document_meta.immediatePrintSetting
      ) {
        const config = this.editor.document_meta.immediatePrintSetting;
        extraInfo.color = config.color ? true : false;
        extraInfo.sort = config.sortValue;
        extraInfo.doublePrint = config.doublePrint;
        extraInfo.isDuplex = config.doublePrint === 1 ? true : false;
        extraInfo.marginLeft = config.marginLeft;
        extraInfo.marginTop = config.marginTop;
        printMultiple = config.printMultiple;
        if (extraInfo.isDuplex) {
          extraInfo.duplexFlip = config.duplexFlip;
        } else {
          extraInfo.duplexFlip = 1;
        }
      }
      const pages = extraInfo.pages ?? null;

      if (!this.isStartUpSocket && !this.isElectron) {
        // 没有启动托盘 并且不是在众阳浏览器中 isStartUpSocket 是否启动了websocket 就是是否启动了托盘 isElectron 是否在众阳浏览器中
        this.$editor.error("托盘未正确启动");
        this.editor.printContinue(false);
        return;
      }
      printerName = this.judgePrinterNameExist(printerName);
      if (!printerName) {
        this.$editor.error("获取打印机列表失败");
        console.log("打印中断 :>> ");
        this.editor.printContinue(false);
        return;
      }

      // 保证托盘版本间不冲突
      if (
        !this.isElectron &&
        (!this.print_plugin_version ||
          !this.cppPrintCanBeUsedHelper(this.print_plugin_version))
      ) {
        printType = "image";
      }
      // json类型的打印
      if (printType === "json") {
        // 众阳浏览器新版打印
        if (this.isElectron) {
          // TODO 版本号可能会变
          if (this.cppPrintCanBeUsedHelper(this.electronVersion)) {
            this.electronImmediatelyPrint_CPP(
              printerName,
              afterPrint,
              extraInfo,
              printMultiple,
              pages
            );
          } else {
            this.electronImmediatelyPrint(
              printerName,
              afterPrint,
              pages,
              extraInfo
            );
          }
        } else {
          // c++版本的直接打印
          this.pluginPrint_CPP(
            printerName,
            printMultiple,
            pages,
            afterPrint,
            extraInfo
          );
        }
      } else if (printType === "pdf") {
        if (this.isElectron) {
          this.electronImmediatelyPrint(
            printerName,
            afterPrint,
            pages,
            extraInfo
          );
        } else {
          this.$editor.error(
            `打印参数printType不能为${printType}，联系开发修改！`
          );
          return;
        }
      } else {
        if (this.isElectron) {
          this.$editor.error(
            `打印参数printType不能为${printType}，联系开发修改！`
          );
          return;
        }
        // 图片类型的打印
        this.pluginPicturePrint(printerName, afterPrint, pages);
      }
    },
    localPrintConfigOption(type, params) {
      const key = "editorSavePrinterConfig";
      if (type === "set") {
        localStorage.setItem(key, JSON.stringify(params));
        this.print_plugin_version = params.printerList[0].version ?? 0;
        this.printerList = params.printerList;
      } else {
        const printerParams = JSON.parse(localStorage.getItem(key));
        if (printerParams) {
          this.printerList = printerParams.printerList;
        }
        return printerParams;
      }
    },
    /**
     * 设置打印机列表
     */
    setPrinterList() {
      const getPrinterParams = this.localPrintConfigOption("get");
      if (getPrinterParams) {
        this.judgePrinterNameExist(getPrinterParams.selectPrinter);
      } else {
        this.updatePrinterList();
      }
    },
    //编辑器初始化时调用一遍，用于无感更新打印机列表
    initPrinterList() {
      const getPrinterParams = this.localPrintConfigOption("get");
      if (getPrinterParams) {
        this.updatePrinterList(getPrinterParams.selectPrinter);
      } else {
        this.updatePrinterList();
      }
    },
    /**
     * 刷新打印机列表
     */
    updatePrinterList(printerName) {
      let timeout = 0;
      if (!this.isElectron && !this.isStartUpSocket) {
        this.isStartUpSocket = true;
        this.initSocket();
        timeout = 2000;
      }
      setTimeout(() => {
        this.getPrintList()
          .then((e) => {
            this.printerList = e;
            const selectPrinter = e.filter((item) => item.isDefault)[0];
            const params = {
              printerList: e,
              selectPrinter: selectPrinter && selectPrinter.printerName,
            };
            if (printerName) {
              params.selectPrinter = printerName;
            }
            this.localPrintConfigOption("set", params);
          })
          .catch(() => {
            this.printerList = [];
          });
      }, timeout);
    },
    savePrintPreviewWindowSizeType(val) {
      this.setLocalOtherConfig("printPreviewWindowType", val);
    },
    savePrintConfig(val1, val2, val3, val4, val5, val6, val7, val8) {
      this.setLocalPrintConfig("isDuplex", val1 === 1 ? true : false);
      this.setLocalPrintConfig("doublePrint", val1);
      this.setLocalPrintConfig("duplexFlip", val2);
      this.setLocalPrintConfig("sortValue", val3);
      this.setLocalPrintConfig("printName", val4);
      this.setLocalPrintConfig("color", val5 === 1 ? true : false);
      this.setLocalPrintConfig("paperOrientation", val6);
      this.setLocalPrintConfig("printOffsetX", val7);
      this.setLocalPrintConfig("printOffsetY", val8);
    },
    /**
     * 打印预览打印
     * @param {number} printMultiple 打印份数
     * @param {array} pages 具体打印哪些页，由选择结果导入
     * @param {string} printerName 打印机名称
     * @param {function} afterPrint 打印回调函数
     */
    previewSystemPrint(
      printMultiple,
      pages,
      printerName,
      extraInfo,
      afterPrint,
      printType
    ) {
      this.localPrintConfigOption("set", {
        printerList: this.printerList,
        selectPrinter: printerName,
      });
      if (!this.isElectron && !this.isStartUpSocket) {
        return this.$editor.error("打印托盘未正常启动，不能正常打印！");
      }
      // this.setLocalPrintConfig("isDuplex", extraInfo.doublePrint === 1 ? 1 : 0);
      if (extraInfo.doublePrint === 1) {
        extraInfo.isDuplex = true;
      } else {
        extraInfo.isDuplex = false;
      }
      // this.setLocalPrintConfig("color", extraInfo.color === 1 ? true : false);
      // this.setLocalPrintConfig("duplexFlip", extraInfo.duplexFlip);
      // this.setLocalPrintConfig("sortValue", extraInfo.sortValue);
      // this.setLocalPrintConfig("doublePrint", extraInfo.doublePrint);
      // this.setLocalPrintConfig("paperOrientation", extraInfo.paperOrientation);

      // 如果未传回页码，则打印全部页
      if (!pages.length) {
        return this.immediatelyPrint(printerName, afterPrint);
      }

      // electron的情况
      if (this.isElectron) {
        this.showSystemPrint = false;
        if (printType === "image") {
          this.$editor.error(
            `打印参数printType不能为${printType}，联系开发修改！`
          );
          return;
        }
        // 浏览器最新版本预览打印接口
        if (this.cppPrintCanBeUsedHelper(this.electronVersion)) {
          if (printType === "pdf") {
            printMultiple =
              printMultiple && printMultiple > 1 ? printMultiple : 1;
            // 打印多份
            for (let index = 0; index < printMultiple; index++) {
              this.electronImmediatelyPrint(
                printerName,
                afterPrint,
                pages,
                extraInfo
              );
            }
          } else {
            this.electronImmediatelyPrint_CPP(
              printerName,
              afterPrint,
              extraInfo,
              printMultiple,
              pages
            );
          }
        } else {
          printMultiple =
            printMultiple && printMultiple > 1 ? printMultiple : 1;
          // 打印多份
          for (let index = 0; index < printMultiple; index++) {
            this.electronImmediatelyPrint(
              printerName,
              afterPrint,
              pages,
              extraInfo
            );
          }
        }
        this.showSystemPrint = false;
      } else {
        this.showSystemPrint = false;
        this.setPluginParameter(printerName);
        if (
          !this.print_plugin_version ||
          !this.cppPrintCanBeUsedHelper(this.print_plugin_version)
        ) {
          printType = "image";
        }
        if (printType === "pdf") {
          this.$editor.error(
            `打印参数printType不能为${printType}，联系开发修改！`
          );
          return;
        }
        if (printType === "image") {
          this.showSystemPrint = false;
          this.pluginPicturePrint(
            printerName,
            afterPrint,
            pages,
            printMultiple
          );
        } else {
          // 托盘c++打印
          this.pluginPrint_CPP(
            printerName,
            printMultiple,
            pages,
            afterPrint,
            extraInfo
          );
        }
      }
    },

    /**
     * 取消打印 关闭打印预览页面
     * @param {function} afterPrint 打印回调函数
     */
    cancelPreviewSystemPrint(afterPrint) {
      // 每次使用完接着清除
      this.newPrintEditor = null;
      this.showSystemPrint = false;
      this.editor.focus();
      this.editor.printContinue(false);
      afterPrint && afterPrint({ cancel: true });
    },
    /**
     * 判断传入的打印机名称存在，如果不传或者传入的打印机名称不存在则用默认打印机
     */
    judgePrinterNameExist(printerName) {
      let defaultPrinter;
      for (let i = 0, len = this.printerList.length; i < len; i++) {
        const printer = this.printerList[i];
        if (printerName) {
          if (printerName === printer.printerName) {
            return printerName;
          }
        } else {
          printerName = this.isObjectEmpty(this.getPrintConfig("printName"))
            ? undefined
            : this.getPrintConfig("printName");
          if (printerName === printer.printerName) {
            return printerName;
          }
        }
        if (printer.isDefault) {
          defaultPrinter = printer.printerName;
        }
      }
      this.updatePrinterList();
      return defaultPrinter;
    },
    batchPrintImage(rawDataList, printerName, afterPrint) {
      if (this.isElectron) {
        this.$editor.error(`打印参数printType不能为image，联系开发修改！`);
        return;
      }
      this.setPluginParameter(printerName);
      //获取所有需要打印的文件的rawData，合并为打印的base64集合
      const printList = this.instance.editor.rawData2Base64(rawDataList);
      const directions = [];
      //获取数据中的纵横项
      rawDataList.forEach((e) => {
        if (e.config && e.config.direction) {
          // 配置的数据采用数据中的参数
          directions.push(e.config.direction);
        } else {
          // 没有配置的则是用系统默认的纵向参数
          directions.push("vertical");
        }
      });
      for (let i = 0; i < printList.length; i++) {
        // 是否横向打印，根据内容动态判断
        this.print_pic.content.landscape =
          directions[i] && directions[i] === "horizontal" ? true : false;
        this.systemPrintBySrc(printList[i]);
      }
      afterPrint && afterPrint();
    },
    async batchPrintCpp(rawDataList, printerName, isUseConfig, afterPrint) {
      const newEditor = this.editor.getPrintEditor();
      const loadRawData = (rawData) => {
        if (isUseConfig) {
          newEditor.reInitRawByConfig(rawData);
        } else {
          newEditor.reInitRaw(rawData);
        }
        if (afterPrint) {
          const originReadOnly = newEditor.readonly;
          newEditor.readonly = false;
          afterPrint(newEditor);
          newEditor.readonly = originReadOnly;
        }
        newEditor.update();
      };
      //处理打印机配置
      const handlePrintConfig = (jsonData) => {
        // 打印机名称
        jsonData.pageSetting.printerName = printerName;
        // 打印份数
        jsonData.pageSetting.pageCopies = 1;
        // 合并本地配置，优先级低于产品设置
        Object.assign(jsonData.pageSetting, this.getPrintConfig());

        // 当打印纸张数据只有一页的时候，并且当前选择的是双面打印，则切换成单面打印
        if (
          jsonData.pageData.length === 1 &&
          jsonData.pageSetting.isDuplex === true
        ) {
          jsonData.pageSetting.isDuplex = false;
        }
      };

      const delayExe = (printData, i) => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve(printData);
          }, 2000 * i);
        });
      };
      for (let i = 0; i < rawDataList.length; i++) {
        loadRawData(rawDataList[i]);
        if (this.isElectron) {
          const electron = window.electron ?? window.top.electron;
          let jsonData = newEditor.assemblePageJson();
          jsonData = await predownloadOnlineImageForPrintJson(jsonData);
          this.editor.event.emit("beforePrint", jsonData.pageSetting);
          handlePrintConfig(jsonData);
          // 处理打印顺序不正确问题
          if (i === 0) {
            electron.hdApi.print({
              busiVersion: "EDITOR_PRINT_V1.0",
              requestContent: JSON.stringify(jsonData),
            });
          } else {
            delayExe(jsonData, i).then((data) => {
              electron.hdApi.print({
                busiVersion: "EDITOR_PRINT_V1.0",
                requestContent: JSON.stringify(data),
              });
            });
          }
        } else {
          let jsonData = newEditor.assemblePageJson();
          jsonData = await predownloadOnlineImageForPrintJson(jsonData);
          this.editor.event.emit("beforePrint", jsonData.pageSetting);
          handlePrintConfig(jsonData);
          // 托盘c++版本
          const printParams = {};
          printParams.content = { jsonData: JSON.stringify(jsonData) };
          printParams.type = 0;
          printParams.application = "RawPrintPlugin";
          printParams.keyword = "printCpp";
          // 处理打印顺序不正确问题
          if (i === 0) {
            this.request(printParams);
          } else {
            delayExe(printParams, i).then((data) => {
              this.request(data);
            });
          }
        }
      }
      afterPrint && afterPrint();
    },
    batchPrintPdf(rawDataList, printerName, isUseConfig, afterPrint) {
      const newEditor = this.editor.getPrintEditor();
      const loadRawData = (rawData) => {
        if (isUseConfig) {
          newEditor.reInitRawByConfig(rawData);
        } else {
          newEditor.reInitRaw(rawData);
        }
        if (afterPrint) {
          const originReadOnly = newEditor.readonly;
          newEditor.readonly = false;
          afterPrint(newEditor);
          newEditor.readonly = originReadOnly;
        }
        newEditor.update();
      };
      const electron = window.electron ?? window.top.electron;
      this.jsPdfFontLoading().then(async () => {
        for (let i = 0; i < rawDataList.length; i++) {
          // 如果是同步的并且在众阳浏览器中，走同步批量打印的接口
          loadRawData(rawDataList[i]);
          this.setPdfPrintQueneData(newEditor);
          const base64 = this.jsPdfOutput({ editor: newEditor });
          // 纵向横向在electron中都能正确打印
          const content = {
            imageBase64: base64,
            printerName: printerName,
          };
          await electron.hdApi.printSync({
            busiVersion: "PDF_PRINT_V1.0",
            content: content,
          });
        }
        afterPrint && afterPrint();
      });
    },
    // 设置pdf打印队列
    setPdfPrintQueneData(editor, params = {}) {
      const assParam = { ...params };
      assParam.printType = "pdf";
      editor.print_mode = true;
      const printData = editor.assemblePageJson(assParam, params.pages);
      editor.print_mode = false;
      this.pdfPrintDataQuene.push(printData);
    },
    /**
     * 批量打印的方法，绘制pdf方式打印
     * @param {array} rawDataList 原始数据数组
     * @param {function} afterPrint 打印回调函数
     * @param {string} printType 打印类型
     * @param {boolean} issync 是否同步打印
     * @param {boolean} isUseConfig 是否按配置批量打印
     */
    async batchPrintNew(
      rawDataList,
      afterPrint,
      printType,
      printerName,
      isUseConfig
    ) {
      const realPrinterName = this.judgePrinterNameExist(printerName);
      if (!realPrinterName) {
        this.$editor.error("获取打印机列表失败");
        console.log("打印中断 :>> ");
        return;
      }
      if (
        this.cppPrintCanBeUsedHelper(this.electronVersion) ||
        this.cppPrintCanBeUsedHelper(this.print_plugin_version)
      ) {
        this.batchPrintCpp(
          rawDataList,
          realPrinterName,
          isUseConfig,
          afterPrint
        );
      } else {
        if (this.isElectron) {
          this.batchPrintPdf(
            rawDataList,
            realPrinterName,
            isUseConfig,
            afterPrint
          );
        } else {
          this.batchPrintImage(rawDataList, realPrinterName, afterPrint);
        }
      }
    },
    async batchPrint(
      rawDataList,
      afterPrint,
      printType,
      issync = false,
      printerName,
      isUseConfig
    ) {
      await this.batchPrintNew(
        rawDataList,
        afterPrint,
        printType,
        printerName,
        isUseConfig,
        issync
      );
      return;
    },
    /**
     * 调用托盘
     * @param {*} srcs 对应页的base64
     */
    systemPrintBySrc(srcs) {
      this.print_pic.content.imageBase64 = srcs;
      this.request(this.print_pic);
    },
    // 判断版本号是否小于1.1.0.12   众阳浏览器1.2.0
    cppPrintCanBeUsedHelper(version) {
      if (version === undefined || version === null) return false;
      const version_str = version + ""; // 版本号字符串化
      const version_list = version_str.split("."); // 版本号根据点分开
      const version_12 = [1, 1, 0, 12]; // 12版本的参考依据
      const version_electron = [1, 2, 0]; // 众阳浏览器版本参考依据
      const reference_version = this.isElectron ? version_electron : version_12; // 根据是否在众阳浏览器中判断使用哪个参考依据
      // 补足对比版本位数
      while (version_list.length < reference_version.length) {
        version_list.push(0);
      }
      // 循环判断每个数值的大小
      for (let i = 0; i < version_list.length; i++) {
        const version_num = version_list[i];
        // 补足参考版本的位数
        if (reference_version[i] === undefined) {
          reference_version.push(0);
        }
        // 遇到字符串的时候转换一下数据类型
        if (version_num * 1 < reference_version[i]) {
          return false;
        }
        if (version_num * 1 > reference_version[i]) {
          return true;
        }
      }
      return true;
    },
    // 是否小于指定版本接口
    async cppPrintCanBeUsed() {
      let version = 0;
      if (this.isElectron) {
        await this.getElectronVersion();
        version = this.electronVersion;
      } else {
        version = await this.getPluginVersion();
      }
      return this.cppPrintCanBeUsedHelper(version);
    },
    //验证jsPdf加载
    judgeJsPDFLoad() {
      if (this.fontLoaded) {
        return true;
      } else {
        if (!this.pdfFontFileLoading) {
          this.pdfFontFileLoading = true;
          this.jsPDFFontLoad();
        }
      }
      return false;
    },
    getNewJsPdfObj() {
      const config = this.instance.editor.config;
      const configFontFamily = config.default_font_style.family;
      const jsPdf = new jsPDF({
        unit: "px",
        format: "A4",
        compress: true,
        hotfixes: ["px_scaling"],
      });
      let fontId = "songti";
      if (configFontFamily === "仿宋") {
        fontId = "fangsong";
      }
      jsPdf.addFileToVFS(fontId, this.fontBase64Map[fontId]);
      jsPdf.addFont(fontId, fontId, "normal");
      jsPdf.addFileToVFS(fontId + "bold", this.fontBase64Map[fontId + "bold"]);
      jsPdf.addFont(fontId + "bold", fontId, "bold");
      return jsPdf;
    },
    //判断图片文件是否全部加载完成
    judgeImageAllLoad() {
      const imageMap = this.instance.imageMap;
      if (imageMap) {
        const vals = imageMap.values();
        for (const val of vals) {
          if (!val.isLoaded) {
            return false;
          }
        }
        return true;
      } else {
        return true;
      }
    },
    //用于检测中文字体是否加载完成后再调用
    jsPdfFontLoading() {
      const _this = this;
      return new Promise((resolve) => {
        if (_this.judgeJsPDFLoad() && _this.judgeImageAllLoad()) {
          resolve();
        } else {
          _this.jsPdfFontLoadingTimer = setInterval(() => {
            if (_this.judgeJsPDFLoad() && _this.judgeImageAllLoad()) {
              clearInterval(_this.jsPdfFontLoadingTimer);
              resolve();
            }
          }, 100);
        }
      });
    },
    //预览pdf // {pages//页数数组 type:默认生成base64, save: 另存为pdf}
    jsPdfOutput(params = {}) {
      const pdf = this.getNewJsPdfObj();
      //绘制页面
      this.jsPdfDrawPage(pdf, params);
      if (params && params.printType === "immediatelyPrint") {
        return;
      }
      //默认输出base64
      if (!params.type || params.type === "base64") {
        const base64 = pdf.output("datauristring");
        return base64;
      }
      if (params.type === "save") {
        pdf.save(params.fileName ? params.fileName : getUUID("PDF"));
      }
      if (params.type === "print") {
        params.afterPrint && this.editor.focus();
        const hideFrameId = "EditorPdfPreviewIframe";
        let hideFrame = document.getElementById(hideFrameId);
        if (!hideFrame) {
          hideFrame = document.createElement("iframe");
          hideFrame.style.display = "none";
          hideFrame.id = hideFrameId;
          document.body.appendChild(hideFrame);
        }
        hideFrame.src = pdf.output("bloburl");
        hideFrame.onload = function () {
          hideFrame.contentWindow.print();
          hideFrame.onload = null;
        };
        this.browserAfterPrint = params.afterPrint;
      }
      if (params.type === "view") {
        pdf.output("dataurlnewwindow");
      }
    },
    //通过jsPdf绘制页面
    jsPdfDrawPage(pdf, params) {
      const editor = params && params.editor ? params.editor : this.editor;
      if (!this.pdfPrintDataQuene.length) {
        return alert("打印队列数据异常");
      }
      const { pageData, fontStyles, pageSetting } =
        this.pdfPrintDataQuene.shift();
      const { startPageNumber, pageWidth, pageHeight, landscape } = pageSetting;

      if (
        Boolean(this.getPrintConfig("isDuplex")) &&
        pageData.length &&
        startPageNumber % 2 === 0
      ) {
        pageData.splice(1, 0, []);
      }
      const family =
        editor.config.default_font_style.family === "仿宋"
          ? "fangsong"
          : "songti";

      pdf.setLineWidth(0.8);
      const instance = this.instance;
      const drawChar = function (item) {
        const font = fontStyles[item.fontId];
        const [value, fontSize, top] = instance.utils.handleRareCharacter(
          item.value,
          Number(font.fontSize),
          Number(item.top)
        );
        pdf.setFont(family, font.bold ? "bold" : "normal");
        pdf.setFontSize(fontSize);
        pdf.setTextColor(font.color);
        if (item.limitWidth) {
          const oriWidth = pdf.getTextWidth(value);
          const scaleX = item.width / oriWidth;
          const matrix = pdf.Matrix(scaleX, 0, 0, 1, 0, 0);
          pdf.text(value, item.left / scaleX, top, null, matrix);
        } else {
          pdf.text(value, item.left, top);
        }
      };
      const drawWaterMark = function (item) {
        let angle; // 旋转角度
        if (item.direction === "left") {
          angle = -40;
        } else if (item.direction === "right") {
          angle = 40;
        } else {
          angle = 0;
        }
        pdf.setGState(pdf.GState({ opacity: 0.5 }));
        pdf.setFontSize(item.font.fontSize);
        pdf.setTextColor(item.font.color);
        const textWidth =
          pdf.getStringUnitWidth(item.text) * item.font.fontSize;
        const textHeight = item.font.fontSize;
        for (let x = 0; x <= editor.init_canvas.width; x += textWidth + 280) {
          for (
            let y = 150;
            y <= editor.init_canvas.height + editor.scroll_top;
            y += textHeight + 280
          ) {
            pdf.text(item.text, x, y, null, -angle);
          }
        }
      };
      for (let i = 0; i < pageData.length; i++) {
        // 因为pdf对象默认有一页，但是其页面尺寸，横向纵向配置可能不正确，所以我们每次重新添加一页，然后将默认页删除
        pdf.addPage([pageWidth, pageHeight], landscape ? "l" : "p");
        pdf.setPage(i + 2); // 因为初始化或每次清空后jspdf对象会默认存留一页为第一页，所以此处使用i+2
        for (let j = 0; j < pageData[i].length; j++) {
          const drawPage = pageData[i];
          const item = drawPage[j];
          switch (drawPage[j].type) {
            case "line":
              if (item.color && item.lineHeight) {
                pdf.setLineWidth(item.lineHeight);
                pdf.setDrawColor(item.color);
                pdf.line(item.left, item.top, item.right, item.bottom);
                pdf.setDrawColor("#000");
                pdf.setLineWidth(0.8);
              } else {
                pdf.line(item.left, item.top, item.right, item.bottom);
              }
              break;
            case "text":
              drawChar(item);
              break;
            case "rect":
              if (item.fill) {
                pdf.setFillColor("#000");
                pdf.rect(item.left, item.top, item.width, item.height, "F");
                pdf.setFillColor("#FFF");
              } else {
                pdf.rect(item.left, item.top, item.width, item.height);
              }
              break;
            case "circle":
              if (item.selected || item.fill) {
                pdf.circle(item.left, item.top, item.radius, "F");
              } else {
                pdf.circle(item.left, item.top, item.radius);
              }
              break;
            case "ellipse":
              pdf.ellipse(item.left, item.top, item.width, item.height);
              break;
            case "waterMark":
              drawWaterMark(item);
              break;
            case "coverRect":
              pdf.setDrawColor("#FFF");
              pdf.setFillColor("#FFF");
              pdf.rect(item.left, item.top, item.width, item.height, "F");
              pdf.setFillColor("#000");
              pdf.setDrawColor("#000");
              break;
            case "image":
              pdf.addImage(
                item.src,
                "jpg",
                item.left,
                item.top,
                item.width,
                item.height,
                item.imageId ? item.imageId : Math.random(),
                "FAST"
              );
              break;
          }
        }
        if (i === 0 || params.printType === "immediatelyPrint") {
          pdf.deletePage(1);
        }
        if (params.printType === "immediatelyPrint") {
          const pdfBase64 = pdf.output("dataurlstring", {
            filename: getUUID("pdf"),
          });
          this.systemPrintBySrc(pdfBase64);
        }
        pdf.close();
      }
    },
    async jsPDFFontLoad() {
      const config = this.instance.editor.config;
      const configFontFamily = config.default_font_style.family;
      let fileName = "songti";
      if (configFontFamily === "仿宋") {
        fileName = "fangsong";
      }
      //首先获取location中pathname
      // let pathName = window.location.pathname;
      // if (pathName === "/portal/") {
      //   pathName = "/";
      // }
      // let pathNameArr = pathName?.split("/");
      // //先进行第一次处理，如果获取到的pathName = "/hsz/index.html" 则处理为"/hsz/"
      // if (pathNameArr && pathNameArr.length > 2) {
      //   pathName = "/" + pathNameArr[1] + "/";
      // }
      //如果配置了pathName则取配置的
      // if (this.upConfig?.fontPathname) {
      //   pathName = this.upConfig.fontPathname;
      // }
      let pathName = "/static-resource/basic/jspdf-fonts/";
      if (
        location.host.startsWith("localhost") ||
        location.host.startsWith("127.")
      ) {
        pathName = "http://************/static-resource/basic/jspdf-fonts/";
      }
      // 首先检测浏览器有没有缓存字体文件，如果缓存了则直接使用缓存中的内容，如果没有在众阳浏览器下则读取本地文件，同时也请求网络文件放到浏览器缓存中待下次使用（测试发现读取浏览器缓存文件比读取本地文件快）
      const isCached = await this.checkBrowserCached(
        pathName + fileName + ".txt"
      );
      const getFileFromOnline = () => {
        //加载方式二：
        const fetchFontTxt = (path, fontName) => {
          return fetch(path)
            .then((response) => response.text())
            .then((data) => {
              if (data && !data.startsWith("<")) {
                this.fontBase64Map[fontName] = data;
              }
            });
        };
        //定义一个后缀参数，用户每次升级后重新请求字体防止更换后不生效问题
        Promise.all([
          fetchFontTxt(pathName + fileName + ".txt", fileName),
          fetchFontTxt(pathName + fileName + "-bold.txt", fileName + "bold"),
        ])
          .then(() => {
            if (this.fontBase64Map[fileName]) {
              this.fontLoaded = true;
            }
          })
          .catch(() => {
            {
              this.pdfFontFileLoading = false;
              clearInterval(this.jsPdfFontLoadingTimer);
            }
          });
      };
      // 先从本地获取，获取不到再从线上获取
      if (this.isElectron && !isCached) {
        const getFileFromLocal = () => {
          const fetchFontTxt = (path, fileName, fontName) => {
            return this.readFile(path, fileName)
              .then((data) => {
                const resStr = data.data;
                if (resStr && resStr.startsWith("AAEAAAA")) {
                  this.fontBase64Map[fontName] = resStr;
                }
              })
              .catch(() => {});
          };
          const pathName = this.localFontFilePath;
          Promise.all([
            fetchFontTxt(pathName, fileName + ".txt", fileName),
            fetchFontTxt(pathName, fileName + "-bold.txt", fileName + "bold"),
          ])
            .then(() => {
              if (this.fontBase64Map[fileName]) {
                this.fontLoaded = true;
              }
            })
            .catch(() => {
              this.pdfFontFileLoading = false;
              clearInterval(this.jsPdfFontLoadingTimer);
            })
            .finally(() => {
              getFileFromOnline();
            });
        };
        getFileFromLocal();
      } else {
        getFileFromOnline();
      }
    },
    // 检查浏览器有没有缓存对应文件
    async checkBrowserCached(url, waitTimeMs = 4) {
      const ac = new AbortController();
      const cachePromise = fetch(url, { signal: ac.signal })
        .then((res) => {
          if (res.ok) {
            return true;
          } else {
            return false;
          }
        })
        .catch(() => {
          return false;
        });
      setTimeout(() => ac.abort(), waitTimeMs);
      return cachePromise;
    },
    /**
     *
     * @param {*} type "print": 打印预览 "save": 下载PDF
     * @param {*} afterPrint
     */
    convertPDF(type = "save", afterPrint) {
      if (type !== "base64" && !this.fontLoaded) {
        this.$editor.info("字体文件加载中，请稍后。。。");
      }
      const exeFun = (type, afterPrint) => {
        this.setPdfPrintQueneData(this.editor);
        if (type === "base64" || type === "base64New") {
          const base64 = this.jsPdfOutput();
          afterPrint && afterPrint(base64);
        } else {
          if (this.isElectron && type === "print") {
            //新浏览器下使用系统打印弹窗
            this.openSystemPrint(afterPrint);
          } else {
            this.jsPdfOutput({ type, afterPrint });
            // 清空续打状态
            this.editor.printContinue(false);
          }
        }
      };
      //当能检测到字体已加载时直接执行
      if (this.judgeJsPDFLoad() && this.judgeImageAllLoad()) {
        exeFun(type, afterPrint);
      } else {
        this.jsPdfFontLoading().then(() => {
          exeFun(type, afterPrint);
        });
      }
    },
    //续打
    printContinue() {
      this.editor.printContinue(!this.editor.print_continue);
    },
    printContinueImmediate() {
      this.editor.print_continue = true;
      // 续打遮罩高度设置为0
      const printInfo = this.editor.document_meta.printInfo;
      if (printInfo && printInfo.height) {
        this.editor.internal.print_absolute_y = printInfo.height;
      }
      this.immediatelyPrint();
      // this.convertPDF("print");
    },
  },
};
export default printMixin;
