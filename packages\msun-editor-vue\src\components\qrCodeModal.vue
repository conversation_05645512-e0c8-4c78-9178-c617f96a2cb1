<template>
  <div>
    <a-modal v-model="visible" title="条码属性" @ok="handleOk" @cancel="cancel">
      <label>文本：</label>
      <a-input v-model="value" style="width: 90%"></a-input>
      <a-radio-group v-model="type" style="margin-top: 10px">
        <a-radio value="qrcode"> 二维码 </a-radio>
        <a-radio value="barcode"> 条形码 </a-radio>
      </a-radio-group>

      <template v-if="type === 'barcode'">
        <label style="margin-left: 10px">编码：</label>
        <a-select
          v-model="barcodeFormat"
          default-value="CODE128"
          style="width: 30%"
        >
          <a-select-option value="CODE39"> CODE39 </a-select-option>
          <a-select-option value="CODE128"> CODE128 </a-select-option>
          <a-select-option value="EAN13"> EAN13 </a-select-option>
          <a-select-option value="EAN8"> EAN8 </a-select-option>
          <a-select-option value="UPCE"> UPCE </a-select-option>
          <a-select-option value="ITF"> ITF </a-select-option>
        </a-select>
        <a-select
          v-model="displayValue"
          default-value="hide"
          style="margin-left: 7px; width: 20%"
        >
          <a-select-option value="hide"> 隐藏文本 </a-select-option>
          <a-select-option value="show"> 显示文本 </a-select-option>
        </a-select>
      </template>
    </a-modal>
  </div>
</template>
<script>
export default {
  name: "QrCodeModal",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    qrCodeInfo: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    show(e) {
      this.visible = e;
    },
    qrCodeInfo(info) {
      this.value = info.value;
      this.type = info.type;
      this.barcodeFormat = info.barcodeFormat;
      this.displayValue = info.displayValue;
    },
    immediate: true,
  },
  data() {
    return {
      visible: false,
      barcodeFormat: "CODE128",
      displayValue: "hide",
      value: "",
      type: "qrcode",
      formatValue: "",
    };
  },
  methods: {
    cancel() {
      this.$emit("cancel");
    },
    handleOk() {
      this.visible = false;
      this.$emit(
        "submit",
        this.type,
        this.value,
        this.barcodeFormat,
        this.displayValue
      );
    },
  },
};
</script>
