<template>
  <div v-show="show" class="editor-float-button" :style="floatBtnPosition_">
    <div class="editor-float-button-level1">
      <div
        v-for="(item, index) in btnData"
        @click="btnClick_(item, index)"
        :key="index"
      >
        <div
          :class="
            index === selectedIndex
              ? 'editor-float-button-item float-button-selected'
              : 'editor-float-button-item'
          "
        >
          <span class="editor-float-button-item-span">
            <span class="icon-text">
              <template v-if="item.icon">
                <icon-common
                  v-if="item.icon.indexOf('icon') > -1"
                  :icon-style="customIconStyle"
                  :icon="item.icon"
                  class="menu-icon"
                />
                <a-icon v-else :type="item.icon" class="menu-icon" />
              </template>
              <span style="margin-left: 7px"> {{ item.name }}</span>
            </span>
          </span>
        </div>
      </div>
      <div
        v-if="btnData[selectedIndex] && btnData[selectedIndex].children"
        class="editor-float-button-level2"
        :style="childBtnPosition"
      >
        <div class="fade-in-item-container">
          <div
            v-for="(item, index) in btnData[selectedIndex].children"
            @click="childBtnClick(item, index)"
            :key="index + item.name"
            data-selector-name="ai-chat"
          >
            <div
              :class="
                index === getSelectedChildIndex()
                  ? 'editor-float-button-item float-button-selected'
                  : 'editor-float-button-item'
              "
            >
              <span class="editor-float-button-item-span">
                <span class="icon-text">
                  <span style="margin-left: 7px"> {{ item.name }}</span>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "floatButton",
  props: {
    show: {
      type: Boolean,
      default: true,
    },
    btnData: {
      type: Array,
      default: () => [],
    },
    floatBtnPosition_: {
      type: Object,
      default: () => {
        return {
          left: "0px",
          top: "0px",
        };
      },
    },
  },
  watch: {
    show() {},
  },
  data() {
    return {
      selectedIndex: 0,
      customIconStyle: {
        width: "14px",
        height: "14px",
        fill: "rgba(0, 133, 255, 0.8)",
      },
      selectChildMap: {},
      childBtnPosition: {
        top: "-45px",
      },
    };
  },
  methods: {
    restoreData() {
      this.selectedIndex = 0;
    },
    btnClick_(item, index) {
      if (index === this.selectedIndex) {
        this.restoreData();
      } else {
        if (!item.children) {
          this.restoreData();
          this.selectedIndex = index;
          this.$emit("floatBtnClick", item, index);
        } else {
          const selChildIndex = item.children.findIndex((ele) => {
            return ele.default;
          });
          this.selectChildMap[index] = selChildIndex;
          this.selectedIndex = index;
          if (this.selectChildMap[index] > -1) {
            this.$emit(
              "floatBtnClick",
              item.children[selChildIndex],
              selChildIndex,
              item
            );
          }
        }
      }
    },
    childBtnClick(item, index) {
      this.selectChildMap[this.selectedIndex] = index;
      const pItem = this.btnData[this.selectedIndex];
      this.$emit("floatBtnClick", item, index, pItem);
      this.$forceUpdate();
    },
    getSelectedChildIndex() {
      if (this.selectChildMap[this.selectedIndex] !== undefined) {
        return this.selectChildMap[this.selectedIndex];
      } else {
        const selChildIndex = this.btnData[
          this.selectedIndex
        ].children.findIndex((ele) => {
          return ele.default;
        });
        if (selChildIndex > -1) {
          return selChildIndex;
        } else {
          return 0;
        }
      }
    },
  },
};
</script>

<style scoped>
.editor-float-button {
  position: absolute;
  width: fit-content !important;
  border-radius: 2px;
  display: block;
  opacity: 1;
  color: rgb(78, 90, 112);
}
.editor-float-button-level1 {
  display: flex;
  width: fit-content;
  -webkit-box-pack: start;
  justify-content: center;
  z-index: 1001;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: rgba(90, 109, 122, 0.3) 0px 1px 16px;
  flex-wrap: wrap;
  background: rgb(255, 255, 255);
  padding: 4px;
  position: relative;
  border-radius: 5px;
  top: 0px;
}
.editor-float-button-level1::before {
  content: "";
  width: 0px;
  height: 0px;
  position: absolute;
  right: 271px;
  bottom: -12px;
  border-width: 7px 5px 5px;
  border-style: solid;
  border-color: rgb(255, 255, 255) transparent transparent;
}
.editor-float-button-level2 {
  position: absolute;
  border-radius: 2px;
  max-width: 100%;
  transition: left 0.5s ease 0s, top 0.5s ease 0s;
}
.fade-in-item-container {
  display: flex;
  place-content: center flex-start;
  -webkit-box-pack: start;
  z-index: 1001;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: rgba(90, 109, 122, 0.3) 0px 1px 16px;
  flex-wrap: wrap;
  background: rgb(255, 255, 255);
  padding: 4px;
  position: relative;
  border-radius: 5px;
  width: 100%;
  top: 0px;
}
.editor-float-button-item {
  display: flex;
  margin-left: 3px;
  position: relative;
  -webkit-box-pack: start;
  justify-content: start;
  font-size: 12px;
  height: 100%;
  width: auto;
  cursor: pointer;
}
.editor-float-button-item-span {
  display: flex;
  user-select: none;
}
.editor-float-button-item-span:hover,
.editor-float-button-item:hover {
  background-color: rgb(237, 242, 255);
}
.float-button-selected {
  border-radius: 2px;
  align-self: center;
  color: #5b89fe !important;
  background-color: rgb(237, 242, 255);
}
.icon-text {
  position: relative;
  display: flex;
  height: 25px;
  padding: 0px 5px;
  -webkit-box-align: center;
  align-items: center;
  box-sizing: border-box;
  line-height: 1.5;
  white-space: nowrap;
  clear: both;
  font-size: 12px;
  font-weight: normal;
  cursor: pointer;
  opacity: 1;
}
</style>
