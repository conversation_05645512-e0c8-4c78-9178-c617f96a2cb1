<!-- eslint-disable standard/no-callback-literal -->
<template>
  <div class="dik">
    <Chat
      ref="chat"
      :visible.sync="active"
      :history-msg="historyMsg"
      :pending="pending"
      :rawDataList="rawDataList"
      @sendMsg="sendMsg"
      @resendMsg="resendMsg"
      @onRestart="reStart"
    />
    <div
      v-show="!active"
      class="robot-icon-dik"
      title="点击与MsunGPT对话"
      @click="showChat"
    >
      <GptRobot :size="40" />
    </div>
  </div>
</template>

<script>
import { parseTime, deepClone } from "./utils/base";
import { postData } from "./utils/req";
import GptRobot from "./robot.vue";
import Chat from "./chat.vue";
import marked from "marked";
export default {
  name: "GptMsg",
  components: {
    GptRobot,
    Chat,
  },
  props: {},
  data() {
    return {
      rawDataList: [], // 保存的原始数据列表 方便恢复
      conversation_id: "", // 会话id
      active: false,
      msg: "",
      sessionId: "",
      // 方法映射
      functions: {
        sum: "+",
        minus: "-",
        multiply: "*",
        divide: "/",
      },
      historyMsg: [this.createDefaultMsg()], // 聊天记录， 初始是默认gpt问用户的信息
      pending: false, // 思考中
    };
  },
  mounted() {},
  methods: {
    getInstance() {
      return this.$refs.msunReport.getInsStore();
    },
    reStart() {
      this.sessionId = "";
      this.historyMsg = [this.createDefaultMsg()]; //[this.historyMsg[0]]
      this.$emit("clearData");
    },
    createDefaultMsg() {
      return {
        question:
          "您好，我是众阳健康智能对话帮手。您有什么编辑器需求可以跟我沟通~",
        answer: "",
        status: "",
        dataTime: parseTime(Date.now(), "{y}/{m}/{d} {h}:{i}"),
      };
    },
    showChat() {
      this.active = true;
    },
    processString(str) {
      // 添加s修饰符以匹配换行符
      const extractRegex = /开\$始(.*?)结\$束/gs; // 注意这里加了s
      const executableStringArr = [];
      let match;

      while ((match = extractRegex.exec(str)) !== null) {
        executableStringArr.push(match[1]);
      }

      // 替换时也需要添加s修饰符
      const newStr = str.replace(/开\$始.*?结\$束/gs, "");

      return {
        executableStringArr,
        newStr,
      };
    },
    sendMsg(data) {
      if (this.pending) return;
      if (!data.msg) {
        this.$message.error("请您输入需求描述");
        return;
      }
      //debugger;
      this.pending = true;

      this.historyMsg.slice(-1)[0].answer = data.msg;

      const msg = data.msg;

      //console.log('解析的当前参数', reqData);
      // 发送消息
      const param = {
        inputs: {},
        query: msg,
        response_mode: "blocking",
        // response_mode: "streaming",
        conversation_id: this.conversation_id || "",
        user: "msun-editor-chat-app",
        files: [],
      };
      if (this.$refs.chat.$refs.chatInput) {
        this.$refs.chat.$refs.chatInput.style.height = "auto";
      }
      postData(
        "http://msunllmtest.msunsoft.com/MSUNGPT/chat-messages",
        param
      ).then(
        (res) => {
          if (!this.sessiongId && res.sessionId) {
            this.sessionId = res.sessionId;
          }
          this.pending = false;
          this.conversation_id = res?.data?.conversation_id;
          if (param.response_mode === "blocking") {
            const { executableStringArr, newStr } = this.processString(
              res.data.answer
            );
            const Msg = {
              question: [
                {
                  contentType: "text",
                  data: "", // 初始为空,用于逐字显示
                },
              ],
              answer: "",
              dataTime: parseTime(Date.now(), "{y}/{m}/{d} {h}:{i}"),
              result: "",
              hasProgram: executableStringArr.length > 0,
              indexInRawDataList: this.rawDataList.length - 1,
            };
            this.historyMsg.push(Msg);

            // 逐字显示文本
            let index = 0;

            const text = marked(newStr);
            const animateText = () => {
              if (index < text.length) {
                this.historyMsg[this.historyMsg.length - 1].question[0].data =
                  text.substring(0, index + 1);
                index++;
                requestAnimationFrame(animateText);
              }
            };
            requestAnimationFrame(animateText);
            const myEditor = this.editor.editor;
            this.$nextTick(() => {
              // 获取chat组件实例并聚焦input
              const chatComponent = this.$refs.chat.$refs.chatInput;
              if (chatComponent) {
                chatComponent.focus();
              }
              if (executableStringArr.length > 0) {
                const originData = this.rawDataList.pop();
                try {
                  const beforeData = myEditor.getRawData();
                  this.rawDataList.push(beforeData);
                  for (let i = 0; i < executableStringArr.length; i++) {
                    const fn = executableStringArr[i].trim();
                    if (fn) {
                      const newFn = new Function("editor", fn);
                      const result = newFn(myEditor);
                      console.log(result, "result");
                    }
                  }
                  const afterData = myEditor.getRawData();
                  this.rawDataList.push(afterData);
                } catch (error) {
                  myEditor.reInitRawByConfig(originData);
                  myEditor.refreshDocument();
                  console.log(error, "error");
                }
              }
            });
          } else {
            console.log("streaming", res);
            // debugger;
            // res = JSON.parse(res);
            // console.log("streaming", res);
            // this.handlerChatRes(res);
          }
        },
        (err) => {
          this.pending = false;
          this.$message.error(JSON.stringify(err));
          this.historyMsg.push({
            question: "当前请求不能完成,功能待开发~",
            answer: "",
            dataTime: parseTime(Date.now(), "{y}/{m}/{d} {h}:{i}"),
            result: "",
          });
        }
      );
    },
    handlerChatRes(res) {
      // 处理发送消息的返回值
      let dataset, message, functions;
      res.forEach((item) => {
        switch (item.type) {
          case "loadDataset":
            dataset = item;
            break;
          case "response":
            message = item;
            break;
          case "function":
            functions = item;
            break;
        }
      });

      dataset?.data && this.$emit("onGetDataset", dataset.data);

      this.historyMsg.push({
        question: message.data,
        answer: "",
        dataTime: parseTime(Date.now(), "{y}/{m}/{d} {h}:{i}"),
        result: "",
      });

      // 执行方法
      // function &&
    },
    resendMsg() {
      // 重新发送消息
      let msg = this.historyMsg.slice(-1)[0].answer;
      if (!msg) {
        this.historyMsg.pop();
        msg = this.historyMsg.slice(-1)[0].answer;
      }
      this.sendMsg(msg);
    },
    getCurrentTable() {
      // 拿到当前table
      const instance = this.getInstance();
      const tables = instance.sheet.tables;
      return tables[0] ? tables[0] : null;
    },
    // 封装的可执行函数
    groupBy() {
      // 分组数据
    },
    addColumn(fieldName, index = -1) {
      // 插入列
      const instance = this.getInstance();
      const table = this.getCurrentTable();
      if (!table) return;
      let allFields = [];
      const dataset = instance.workSpace.DataSets.get(table.dataId);
      dataset && (allFields = [...dataset.fields]);
      const field = allFields.find((item) => item.value === fieldName);
      if (!field) return;
      const obj = {
        name: field.name,
        value: field.value,
        alias: field.alias || undefined,
        showName: true,
        totalTypes: [],
        composeRule: field.composeRule,
        isMergeCell: true,
        filterComponents: false,
        transformType: field.transformType || field.type, // 要作为什么值使用
        format: field.format,
      };
      const rowFields = table.rowFields;
      if (rowFields.some((item) => item.value === fieldName)) {
        rowFields.splice(
          rowFields.findIndex((item) => item.value === fieldName),
          1
        );
      }
      if (index !== -1) {
        rowFields.splice(index, 0, obj);
      } else {
        rowFields.push(obj);
      }
      table.rowFields = rowFields;
      table.updateTable();
      table.renderTable();
    },
    deleteColumn(fieldName) {
      // 删除列
      const table = this.getCurrentTable();
      if (!table) return;

      const rowFields = table.rowFields;
      if (rowFields.some((item) => item.value === fieldName)) {
        rowFields.splice(
          rowFields.findIndex((item) => item.value === fieldName),
          1
        );
        table.rowFields = rowFields;
        table.updateTable();
        table.renderTable();
      }
    },
    createTree(composeRule) {
      // 创建自建字段的树
      return {
        node: {
          type: "OPERATOR",
          value: this.functions[composeRule.functionName],
        },
        children: [
          {
            type: "VARIABLE",
            value: composeRule.functionoParams[0],
          },
          {
            type: "VARIABLE",
            value: composeRule.functionoParams[1],
          },
        ],
      };
    },
    createParam(data) {
      const param = {};
      param.confirmDeleteTableColumn = this.confirmDeleteTableColumn || [];
      param.confirmDeleteCondition = this.confirmDeleteCondition || [];
      if (data.confirmTableColumn) {
        param.confirmTableColumn = this.splitFieldsTree(
          data.confirmTableColumn,
          param.confirmDeleteTableColumn
        );
      } else if (data.confirmCondition) {
        param.confirmCondition = this.splitConditionsTree(
          data.confirmCondition,
          param.confirmDeleteCondition
        );
      }
      if (data.key === "confirmStageTableColumn") {
        param.confirmStageTableColumn = this.splitFieldsTree(
          data.value,
          param.confirmDeleteTableColumn
        );
      } else if (data.key === "confirmStageCondition") {
        param.confirmStageCondition = this.splitConditionsTree(
          data.value,
          param.confirmDeleteCondition
        );
      }
      return param;
    },
    splitFieldsTree(fields, deleteFields) {
      // 拆分 保留和删除的 字段tree
      const confirm = [];
      fields.forEach((item) => {
        let deleteField = deleteFields.find((item) => item.tableName === item);
        if (!deleteField) {
          deleteFields.push({
            desc: item.desc,
            tableName: item.tableName,
            fields: item.fields.filter((it) => it.isDelete),
          });
        } else {
          deleteField.fields.push(...item.fields.filter((it) => it.isDelete));
        }
        let confirmField = confirm.find((item) => item.tableName === item);
        if (!confirmField) {
          const obj = {
            desc: item.desc,
            tableName: item.tableName,
            fields: item.fields.filter((it) => !it.isDelete),
          };

          obj.fields.length && confirm.push(obj);
        } else {
          confirmField.fields.push(...item.fields.filter((it) => !it.isDelete));
        }
      });
      return confirm;
    },
    splitConditionsTree(conditions, deleteCons) {
      // 拆分 保留和删除的条件 tree
      const confirm = [];
      conditions.forEach((item) => {
        let deleteCon = deleteCons.find((item) => item.tableName === item);
        if (!deleteCon) {
          deleteCons.push({
            desc: item.desc,
            tableName: item.tableName,
            fields: item.fields.filter((it) => it.isDelete),
          });
        } else {
          deleteCon.fields.push(...item.fields.filter((it) => it.isDelete));
        }
        let confirmCon = confirm.find((item) => item.tableName === item);
        if (!confirmCon) {
          const obj = {
            desc: item.desc,
            tableName: item.tableName,
            fields: item.fields.filter((it) => !it.isDelete),
          };

          obj.fields.length && confirm.push(obj);
        } else {
          confirmCon.fields.push(...item.fields.filter((it) => !it.isDelete));
        }
      });
      return confirm;
    },
  },
};
</script>

<style lang="less" scoped>
.dik {
  position: fixed;
  right: 15px;
  bottom: 40px;
  z-index: 999;
  width: 0px;
  height: 0px;
  padding: 0;
  .robot-icon-dik {
    position: fixed;
    right: -20px;
    bottom: 80px;
    transform: rotate(-35deg);
    z-index: 999;
    width: 40px;
    height: 40px;
    transition: all 0.3s linear;
    cursor: pointer;
    &:hover {
      transform: rotate(0deg);
      right: 0;
    }
  }
  .button {
    width: 50px;
    height: 50px;
    position: absolute;
    right: 0;
    bottom: 0;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    z-index: 99;
  }
  .close {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 20px;
    height: 20px;
    line-height: 18px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 50;
    cursor: pointer;
    color: #fff;
    text-align: center;
    font-size: 12px;
  }
  &.active {
    width: 280px;
    height: 200px;
    padding: 15px 20px 43px 15px;
    border-radius: 10px;
    box-shadow: 0 0 10px 8px rgba(0, 0, 0, 0.15);
    .button {
      width: 100%;
      background: #0e8bff;
      color: #fff;
    }
  }
}
</style>
