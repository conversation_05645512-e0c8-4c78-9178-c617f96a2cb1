import fs from 'fs';
import { exec } from 'child_process';
import { getPackages } from '@manypkg/get-packages'

const changesetMdPath = './.changeset/bump-patch.md'

const bumpTarget = process.argv[2] || 'patch' // 'major' | 'minor' | 'patch'

async function getMsunPackageNames() {
  const { packages } = await getPackages(process.cwd())

  const packageNames = packages.map((pkg) => pkg.packageJson.name)
  const msunPackageNames = packageNames.filter((name) => name.startsWith('msun-'))

  return msunPackageNames
}

async function bumpPackagesPatchVersion() {

  const msunPackageNames = await getMsunPackageNames();

  const content = `
---
${msunPackageNames.map(pkg => `"${pkg}": ${bumpTarget}`).join('\n')}
---
`;

  try {
    await fs.promises.writeFile(changesetMdPath, content);

    // 文件写入成功后，执行 pnpm changesets version 命令
    const { stdout } = await new Promise((resolve, reject) => {
      exec('pnpm changeset version', { cwd: process.cwd() }, (err, stdout, stderr) => {
        if (err) {
          console.error(`Error executing command: ${err}`);
          reject(err);
        } else {
          resolve({ stdout, stderr });
        }
      });
    });

    console.log(`${stdout}`);
    console.log("已更新以下包的版本号：", msunPackageNames.join(', '));
  } catch (err) {
    console.error(`Error writing file: ${err}`);
  }

}

async function bumpRootPackagePatchVersion() {
  exec(`pnpm version ${bumpTarget} --no-git-tag-version`)
  console.log("已更新根目录版本号");
}

await bumpPackagesPatchVersion();
await bumpRootPackagePatchVersion();


import path from 'path'

async function attachRealVersion() {
  const { packages } = await getPackages(process.cwd())

  await Promise.all(packages.map(async (pkg) => {
    const { relativeDir } = pkg
    const packageJsonPath = path.join(relativeDir, 'package.json')

    const packageJson = JSON.parse(await fs.promises.readFile(packageJsonPath, 'utf-8'))

    packageJson.realVersion = packageJson.version
    await fs.promises.writeFile(packageJsonPath, JSON.stringify(packageJson, null, 2))
  }))

  console.log("已更新子包的 realVersion 字段");
}

// 给 package.json 附加 realVersion 字段
await attachRealVersion();
