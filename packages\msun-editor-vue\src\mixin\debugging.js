import debuggingTool from "../components/debuggingTool.vue";
import { convertToNumber } from "../assets/js/utils";
const debuggingMixIn = {
  data() {
    return {
      showDebuggingTool: false,
      debugInputModalInfo: {},
      showDebugInputModal: false,
      isDebugMode: false,
      delFieldSymbolCount: 0,
      debugButtons: [
        {
          title: "常用命令",
          btnList: [
            {
              name: "测试按钮",
              func: this.testFn,
            },
            {
              name: "打开",
              func: () => {
                this.openFile("load");
              },
            },
            {
              name: "加载",
              func: () => {
                this.openLoadRawModal("load");
              },
            },
            {
              name: "按配置打开",
              func: () => {
                this.openFile("useConfig");
              },
            },
            {
              name: "按配置加载",
              func: () => {
                this.openLoadRawModal("useConfig");
              },
            },
            {
              name: "导出JSON",
              func: () => {
                this.editor.download("data.json");
              },
            },
            {
              name: "调试模式",
              func: this.debuggerMode,
            },
            {
              name: "配置重载",
              func: this.openSystemConfigModal,
            },
            {
              name: "强制刷新",
              func: () => {
                this.editor.refreshDocument(true);
                this.$editor.info("已执行!");
              },
            },
          ],
        },
        {
          title: "功能设置",
          btnList: [
            {
              name: "切换旧版本",
              func: () => {
                const switchOff = this.instance.localTest.transUse;
                this.instance.localTest.transUse = !switchOff;
                localStorage.setItem("EditorTransUse", switchOff ? "0" : "1");
                this.$editor.info("切换" + (switchOff ? "旧" : "新") + "版！");
              },
            },
            {
              name: "图片DPI修改",
              func: this.toggleChangeImageDpi,
            },
            {
              name: "辅助按钮",
              func: () => {
                if (this.showFloatMenu) {
                  this.setLocalOtherConfig("floatMenu", 0);
                  this.showFloatMenu = false;
                } else {
                  this.setLocalOtherConfig("floatMenu", 1);
                  this.initFloatMenu();
                }
              },
            },
            {
              name: "文本域无边框",
              func: () => {
                let fieldShowMode = this.editor.config.fieldShowMode;
                fieldShowMode === 5 ? (fieldShowMode = 0) : fieldShowMode++;
                this.editor.reInitConfig({ fieldShowMode });
                this.setLocalSystemConfig("fieldShowMode", fieldShowMode);
                if (fieldShowMode === 0) {
                  this.$editor.info("已退出");
                } else {
                  this.$editor.info("已切换【模式" + fieldShowMode + "】");
                }
              },
            },
            {
              name: "固定纸张打印",
              func: () => {
                // const source = this.upConfig?.systemConfig?.source;
                // if (!source) {
                //   return this.$editor.info(
                //     "未配置产品名，不能配置，以免影响其他产品使用！"
                //   );
                // }
                const fixedPaper = this.getPrintConfig("fixedPaper");
                this.setLocalPrintConfig("fixedPaper", !fixedPaper);
                this.$editor.info(fixedPaper ? "关闭！" : "开启");
              },
            },
            {
              name: "清除本地配置",
              func: () => {
                this.clearLocalConfig();
                this.$editor.info("已执行！");
              },
            },
            {
              name: "更新数据版本",
              func: () => {
                this.editor.clearVersionInfo();
                const version = this.versionInfo.version;
                this.editor.document_meta.versionList.push({
                  version,
                  time: Date.now(),
                });
                this.$editor.info("已执行！");
              },
            },
            {
              name: "文本域转文本",
              func: () => {
                const handleFields = this.editor.transFields2Content();
                if (handleFields) {
                  this.$editor.info("已处理" + handleFields);
                } else {
                  this.$editor.info("未获取到需要处理的文本域");
                }
              },
            },
          ],
        },
        {
          title: "打印预览",
          btnList: [
            {
              name: "浏览器打印",
              func: () => {
                this.convertPDF("print");
              },
            },
            {
              name: "查看PDF",
              func: () => {
                this.convertPDF("view");
              },
            },
            {
              name: "图片打印",
              func: () => {
                this.openSystemPrint(null, "image");
              },
            },
            {
              name: "pdf打印",
              func: () => {
                this.openSystemPrint(null, "pdf");
              },
            },
            {
              name: "C++打印",
              func: () => {
                this.openSystemPrint(null, "json");
              },
            },
          ],
        },
        {
          title: "输出",
          btnList: [
            {
              name: "编辑器实例",
              func: () => {
                this.consoleLog("编辑器实例：", "debug");
                this.consoleLog(this.editor, "dir");
              },
            },
            {
              name: "配置信息",
              func: () => {
                this.consoleLog("编辑器当前配置：", "debug");
                this.consoleLog(this.instance.config.getConfig(), "dir");
              },
            },
            {
              name: "选区信息",
              func: () => {
                this.consoleLog("编辑器选区：", "debug");
                this.consoleLog(this.editor.selection, "dir");
              },
            },
            {
              name: "原始数据",
              func: () => {
                this.consoleLog("原始数据：", "debug");
                const resData = this.editor.getRawData();
                this.consoleLog(resData, "dir");
                this.instance.Clipboard.write(
                  JSON.stringify(resData, null, "\t")
                );
                this.$editor.info("已复制到剪切板！");
              },
            },
            {
              name: "数据直改",
              func: () => {
                let rawData = this.editor.getRawData();
                if (typeof rawData === "string") {
                  rawData = this.instance.utils.uncompressData(rawData);
                }
                const resData = JSON.stringify(rawData, null, 4);
                this.debugInputModalInfo = {
                  key: "rawDataEdit",
                  title: "原始数据",
                  width: 1000,
                  height: 500,
                  placeholder: "请数据正确的编辑器原始数据",
                  value: resData,
                };
                this.showDebugInputModal = true;
              },
            },
            {
              name: "BASE64",
              func: () => {
                this.convertPDF("base64", (base64) => {
                  this.consoleLog("base64：", "debug");
                  this.consoleLog(base64);
                  this.instance.Clipboard.write(base64);
                  this.$editor.info("已复制到剪切板！");
                });
              },
            },
            {
              name: "CppJSON",
              func: () => {
                this.consoleLog("C++打印数据：", "debug");
                const resData = this.editor.assemblePageJson();
                this.consoleLog(resData, "dir");
                this.instance.Clipboard.write(JSON.stringify(resData));
                this.$editor.info("已复制到剪切板！");
              },
            },
            {
              name: "API",
              func: () => {
                this.consoleLog("editor_api", "debug");
                const keyword = prompt("输入关键字：");
                console.log(">>>>>>>>>instance:");
                this.consoleLog(
                  this.getAllProperties(this.instance, keyword).sort(function (
                    a,
                    b
                  ) {
                    return a.localeCompare(b);
                  }),
                  "table"
                );
                console.log(">>>>>>>>>editor:");
                this.consoleLog(
                  this.getAllProperties(this.editor, keyword).sort(function (
                    a,
                    b
                  ) {
                    return a.localeCompare(b);
                  }),
                  "table"
                );
              },
            },
            {
              name: "接口测试",
              func: this.openApiTestModal,
            },
          ],
        },
        {
          title: "测试",
          btnList: [
            {
              name: "保存",
              func: () => {
                if (
                  confirm("测试完后请【手动清除】，否则会影响其他内容加载！")
                ) {
                  localStorage.setItem(
                    "testRawData",
                    JSON.stringify(this.editor.getRawData())
                  );
                  this.$editor.info(
                    "已保存至浏览器LocalStorage【testRawData】!"
                  );
                }
              },
            },
            {
              name: "清除保存",
              func: () => {
                localStorage.removeItem("testRawData");
                this.$editor.info("已清除【testRawData】!");
              },
            },
            {
              name: "本地数据集",
              func: () => {
                let resDataSet = localStorage.getItem("EditorLocalDataSet");
                if (resDataSet) {
                  try {
                    resDataSet = JSON.stringify(
                      JSON.parse(resDataSet),
                      null,
                      4
                    );
                  } catch (e) {
                    this.$editor.error("格式错误");
                  }
                }
                this.debugInputModalInfo = {
                  key: "localDataSet",
                  title: "本地数据集",
                  width: 1000,
                  height: 500,
                  placeholder:
                    "请数据正确的数据集格式{'数据集1':{},'数据集2':{}}",
                  value: resDataSet,
                };
                this.showDebugInputModal = true;
              },
            },
            {
              name: "批注",
              func: this.testComment,
            },
            {
              name: "图形编辑模式",
              func: this.shapeMode,
            },
            {
              name: "设置工具",
              func: this.openSettingTool,
            },
            {
              name: "级联占位加宽",
              func: this.changeBigBoxWidth,
            },
            {
              name: "启动本地测试",
              func: () => {
                const useLocal = this.instance.localTest.useLocal;
                this.instance.localTest.useLocal = !useLocal;
                localStorage.setItem("EditorLocalTest", useLocal ? "" : "true");
                this.$editor.info((useLocal ? "关闭" : "开启") + "本地测试!");
              },
            },
            {
              name: "开启内测功能",
              func: () => {
                const useNew = this.instance.localTest.useNew;
                this.instance.localTest.useNew = !useNew;
                localStorage.setItem("EditorUseNew", useNew ? "" : "true");
                this.$editor.info((useNew ? "关闭" : "开启") + "内测功能!");
              },
            },
            {
              name: "分组表单",
              func: this.setGroupForm,
            },
            {
              name: "导出HTML",
              func: this.exportHTML,
            },
            {
              name: "插入模板",
              func: () => {
                this.openFile("insert");
              },
            },
            {
              name: "管理员模式",
              func: () => {
                if (this.editor.adminMode) {
                  this.editor.setAdminMode(false);
                  this.$editor.info("已关闭!");
                } else {
                  this.editor.setAdminMode(true);
                  this.$editor.info("已开启!");
                }
              },
            },
            {
              name: "保存痕迹",
              func: () => {
                if (
                  localStorage.getItem("saveTrace1") &&
                  localStorage.getItem("saveTrace2") &&
                  localStorage.getItem("saveTrace3") &&
                  localStorage.getItem("saveTrace4") &&
                  localStorage.getItem("saveTrace5") &&
                  localStorage.getItem("saveTrace6") &&
                  localStorage.getItem("saveTrace7") &&
                  localStorage.getItem("saveTrace8")
                ) {
                  localStorage.removeItem("saveTrace1");
                  localStorage.removeItem("saveTrace2");
                  localStorage.removeItem("saveTrace3");
                  localStorage.removeItem("saveTrace4");
                  localStorage.removeItem("saveTrace5");
                  localStorage.removeItem("saveTrace6");
                  localStorage.removeItem("saveTrace7");
                  localStorage.removeItem("saveTrace8");
                }
                this.userLogin({ id: "111", name: "侧旗" });
                const { success } = this.saveTraceInfo();
                if (success) {
                  const raw = this.editor.getRawData();
                  if (!localStorage.getItem("saveTrace1")) {
                    localStorage.setItem("saveTrace1", JSON.stringify(raw));
                    this.$editor.info("保存成功！saveTrace1");
                  } else if (!localStorage.getItem("saveTrace2")) {
                    localStorage.setItem("saveTrace2", JSON.stringify(raw));
                    this.$editor.info("保存成功！saveTrace2");
                  } else if (!localStorage.getItem("saveTrace3")) {
                    localStorage.setItem("saveTrace3", JSON.stringify(raw));
                    this.$editor.info("保存成功！saveTrace3");
                  } else if (!localStorage.getItem("saveTrace4")) {
                    localStorage.setItem("saveTrace4", JSON.stringify(raw));
                    this.$editor.info("保存成功！saveTrace4");
                  } else if (!localStorage.getItem("saveTrace5")) {
                    localStorage.setItem("saveTrace5", JSON.stringify(raw));
                    this.$editor.info("保存成功！saveTrace5");
                  } else if (!localStorage.getItem("saveTrace6")) {
                    localStorage.setItem("saveTrace6", JSON.stringify(raw));
                    this.$editor.info("保存成功！saveTrace6");
                  } else if (!localStorage.getItem("saveTrace7")) {
                    localStorage.setItem("saveTrace7", JSON.stringify(raw));
                    this.$editor.info("保存成功！saveTrace7");
                  } else if (!localStorage.getItem("saveTrace8")) {
                    localStorage.setItem("saveTrace8", JSON.stringify(raw));
                    this.$editor.info("保存成功！saveTrace8");
                  }
                } else {
                  this.$editor.info("未改动，保存失败！");
                }
              },
            },
            {
              name: "痕迹对比",
              func: () => {
                const raw1 = localStorage.getItem("saveTrace1");
                const raw2 = localStorage.getItem("saveTrace2");
                const raw3 = localStorage.getItem("saveTrace3");
                const raw4 = localStorage.getItem("saveTrace4");
                const raw5 = localStorage.getItem("saveTrace5");
                const raw6 = localStorage.getItem("saveTrace6");
                const raw7 = localStorage.getItem("saveTrace7");
                const raw8 = localStorage.getItem("saveTrace8");
                const lastRawData =
                  raw8 || raw7 || raw6 || raw5 || raw4 || raw3 || raw2 || raw1;
                console.log(lastRawData);
                const raw = this.newVersionCompareTraces(raw1, raw6, true);
                this.editor.reInitRaw(raw);
                this.instance.editor.view_mode = "person";
                this.editor.refreshDocument();
              },
            },
            {
              name: "分组痕迹对比",
              func: () => {
                const raw1 = localStorage.getItem("saveTrace1");
                const raw2 = localStorage.getItem("saveTrace2");
                const focusGroup = this.editor.selection.getFocusGroup();
                const focusGroupId = focusGroup.id;

                const raw = this.compareGroupTraces({
                  rawData1: raw1,
                  rawData2: raw2,
                  showPersonInfo: true,
                  groupId: focusGroupId,
                });
                this.editor.reInitRaw(raw);
                this.editor.refreshDocument();
              },
            },
            {
              name: "批量打印",
              func: () => {
                const rawList = [];
                for (let i = 0; i < 5; i++) {
                  const key = "testRawData" + i;
                  let btRaw = localStorage.getItem(key);
                  if (!btRaw) {
                    this.$editor.warning("未发现" + key);
                    continue;
                  }
                  rawList.push(btRaw);
                }
                console.log(rawList);
                this.batchPrint(rawList);
              },
            },
            {
              name: "网格线",
              func: () => {
                if (this.editor.config.rowLineType) {
                  this.editor.toggleRowLineType(0);
                } else {
                  this.editor.toggleRowLineType(1);
                }
              },
            },
            {
              name: "文本域检查",
              func: () => {
                this.checkAllFields();
              },
            },
            {
              name: "文本域检查(破坏性)",
              func: () => {
                this.checkAllFields(true);
              },
            },
            {
              name: "文本域修复",
              func: () => {
                this.fixCheckedFields(true);
              },
            },
            {
              name: "去文本域修复(通用)",
              func: () => {
                this.fixFieldsByTrans();
              },
            },
          ],
        },
      ],
    };
  },
  components: {
    debuggingTool,
  },
  methods: {
    testFn() {
      const { editor } = this.instance;
      const fields = editor.getFieldsByName("ceshi")[0];
      fields.updateGroupCheckedByValue("ceshi2");
      fields.updateGroupCheckedByValue("ceshi4");
      // fields.forEach(field => {
      //   field.updateGroupCheckedByValue("ceshi2")
      // })
      // const { editor, saveTraceInfo, getLastModificationRecord } =
      //   this.instance;
      // const info = this.saveTraceInfo();
      // console.log(info);
      // editor.changePageDirection("horizontal");
      // editor.selection.setCursorPosition([46, 20]);
      // editor.changeContentAlign("right");
      // const arr = editor.getPositionRelativeToPageLeftBottom("患者签名");
      // console.log(arr, "打印 arr");
      // const field = editor.getFieldById("field-5cbf8092");
      // field.setNewText("1998-01-01 23:59:59");
      // editor.updateFieldText({
      //   fields: [field],
      // });
      // console.log(1);
    },
    setFieldCheckMark(editor, f) {
      const font = editor.createElement("font");
      font.temp_word_bgColor = "red";
      font.temp_word_color = "white";
      editor.fontMap.add(font);
      f.getAllElements().forEach((item) => {
        if (item.field_id === f.id) {
          item.font = font;
        }
      });
      editor.refreshDocument();
      this.$editor.error(
        `异常文本域id:【${f.id}】,name:【${f.name}】,placeholder:${f.placeholder}（注意临近文本域）`
      );
    },
    checkAllFields(isForce) {
      const editor = this.editor;
      const fields = editor.getAllFields(editor.current_cell);
      if (isForce) {
        editor.setAdminMode(true);
        for (let i = 0; i < fields.length; i++) {
          let f = fields[i];
          try {
            editor.clearFields([f]);
          } catch (e) {
            // editor.reInitRaw(editor.raw);
            // editor.refreshDocument();
            // f = editor.getFieldById(f.id);
            if (f.isRemove) {
              f = f.parent;
            }
            this.setFieldCheckMark(editor, f);
            return;
          }
          try {
            editor.removeFields([f]);
          } catch (e) {
            // editor.reInitRaw(editor.raw);
            // editor.refreshDocument();
            // f = editor.getFieldById(f.id);
            this.setFieldCheckMark(editor, f);
            return;
          }
        }
        editor.setAdminMode(false);
        this.$editor.info("检测完毕，无异常文本域");
      } else {
        for (let i = 0; i < fields.length; i++) {
          const f = fields[i];
          let hasStart = false;
          let hasEnd = false;
          const start = f.start_sym_char;
          const end = f.end_sym_char;
          // 以上为第一重检查，再检查文本域闭合状态
          const allParagraph = f.cell.parent
            ? f.cell.paragraph
            : editor.getAllParagraph(true);
          for (const p of allParagraph) {
            for (let j = 0; j < p.characters.length; j++) {
              const item = p.characters[j];
              if (item === start) {
                if (hasStart) {
                  this.setFieldCheckMark(editor, f);
                  return;
                }
                hasStart = true;
              }
              if (item === end) {
                if (hasEnd) {
                  this.setFieldCheckMark(editor, f);
                  return;
                }
                hasEnd = true;
              }
            }
          }
        }
        this.$editor.info("检测完毕，无异常文本域");
      }
    },
    fixFieldsByTrans() {
      const editor = this.editor;
      const fields = editor.getAllFields();
      for (let i = 0; i < fields.length; i++) {
        const f = fields[i];
        let hasStart = false;
        let hasEnd = false;
        const start = f.start_sym_char;
        const end = f.end_sym_char;
        // 以上为第一重检查，再检查文本域闭合状态
        const allParagraph = f.cell.parent
          ? f.cell.paragraph
          : editor.getAllParagraph(true);
        for (const p of allParagraph) {
          for (let j = 0; j < p.characters.length; j++) {
            const item = p.characters[j];
            if (item === start) {
              if (hasStart) {
                this.transField2Content(f);
                return;
              }
              hasStart = true;
            }
            if (item === end) {
              if (hasEnd) {
                this.transField2Content(f);
                return;
              }
              hasEnd = true;
            }
          }
        }
      }
      this.$editor.info("修复完毕，无异常文本域");
    },
    transField2Content(field) {
      this.editor.transFields2Content([field]);
      this.$editor.info(
        `已将文本域${field.id}，${field.placeholder}转为普通文本内容`
      );
    },
    fixCheckedFields() {
      try {
        const editor = this.editor;
        const useLocal = this.instance.localTest.useLocal;
        if (!useLocal) {
          this.instance.localTest.useLocal = true;
        }
        editor.refreshDocument(true);

        const fields = editor.getAllFields(editor.root_cell);
        const spliceErrorSymbol = (symbols, p) => {
          let count = 0;
          if (symbols.length > 1) {
            for (let k = 0; k < symbols.length - 1; k++) {
              const sIndex = p.characters.indexOf(symbols[k]);
              if (sIndex > -1) {
                p.characters.splice(sIndex, 1);
                count++;
              }
            }
          }
          return count;
        };
        let count = 0;
        for (let i = 0; i < fields.length; i++) {
          const f = fields[i];
          const start = f.start_sym_char;
          const end = f.end_sym_char;
          // 以上为第一重检查，再检查文本域闭合状态
          const allParagraph = f.cell.parent
            ? f.cell.paragraph
            : editor.getAllParagraph(true);
          const startSym = [];
          const endSym = [];
          for (const p of allParagraph) {
            for (let j = 0; j < p.characters.length; j++) {
              const item = p.characters[j];
              if (item === start) {
                startSym.push(item);
              }
              if (item === end) {
                endSym.push(item);
              }
            }
          }
          for (const p of allParagraph) {
            for (let j = 0; j < p.characters.length; j++) {
              count += spliceErrorSymbol(startSym, p);
              count += spliceErrorSymbol(endSym, p);
            }
          }
        }
        editor.refreshDocument(true);
        this.instance.localTest.useLocal = useLocal;
        if (this.delFieldSymbolCount > 100 && count > 0) {
          this.delFieldSymbolCount = 0;
          return this.$editor.warn("修复失败，已终止");
        }
        if (count > 0) {
          this.delFieldSymbolCount += count;
          this.fixCheckedFields();
        } else {
          this.$editor.info("已删除" + this.delFieldSymbolCount + "个多余边框");
          this.delFieldSymbolCount = 0;
        }
      } catch (e) {
        this.delFieldSymbolCount = 0;
        console.error(e);
      }
    },
    /**
     * 打开编辑器调试工具
     * @param isShow
     */
    openDebuggingTool(isShow) {
      this.showDebuggingTool = !!isShow;
    },
    /**
     * 控制台打印需要信息
     * @param info
     * @param type
     */
    consoleLog(info, type) {
      const print = console;
      if (print[type]) {
        print[type](info);
      } else {
        print.log(info);
      }
    },
    /**
     * 调试模式下控制台输出信息
     * @param clickInfo
     */
    logDebugInfo(clickInfo) {
      // const sel = this.editor.selection;
      // const focus = sel.focus;
      // const para_focus = sel.para_focus;
      // this.$editor.info(
      //   "modelPath:【" + focus + "】;paraPath:【" + para_focus + "】"
      // );
      this.consoleLog(clickInfo);
    },
    /**
     * 获取包含关键字的所有内容
     * @param obj
     * @param arg
     * @returns {[]|*}
     */
    getAllProperties(obj, ...arg) {
      function getProperty(new_obj) {
        if (new_obj.__proto__ === null) {
          //说明该对象己经是最顶层的对象
          return [];
        }
        let properties = Object.getOwnPropertyNames(new_obj);
        let arr = [];
        arg = arg.filter((item) => item);
        arg.forEach((v) => {
          const newValue = properties.filter((property) => {
            return property.toLowerCase().indexOf(v.toLowerCase()) > -1;
          });
          if (newValue.length > 0) {
            arr = arr.concat(newValue);
          }
        });
        if (!arg.length) {
          arr = arr.concat(properties);
        }
        return [...arr, ...getProperty(new_obj.__proto__)];
      }
      return getProperty(obj);
    },
    debuggerMode() {
      this.isDebugMode = !this.isDebugMode;
      if (this.isDebugMode) {
        this.$editor.info("进入调试模式！");
        // this.showDebuggingTool = false;
      } else {
        this.$editor.info("退出调试模式！");
      }
    },
    setGroupForm() {
      const editor = this.editor;
      const group = editor.selection.getFocusGroup();
      if (group) {
        editor.setGroupFormMode(!group.is_form, group);
        if (group.is_form) {
          this.$editor.info("已设置!");
        } else {
          this.$editor.info("已取消设置!");
        }
      } else {
        this.$editor.info("未获取到分组!");
      }
    },
    exportHTML() {
      const editor = this.editor;
      const html = editor.modelData2Html();
      editor.download("Test.html", html);
    },
    openSystemConfigModal() {
      this.debugInputModalInfo = {
        key: "reInitConfig",
        title: "配置重载 例：page_padding_left:10,page_padding_right:10",
        placeholder: "请填入systemConfig中的配置项，多个使用逗号隔开",
      };
      this.showDebugInputModal = true;
    },
    openApiTestModal() {
      this.debugInputModalInfo = {
        key: "apiTest",
        title: "接口测试",
        placeholder:
          "请填入需要测试的接口与参数，例：printView()或changePageSize('A5','horizontal')或getFocusField()",
      };
      this.showDebugInputModal = true;
    },
    openLoadRawModal(type) {
      this.openFileType = type ? type : "load";
      this.debugInputModalInfo = {
        key: "load",
        title: "加载JSON或都昌XML",
        placeholder: "JSON或XML数据",
      };
      this.showDebugInputModal = true;
    },
    cancelDebugChildModal() {
      this.showDebugInputModal = false;
    },
    handleDebugChildModalOk(key, value) {
      if (key === "reInitConfig") {
        this.handleSystemConfigOk(value);
      }
      if (key === "load") {
        this.loadSupportFile(value);
      }
      if (key === "rawDataEdit") {
        this.editor.reInitRawByConfig(value);
        this.editor.refreshDocument();
      }
      if (key === "apiTest") {
        this.apiTest(value);
      }
      if (key === "localDataSet") {
        if (!value) {
          localStorage.removeItem("EditorLocalDataSet");
          this.$editor.info("已删除临时数据集");
        } else {
          try {
            JSON.parse(value);
            localStorage.setItem("EditorLocalDataSet", value);
            this.$editor.info("保存成功，刷新页面后生效");
          } catch (e) {
            this.$editor.error("格式错误");
            return;
          }
        }
      }
      this.showDebugInputModal = false;
    },
    apiTest(value) {
      const out = console;
      value = value.replace(/\s+/g, "");
      // 使用正则表达式提取方法名和参数部分
      const matches = value.match(/(\w+)\((.*)\)/);

      if (matches) {
        const methodName = matches[1]; // 方法名
        const argsString = matches[2]; // 参数字符串部分

        try {
          // 使用 Function 构造函数来解析参数字符串
          const args = Function(`return [${argsString}]`)();

          const methodSources = [
            this.instance,
            this.editor,
            this.editor.selection,
          ];

          const method = methodSources.find(
            (source) => methodName && typeof source[methodName] === "function"
          );

          if (method) {
            const res = method[methodName](...args);
            if (res) {
              out.log(res);
            }
            this.$editor.info("已执行,如存在返回值请在控制台查看");
          } else {
            this.$editor.error("未找到方法或方法名不正确");
          }
        } catch (error) {
          this.$editor.error("参数解析失败:", error);
          out.error(error);
        }
      } else {
        this.$editor.error("输入格式不正确");
      }
    },
    /**
     * 重载编辑器systemConfig
     */
    handleSystemConfigOk(value) {
      if (value) {
        try {
          // 使用正则表达式来分割字符串
          const parts = value.split(/[,，]/);
          // 移除空白项
          const result = parts.filter((part) => part.trim() !== "");
          // 分割后的结果数组
          const splitByComma = result.map((item) => item.split(/[:：]/));
          // 创建 JSON 对象
          const jsonObject = {};
          splitByComma.forEach((parts) => {
            if (parts.length === 2) {
              const key = parts[0].trim();
              const value = convertToNumber(parts[1].trim());
              jsonObject[key] = value;
            }
          });
          this.editor.reInitConfig(jsonObject);
        } catch (e) {
          this.$editor.error(e.message);
        }
      }
      // 按数据中配置的信息重新加载
      if (value === "") {
        const pageInfo = this.editor.raw.config.page_info;
        if (pageInfo) {
          this.editor.reInitConfig(pageInfo);
        }
      }
    },
    toggleChangeImageDpi() {
      //首先从localStorge中获取内容，如果能够获取到
      const editorPrintChangeDpi = localStorage.getItem("editorPrintChangeDpi");
      if (editorPrintChangeDpi) {
        localStorage.removeItem("editorPrintChangeDpi");
        this.$editor.info("已取消设置！");
      } else {
        localStorage.setItem("editorPrintChangeDpi", "1");
        this.$editor.info("已设置！");
      }
    },
  },
};
export default debuggingMixIn;
