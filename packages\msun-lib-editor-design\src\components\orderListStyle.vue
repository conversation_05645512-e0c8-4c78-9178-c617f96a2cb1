<template>
  <div class="list_type">
    <div @click="numOrChinese('number')">
      <div>
        <span> 1. </span>
        <hr />
      </div>
      <div>
        <span> 2. </span>
        <hr />
      </div>
      <div>
        <span> 3. </span>
        <hr />
      </div>
    </div>
    <div @click="numOrChinese('chinese')">
      <div>
        <span> 一、 </span>
        <hr />
      </div>
      <div>
        <span> 二、 </span>
        <hr />
      </div>
      <div>
        <span> 三、 </span>
        <hr />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  mounted() {},
  methods: {
    numOrChinese(data) {
      this.$emit("orderType", data);
    },
  },
};
</script>

<style lang="less" scoped>
.list_type {
  width: 160px;
  display: flex;
}
.list_type > div {
  width: 50%;
  border: 1px solid #000;
  margin: 5px;
  padding: 5px;
  div {
    display: flex;
    hr {
      margin-top: 10px;
      width: 60%;
    }
  }
}
.list_type > div:hover {
  border: 1px solid rgb(96, 146, 231);
}
</style>
