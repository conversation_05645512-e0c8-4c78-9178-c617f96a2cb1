<template>
  <div class="menstruation1">
    <div class="editor_col_menstrual">
      <div style="height: 40px"></div>
      <div class="editor_left_top_menstrual">初潮年龄</div>
      <a-date-picker
        dropdownClassName="xeditor-input-up"
        v-if="showFirstDate"
        :format="dateFormat"
        :valueFormat="valueFormat"
        :defaultValue="meta.params[0]"
        @change="changeFirstDate"
      ></a-date-picker>
      <a-input v-if="!showFirstDate" type="text" v-model="meta.params[0]" />
      <div class="editor_left_bottom_menstrual">
        <a-checkbox @change="firstUseDate" :checked="showFirstDate">
          是否使用日期
        </a-checkbox>
      </div>
    </div>
    <div class="editor_col_menstrual">
      <div class="editor_center_top_menstrual">经期（天）</div>
      <div class="editor_select_menstrual">
        <a-input v-model="meta.params[1]">
          <div
            slot="addonAfter"
            style="height: 30px; line-height: 30px"
            @click="showmenstrual"
          >
            <icon-common icon="icon-xiala"></icon-common>
          </div>
        </a-input>
        <div v-if="showMenstrual" class="editor_date-select_menstrual">
          <div
            v-for="(item, i) in dayNum"
            :key="i"
            class="editor_selected_menstrual"
            @click="changeMenstrualDate(i)"
          >
            {{ item }}
          </div>
        </div>
      </div>
      <div class="editor_horizontal_menstrual"></div>
      <div class="editor_center_menstrual">周期（天）</div>
      <div class="editor_select_menstrual">
        <a-input v-model="meta.params[2]">
          <div
            slot="addonAfter"
            style="height: 30px; line-height: 30px"
            @click="showmenstrualCycle"
          >
            <icon-common
              icon="icon-xiala"
              style="line-height: 32px"
            ></icon-common>
          </div>
        </a-input>
        <div v-if="showMenstrualCycle" class="editor_date-select_menstrual">
          <div
            v-for="(item, i) in dayNum"
            :key="i"
            class="editor_selected_menstrual"
            @click="changeMenstrualCycleDate(i)"
          >
            {{ item }}
          </div>
        </div>
      </div>
    </div>
    <div class="editor_col_menstrual">
      <div class="editor_right_top_menstrual">末次月经/绝经年龄</div>
      <a-date-picker
        dropdownClassName="xeditor-input-up"
        v-if="showEndDate"
        :format="dateFormat"
        :valueFormat="valueFormat"
        :defaultValue="meta.params[3]"
        @change="changeEndDate"
      ></a-date-picker>
      <a-input v-if="!showEndDate" type="text" v-model="meta.params[3]" />
      <div class="editor_left_bottom_menstrual">
        <a-checkbox @change="endUseDate" :checked="showEndDate">
          是否使用日期
        </a-checkbox>
      </div>
    </div>
  </div>
</template>

<script>
import iconCommon from "../common/iconCommon.vue";
import moment from "moment";
export default {
  name: "menstruation1",
  components: { iconCommon },
  data() {
    return {
      showMenstrual: false,
      showMenstrualCycle: false,
      showFirstDate: false,
      showEndDate: false,
      defaultDate: "",
      dateFormat: "YYYY-MM-DD",
      valueFormat: "YYYY-MM-DD",
      dayNum: [
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
        21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31,
      ],
    };
  },
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {
    let dateRule =
      /\d{4}-(((0[1-9])|(1[0-2])))(-((0[1-9])|([1-2][0-9])|(3[0-1])))$/;
    if (this.meta.params[0] && dateRule.test(this.meta.params[0])) {
      this.showFirstDate = true;
    } else {
      this.showFirstDate = false;
    }
    if (this.meta.params[3] && dateRule.test(this.meta.params[3])) {
      this.showEndDate = true;
    } else {
      this.showEndDate = false;
    }
  },
  methods: {
    moment,
    changeMenstrualDate(i) {
      this.meta.params[1] = i + 1;
      this.showMenstrual = false;
    },
    changeMenstrualCycleDate(i) {
      this.meta.params[2] = i + 1;
      this.showMenstrualCycle = false;
    },
    //是否展示周期下拉框
    showmenstrualCycle() {
      this.showMenstrualCycle = !this.showMenstrualCycle;
    },
    //是否展示经期下拉框
    showmenstrual() {
      this.showMenstrual = !this.showMenstrual;
    },
    changeFirstDate(date) {
      this.meta.params[0] = date;
    },
    changeEndDate(date) {
      this.meta.params[3] = date;
    },
    // changeMenstruationDayNum(num) {
    //   this.meta.params[1] = num + 1;
    // },
    // changeCycleDayNum(num) {
    //   this.meta.params[2] = num + 1;
    // },
    firstUseDate() {
      this.meta.params[0] = "";

      this.showFirstDate = !this.showFirstDate;
    },
    endUseDate() {
      this.meta.params[3] = "";

      this.showEndDate = !this.showEndDate;
    },
  },
};
</script>
<style scoped>
.menstruation1 {
  width: 100%;
  height: 100%;
  display: flex;
}
.editor_col_menstrual {
  text-align: center;
  width: 166.5px;
  height: 165px;
}
.editor_left_top_menstrual {
  height: 30px;
  align-items: center;
}
.editor_left_bottom_menstrual {
  margin-top: 30px;
}
.editor_center_top_menstrual {
  margin-bottom: 5px;
}
.editor_center_menstrual {
  margin-top: 10px;
  margin-bottom: 5px;
}
.editor_right_top_menstrual {
  margin-top: 40px;
  margin-bottom: 10px;
}
.editor_horizontal_menstrual {
  height: 5px;
  width: 156px;
  background-color: black;
  margin-left: 5px;
  margin-top: 25px;
}
.xeditor-input-up {
  z-index: 99999;
}

.editor_selected_menstrual {
  background-color: rgb(255, 255, 255);
  padding-left: 15px;
  cursor: pointer;
}
.editor_selected_menstrual:hover {
  background-color: rgb(230, 247, 255);
  padding-left: 15px;
  cursor: pointer;
}
.editor_date-select_menstrual {
  text-align: left;
  position: absolute;
  height: 148px;
  width: 162px;
  overflow-y: scroll;
  z-index: 99999 !important;
  box-shadow: 0 0 5px rgba(100, 100, 100, 0.6);
}
.editor_select_menstrual /deep/.ant-input-group-addon {
  padding: 0;
}
</style>
