<template>
  <div class="container">
    <div class="small-window">
      <p v-for="(val, key) in memory" :key="key">{{ key }} : {{ val }}MB</p>
      <p v-for="(val, key) in monitorResult" :key="key">
        {{ getMonitorTime(key) }} {{ key }} : {{ val }}ms
      </p>
    </div>
  </div>
</template>

<script>
import { keepDecimal } from "../assets/js/utils";
import moment from "moment";
export default {
  name: "monitor",
  data() {
    return {
      memory: {},
      monitorResult: {},
      monitorProcess: {},
      intervalId: -1,
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editorMonitor: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {
    this.monitorResult = this.editorMonitor.result;
    this.monitorProcess = this.editorMonitor.process;
    this.refreshPerformanceMemory();
  },
  methods: {
    getMonitorTime(key) {
      return moment(this.monitorProcess[key]).format("HH:mm:ss");
    },
    refreshPerformanceMemory() {
      this.memory = this.getPerformanceMemory();
      this.intervalId = setInterval(() => {
        this.memory = this.getPerformanceMemory();
      }, 1000);
    },
    getPerformanceMemory() {
      const memory = performance.memory;
      return {
        可用最大内存量: keepDecimal(memory.jsHeapSizeLimit / 1024 / 1024, 2),
        已分配内容总量: keepDecimal(memory.totalJSHeapSize / 1024 / 1024, 2),
        初始化使用内存: keepDecimal(
          this.editorMonitor.initMemory / 1024 / 1024,
          2
        ),
        正在使用内存量: keepDecimal(memory.usedJSHeapSize / 1024 / 1024, 2),
      };
    },
  },
  beforeDestroy() {
    clearInterval(this.intervalId);
  },
};
</script>

<style scoped>
.container {
  position: relative;
}

.small-window {
  position: absolute;
  pointer-events: none;
  top: 10px;
  right: 20px;
  padding: 5px;
  background-color: #f0f0f0;
  border: 1px solid #ccc;
  border-radius: 5px;
  opacity: 0.6; /* 设置透明度为 80% */
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  display: block;
}
.small-window p {
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: #333;
  background-color: #f2f2f2;
  border: 1px solid #ddd;
  text-align: justify;
  line-height: 1;
}
</style>
