const fieldAdvancedMixin = {
  data() {
    return {
      showCascadeOption: false,
      showFieldAutomation: false,
      caretPath: null, //缓存光标位置，定位打开时的文本域
      fieldSource: "field",
    };
  },
  methods: {
    /**
     *  打开文本域高级功能窗口
     * @param {*} type cascade/fieldAuto/fieldFormula/replaceRule
     * @param {*} field
     * @param {*} area side/field/interface
     * @returns
     */
    openFieldAdvanced(type, area, field) {
      if (!field) {
        field = this.instance.editor.focusElement["field"];
        if (!field) {
          this.$editor.info("没有关联文本域");
          return;
        }
      }
      this.fieldSource = area;
      this.caretPath = this.instance.editor.selection.focus;
      if (type === "cascade") {
        this.$refs.cascade.field = field;
        this.showCascadeOption = true;
        this.instance.editor.formulaMode(true, field?.name);
      } else if (type === "fieldAuto") {
        this.$refs.fieldAuto.field = field;
        this.showFieldAutomation = true;
        this.instance.editor.formulaMode(true, field?.name);
      } else if (type === "fieldFormula") {
        this.$refs.fieldFormulaEdit.fieldPropertyField = field;
        this.showFieldFormula = true;
        this.focusNode = null;
        this.instance.editor.formulaMode(true);
      } else if (type === "replaceRule") {
        this.showRulerEditorModal = true;
        this.replaceRule = field.replaceRule;
      }
    },

    cascadeSubmit(list) {
      const editor = this.instance.editor;
      this.showCascadeOption = false;
      editor.selection.setCursorPosition(this.caretPath);
      editor.updateCaret();
      if (this.fieldSource === "field") {
        this.$refs.fieldProperty.field.cascade_list = list;
        this.$refs.fieldProperty.$el.style.display = "block";
      } else {
        if (editor.focusElement["field"]) {
          editor.focusElement["field"].cascade_list = list;
        }
      }
      this.instance.editor.formulaMode(false);
    },
    cascadeCancel() {
      this.showCascadeOption = false;
      this.instance.editor.formulaMode(false);
      this.instance.editor.selection.setCursorPosition(this.caretPath);
      this.instance.editor.updateCaret();
      if (this.fieldSource === "field") {
        this.$refs.fieldProperty.$el.style.display = "block";
      }
    },

    fieldAutomationSubmit(params) {
      const editor = this.instance.editor;
      this.showFieldAutomation = false;
      editor.selection.setCursorPosition(this.caretPath);
      editor.updateCaret();
      if (this.fieldSource === "field") {
        this.$refs.fieldProperty.field.automation_list = JSON.parse(
          JSON.stringify(params)
        );
        this.$refs.fieldProperty.$el.style.display = "block";
      } else if (this.fieldSource === "checkbox") {
        this.$refs.choiceField.choicedScopeInfo.automation_list = params;
        this.$refs.choiceField.$el.style.display = "block";
      } else {
        if (editor.focusElement["field"]) {
          editor.focusElement["field"].automation_list = JSON.parse(
            JSON.stringify(params)
          );
        }
      }
    },
    fieldAutomationCancel() {
      this.showFieldAutomation = false;
      if (this.fieldSource === "field") {
        this.$refs.fieldProperty.$el.style.display = "block";
      } else if (this.fieldSource === "checkbox") {
        this.$refs.choiceField.$el.style.display = "block";
      }
    },

    submitFieldFormula() {
      const editor = this.instance.editor;
      this.showFieldFormula = false;
      const editableDiv = this.$refs.fieldFormulaEdit.$refs.formulaEdit;
      editor.selection.setCursorPosition(this.caretPath);
      editor.refreshDocument(true);
      if (this.fieldSource === "field") {
        this.$refs.fieldProperty.field.formula = editableDiv.textContent;
        this.$refs.fieldProperty.$el.style.display = "block";
      } else {
        if (editor.focusElement["field"]) {
          editor.focusElement["field"].formula = editableDiv.textContent;
        }
      }

      const doms = document.getElementsByClassName("ant-modal-wrap");
      if (doms && doms.length) {
        for (let i = 0; i < doms.length; i++) {
          const dom = doms[i];
          dom.style.pointerEvents = "inherit";
        }
      }
    },
    closeFieldFormula() {
      this.showFieldFormula = false;
      this.editor.selection.setCursorPosition(this.caretPath);
      if (this.fieldSource === "field") {
        this.$refs.fieldProperty.$el.style.display = "block";
      }

      const doms = document.getElementsByClassName("ant-modal-wrap");
      if (doms && doms.length) {
        for (let i = 0; i < doms.length; i++) {
          const dom = doms[i];
          dom.style.pointerEvents = "inherit";
        }
      }
    },
  },
};
export default fieldAdvancedMixin;
