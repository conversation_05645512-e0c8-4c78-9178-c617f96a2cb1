import Editor from "./editor/Editor";
import { ScriptType } from "./editor/Constant";
import InputAdapter from "./editor/InputAdapter";
import {
  Config , fieldDefaultFormatList ,
  font_size_config ,
  font_type_config ,
  getFontSize ,
  setFontSize ,
  system_variables ,
  vertical_page_size_config
} from "./editor/Config";
import { redo, undo } from "./editor/Command";
import createImagePlaceholder, {
  changeCanvasSize,
  compressData, deepClone,
  handleR<PERSON><PERSON>haracter,
  is<PERSON>haracter,
  isEmptyObj,
  isField,
  isPage,
  isParagraph,
  isRow,
  isTable,
  numberToChinese,
  readClipboard, removeRepeat, serializeCopy,
  uncompressData,
  uuid,
  versionDiff,
  writeClipboard,
  useRawDataByConfig, getBarcodeOrQrCodeSrc, colorToHex,isTibetan
} from "./editor/Utils";
import {
  getParagraphsText,
  handleCopyDataToParas, isBox,
  is<PERSON><PERSON><PERSON><PERSON>,
  is<PERSON><PERSON><PERSON>, is<PERSON><PERSON>,
  is<PERSON><PERSON>,
  isGroup,
  isImage,
  isImageTable, isLine, isWidget, specialCharHandle
} from "./editor/Helper";
import valid from "./editor/Validate";
import ServerEditor from "./editor/ServerEditor";
import PathUtils from "./editor/Path";
import MarkInput from "./editor/MarkInput";
import localTest from "../localtest";
import { Direction } from "./editor/Definition";
import { default_para_style } from "./editor/ContextState";
import Font from "./editor/Font";
import Cell from "./editor/Cell";
import Paragraph from "./editor/Paragraph";
import Table from "./editor/Table";
import EditorHelper from "./editor/EditorHelper";
import { medicalFormula } from "./editor/medicalFormula";
import * as echarts from 'echarts';
import Echarts from "./editor/Echarts";
// const Config = getConfig();

export default function ( // 这里不传 rawData 了，不需要了，传了也没有用
  dom: string | HTMLElement,
  config: any = {},
  source: string
) {
  const commonFunction = {
    medicalFormula,
    pathUtil: PathUtils,
    // 内置变量
    builtInVariable: {
      Direction,
      ScriptType,
      fieldDefaultFormatList,
      fontSizeList: font_size_config,
      fontTypeList: font_type_config,
      defaultParaStyle: default_para_style,
      vertical_page_size_config: vertical_page_size_config

    },
    TypeJudgment: {
      isTable, isParagraph, isRow, isPage, isCharacter, isField, isGroup, isImage, isCell, isWidget, isButton, isBoxField, isBox, isLine, isImageTable, isFraction
    },

    // 系统可替换变量
    sysVariables: system_variables,
    utils: {
      getUUID: (prefix: string) => uuid(prefix),
      valid: valid,
      getBarcodeOrQrCodeSrc,
      createImagePlaceholder,
      isEmptyObj,
      deepClone,
      versionDiff,
      numberToChinese,
      removeRepeat,
      serializeCopy,
      compressData,
      uncompressData,
      handleRareCharacter,
      fontIsEqual: Font.isEqual,
      useRawDataByConfig,
      specialCharHandle,
      colorToHex,
      isTibetan
    },
    EditorHelper,
    assignDefinedPropertiesToTable: Table.attrJudgeUndefinedAssign,
    assignDefinedPropertiesToParagraph: Paragraph.attrJudgeUndefinedAssign,
    assignDefinedPropertiesToCell: Cell.attrJudgeUndefinedAssign,
    localTest: localTest
  };

  // 如果是服务端调用
  if (source === "server") {
    const serverEditor = new ServerEditor(config); // FIXME 这里 rawData 也不传了，服务端会有问题，需要处理
    return {
      ...commonFunction,
      editor: serverEditor,
      getHttpImage: () => {
        const httpMap: any = [];
        const images = serverEditor.imageMap.get();
        for (const [key] of images.entries()) {
          if (!key) continue;
          if (key.startsWith("http")) {
            httpMap.push(key);
          }
        }
        return httpMap;
      },
      config: {
        getConfig() {
          return { ...Config, ...serverEditor.config };
        }
      }
    };
  }

  let wrap_element: HTMLElement;

  if (dom instanceof HTMLElement) {
    wrap_element = dom;
  } else if (dom.startsWith("#") || dom.startsWith(".")) {
    wrap_element = document.querySelector(dom)!;
  } else {
    throw new Error("参数格式不对");
  }

  const canvas = document.createElement("canvas");
  const input = document.createElement("textarea");
  const editorEchartsContainer = document.createElement("div");
  wrap_element.appendChild(editorEchartsContainer);
  editorEchartsContainer.style.width = "200px";
  editorEchartsContainer.style.height = "100px";
  editorEchartsContainer.style.position= "fixed";
  editorEchartsContainer.style.zIndex = "-9999999999999999"
  const myChart = echarts.init(editorEchartsContainer);
  Echarts.myChart = myChart;
  
  const div = document.createElement("div");
  div.style.position = "absolute";
  // @ts-ignore
  div.style["-webkit-user-modify"] = "read-write-plaintext-only"; // 是为了解决 contentable 为 true 的 div 回车多个换行符的问题
  div.style.padding = "5px";
  div.contentEditable = "true";
  div.style.border = "1px solid black";
  div.style.display = "none";
  div.style.top = "200px";
  input.style.pointerEvents = "none"; // 目的是为了点击input框的时候,也能穿透到 canvas 上,使绘制的光标能够在正确的位置绘制出来 去掉之后,可以打一连串的文字,然后点击前边的文字,正确定位光标后,再点击 input 框后边部分,此时光标位置不变,所以要加这个
  input.style.opacity = "0";
  input.style.position = "fixed";
  input.style.left = "0";
  input.style.width = "170px";
  input.style.height = "20px";
  input.style.overflow = "hidden";
  input.name = "editor_input_name";// 防止浏览器输入建议出来
  input.autocomplete = "off"; // 防止浏览器输入建议出来
  wrap_element.appendChild(canvas);
  div.style.left = (wrap_element.offsetWidth - 200) / 2 + "px";
  const editor = new Editor(config);
  editor.editorId = wrap_element.getAttribute("id")
  if (!editor.isMobileTerminal()) {
    wrap_element.appendChild(div);
    wrap_element.appendChild(input);
  }

  const { width, height } = changeCanvasSize(canvas, editor);
  const size = {
    width: width,
    height: height
  };

  let timer: any = null;

  let is_can = true;

  const adapter = new InputAdapter(input, editor, size);
  const markInput = new MarkInput(div);
  editor.internal.attach(canvas, input, div);

  canvas.addEventListener("mousewheel", event => adapter.wheel(event as WheelEvent), { passive: false });

  input.addEventListener("input", (event: Event) => {
    // TODO 升级 ts 不好使 好像还得装个包 @types/dom-mediacapture-record 才能给 event 指定 InputEvnet 这个没试
    if (!(event instanceof InputEvent)) return;

    // event.inputType === "insertText" 说明是正在输入英文 用的英文输入法 中文输入法的时候 event.inputType === insertCompositionText
    // 加判断让只有英文输入法的时候才走 中文输入法的时候走 compositionstart compositionupdate compositionend 这几个
    if (event.inputType === "insertText") {
      adapter.input(event as InputEvent);
    } else if (event.inputType === "insertFromPaste" && !editor.isPastedFromKeyboard) {
      adapter.handleHandwritingInput();
    }
  });

  input.addEventListener("compositionstart", event => adapter.composition_start(event as CompositionEvent));

  input.addEventListener("compositionupdate", event => adapter.composition_update(event as CompositionEvent));

  input.addEventListener("compositionend", event => adapter.composition_end(event as CompositionEvent));

  input.addEventListener("paste", event => { adapter.paste(event); });

  input.addEventListener("blur", () => editor.blur());

  input.addEventListener("focus", () => editor.focus());

  input.addEventListener("keydown", event => adapter.key_down(event as KeyboardEvent));

  input.addEventListener("keyup", event => adapter.key_up(event as KeyboardEvent));

  canvas.addEventListener("dblclick", event => adapter.dblclick(event as MouseEvent));

  canvas.addEventListener("pointerdown", event => adapter.pointer_down(event as PointerEvent));

  canvas.addEventListener("pointerup", event => adapter.pointer_up(event as PointerEvent));

  canvas.addEventListener("pointermove", event => adapter.pointer_move(event as PointerEvent));

  if (!editor.isMobileTerminal()) {

    div.addEventListener("pointerdown", event => markInput.pointer_down(event as PointerEvent));

    div.addEventListener("pointerup", event => markInput.pointer_up(event as PointerEvent, editor));

    div.addEventListener("pointermove", event => markInput.pointer_move(event as PointerEvent, editor));
  }

  canvas.ondragover = function (e) {
    // 经过canvas时需阻止默认事件，否则ondrop不生效
    e.preventDefault();
    // 添加节流 优化性能
    if (is_can) {
      is_can = false;
      // 重新设置光标坐标，解决连续外部拖拽至内部报错
      editor.selection.clearSelectedInfo();
      editor.selection.setCursorPosition(editor.selection.focus);
      editor.hold_mouse = true;
      editor.internal.point_is_selected = true;
      const { x, y } = editor.getNeedXYbyXY(e.offsetX, e.offsetY);
      editor.pointer_move(x, y);
      timer = setTimeout(() => {
        clearTimeout(timer);
        timer = null;
        is_can = true;
      }, 16);
    }
  };

  // 从 canvas 外部往文档内粘贴时走这个
  canvas.ondrop = function (e) {
    e.preventDefault();
    const resVal: any = editor.event.emit("beforeDrop", e);
    if (resVal !== "origin") {
      const drag_in_path = editor.viewPath2ModelPath(editor.internal.drag_in_path);
      editor.selection.setCursorPosition(drag_in_path);
      editor.refreshDocument();
      editor.internal.drag_in_path = [];
      resVal.customCallBack && resVal.customCallBack();
    } else {
      if (e.dataTransfer) {
        const items = e.dataTransfer.items;
        if (items) {
          // Chrome有items属性，对Chrome的单独处理
          for (let i = 0; i < items.length; i++) {
            const item = items[i];
            if (item.kind === "string") {
              item.getAsString((str) => {
                editor.internal.drag_data = str;
                const { x } = editor.getNeedXYbyXY(e.offsetX, e.offsetY);
                editor.dragSelectedArea(x, e);
              });
              break;
            }
          }
        }
      }
    }
    editor.hold_mouse = false;
    editor.internal.point_is_selected = false;
  };
  // 从外部拖拽内容到canvas不松手又拖出canvas时触发
  canvas.ondragleave = function () {
    editor.hold_mouse = false;
    editor.internal.point_is_selected = false;
  };

  return {
    editor,
    ...commonFunction,
    // 历史堆栈
    history: {
      canBeUndo: () => editor.history.canBeUndo(), // 是否可以撤销
      canBeRedo: () => editor.history.canBeRedo(), // 是否可以重做
      redo: () => { redo(editor); }, // 重做
      undo: () => { undo(editor); }, // 撤销
      getUndo: () => editor.history.getUndo(), // 获取撤销堆栈
      getRedo: () => editor.history.getRedo(), // 获取重做堆栈
      clear: () => editor.history.clear()// 清空历史堆栈
    },

    copy: () => { editor.selection.copy(); }, // 复制
    cut: () => { editor.cut(); }, // 剪切
    paste: (is_drag: boolean, is_only_text: boolean) => { editor.paste(is_drag, is_only_text); }, // 粘贴
    helper: {
      handleCopyDataToParas,
      getParagraphsText
    },
    // 系统参数获取设置
    config: {
      getFontSize,
      setFontSize,
      getConfig() {
        return { ...Config, ...editor.config };
      }
    },
    contextState: editor.contextState,
    imageMap: editor.imageMap.get(),
    Clipboard: {
      write: writeClipboard,
      read: readClipboard
    }
  };
}
