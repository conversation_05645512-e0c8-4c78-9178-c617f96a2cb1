export default class FlowRectangle {
  left: number = 0;
  top: number = 0;
  width: number = 0;
  height: number = 0;

  constructor (
    left: number = 0,
    top: number = 0,
    width: number = 0,
    height: number = 0
  ) {
    this.left = left;
    this.top = top;
    this.width = width;
    this.height = height;
  }

  get right (): number {
    return this.left + this.width;
  }

  get bottom (): number {
    return this.top + this.height;
  }

  center (): number {
    return this.left + this.width * 0.5;
  }

  contain_vertical (y: number) {
    return this.top <= y && y <= this.bottom;
  }

  contain_horizontal (x: number) {
    return this.left <= x && x <= this.right;
  }

  contain (x: number, y: number) {
    return this.contain_horizontal(x) && this.contain_vertical(y);
  }
}
