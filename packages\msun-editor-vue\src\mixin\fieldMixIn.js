import fieldSelect from "../components/field/fieldSelect.vue";
import fieldDatePicker from "../components/field/fieldDatePicker.vue";
import fieldProperty from "../components/field/fieldProperty.vue";
import fieldNumberSelect from "../components/field/fieldNumberSelect.vue";
import medicalCalcFormula from "../components/field/medicalCalcFormula.vue";
import fieldSearchBox from "../components/field/fieldSearchBox.vue";
import { keepDecimal } from "../assets/js/utils";

export const placeholderKeyMap = {
  应补钠总量男: "bnjs-male-1",
  应补氯化钠总量男: "bnjs-male-2",
  应补生理盐水男: "bnjs-male-3",
  "应补3%氯化钠男": "bnjs-male-4",
  "应补5%氯化钠男": "bnjs-male-5",
  应补钠总量女: "bnjs-female-1",
  应补氯化钠总量女: "bnjs-female-2",
  应补生理盐水女: "bnjs-female-3",
  "应补3%氯化钠女": "bnjs-female-4",
  "应补5%氯化钠女": "bnjs-female-5",
  输液量: "byjs-1",
  补液量: "byjs-2",
  总缺铁量: "btjs-1",
  需补充铁量: "btjs-2",
  电解质缺少总量: "djzbcjs-1",
  克数: "djzbcjs-2",
  "Harris-Benedict男": "jxnlxhjs-male-1",
  "Harris-Benedict女": "jxnlxhjs-female-1",
  Ccr男: "Ccr-male-1",
  Ccr女: "Ccr-female-1",
  每小时输入量: "ywsysdjs-sysdpd-1",
  每分钟滴数: "ywsysdjs-sysdpd-2",
  输液所需时间: "ywsysdjs-sysdpd-3",
  每分钟滴数1: "ywsysdjs-jmsydjsjsf-1",
  每小时输入量1: "ywsysdjs-jmsydjsjsf-2",
  体表面积男: "tbmj-male-1",
  体表面积女: "tbmj-female-1",
  体表面积小儿: "tbmj-kids-1",
  孕周: "ck-1",
  BMI: "bmi",
};
const type = {
  应补钠总量: true,
  应补氯化钠总量: true,
  应补生理盐水: true,
  "应补3%氯化钠": true,
  "应补5%氯化钠": true,
  "Harris-Benedict": true,
  Ccr: true,
  体表面积: true,
};
/**
 * 文本域相关逻辑混入
 * @type {{data(): {}, method: {}}}
 */
const fieldMixIn = {
  components: {
    fieldSelect,
    fieldDatePicker,
    fieldProperty,
    fieldNumberSelect,
    medicalCalcFormula,
    fieldSearchBox,
  },
  data() {
    return {
      source_list: [],
      fieldPanelOffset: {
        left: 0,
        top: 0,
      },
      fieldTipStyle: {
        left: 0,
        top: 0,
      },
      caretPath: null,
      fieldTipMsg: "", //文本域提示信息
      fieldTipTitle: "", //文本域提示标题
      pointerStayTimeout: -1, //鼠标停留定时器
      showFieldSelectPanel: false,
      showFieldPropModalWin: false, //元素属性面板是否展示
      replaceFormatList: [],
      showMedicalCalcFormula: false, // 展示医学计算公式
      showFieldDatePicker: false, //文本域日期选择组件展示
      // 移动端 - 日期选择器 ↓
      mobileDatePickerShow: false, // 移动端日期选择器
      currentDate: new Date(),
      // 移动端 - 日期选择器 ↓
      showFieldSearchBox: false,
      separator: 0,
      inputMode: 0, //输入方式
      formula: "", //数学公式
      stayTop: false,
      isInsert: false,
      quickSelectIndex: -1,
    };
  },
  methods: {
    handleNumberIsNull(field) {
      if (field.min_width === null) {
        field.min_width = 0;
      }
      if (field.max_width === null) {
        field.max_width = 0;
      }
      if (field.maxHeight === null) {
        field.maxHeight = 0;
      }
    },
    //修改文本域属性
    changeFieldProps(field) {
      // debugger;
      const editor = this.editor;
      const fields = editor._curFieldInfo?.fields;
      if (fields && fields.length > 1) {
        field.fields = fields;
        this.handleNumberIsNull(field);
        editor.reviseFieldAttr(field);

        editor.locatePathInField(fields[0]);
        editor.focus();
        editor.refreshDocument();
        delete editor._curFieldInfo.fields;
        if (!this.stayTop) {
          this.showFieldPropModalWin = false;
        }
        this.$editor.success("修改成功！");
        return;
      }
      if (this.fieldInfo) {
        // this.editor.insertTemplateData(this.fieldInfo.getRawData());
        this.editor.internal.field_shadow_xy = {
          x: 0,
          y: 0,
          endX: 0,
          type: "max",
          maxX: 0,
        };
        if (field.formula) {
          field.meta["is_edit"] = true;
        }
        if (this.fieldInfo.isInsert) {
          this.curClickInfo.field = this.editor.insertField(field);
        } else {
          // 处理， 如果最小宽度，最大宽度、最大高度为null，则将其自动改为0
          this.handleNumberIsNull(field);
          this.editor.reviseFieldAttr(field);
          if (this.separator != field.separator && field.multi_select) {
            let oldText = [];
            let newText;
            let text = this.editor._curFieldInfo.text;
            const map = new Map();
            this.separatorGroups.forEach((item) => {
              map.set(item.index, text.split(item.separator));
            });
            if (text) {
              oldText = map.get(this.separator);
            }
            const joinMap = new Map();
            this.separatorGroups.forEach((item) => {
              joinMap.set(item.index, oldText.join(item.separator));
            });
            newText = joinMap.get(field.separator);
            this.fieldInfo.new_text = newText;
            this.editor.updateFieldText({ fields: [this.fieldInfo] });
          }
          this.$editor.success("修改成功！");
        }
        this.editor.focus();
        if (!this.stayTop) {
          this.showFieldPropModalWin = false;
        }
      } else {
        if (this.isInsert) {
          this.curClickInfo.field = this.editor.insertField(field);
          if (!this.stayTop) {
            this.showFieldPropModalWin = false;
          }
        } else {
          this.$editor.error("操作的文本域不存在");
        }
      }
    },
    setFieldMaxOrMinWidth() {
      const field = this.editor.selection.getFocusField();
      if (field) {
        this.editor.internal.focus_drag_field = field;
        field.showPoint = true;
        this.editor.reviseFieldAttr({ field: field });
      }
    },
    openMedicalCalcFormula(placeholder = "", isDblClick) {
      // 如果是双击事件触发,就要判断该不该展示弹窗
      // 如果是其他团队调用就不用判断,可以直接展示弹窗
      if (!placeholder && !isDblClick) {
        const currentField = this.editor.selection.getFocusField();
        currentField && (placeholder = currentField.placeholder);
      }
      let selectedKey = "";
      if (type[placeholder]) {
        const fieldsMap = this.editor.getFieldsByPlaceholder(["性别", "年龄"]);

        let gender = "";
        const genderField = fieldsMap.get("性别")?.[0];
        if (genderField) {
          gender = genderField.text || "男";
          selectedKey = placeholderKeyMap[placeholder + gender] || ""; // 体表面积有男 女 小儿 三种
        } else {
          selectedKey = placeholderKeyMap[placeholder + "男"];
        }
        if (placeholder === "体表面积") {
          const age = fieldsMap.get("年龄")?.[0].text;
          if (age !== undefined && age.trim() !== "" && age <= 12) {
            // 认为 12 周岁以下的为小儿
            selectedKey = placeholderKeyMap["体表面积小儿"];
          }
        }
      } else {
        selectedKey = placeholderKeyMap[placeholder];
      }
      // 如果是双击事件,还没找到对应的 key,就直接 return,因为双击必须要求点击正确的文本域,不可能让点不正确的文本域也有个默认的展示
      // 那样点所有文本域就都弹窗了
      if (!selectedKey && isDblClick) {
        return;
      } else {
        this.showMedicalCalcFormula = true;
      }
      this.$refs.medicalCalcFormula.open(selectedKey);
    },
    medicalCalcFormulaSubmit(formData, inputValue) {
      if (
        !this.instance.editor.operableOrNot([
          "cell",
          "group",
          "field",
          "editor",
        ])
      ) {
        return this.$editor.warning("当前状态不允许插入公式");
      }
      const res = this.editor.event.emit(
        "medicalCalcFormulaSubmit",
        formData,
        inputValue
      );
      if (res === "origin") {
        const focusField = this.editor.selection.getFocusField();
        if (
          focusField &&
          (placeholderKeyMap[focusField.placeholder] ||
            type[focusField.placeholder])
        ) {
          if (focusField.placeholder === formData.dblclickPlaceholder) {
            focusField.setNewText(
              formData.dblclickPlaceholder === "孕周"
                ? formData.formulaFn(inputValue)
                : keepDecimal(formData.formulaFn(inputValue), 2)
            );
            this.editor.updateFieldText({
              fields: [focusField],
            });
          } else {
            focusField.placeholder = formData.dblclickPlaceholder;
            focusField.setNewText(
              formData.dblclickPlaceholder === "孕周"
                ? formData.formulaFn(inputValue)
                : keepDecimal(formData.formulaFn(inputValue), 2)
            );
            this.editor.updateFieldText({
              fields: [focusField],
            });
          }
        } else {
          const fieldInfo = this.initFieldDefault();
          fieldInfo.placeholder = formData.dblclickPlaceholder;
          fieldInfo.type = "label";
          fieldInfo.label_text =
            formData.dblclickPlaceholder === "孕周"
              ? formData.formulaFn(inputValue)
              : keepDecimal(formData.formulaFn(inputValue), 2);
          const field = this.editor.insertField(fieldInfo);
          field.type = "normal";
        }
      }
      // if (res === "origin") {
      //   this.editor.insertText(keepDecimal(formData.formulaFn(inputValue), 2));
      // }
      this.showMedicalCalcFormula = false;
    },
    medicalCalcFormulaCancel() {
      this.showMedicalCalcFormula = false;
    },
    //关闭修改文本域属性框
    cancelFieldProps() {
      if (this.fieldInfo) {
        this.editor.internal.field_shadow_xy = {
          x: 0,
          y: 0,
          endX: 0,
          type: "max",
          maxX: 0,
        };
      }
      this.showFieldPropModalWin = false;
      this.stayTop = false;
      this.editor.focus();
    },
    //打开文本域属性编辑窗口
    showFieldPropWindow() {
      const editor = this.editor;
      if (!editor.selection.isCollapsed) {
        // 如果是选区，则先将文本域置到选区内第一个文本域中
        // const char = editor.selection.selected_fields_chars.field_chars[0];
        // if (char) {
        //   const field = editor.getFieldById(char.field_id);
        //   editor.locatePathInField(field);
        //   this.curClickInfo.field = field;
        // }

        const fields = editor.getFieldsBySelection();
        // 选区内多个文本域就走新逻辑 只有一个文本域就走原来的逻辑 在 _curFieldInfo 里边增加 fields 属性记录新的选区内的全部文本域
        if (fields && fields.length > 1) {
          editor._curFieldInfo = { fields };
          this.showFieldPropModalWin = true;
          return;
        } else {
          const field = fields[0];
          if (field) {
            editor.locatePathInField(field);
            this.curClickInfo.field = field;
          }
        }
      }
      if (this.curClickInfo.field) {
        this.fieldInfo = this.curClickInfo.field;

        editor._curFieldInfo = this.fieldInfo;
        this.separator = this.fieldInfo.separator;
        this.inputMode = this.fieldInfo.inputMode;

        // BUS.$emit("editor_" + this.editorId, { fieldInfo: this.fieldInfo });
        // this.$refs.fieldProperty.fieldInfo = this.fieldInfo;
        this.hideFieldNeedPanel();
        this.fieldInfo.isInsert = false;
        this.isInsert = false;
        this.showFieldPropModalWin = true;
      }
    },
    //处理文本域点击事件
    handleFieldClick(isDblClick) {
      this.hideFieldNeedPanel();
      this.fieldInfo = this.curClickInfo.field;
      if (this.stayTop && this.isInsert && this.fieldInfo) {
        this.fieldInfo.isInsert = true;
      }
      if (this.stayTop && this.fieldInfo && !this.isInsert) {
        const curField = this.editor.selection.getFocusField();
        this.curFieldID = curField.id;
      }
      if (!this.fieldInfo) {
        // 点击位置不是文本域，关闭提示窗
        this.show_tip = false;
        this.showFieldSearchBox = false;
        this.isShowNumberSelect = false;
        return;
      }
      if (this.fieldInfo.type !== "number" || this.fieldInfo.formula) {
        this.isShowNumberSelect = false;
      }
      //如果是选区则不触发
      if (!this.editor.selection.isCollapsed && !isDblClick) {
        return;
      }
      if (
        (this.fieldInfo.active_type === 1 && !isDblClick) ||
        (this.fieldInfo.active_type === 0 && isDblClick)
      ) {
        return;
      }
      const group = this.curClickInfo.group;

      if (
        !this.editor.permitOperationValidation() ||
        this.fieldInfo.readonly ||
        (group && group.lock)
      ) {
        if (
          this.fieldInfo.type === "select" ||
          this.fieldInfo.type === "date"
        ) {
          this.$editor.info("当前文本域不可编辑");
        }
        return;
      }

      const { scroll_top, viewScale, caret } = this.editor;
      //使用该方法获取放大或缩小后的位置
      const focus_row = this.editor.selection.getFocusRow();
      const { x, y } = this.editor.getViewPositionByAbsolutePosition(
        caret.x,
        caret.y
      );
      this.fieldPanelOffset = {
        left: x + this.$refs.content.offsetLeft,
        top:
          y +
          focus_row.height * viewScale -
          scroll_top * viewScale +
          this.$refs.content.offsetTop,
      };
      // BUS.$emit("editor_" + this.editorId, { fieldInfo: this.fieldInfo });
      this.editor._curFieldInfo = this.fieldInfo;
      let isCellLock = false;
      let focusCell = this.curClickInfo.cell;
      if (focusCell) {
        isCellLock = focusCell.lock;
      }
      if (this.editor.formula_mode) return;
      let isSearchBox = false;
      switch (this.fieldInfo.type) {
        case "date": //日期类型
          // this.$refs.field_date_picker.fieldInfo = this.fieldInfo;
          this.datePickerPanelSeatSet(this.fieldPanelOffset, focus_row);
          setTimeout(() => {
            if (!isCellLock) {
              this.showDatePicker();
            }
          });
          return;
        case "select": //选择框类型
          // this.$refs.field_select.fieldInfo = this.fieldInfo;
          if (this.fieldInfo.source_id) {
            isSearchBox =
              this.fieldInfo.source_id.split("_")[0] === "searchBox" || false;
          }

          this.fieldPanelOffset.top -= 50; //- 50 是为了解决在底部弹出时闪烁问题
          setTimeout(() => {
            if (!this.stayTop && !isCellLock) {
              this.showFieldSelect(isSearchBox);
            }
          });
          return;
        case "number": //选择框类型
          if (this.fieldInfo.formula) return;
          // this.$refs.field_select.fieldInfo = this.fieldInfo;
          setTimeout(() => {
            if (!isCellLock) {
              this.showNumberSelectPane();
            }
          });
          return;
      }
    },
    //日期选择框位置调整
    datePickerPanelSeatSet(fieldPanelOffset, focus_row) {
      const { viewScale } = this.editor;
      const view_height = document.body.clientHeight;
      const datePanelHeight = 345;
      //获取design外层的距顶部的距离
      const top =
        this.$refs.content.getBoundingClientRect().top -
        this.$refs.content.offsetTop;
      if (fieldPanelOffset.top + top + datePanelHeight > view_height) {
        fieldPanelOffset.top =
          fieldPanelOffset.top - datePanelHeight - focus_row.height * viewScale;
      }
    },
    //打开下拉选择菜单
    showFieldSelect(isSearchBox) {
      if (isSearchBox) {
        this.quickSelectIndex = -1;
        this.showFieldSearchBox = true;
      } else {
        this.showFieldSelectPanel = true;
      }
    },
    //打开日期选择器
    showDatePicker() {
      if (this.editor.isMobileTerminal()) {
        const text = this.editor._curFieldInfo.text;
        const date = text && new Date(text);
        date && (this.currentDate = date);
        this.mobileDatePickerShow = true;
      } else {
        this.showFieldDatePicker = true;
      }
    },
    //初始化文本域默认值
    initFieldDefault() {
      const id = this.instance.utils.getUUID("field");
      return {
        id: id,
        name: "",
        type: "normal",
        tip: "",
        placeholder: "文本域",
        start_symbol: "[",
        end_symbol: "]",
        show_format: 0,
        replace_format: 0,
        number_format: 0,
        readonly: 0,
        deletable: 1,
        canBeCopied: 1,
        replaceRule: [],
        source_list: [],
        // source_id: "",
        label_text: "",
        multi_select: 0,
        ext_cell: null,
        separator: 0,
        formula: "",
        inputMode: 0,
        active_type: 0,
      };
    },
    //弹出文本域插入窗口
    insertField() {
      this.fieldInfo = this.initFieldDefault();
      this.editor._curFieldInfo = this.fieldInfo;
      // BUS.$emit("editor_" + this.editorId, { fieldInfo: this.fieldInfo });
      this.fieldInfo.isInsert = true;
      this.isInsert = true;
      this.showFieldPropModalWin = true;
    },
    //设置光标位置
    setCursorAfterFieldReplace() {
      const para_path = [...this.fieldInfo.end_para_path];
      const model_path = this.editor.paraPath2ModelPath(para_path);
      //将光标定位到文本域末尾
      this.editor.selection.setCursorPosition(model_path);
      // 设置取消选区
      this.editor.selection.clearSelectedInfo();
    },
    async replaceNumberFieldText(val) {
      if (this.fieldInfo) {
        const newVal = await this.editor.event.emit(
          "beforeFieldSelect",
          val,
          "number"
        );
        if (newVal !== "origin") {
          val = newVal;
        }
        this.fieldInfo.new_text = val;
        this.editor.updateFieldText({ fields: [this.fieldInfo] });
      }
    },
    //替换文本域内容
    async replaceFieldText(val, selectType, isNotClose, value, code) {
      if (val !== "Quit" && this.fieldInfo) {
        const newVal = await this.editor.event.emit(
          "beforeFieldSelect",
          val,
          selectType
        );
        if (newVal !== "origin") {
          val = newVal;
        }
        this.fieldInfo.new_text = val;
        this.fieldInfo.formula_value = value ? value : null;
        if (this.fieldInfo.inputMode) {
          this.editor.setAdminMode();
          this.editor.updateFieldText({ fields: [this.fieldInfo] });
          this.editor.setAdminMode(false);
        } else {
          this.editor.updateFieldText({ fields: [this.fieldInfo] });
        }
        this.editor.event.emit("fieldSelect", val, selectType, code, value);
      }
      //是否关闭面板
      if (!isNotClose || !this.fieldInfo) {
        this.hideFieldNeedPanel();
        this.editor.focus();
      }
      // this.fieldInfo.replaceText(val);
      // this.hideFieldNeedPanel();
      // this.setCursorAfterFieldReplace();
      // this.editor.update();
      // this.editor.render();
      // console.log(this.editor.selection.getFocusField());
    },
    //隐藏文本域相关下拉菜单与日期选择器
    hideFieldNeedPanel() {
      this.showFieldDatePicker = false;
      this.showFieldSelectPanel = false;
      this.isShowNumberSelect = false;
      this.showFieldSearchBox = false;
    },
    //插入页码页数insType = 1页码
    insertPageNumCount(insType = 0) {
      const field = this.initFieldDefault();
      field.type = "label";
      if (insType === 1) {
        field.name = this.instance.sysVariables.page_number;
        field.placeholder = "页";
      } else {
        field.name = this.instance.sysVariables.page_count;
        field.placeholder = "页数";
      }
      this.curClickInfo.field = this.editor.insertField(field);
    },
    //插入页脚页码信息  第 X 页 共 X 页
    insertPageInfo() {
      this.editor.insertText("第 ");
      this.insertPageNumCount(1);
      this.editor.insertText(" 页  共 ");
      this.insertPageNumCount();
      this.editor.insertText(" 页");
      // 在update接口调用时会将页脚footer_cell进行copy,copy完之后editor上对应的current_cell中的数据与copy后替换完页码的数据不一致，此时ctrl+a会报错。
      // 所以此处要重新赋值
      this.editor.current_cell =
        this.editor.focusElement.page.footer.footer_cell;
    },
    /**
     * 内容改变触发的验证
     */
    editorContentChangedValid(field) {
      if (field) {
        if (field.type === "box") {
          field.parent_box?.validValue();
        } else {
          field.validValue();
        }
      }
      this.editor.render();
    },
    /**
     * 校验提示信息展示在文本域上方
     */
    // validTipPosition(field) {
    //   field.validValue();
    //   if (field.valid_tip) {
    //     const field_para_path = field.start_para_path;
    //     const strat_mode_path = this.editor.paraPath2ModelPath(field_para_path);
    //     const start_view_path = this.editor.modelPath2viewPath(strat_mode_path);
    //     const { x, y } =
    //       this.editor.getElementAbsolutePositionByViewPath(start_view_path);
    //     //使用该方法获取放大或缩小后的位置
    //     const { x: view_x, y: view_y } =
    //       this.editor.getViewPositionByAbsolutePosition(x, y);
    //     this.fieldTipMsg = field.valid_tip;
    //     this.fieldTipTitle = "校验错误";
    //     this.$nextTick(() => {
    //       this.fieldTipStyle = {
    //         left: view_x + this.$refs.content.offsetLeft,
    //         top:
    //           view_y -
    //           this.editor.scroll_top * this.editor.viewScale +
    //           this.$refs.content.offsetTop,
    //       };
    //     });
    //   }
    // },
    //鼠标停留展示文本域提示或校验信息
    mouseoutFn() {
      setTimeout(() => {
        clearTimeout(this.pointerStayTimeout);
        this.fieldTipMsg = "";
      }, 100);
    },
    showFieldTip(x, y) {
      if (!this.editor) return;
      if (
        (!this.editor.show_tips && !this.editor.field_valid) ||
        !this.$refs.content
      )
        return;
      clearTimeout(this.pointerStayTimeout);
      setTimeout(() => {
        this.fieldTipMsg = "";
        this.fieldTipTitle = "提示";
      }, 500);
      this.pointerStayTimeout = setTimeout(() => {
        if (!this.editor) return;
        const curInfo = this.editor.getElementByPoint(x, y);
        const page = curInfo.page;
        if (
          !curInfo ||
          !curInfo.field ||
          !page ||
          page.left + page.padding_left > x ||
          x > page.left + page.width - page.padding_right
        ) {
          return;
        }
        if (
          curInfo.field &&
          (curInfo.field.required || curInfo.field.parent?.required)
        ) {
          curInfo.field.validValue();
          this.fieldTipMsg = "必选";
        }
        if (this.editor.field_valid && curInfo.field.valid) {
          // 进行校验过程 更新提示信息
          curInfo.field.validValue();
          if (curInfo.field.valid_tip) {
            this.fieldTipTitle = "数据校验错误";
            this.fieldTipMsg = curInfo.field.valid_tip;
          }
        }
        if (!this.fieldTipMsg && curInfo.field.tip) {
          this.fieldTipMsg = curInfo.field.tip;
        }

        if (this.fieldTipMsg) {
          const { x: view_x, y: view_y } =
            this.editor.getViewPositionByAbsolutePosition(x, y);
          this.$nextTick(() => {
            const outerTop = this.$refs.content.offsetTop;
            const lbLength = this.fieldTipMsg.split(/\n/).length - 1;
            const contentHeight = lbLength * 14 * 1.5;
            let top = view_y + outerTop - contentHeight + 15;
            if (view_y - contentHeight - 35 < outerTop) {
              top += contentHeight + 65;
            }
            this.fieldTipStyle = {
              left: view_x + this.$refs.content.offsetLeft,
              top,
            };
          });
        }
      }, 500);
    },
    showNumberSelectPane() {
      this.isShowNumberSelect = true;
    },
    changeStayTop(command) {
      this.stayTop = command;
    },
    altQuickInput(event) {
      if (event.altKey && !isNaN(event.key)) {
        this.quickSelectIndex = Number(event.key);
      }
    },
  },
};
export default fieldMixIn;
