<template>
  <div>
    <a-modal
      :visible="show"
      :width="width"
      :maskClosable="false"
      @ok="submit(false)"
      @cancel="cancel"
      :dialogStyle="dialogStyle"
      :footer="null"
      class="no-select"
    >
      <div slot="title" class="editor-title-x editor-title-x-header">
        <div style="font-size: 14px; display: flex; align-items: center">
          <div>自定义字段管理</div>
          <create-fields-popover
            :symbol="symbol"
            popover-title="说明"
          ></create-fields-popover>
        </div>
      </div>
      <div style="display: flex; height: 60px">
        <div class="editDivName"><i style="color: red"> * </i>字段名称</div>
        <a-input v-model="fieldName" style="width: 71%"></a-input>
        <a-button type="primary" @click="create(true)" style="margin-left: 60px"
          >新建</a-button
        >
      </div>
      <div style="display: flex; height: 480px">
        <div class="editDivLeft">
          <tree-menu
            :tree-data="treeData"
            :draggable="draggable"
            :dataSetInfo="dataSetInfo"
            @clickTree="clickTreeMenu"
            @dragstart="treeMenuDragstart"
            @handleDataSetChange="handleDataSetSelect"
          >
          </tree-menu>
        </div>
        <div class="editDivOut" @drop="onDropTag($event)">
          <div style="padding-top: 5px">
            <i style="color: red"> * </i>字段表达式编辑
          </div>
          <div style="display: flex; height: 321px; width: 447px">
            <textarea id="createTagEditor" class="editDiv" />
          </div>
        </div>
        <div class="editFormula">
          <!-- 右侧自定义字段 ↓ -->
          <div class="side-font custom-field-title">
            <div>点击操作自定义字段</div>
          </div>
          <div class="custom-field msun-editor-forms-scrollbar">
            <div
              class="custom-field-item"
              :style="{
                backgroundColor:
                  item.key === checkedCustomField.key
                    ? 'rgb(231, 247, 255)'
                    : '',
              }"
              :key="item.key"
              v-for="(item, index) in list"
              @click="handleCustomFieldClick($event, item, index)"
            >
              <div class="custom-field-item-text">
                {{ item.fieldName }}
              </div>
              <div
                class="custom-field-item-symbol"
                @click="deleteCustomFiledItem($event, item)"
              >
                ×
              </div>
            </div>
          </div>
          <!-- 右侧自定义字段 ↑ -->
          <div class="side-font">点击插入操作符</div>
          <div>
            <a-popover
              title="操作符说明"
              v-for="(item, i) in symbol"
              :key="i"
              placement="bottom"
              @mousedown="addOperator($event, item.type)"
            >
              <template slot="content">
                <span>{{ item.tip }}</span>
              </template>
              <div class="edit-symbol" :title="item.tip">
                {{ item.type }}
              </div>
            </a-popover>
          </div>
          <div class="side-font">点击插入公式</div>
          <div>
            <a-input-search
              v-model="searchValue"
              placeholder="输入关键字搜索"
              @search="onSearch"
            >
            </a-input-search>
          </div>
          <div class="edit-search msun-editor-forms-scrollbar">
            <div
              v-for="(item, i) in filterFormulaList"
              :key="i"
              @click="addHandler($event, item)"
            >
              <div class="edit-formula-div">
                <div class="edit-formula">{{ item.key }}</div>
                <a-popover title="函数介绍" placement="leftBottom">
                  <template slot="content">
                    <span style="white-space: pre-wrap">
                      {{ `${item.key} : ${item.tip}` }}
                    </span>
                  </template>
                  <div class="edit-formula-div">
                    <div class="edit-tip">{{ item.tip }}</div>
                  </div>
                </a-popover>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="display: flex; margin-top: 25px">
        字段描述：
        <a-textarea class="edit-textarea" v-model="textarea"></a-textarea>
        <a-button
          type="primary"
          style="margin-left: 80px"
          @click="saveChangedCustomField($event)"
          >保存</a-button
        >
      </div>
    </a-modal>
  </div>
</template>
<script>
import "codemirror/lib/codemirror.css";
import "codemirror/addon/hint/show-hint.css";
import "codemirror/addon/display/fullscreen.css";
import codeMirror from "codemirror/lib/codemirror";
import "codemirror/addon/hint/show-hint.js";
import "codemirror/addon/edit/matchbrackets.js";
import "codemirror/mode/javascript/javascript";
import createFieldsPopover from "./createFieldsPopover.vue";
import TreeMenu from "./treeMenu.vue";
export default {
  name: "createFields",
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    draggable: {
      type: Boolean,
      default: true,
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    dataSetInfo: {
      type: Object,
      default: () => {},
    },
    allDataSetTree: {
      type: Object,
      default: () => {},
    },
    customFieldList: {
      type: Array,
      default: () => [],
    },
  },
  components: { TreeMenu, createFieldsPopover },
  data() {
    return {
      popoverTitle: "说明",
      checkedCustomField: {},
      list: [], // 右侧自定义字段列表
      searchValue: "",
      width: 1050,
      fieldName: "custom",
      textarea: "",
      dialogStyle: {
        top: "80px",
      },
      symbol: [
        { type: "+", tip: "加法运算，用于两数相加" },
        { type: "-", tip: "减法运算，用于两数相减" },
        { type: "*", tip: "乘法运算，用于两数相乘" },
        { type: "/", tip: "除法运算，用于两数相除" },
        { type: ",", tip: "英文逗号，多个参数之间间隔使用" },
        { type: "%", tip: "取模运算，用于计算一个数除以另一个数后的余数" },
        {
          type: "&",
          tip: "字符串连接，用于将前后两个字符串相连，返回新字符串",
        },
        {
          type: ">",
          tip: "比较运算：大于。判断前后两个数的大小，返回true|false",
        },
        { type: "<", tip: "比较运算：小于。判断前后两数大小，返回true|false" },
        { type: "=", tip: "比较运算:等于。两数相等返回true,不等返回false" },
        { type: "<>", tip: "比较运算：不等于。不等于返回true, 等于返回false" },
      ],
      formula: [
        {
          key: "AND(...x)",
          type: "boolean",
          paramType: [],
          tip: "如果所有参数都为true，返回true;如果至少存在一个false就返回false",
        },
        {
          key: "OR(...x)",
          type: "boolean",
          paramType: [],
          tip: "如果所有参数都为false，返回false;如果至少存在一个true就返回true",
        },
        {
          key: "ABS(x)",
          type: "number",
          paramType: ["number"],
          tip: "绝对值 例如：ABS(-1) = 1",
        },
        {
          key: "AVERAGE(x, y...)",
          type: "number",
          paramType: ["number"],
          tip: "平均值,求所有传入参数的平均值  AVERAGE(number1, [number2], ...)",
        },
        {
          key: "FLOAT(x)",
          type: "number",
          paramType: ["number|string"],
          tip: "把x转为浮点数",
        },
        {
          key: "INT(x)",
          type: "number",
          paramType: ["number|string"],
          tip: "把x转为整数",
        },
        {
          key: "POWER(x, y)",
          type: "number",
          paramType: ["number"],
          tip: "求x的y次幂",
        },
        {
          key: "PI()",
          type: "number",
          paramType: [],
          tip: "返回圆周率，精确到小数点后15位",
        },
        {
          key: "PRODUCT(x, y...)",
          type: "number",
          paramType: ["number"],
          tip: "返回所有参数的乘积",
        },
        {
          key: "TRUNC(x, y)",
          type: "number",
          paramType: ["number"],
          tip: "返回数字x 取保留y位小数的结果（多余的直接舍弃，不会四舍五入）",
        },
        {
          key: "ROUND(x, y)",
          type: "number",
          paramType: ["number"],
          tip: "返回数字x 四舍五入保留y位小数的结果",
        },
        {
          key: "ROUNDUP(x)",
          type: "number",
          paramType: ["number"],
          tip: "返回x向上舍入的结果，例如ROUNDUP(3.2) = 4",
        },
        {
          key: "ROUNDDOWN(x)",
          type: "number",
          paramType: ["number"],
          tip: "返回x向下舍入的结果， 例如ROUNDDOWN(3.8) = 3",
        },
        {
          key: "SUM(x, y...)",
          type: "number",
          paramType: ["number"],
          tip: "返回传入所有参数的和,参数如果在表格中自增，那么就是每一行的和",
        },
        {
          key: "MOD(x, y)",
          type: "number",
          paramType: ["number"],
          tip: "返回x 对 y取余的值",
        },
        {
          key: "CONTAINS(x, y)",
          type: "boolean",
          paramType: ["string"],
          tip: "判断字符串x中是否包含片段y, 返回true|false",
        },
        {
          key: "INCLUDESALL(x, y...)",
          type: "boolean",
          paramType: ["string"],
          tip: "可以传入多个y,判断字符串x中是否包含所有后边的字符串y, 返回true|false",
        },
        {
          key: "INCLUDESONE(x, y...)",
          type: "boolean",
          paramType: ["string"],
          tip: "可以传入多个y,判断字符串x中是否包含某一个字符串y, 返回true|false",
        },
        {
          key: "LEFT(s, n)",
          type: "string",
          paramType: ["string", "number"],
          tip: '对字符串s从左侧开始截取n位，例如: LEFT("食品有限公司", 2) => "食品"',
        },
        {
          key: "RIGHT(s, n)",
          type: "string",
          paramType: ["string", "number"],
          tip: '对字符串s从右侧开始截取n位，例如: LEFT("食品有限公司", 2) => "公司"',
        },
        {
          key: "LEN(s)",
          type: "string",
          paramType: ["string"],
          tip: "返回字符串s的长度",
        },
        {
          key: "REPLACE(s, r, i)",
          type: "string",
          paramType: ["string", "string", "string"],
          tip: "将字符串s中的第一个r 替换成i",
        },
        {
          key: "REPLACEALL(s, r, i)",
          type: "string",
          paramType: ["string", "string", "string"],
          tip: "将字符串s中所有的r 都替换成i",
        },
        {
          key: "SPLIT(s, k, i)",
          type: "string",
          paramType: ["string", "string", "number"],
          tip: "将字符串s按照k拆分，然后返回第i个值(i从1开始计数)",
        },
        {
          key: "JOIN(s, k)",
          type: "string",
          paramType: ["array", "string"],
          tip: "将s内的元素按照k拼接起来",
        },
        {
          key: "NOW()",
          type: "date",
          paramType: null,
          tip: "返回当前的时间，注意拿到的值为日期对象",
        },
        {
          key: 'DATEDIF(d1, d2, "D")',
          type: "number",
          paramType: ["date", "date", "string"],
          tip: `计算d1和d2之间的相差日期，默认为天数差。
              可以提供第三个参数来决定时间差单位
              · "Y" : 年份差
              · "M" : 月份差
              · "D" : 天数差
              · "H" : 小时差
              · "MI": 分钟差
              · "S" : 秒数差
              · "MS": 毫秒差

              注意：计算年、月份差时，此函数未考虑具体天数影响。如 2023-12-31 至 2024-01-01 之间的年份差为1， 月份差为1`,
        },
        {
          key: 'FINDINTERVALDATE(d1, d2, "{m}个月{d}天")',
          type: "string",
          paramType: ["date", "date", "string"],
          tip: `计算日期 d2 减去 d1 得到相差的日期，默认返回 3个月15天 格式的字符串。
              可以提供第三个参数来决定时间差的显示格式,关键字需要用{} 包裹。关键信息如下：
              · y 代表差几年
              · m 代表差几个月
              · d代表差几天
              · h代表差几小时
              · i代表差几分钟
              · s 代表差几秒

              除了{}包裹的变量外，还可以组合汉字和其他特殊字符等生成结果。例如：相差{y}年{m}个月{dd}天——{hh}H`,
        },
        {
          key: "YEAR(d)",
          type: "string",
          paramType: ["date"],
          tip: "返回日期d的年",
        },
        {
          key: "MONTH(d)",
          type: "string",
          paramType: ["date"],
          tip: "返回日期d的月",
        },
        {
          key: "DAY(d)",
          type: "string",
          paramType: ["date"],
          tip: "返回日期d的日",
        },
        {
          key: "HOUR(d)",
          type: "string",
          paramType: ["date"],
          tip: "返回日期d的时",
        },
        {
          key: "MINUTE(d)",
          type: "string",
          paramType: ["date"],
          tip: "返回日期d的分钟",
        },
        {
          key: "SECOND(d)",
          type: "string",
          paramType: ["date"],
          tip: "返回日期d的秒",
        },
        // {
        //   key: 'MILLISECOND(d)',
        //   type: 'string',
        //   tip: '返回日期d的毫秒'
        // },
        {
          key: "LTRIM(s)",
          type: "string",
          paramType: ["string"],
          tip: "去除字符串s左侧的空格",
        },
        {
          key: "RTRIM(s)",
          type: "string",
          paramType: ["string"],
          tip: "去除字符串s右侧的空格",
        },
        {
          key: "TRIM(s)",
          type: "string",
          paramType: ["string"],
          tip: "去除字符串两侧的空格",
        },
        {
          key: "IF(rule, res1, res2)",
          type: 1, // 数值的返回此索引参数的类型
          paramType: [],
          tip: "判断 rule 条件计算结果的真假。真就返回 res1；假就返回 res2 ",
        },
        {
          key: "ISNULL(s)",
          type: "boolean",
          paramType: ["VARIABLE"],
          tip: "判断 s 是否为null，为空返回true，否则返回false。",
        },
      ],
      filterFormulaList: [],
    };
  },
  watch: {
    show(val) {
      if (val) {
        this.filterFormulaList = this.formula;
        this.searchValue = "";
        this.$nextTick(() => {
          const doms = this.$el.getElementsByClassName("ant-modal-wrap");
          const modalDoms = this.$el.getElementsByClassName("ant-modal");
          if (modalDoms && modalDoms.length) {
            for (let i = 0; i < modalDoms.length; i++) {
              const modalDom = modalDoms[i];
              modalDom.style.top = "20px";
            }
          }
          if (doms && doms.length) {
            for (let i = 0; i < doms.length; i++) {
              const dom = doms[i];
              dom.style.pointerEvents = "none";
            }
          }
          // 如果没有editor，要初始化editor
          if (!this.editor) {
            this.initEditor();
          } else {
            //置空
            this.editor.setValue("");
          }
          // 62 创建字段 创建 list ↓
          this.list = [];
          const editor = this.instance.editor;
          if (editor.document_meta && editor.document_meta.customField) {
            for (let i = 0; i < editor.document_meta.customField.length; i++) {
              const c = editor.document_meta.customField[i];
              c.key = i + "" + Date.now();
              this.list.push({ ...c });
            }
          }
          this.checkedCustomField = this.list[this.list.length - 1] || {};
          if (this.checkedCustomField.fieldName) {
            this.updateByField(this.checkedCustomField);
          }
          // 62 创建字段 创建 list ↑
        });
        if (this.customFieldList.length) {
          const maxItem = this.customFieldList.reduce((max, current) => {
            const currentNum = parseInt(
              (current.fieldName.match(/\d+/) || [0])[0],
              10
            );
            const maxNum = parseInt((max.fieldName.match(/\d+/) || [0])[0], 10);
            return currentNum > maxNum ? current : max;
          }, this.customFieldList[0]);
          if (maxItem && maxItem.fieldName) {
            const maxNum = parseInt(
              (maxItem.fieldName.match(/\d+/) || [0])[0],
              10
            );
            this.fieldName = `custom${maxNum + 1}`;
          }
        }
        this.textarea = "";
      }
    },
  },
  created() {},
  methods: {
    isNumber(value) {
      return !isNaN(parseFloat(value)) && isFinite(value);
    },
    create() {
      this.checkedCustomField = {};
      this.editor.setValue("");
      this.textarea = "";
      let max = 0;
      if (this.list.length) {
        for (let i = 0; i < this.list.length; i++) {
          const field = this.list[i];
          if (field.fieldName.startsWith("custom")) {
            let num = "";
            for (let j = 6; j < field.fieldName.length + 1; j++) {
              if (j === field.fieldName.length) {
                num *= 1;
                if (num > max) {
                  max = num;
                }
                continue;
              }
              if (this.isNumber(field.fieldName[j])) {
                num += field.fieldName[j];
              } else {
                // 必须是数字 如果不是数字就没必要循环了
                num *= 1;
                if (num > max) {
                  max = num;
                }
                break;
              }
            }
          }
        }
      }
      this.fieldName = "custom" + ++max;
    },

    updateList() {
      // 根据最新的 editor.document_meta.customField 更新 list
      const editor = this.instance.editor;
      this.list = [];
      for (let i = 0; i < editor.document_meta.customField.length; i++) {
        const c = editor.document_meta.customField[i];
        this.list.push({ ...c });
      }
    },

    // 根据传入的字段 更新展示的三个地方 字段名称 字段表达式编辑 还有字段描述
    updateByField(item) {
      this.checkedCustomField = item;
      this.textarea = item.description || "";
      this.fieldName = item.fieldName || "";
      this.editor.setValue(item.formula || "");
      this.editor.focus();
    },
    handleCustomFieldClick(ev, item) {
      ev.preventDefault();
      this.updateByField(item);
    },
    deleteCustomFiledItem(ev, item) {
      ev.preventDefault();
      ev.stopPropagation();
      if (item.key === this.checkedCustomField.key) {
        this.updateByField({});
      }

      this.$emit("deleteCustomFiledItem", item);
    },
    saveChangedCustomField(ev) {
      ev.preventDefault();
      if (this.checkedCustomField.key) {
        // 说明是保存 因为新建的话 我会主动将 this.checkedCustomField 置为空对象
        const text = this.editor.getValue();
        const param = {
          fieldName: this.fieldName,
          formula: text,
          description: this.textarea,
          key: this.checkedCustomField.key,
        };
        this.$emit("saveChangedCustomField", param, { type: "modify" });
      } else {
        const text = this.editor.getValue();
        const param = {
          fieldName: this.fieldName,
          formula: text,
          description: this.textarea,
          key: Date.now(),
        };
        this.checkedCustomField = param;
        this.$emit("saveChangedCustomField", param, { type: "create" });
      }
    },
    onSearch(searchValue) {
      searchValue = searchValue.toUpperCase().trim();
      if (searchValue) {
        this.filterFormulaList = this.formula.filter((ite) =>
          ite.key.includes(searchValue)
        );
      } else {
        this.filterFormulaList = this.formula;
      }
    },
    submit(multiple) {
      let text = this.editor.getValue();
      const param = {
        fieldName: this.fieldName,
        formula: text,
        description: this.textarea,
      };
      this.$emit("submit", param, multiple);
      this.fieldName = "";
      this.editor.setValue("");
    },
    cancel() {
      this.$emit("cancel");
    },
    //初始化textArea框
    initEditor() {
      const ele = document.getElementById("createTagEditor");
      this.editor = codeMirror.fromTextArea(ele, {
        // line: true,
        styleActiveLine: true,
        indentWithTabs: true,
        autofocus: true,
        lineWrapping: true,
        completeSingle: false,
        // theme: 'monokai',
        mode: "text/javascript", // 高亮模式，json高亮
        tabSize: 1,
        lineNumbers: true, // 行号
        cursorHeight: 0.8, // 光标高度，默认1
        autoCloseBrackets: true, // 自动闭合括号
        matchBrackets: true, // 括号匹配
        // lineWrapping: 'wrap', // 文字过长是 wrap  还是scroll， 默认scroll
        showCursorWhenSelecting: true, // 选择内容的时候是否显示光标
        smartIndent: true, // 智能缩进
        // completeSingle: false, // 当匹配只有一项时，是否自动补全
        // extraKeys: { 'Ctrl': 'autocomplete' },
        hintOptions: {
          // 自定义提示
          completeSingle: false,
          hint: this.hintHandler.bind(this),
        },
      });
      this.editor.on("cursorActivity", () => {
        // 调用显示提示
        this.editor.showHint();
      });
    },
    hintHandler(cmInstance) {
      const cursor = cmInstance.getCursor();
      const token = cmInstance.getTokenAt(cursor);
      let str = token.string.toUpperCase();
      if (!/^[a-z]+$/gi.test(str)) {
        return false;
      }
      const list = [];
      this.formula.forEach((item) => {
        if (item.key.includes(str)) {
          list.push(item.key);
        }
      });

      return {
        list,
        from: {
          ch: token.start,
          line: cursor.line,
        },
        to: {
          ch: token.end,
          line: cursor.line,
        },
      };
    },
    addHandler(ev, handler) {
      // 点击函数，添加到编辑区
      ev.preventDefault();
      const str = handler.key; //  .replace(/\((.*)?\)/g, '()')
      const splitStr = str.replace(/\([^)]*\)/g, "()");
      const pos = this.editor.getCursor();
      const curPos = {
        line: pos.line,
        ch: pos.ch,
      };
      this.editor.replaceRange(splitStr, curPos, curPos);
      curPos.ch += splitStr.length - 1;
      this.editor.setCursor(curPos);
      this.editor.focus();
    },
    addOperator(ev, key) {
      // 点击添加操作符
      ev.preventDefault();
      const pos = this.editor.getCursor();
      const curPos = {
        line: pos.line,
        ch: pos.ch,
      };
      this.editor.replaceRange(key, curPos, curPos);
      curPos.ch += key.length;
      this.editor.setCursor(curPos);
    },
    // 拖拽字段放入编辑区
    onDropTag(ev) {
      ev.preventDefault();
      const sourceKey = ev.dataTransfer.getData("text");
      const str = `[${this.dragInfo.data.code}]`;
      this.insertText(str, sourceKey.length);
    },
    insertText(text, minus = 0) {
      const pos = this.editor.getCursor();
      const curPos = {
        line: pos.line,
        ch: pos.ch,
      };
      const startPos = {
        line: pos.line,
        ch: pos.ch - (minus || 0),
      };
      this.editor.replaceRange(text, startPos, curPos);
      startPos.ch += text.length;
      this.editor.setCursor(startPos);
    },
    // 点击的是字段 执行的逻辑是 往 字段表达式编辑 中添加
    clickTreeMenu(data, key, arr) {
      const str = `[${data.code}]`;
      const splitStr = str.replace(/\([^)]*\)/g, "()");
      const pos = this.editor.getCursor();
      const curPos = {
        line: pos.line,
        ch: pos.ch,
      };
      this.editor.replaceRange(splitStr, curPos, curPos);
      curPos.ch += splitStr.length;
      this.editor.setCursor(curPos);
      this.editor.focus();
    },
    treeMenuDragstart(data, key, arr) {
      this.dragInfo = {
        data,
        key,
        arr,
      };
    },
    handleDataSetSelect(value) {
      this.$emit("handleDataSetChange", value);
    },
  },
};
</script>
<style>
.ant-popover {
  z-index: 9999999 !important;
}

.CodeMirror {
  font-size: 16px;
  font-weight: bold;
  width: 440px;
  height: 450px;
}

.CodeMirror {
  font-family: "幼圆";
}
.ant-message {
  z-index: 99999999999999 !important;
}
</style>
<style scoped lang="less">
.custom-field-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.custom-field {
  height: 80px;
  margin-bottom: 2px;
  overflow-y: auto;
  .custom-field-item {
    height: 28px;
    line-height: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px;
    padding: 0 8px;
    cursor: pointer;
    .custom-field-item-symbol {
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      border-radius: 50%;
    }
    .custom-field-item-symbol:hover {
      background-color: rgb(200, 225, 255);
    }
  }
  .custom-field-item:hover {
    background-color: rgb(231, 247, 255);
  }
}

.no-select {
  -webkit-user-select: none;
  /* Safari */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* IE/Edge */
  user-select: none;
  /* 标准语法 */
}

.editDiv {
  margin-top: 10px;
  margin-bottom: 10px;
  height: 300px;
  width: 400px;
  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.1);
  resize: none;
  border: none;
  font-size: 20px;
  font-weight: bold;
  padding: 10px;
  overflow: hidden;

  &:focus {
    outline: none;
  }
}

.editDivLeft {
  height: 480px;
  width: 250px;
  border: 1px solid rgb(221, 221, 221);
  border-radius: 5px;
  margin-right: 10px;
}

.editDivOut {
  border: 1px solid rgb(221, 221, 221);
  border-radius: 5px;
  width: 532px;
}

.editDivName {
  margin-top: 5px;
  width: 80px;
}

.editFormula {
  width: 200px;
  height: 480px;
  border: 1px solid rgb(221, 221, 221);
  border-radius: 5px;
  margin-left: 10px;
  padding: 0 5px;
}

.side-font {
  font-size: 11px;
  height: 26px;
  line-height: 26px;
  color: #333;
  opacity: 0.7;
  margin-top: 5px;
}

.edit-symbol {
  min-width: 20px;
  height: 26px;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 0 3px;
  line-height: 20px;
  text-align: center;
  display: inline-block;
  cursor: pointer;
  margin-right: 5px;
  font-size: 18px;
  white-space: nowrap;
  color: #3587e0;
  font-weight: 700;
  margin-top: 5px;
}

.edit-search {
  overflow-y: scroll;
  width: 192px;
  height: 210px;
}

/* 全局自定义的滚动条样式（基于webkit-scrollbar，注意兼容性 / 不会像原生的一样，可以自定隐藏） */
.msun-editor-forms-scrollbar {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d3e3fd;
    border-radius: 6px;
  }

  /* 鼠标悬停时滑块样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #a8c7fa;
  }

  &::-webkit-scrollbar-track {
    background-color: white;
    -webkit-box-shadow: inset 0 0 4px rgba(100, 100, 100, 0.01);
  }
}

.edit-tip {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  line-height: 16px;
  color: #999;
}

.edit-formula {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 16px;
  line-height: 20px;
  color: #096dd9;
}

.edit-formula-div {
  padding: 5px 0;
  cursor: "pointer";
}

.edit-formula-div:hover {
  padding: 5px 0;
  cursor: pointer;
  background-color: rgb(247, 247, 247);
}

.edit-textarea {
  width: 723px;
  height: 20px;
}
</style>
