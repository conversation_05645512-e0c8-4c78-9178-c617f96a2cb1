// 以下是插入文本域以及修改文本域方法传入的参数 都是同一个对象内的各个属性：
//   field: undefined, // 只有修改文本域属性的时候 该属性才有内容 是文本域实例 默认是 undefined 表示没有内容 就是没有的意思
//   fields: [], // 只有修改文本域属性的时候 该属性才有内容 是文本域实例数组 默认是 [] 表示没有内容 就是没有的意思 field 和 fields 都只是调用 reviseFieldAttr 方法的时候 传入的参数 表示我要修改这些文本域的属性 其他地方用不到
//   id: "这id", // 文本域的 id 整篇文档唯一 不能重复 insertField 可以不传，插入的时候会自动生成 如果插入的时候传入 id 会判断是否存在 如果存在会删除 id 重新生成
//   name: "name", // 文本域的 name 值 多个文本域的 name 值可以重复 可以通过 editor.getFieldsByName 拿到所有的同 name 的文本域 设置复选框 自定义复选框 成组 时 name 值要保持一致 就是一组的 可以设置这一组内是单选 多选等
//   tip: "", // 文本域的提示信息 默认是空字符 当鼠标悬停时显示的内容
//   placeholder: "文本域", // 文本域的 placeholder 值 当没有内容的时候显示该内容 字体为灰色
//   分段结束
//   type: "label", // 文本域的类型 默认是 normal 类型 有这几种类型：
//   // select 类型 是可以下拉列表的
//   //  label 类型 是标签类型 可以设置标签内容 就是 label_text 的值 该类型的文本域 光标点击在该标签上就会整体选中 是不能够在文档中编辑标签内容的 (本身就不能编辑 不需要设置 只读属性) 只能通过接口或者文本域属性弹窗修改
//   //  normal 类型 是常规类型 可以输入内容
//   //  date 类型 是日期类型 可以设置弹出日期面板(可以单击或者双击弹出)
//   //  number 类型 是数字类型  可以设置保留小数位数 激活模式等 当点击时 会有个数字面板弹出
//   //  anchor 类型 是锚点类型 该类型是不会打印出来的也不会在文档上占位置 只是显示一个可以看见的小三角 当有些内容必须存在于文档上 但是又不想看见 也不打印 就可以使用该类型
//   start_symbol: "[", // 文本域的开始符号 默认是 [
//   end_symbol: "]", // 文本域的结束符号 默认是 ]
//   readonly: 0, // 文本域的只读属性 默认是 0 表示可读可写 1 表示只读
//   deletable: 1, // 文本域的删除属性 默认是 1 表示可删除 0 表示不可删除
//   canBeCopied: 1, // 文本域的复制属性 默认是 1 表示可复制 0 表示不可复制
//   分段结束
//   replaceRule: [], // 文本域的展示规则 跟级联和自动化 都是文本域的高级功能 是个对象数组 每个对象就是一条展示规则
//   // 展示规则的格式如下：
//   // {
//   //    custom: "", // 值为 'custom' 是自定义匹配规则的的意思 没有这个属性 就是 rule 的匹配规则
//   //    flags: "g", // 值为 'g' 是全局的意思 没有 flags 这个属性 就是首项的意思
//   //    replace: "", // 是将满足 rule 和 flags 的文本域内容 替换为 replace 的内容
//   //    rule: "", // 值是设置的匹配规则 正则表达式
//   // }
//   // 会根据 rule 和 flags 用 new RegExp 生成一个正则表达式，比如：new RegExp(regOb.rule, regOb.flags || "");
//   // 会根据这个正则表达式 去匹配文本域的内容 如果匹配到了 就替换为 replace 的内容
//   // 计算公式的优先级是比展示规则高的 所以拿到计算结果以后 展示规则还能根据计算后的结果 进行一些规则的匹配 进行展示
//   // 展示规则的优先级又比 整数类型 里边的取整的优先级 高 因为先设置了展示规则是生效的 并没有拿取整的结果去用展示规则
//   // 数字类型的取整 是四舍五入的
//   分段结束
//   max_width: 0, // 文本域的最大宽度 默认是 0 没有设置 就是没有限制 当文本域内容超过该宽度时 没个字的宽度就变小了 确保整体内容不超出该宽度
//   forbidden: {
//     // 该属性是 type 为 date 类型的时候才有意义
//     start: null, // 开始时间 默认是 null 没有设置 就是没有限制 设置的时候是字符串类型 比如: "2025-03-01" 意思就是该日期之前的日期都是禁用的 当前日期是可以选择的
//     end: null, // 结束时间 默认是 null 没有设置 就是没有限制 设置的时候是字符串类型 比如: "2025-03-30" 意思就是该日期之后的日期都是禁用的 当前日期是可以选择的
//   },
//   min_width: 0, // 文本域的最小宽度 默认是 0 没有设置 就是没有限制 如果要修改文本域的固定宽度 就是设置 max_width 和 min_width 的值一样
//   maxHeight: 0, // 文本域的最大高度 默认是 0 没有设置 就是没有限制
//   分段结束
//   display_type: "normal", // 是文本域边框的显示类型 默认是 normal 类型
//   //  normal 类型 是常规类型 可以输入内容 |
//   //  input 类型 绘制一个矩形边框将该文本域包裹起来了 但是不能打印出来这个矩形边框
//   //  line 类型 是在该文本域下方绘制一条横线 也能够打印出来这条横线 跟填空题的横线是一样的 但是该文本域的文本内容是能够编辑的 跟填空题不一样 填空题的文本内容是不可编辑的 是固定的
//   //  printInput 类型 是打印输入类型 可以输入内容 |
//   分段结束
//   cascade_list: [], // 级联功能 文本域的级联列表 默认是 [] 控制级联文本域的显示隐藏 和 文本域的展示规则 文本域自动化 一样都属于文本域的高级功能
//   // 是个对象数组
//   // {show_field_names: [], text: ""} show_field_names 是个数组 存储了要显示的文本域的 name 值
//   //  意思是：当该文本域的内容为 text 时，级联列表中的文本域 show_field_names 会显示出来
//   // 如果 文本域的内容 跟 text 不相等时，级联列表中的文本域 show_field_names 会隐藏起来 这就是文本域的级联功能
//   分段结束
//   automation_list: [], // 自动化功能 文本域的自动化列表 是个对象数组 默认是 [] 每一个对象都是一条自动化规则 格式如下：
//   //   {
//   //     action: "关联", // 是要执行的一些操作 有以下几个操作：
//   // 关联:这个动作会影响到其他文本域的文本内容、
//   // 弹窗:这个动作会弹出一个弹窗、设置为 弹窗后 changeFields 就是个字符串数组 内容为弹窗内容
//   // 提示:这个动作会提示一个信息、设置为 提示后 changeFields 就是个字符串数组 内容为提示内容 跟设置弹窗一样 只不过展示效果不一样
//   // 联动: 这个文本域显示的内容会跟联动的文本域显示的内容一样 该文本域内容发生变化 联动的文本域内容也会发生变化
//   // 隐藏:这个动作会影响到其他文本域的隐藏、一旦隐藏以后就不会再显示出来了 除非符合下一条自动化让其显示的规则
//   // 显示:这个动作会影响到其他文本域的显示、一旦显示以后就不再隐藏了  除非符合下一条自动化让其隐藏的规则 这是自动化的显示隐藏跟级联功能不一样的地方
//   //     changeFields: [], // 是个对象数组 每个对象的格式如下：
//   //     {
//   //       changeText:"", // action 这个动作影响到的文本域的文本内容 比如当 action 为 关联 时，changeText 为必填项 当 action 为 提示 时，changeText 为必填项 当 action 为 联动 时，changeText 为必填项 当 action 为 隐藏和显示时 ，changeText 就不用填写
//   //       key:"", // 存储的是 action 这个动作影响到的文本域的 id 值
//   //       name:"",  // 存储的是 action 这个动作影响到的文本域的 name 值
//   //       type:"" // 存储的是 action 这个动作影响到的文本域的 type 值
//   //     }
//   //    但是当 action 为 弹窗和提示时，changeFields 就不是个对象数组了 而是个字符串数组 记录弹窗显示的文本内容和提示的文本域内容
//   //     condition: "", // 是条件 有以下几种条件：'='：等于、'≠'：不等于、'>'：大于、'<'：小于、'>='：大于等于、'<='：小于等于、'>=<='：大于等于并且小于等于在两个值之间、'><='：大于并且小于等于、'>=<'：大于等于并且小于、'>=<='：大于并且小于
//   //     content: {
//   //        text:"" // 当这个 text 的值满足 condition 的条件时，action 这个动作就会执行 如果是 大于等于并且小于等于这种两个值之间的 该 text 用逗号隔开了两个值
//   //     }
//   //     key: ""
//   //   }
//   分段结束
//   show_format: 0, // 文本域的显示格式 默认是 0 表示不显示 1 表示显示
//   align: "left", // 文本域的对齐方式 默认是 left 还有 center 和 right 三种 是该文本域的文本内容相对于该文本域的显示位置
//   replace_format: 0, // 当 type 为 date 类型的时候 该属性才有内容 表示该文本域的显示格式 默认是 0 以下是各种值的含义：
//   /* 0: "YYYY-MM-DD",
//       1: "YYYY-MM-DD HH:mm",
//       2: "YYYY-MM-DD HH:mm:ss",
//       3: "YYYY年MM月DD日",
//       4: "YYYY年MM月DD日 HH时",
//       5: "YYYY年MM月DD日 HH时mm分",
//       6: "MM-DD",
//       7: "MM月DD日",
//       8: "HH:mm",
//       9: "HH时mm分",
//       10: "YYYY年MM月DD日 HH时mm分ss秒",
//       11："MM-DD HH:mm" */
//   number_format: 0, // 当 type 为 number 类型的时候 该属性才有内容 表示该文本域的显示格式 默认是 0 以下是各种值的含义：
//   /* 0:无限制
//     1：整数形式
//     2：保留一位小数
//     3：保留两位小数
//     4：保留三位小数
//     5：保留四位小数
//     */
//   valid: 0, // 是否进行校验 默认是 0 表示不进行校验 1 表示进行校验
//   分段结束
//   label_text: "", // 当 type 为 label 类型的时候 该属性才有内容 表示该文本域的标签内容 默认是空 就是没有的意思
//   source_id: "", // 数据源id 默认是空 就是没有的意思 只有 type 为 select 类型的时候 才有这个属性 表示该文本域的数据源id 通过该id 去数据源中获取数据 然后去匹配该文本域的内容 如果匹配到了 就显示该文本域的内容 否则显示该文本域的默认值
//   multi_select: 0, // type 为 select 类型的时候 该属性才有内容 默认是 0 表示单选 1 表示多选
//   meta: {}, // 扩展属性 默认是空 就是没有的意思 可以设置一些扩展属性 比如：{
//   //   key: "value",
//   // } 等等 任意添加
//   inputMode: 0, // 是 type 为 select 类型的时候 该属性才有内容 默认是 0 表示该文本域可以输入内容 1 表示只能选择下拉项不能输入内容
//   separator: 0, // 是 type 为 select 类型的时候 并且 multi_select 为 1 的时候 该属性才有内容 默认是 0 表示该文本域的选项之间用逗号隔开 1 表示该文本域的选项之间用换行符隔开
//   分段结束
//   formula: "", // 计算公式 字符串 默认为空 就是没有的意思
//   // 该计算公式可以使用的 连接符号有：
//   // +：加法运算符
//   // -：减法运算符
//   // ×：乘法运算符
//   // ÷：除法运算符
//   // (：左侧括号
//   // )：右侧括号
//   // >：大于运算符
//   // <：小于运算符
//   // >=：大于等于运算符
//   // <=：小于等于运算符
//   // =：等于运算符
//   // !=：不等于运算符
//   // : 冒号 后边可以跟结果
//   // ; 如果有多个计算公式 就用 ; 连接起来 程序会根据 ; 去分割计算公式 然后去计算 该文本域就显示计算公式的结果 最后一个是不需要 ; 的
//   // 并
//   // 或
//   // 还有数字以及小数点
//   // 计算公式的字符串是 文本域的 name 值(要用[]包裹) 用连接符连接起来的 程序会自动去找对应 name 值的文本域的内容 带入到公式中计算 该文本域就显示计算公式的结果
//   // 计算公式的优先级是比展示规则高的 所以拿到计算结果以后 展示规则还能根据计算后的结果 进行一些规则的匹配 进行展示
//   // 规则如下：
//   // (1)如果多个文本域具有相同名称,则公式中只需设置一次该名称后会将所有同名文本域中填写的数字自动求和。(适用于表格中统计行或者列求和)
//   // (2)满足基本的数学公式如＋、－、×、÷及（）运算。例如文本域录入的公式为[name1]+[name2]，则当前文本域显示的内容为name1的文本域的值与name2文本域值的和。
//   // (3)满足＞、＜、≥、≤、=，其中≥也可写为“＞=”，≤可写为"＜=" 运算，条件组合需与“：”配合使用，多个条件表达式用“；”间隔，判断“=”时需要用“==”进行判断。
//   // (4)做条件运算的时候，当所有条件都不符合时将清空文本域内容，如果所有条件都不符合时需要显示其他内容则在公式末尾直接拼上即可，例如：[name]＜60:不合格；"合格"，表示名称为name的文本域值小于60的时候显示不合格，否则显示合格。
//   // 例1：[name1]>[name2]:大；[name1]< [name2]:小；[name1]=[name2]:中，意为名称为name1的文本域的值若大于name2文本域的值，则当前文本域内容显示为“大”；当name1文本域的值小于name2文本域的值则内容显示为“小”；两个文本域值相等则显示为“中”。
//   // 例2：[name]＜60:不及格；[name]≥60&&[name]＜80:良好；[name]≥80:优秀< 其中&&代表”并且“，如需表达“或者”使用||表示。
//   分段结束
//   active_type: 0, // 是 type 为 select 或者 date 或者 number 类型的时候 该属性才有内容 默认是 0 代表单击激活 1 代表双击激活 就是弹出对应的弹窗
//   show_field: true,
//   source_list: [], // type 为 select 类型的时候 该数组才有内容 是个对象数组 里边每个对象都是一个下拉选项
//   // 下拉选项的格式如下：
//   // {
//   //   text: "", // 下拉选项的文本内容显示出来能看到的东西
//   //   value: "", // 下拉选项的值
//   //   code: "", // 是检索码 如果下拉选项太多的时候 会有个搜索框 会根据 code 值去匹配 如果匹配到了 就显示该选项 否则显示该选项的文本内容
//   // }
//   分段结束
//   valid_content: {
//     // 记录该文本域的校验规则 只有 valid 为 1 的时候 该对象才有内容 是个对象 里边每个属性都是校验规则
//     require: 0, // 是否必填 默认是 0 表示不必填 1 表示必填
//     type: "", // 校验类型 默认是空 就是没有的意思 有以下几种类型：
//     //  date 日期类型 当 type 为 date 类型的时候 只有 require 生效 phone_type rule regex 不生效
//     //  number 数字类型 可以校验该文本域是否必填 最小值 最大值 超范围显示箭头等
//     //  string 校验字符串长度
//     //  idCard 是校验身份证 只有是否必填
//     //  phone 校验手机号 只有是否必填
//     // regex 正则表达式 可以校验该文本域是否符合正则表达式
//     phone_type: "", // 当 type 为 phone 的时候 该属性才有内容 默认是空 就是没有的意思 fixed_phone 表示校验固定电话 空字符串 代表手机号
//     rule: {
//       min_length: 0, // 当 type 为 string 时 表示最小长度 默认为 0 表示没有最小长度
//       max_length: 20, // 当 type 为 string 时 表示最大长度 默认为 20 最大长度 20
//       max_num: undefined, // 当 type 为 number 时 表示最大值 如果没设置的话 就是 undefined 或者直接没有该属性
//       min_num: undefined, // 当 type 为 number 时 表示最小值 如果没设置的话 就是 undefined 或者直接没有该属性
//       outRangeShowSymbol: true, // 当 type 为 number 类型的时候 该属性才有内容 默认是 false 表示当数值超出范围时 显示超出范围的符号(上箭头 下箭头) 否则不显示
//     },
//     regex: "", // 当 type 为 regex 时 该属性才有内容 默认是空 就是没有的意思 表示该文本域的正则表达式 比如：/^1[3-9]\d{9}$/ 表示手机号
//   },
//   defaultValue: null, // 文本域的默认值 默认为 null 有值时 为字符串 通过文本域属性弹窗设置的默认值 插入文本域的时候就会显示该默认值 文本域的静态方法 updateFieldsTextByDefaultValue 专门修改默认值 底层调用的还是 editor.updateFieldText
//   分段结束
// // 获取文本域的方法有：
// // 1. editor.getFieldById(id) 根据id获取文本域 只返回一个文本域实例 没有找到就返回 null
// // 2. editor.getFieldsByName(name) 根据name获取文本域 返回一个文本域实例数组 没有找到就返回空数组
// // 以上是 editor.insertField 这个方法插入文本域的时候的参数
// // 也是 editor.reviseFieldAttr 这个方法修改文本域属性的时候的参数 以及参数的含义 你理解分析
// // 我会问你一些我想要实现的效果 你告诉我调用方法 无非就是插入文本域 修改文本域属性 修改文本域属性之前多个获取 editor.getFieldsByName("name") 就调用这三个方法 接下来我说的文本域 xxx 这个 xxx 就是已经存在的文本域 name 值，或者我不加文本域前缀 就直接说操作 xxx 这个 xxx 也是已经存在的文本域 name 值直接使用就可以
// // 除非我特别指定 id 否则我问的文本域 xxx 都是指 name 值 而不是 id 值
// // 用到的每个方法 你都单独放在 开$始 和 结$束 里回复我 我要修改A的固定宽度为200 你就回复我:
// // 首先获取文本域A, 然后修改A的固定宽度为200,只要你返回给我方法 全部都要放在一个 开$始 和 结$束 里 比如：
// // 开$始 const fields = editor.getFieldsByName("A"); editor.reviseFieldAttr({fields:fields, max_width:200, min_width:200})]} 结$束
// // 有返回值的 你要声明变量接一下 然后接下来用的着该变量的就使用该变量
// // 开$始 和 结$束 是成对出现的特殊标记 不能错
// // 我会将 开$始 和 结$束 里的内容 提取出来我自己调用 是不给用户看到的 所以你别告诉我有指向这里边内容的话
// 分段结束
