/**
 * 都昌xml数据转换
 */
// @ts-ignore
import { nameVsName, onlyOneName, placeholderVsName } from "../utils/newOldCorrespondence";
// @ts-ignore
import newRawInitModel, { loadXML, xmlToJson } from "../utils/Util";
import XField from "msun-lib-editor-common/src/editor/XField";

export const D2R = {
  editor: null,
  styles: {}, // 记录转换出来的style
  alignMap: {
    Center: "center",
    Right: "right"
  },
  dcBodyText: "", // 都昌模板纯文本
  isExceptionMark: 0, // 异常标记 1、 有文本域嵌套表格情况  2、有表格嵌套情况
  maxNumOfPage: 200, // 能够转换的最大页数，修改此值可防止转换逻辑卡死问题
  linebreak_mark: "$$linebreak$$",
  fontSizeList: {},
  default_font_style: null,
  // field_select_start_symbol: Config.field_select_start_symbol,
  // field_select_end_symbol: Config.field_select_end_symbol,
  curRootCell: null, // 当前正在进行转换的root_cell
  existFieldIds: [],
  existCantRepeatName: {},
  selectFieldsListValText: {}, // 下拉文本域值与文本的对应关系
  cascadeFieldInfo: {}, // 级联文本域id对应关系
  needShowLineTables: {}, // 需要显示表格线的表格
  hiddenFieldIds: {},
  groups: [], // 所有的分组

  startEntrance (xml:any, instance: any) {
    console.time("数据转换耗时：");
    const editor = instance.editor;
    // 如果传入的xml字符串不为空
    if (xml) {
      let rawData = this.dcXmlDataTrans(xml, instance);
      // 先将rawData转成字符串，判断其中的换行镖旗
      rawData = this.handleTooManyLinebreak(rawData);
      editor.reInitRaw(rawData);
      editor.refreshDocument(true);
      // 将文本域中的特定字符替换成换行符
      editor.replaceAllFont(this.linebreak_mark, "\n");
      if (this.isExceptionMark === 1) {
        console.log("异常：有表格在文本域内，需特殊处理");
      }
      if (this.isExceptionMark === 2) {
        console.log("异常：有表格嵌套，需特殊处理");
      }
    } else {
      editor.reInitRaw(editor.config.rawData);
      editor.refreshDocument(true);
    }
    // 内容校验
    // const dcBodyText = dcXmlHandle.dcBodyText.replace(/\n/g, "").replace(/\s/g, "").replace(/○/g, "");
    // const bodyText = editor.getBodyText().replace(/\n/g, "").replace(/\s/g, "");
    // if (dcBodyText.length !== bodyText.length) {
    // }
    this.afterInitRawHandle();
    console.timeEnd("数据转换耗时：");
    return this;
  },
  /**
     * 都昌模板转换开始时必须要初始化的属性
     */
  initAttrInfo () {
    // 初始化转换标记
    this.isExceptionMark = 0;
    this.dcBodyText = "";
    this.existFieldIds = [];
    // 必须重新初始化
    this.existCantRepeatName = {};
    this.selectFieldsListValText = {};
    this.cascadeFieldInfo = {};
    this.needShowLineTables = {};
    this.hiddenFieldIds = {};
    this.groups = [];
  },
  /**
     * 初始化配置信息
     */
  initConfigInfo (instance: any) {
    const { editor } = instance;
    const config = instance.config.getConfig();
    this.config = config;
    this.fontSizeList = instance.builtInVariable.fontSizeList;
    this.default_font_style = editor.config.default_font_style;
    this.config_row_ratio = editor.config.row_ratio;
    this.devicePixelRatio = editor.config.devicePixelRatio;
    this.img_margin = config.img_margin;
    this.min_row_size = config.min_row_size;
    this.field_select_start_symbol = editor.config.field_select_start_symbol;
    this.field_select_end_symbol = editor.config.field_select_end_symbol;
    this.uuid = instance.utils.getUUID;
    this.isEmptyObj = instance.utils.isEmptyObj;
    this.pathUtils = instance.pathUtil;
    this.sysVariables = instance.sysVariables;
    this.TypeJudgment = instance.TypeJudgment;
  },
  /**
     * 都昌xml数据转换主方法
     */
  dcXmlDataTrans (xml: any, instance: any) {
    const editor = this.editor = instance.editor;
    this.initConfigInfo(instance);
    this.initAttrInfo();
    let xmlObj = xml;
    if (typeof (xml) === "string") {
      const xmlStr = this.handleLoadBeforeXmlStr(xml);
      xmlObj = loadXML(xmlStr);
    }
    const json = xmlToJson(xmlObj);
    const xTextDocument = json.XTextDocument;
    // 如果是服务端，则设置可转最大页码为150页
    if (editor.config.source === "server") {
      this.maxNumOfPage = 150;
    }
    // 当页数大于10页时不再进行转换，防止页数过多导致卡死
    if (xTextDocument.Info && xTextDocument.Info.NumOfPage &&
            Number(xTextDocument.Info.NumOfPage["#text"]) > this.maxNumOfPage) {
      this.isExceptionMark = 5;
      return editor.config.rawData;
    }

    if (xTextDocument.BodyText && xTextDocument.BodyText["#text"]) {
      this.dcBodyText = xTextDocument.BodyText["#text"];
    }

    // 样式转换
    if (xTextDocument.ContentStyles) {
      this.handleDcStyles(xTextDocument.ContentStyles);
    }

    const xElement = xTextDocument.XElements;
    // 页眉数据转换
    const headerRawData: any[] = [];
    this.curRootCell = headerRawData;
    const headerElements = xElement.Element[0].XElements;
    const rawHeaderPara = this.handleDcElements(headerElements, headerRawData, null, editor);
    this.judgeRawDataEmpty(headerRawData, rawHeaderPara);
    // 正文内容
    const contentRawData: any[] = [];
    this.curRootCell = contentRawData;
    let contentElements = xElement.Element[1].XElements;
    contentElements = this.handleOuterOnlyField(contentElements);
    contentElements = this.handleOuterOnlyField2(contentElements);
    const rawContentPara = this.handleDcElements(contentElements, contentRawData, null, editor);
    this.judgeRawDataEmpty(contentRawData, rawContentPara);
    // 页脚
    const footerRawData: any[] = [];
    this.curRootCell = footerRawData;
    const footerElements = xElement.Element[2].XElements;
    const rawFooterPara = this.handleDcElements(footerElements, footerRawData, null);
    this.judgeRawDataEmpty(footerRawData, rawFooterPara);
    const rawData = {
      header: headerRawData,
      footer: footerRawData,
      content: contentRawData,
      groups: this.groups,
      fontMap: this.styles.rawStyleMap
    };
    return rawData;
  },
  /**
     * 加载xml先对其字符串进行处理
     */
  handleLoadBeforeXmlStr (xmlStr: string) {
    xmlStr = xmlStr.replace(/&gt;/g, "＞");
    xmlStr = xmlStr.replace(/&lt;/g, "＜");
    xmlStr = xmlStr.replace(/\\t/g, "  ");
    // loadxml不支持&字符
    xmlStr = xmlStr.replace(/&/g, " ");
    return xmlStr;
  },
  // 处理只是外层有一个文本域的情况（主要是病历知情文件）
  handleOuterOnlyField (contentElements: any) {
    // if (localStorage.getItem("dcXmlHandle-restore")) {
    //   return contentElements;
    // }
    let elements = contentElements.Element;
    if (!(elements instanceof Array)) {
      elements = [elements];
    }
    const newElements: any = [];
    for (let i = 0; i < elements.length; i++) {
      const ele = elements[i];
      if (!ele) {
        continue;
      }
      const attribute = ele["@attributes"];
      const eleType = attribute["xsi:type"];
      if (eleType === "XInputField") {
        let hasTable = false;
        if (Array.isArray(ele.XElements.Element)) {
          // 检测哪个文本域第一层有表格
          hasTable = ele.XElements.Element.some((e: any) => {
            if (e && e["@attributes"] && e["@attributes"]["xsi:type"] === "XTextTable") {
              return true;
            }
            return false;
          });
          hasTable && newElements.push(...ele.XElements.Element);
        } else {
          const e = ele.XElements.Element;
          if (e && e["@attributes"] && e["@attributes"]["xsi:type"] === "XTextTable") {
            hasTable = true;
          }
          hasTable && newElements.push(e);
        }
        if (!hasTable) {
          newElements.push(ele);
        }
      } else {
        newElements.push(ele);
      }
    }
    contentElements.Element = newElements;
    return contentElements;
  },
  // 处理第一个元素是文本域的情况
  handleOuterOnlyField2 (contentElements: any, lastPara?: any) {
    // if (localStorage.getItem("dcXmlHandle-restore1")) {
    //   return contentElements;
    // }
    let elements = contentElements.Element;
    if (!(elements instanceof Array)) {
      elements = [elements];
    }
    let res: boolean = false;
    for (let i = 0; i < elements.length; i++) {
      const ele = elements[i];
      if (!ele) {
        continue;
      }
      const attribute = ele["@attributes"];
      const eleType = attribute["xsi:type"];
      // 如果元素中除了一个文本域外 其余的都是段落标记
      if (i === 0) {
        if (eleType === "XInputField") {
          res = true;
        }
      } else {
        if (eleType !== "XParagraphFlag") {
          res = false;
        }
        if (res && eleType === "XParagraphFlag" && i === elements.length - 1 && !lastPara) {
          lastPara = ele;
        }
      }
    }
    if (res) {
      if (contentElements.Element instanceof Array) {
        contentElements.Element = contentElements.Element[0].XElements.Element;
      } else {
        contentElements.Element = contentElements.Element.XElements.Element;
      }
      return this.handleOuterOnlyField2(contentElements, lastPara);
    }
    if (lastPara) {
      if (contentElements.Element instanceof Array) {
        contentElements.Element.push(lastPara);
      } else {
        const ele = contentElements.Element;
        contentElements.Element = [];
        contentElements.Element.push(ele, lastPara);
      }
    }
    return contentElements;
  },
  // 判断处理完成后rawData是否为空，如果为空则补充段落
  judgeRawDataEmpty (rawData: any, rawParagraph: any) {
    // // 判断当前元素中是否存在段落标签
    // const hasParagraph = elements.some((ele:any) => (ele["@attributes"] && ele["@attributes"]["xsi:type"] === "XParagraphFlag"));
    if (!rawData.length) {
      rawData.push(rawParagraph);
    }
  },
  /**
     * 处理都昌病程记录分组信息
     * @param ele
     * @param rawData
     */
  handleDcGroup (ele: any, rawData: any) {
    /* if (this.editor.config.source !== "server") {
         return;
         } */
    const group = newRawInitModel("group");
    group.id = ele.ID["#text"] || this.uuid("group");
    this.groups.push(group);
    for (let i = 0, len = rawData.length; i < len; i++) {
      if (rawData[i].group_id) {
        continue;
      }
      rawData[i].group_id = group.id;
      group.content_para_id.push(rawData[i].id);
    }
  },
  /**
     * 处理都昌xml元素
     */
  handleDcElements (xElement: any, rawData: any[], parent_field: any, parent_table: any): any {
    let elements = xElement.Element;
    if (!(elements instanceof Array)) { // Array.isArray(elements) 更强大
      elements = [elements];
    }
    let raw_paragraph = newRawInitModel("p");
    raw_paragraph.row_ratio = this.config_row_ratio;
    // 用于修复都昌新老版本数据接口不一致问题（当elements只有一个文本域时）
    if (!elements[0]) {
      rawData.push(raw_paragraph);
      return;
    }
    for (let i = 0; i < elements.length; i++) {
      const ele = elements[i];
      if (!ele) {
        continue;
      }
      const attribute = ele["@attributes"];
      const eleType = attribute["xsi:type"];
      if (eleType === "XTextSubDocument") {
        this.handleDcElements(ele.XElements, rawData, null);
        this.handleDcGroup(ele, rawData);
      } else if (eleType === "XInputField") {
        // if (!this.isEmptyObj(ele.XElements)) {
        //   this.handleDcElements(ele.XElements, rawData, parent_field, parent_table);
        // }
        const raw_field = newRawInitModel("field");
        this.handleDcFieldData(ele, raw_field, parent_field, raw_paragraph);
        // 当不是标签文本域情况下进入以下逻辑
        // if (!(ele.LabelText && ele.LabelText["#text"])) {
        if (!this.isEmptyObj(ele.XElements)) {
          this.handleDcElements(ele.XElements, rawData, raw_field, parent_table);
        }
        // }
      } else if (eleType === "XTextLabelElement") {
        this.handleDcLabel(ele, parent_field, raw_paragraph);
      } else if (eleType === "XParagraphFlag") {
        this.handleDcParagraph(ele, parent_field, raw_paragraph);
        if (raw_paragraph.id) {
          rawData.push(raw_paragraph);
          raw_paragraph = newRawInitModel("p");
          raw_paragraph.row_ratio = this.config_row_ratio;
          raw_paragraph.id = this.uuid("para-");
        }
      } else if (eleType === "XString") {
        this.handleDcString(ele, parent_field, raw_paragraph);
      } else if (eleType === "XTextTable") {
        if (parent_field) {
          this.isExceptionMark = 1;
          this.handleFieldNestingTable(ele, parent_field);
        } else if (parent_table) {
          // this.isExceptionMark = 2;
          // 如果table有值说明是嵌套表格
          // 将被嵌套的表格放置到当前表格上方，并追加一个换行
          this.handleDcTable(ele, this.curRootCell);
          const raw_paragraph = newRawInitModel("p");
          raw_paragraph.row_ratio = this.config_row_ratio;
          raw_paragraph.id = this.uuid("para-template");
          this.curRootCell.push(raw_paragraph);
          // const raw_text = newRawInitModel("text");
          // raw_text.value = ">>>此处原为子表格位置<<<";
          if (parent_field) {
            this.isExceptionMark = 1;
            // parent_field.children.push(raw_text);
          } else {
            // 在表格原位置替换字符
            // const raw_text_para = newRawInitModel("p");
            // raw_text_para.id = this.uuid("para-template");
            // raw_text_para.children.push(raw_text);
            // rawData.push(raw_text_para);
          }
        } else {
          this.handleDcTable(ele, rawData);
        }
      } else if (eleType === "XTextCheckBox" || eleType === "XTextRadioBox") {
        this.handleDcWidget(eleType, ele, parent_field, raw_paragraph);
      } else if (eleType === "XNewMedicalExpression") {
        this.handleDcMedicalExpression(ele, parent_field, raw_paragraph);
      } else if (eleType === "XImage") {
        this.handleDcImage(ele, parent_field, raw_paragraph);
      } else if (eleType === "XPageInfo") {
        this.handleDcPageInfo(ele, parent_field, raw_paragraph);
      }
    }
    return raw_paragraph;
  },
  /**
     * 处理文本域嵌套表格
     * @param ele
     * @param parent_field
     */
  handleFieldNestingTable (ele: any, parent_field: any) {
    const raw_paragraph = newRawInitModel("p");
    raw_paragraph.row_ratio = this.config_row_ratio;
    raw_paragraph.id = this.uuid("para-template");

    let dc_rows = ele.XElements.Element;
    if (!dc_rows.length) {
      dc_rows = [dc_rows];
    }
    // 需要跳过的合并单元格
    const needJumpRow: any[] = [];
    // 遍历行数据
    for (let i = 0; i < dc_rows.length; i++) {
      const new_parent_field = newRawInitModel("field");
      new_parent_field.id = parent_field.id;
      let dc_cells = dc_rows[i].XElements.Element;
      if (!dc_cells) {
        continue;
      }
      // 当只有一列时 dc_cells不是一个数组
      if (!dc_cells.length) {
        dc_cells = [dc_cells];
      }
      // 遍历单元格数据
      for (let j = 0; j < dc_cells.length; j++) {
        const cell = dc_cells[j];
        const raw_cell = newRawInitModel("cell");
        raw_cell.pos = [i, j];
        if (cell.ColSpan && cell.ColSpan["#text"]) {
          raw_cell.colspan = cell.ColSpan["#text"] * 1;
          j += raw_cell.colspan - 1;
        }
        if (needJumpRow.find(item => this.pathUtils.equals(item, [i, j]))) {
          continue;
        }
        // 出现rowspan的情况
        if (cell.RowSpan && cell.RowSpan["#text"]) {
          raw_cell.rowspan = cell.RowSpan["#text"] * 1;
          for (let k = 0; k < raw_cell.rowspan; k++) {
            needJumpRow.push([i + k, j]);
          }
        }
        // 将内容平铺展示
        this.handleDcElements(cell.XElements, raw_cell.children, new_parent_field);
      }
      new_parent_field.children.forEach((item:any, index:number) => {
        if (item.type === "text" && item.value === this.linebreak_mark && index < new_parent_field.children.length - 1) {
          item.value = "\t";
        }
      });
      parent_field.children.push(...new_parent_field.children);
    }
  },
  /**
     * 处理页脚页码信息
     * @param ele
     * @param parent_field
     * @param raw_paragraph
     */
  handleDcPageInfo (ele: any, parent_field: any, raw_paragraph: any) {
    const raw_field = newRawInitModel("field");
    raw_field.start_symbol = "";
    raw_field.end_symbol = "";
    raw_field.field_type = "label";
    raw_field.id = this.uuid("field");
    if (ele.ValueType && ele.ValueType["#text"] === "NumOfPages") {
      // 共几页
      raw_field.name = this.sysVariables.page_count;
      raw_field.placeholder = "页数";
    } else {
      // 第几页
      raw_field.name = this.sysVariables.page_number;
      raw_field.placeholder = "页";
    }
    this.fieldStyleIdSet(ele, raw_field);
    if (parent_field) {
      parent_field.children.push(raw_field);
    } else {
      raw_paragraph.children.push(raw_field);
    }
  },
  // 文本域样式id设置
  fieldStyleIdSet (ele: any, raw_field: any) {
    const style_index = ele["@attributes"].StyleIndex;
    if (style_index && this.styles.styleMap[style_index]) {
      raw_field.font_id = this.styles.styleMap[style_index].id;
    } else {
      raw_field.font_id = this.styles.styleMap.default.id;
    }
  },
  /**
     * 处理都昌小组件（复选框、单选框）
     */
  handleDcWidget (eleType: string, ele: any, parent_field: any, raw_paragraph: any) {
    const raw_widget = newRawInitModel("widget");
    // 如果是复选框类型或者样式是复选框类型
    if (eleType === "XTextCheckBox" || (ele.VisualStyle && ele.VisualStyle["#text"].indexOf("CheckBox") > -1)) {
      raw_widget.widgetType = "checkbox";
    } else {
      raw_widget.widgetType = "radio";
    }
    if (ele.Checked && ele.Checked["#text"] === "true") {
      raw_widget.selected = true;
    }
    let raw_text = null;
    if (ele.Caption && ele.Caption["#text"]) {
      raw_text = newRawInitModel("text");
      raw_text.value = ele.Caption["#text"];
      const style_index = ele["@attributes"].StyleIndex;
      // 判断是否已经删除
      const dcStyle = this.styles.dcStyles[style_index];
      // 如果已经删除不放入父节点中
      if (dcStyle && dcStyle.DeleterIndex && dcStyle.DeleterIndex["#text"]) {
        return;
      }
      if (style_index) {
        raw_text.font_id = this.styles.styleMap[style_index].id;
      } else {
        raw_text.font_id = this.styles.styleMap.default.id;
      }
    }
    if (parent_field) {
      parent_field.children.push(raw_widget);
      if (raw_text) {
        parent_field.children.push(raw_text);
      }
    } else {
      raw_paragraph.children.push(raw_widget);
      if (raw_text) {
        raw_paragraph.children.push(raw_text);
      }
    }
  },
  /**
     * 处理都昌图片数据
     */
  handleDcImage (ele: any, parent_field: any, raw_paragraph: any) {
    if (!ele.Image || !ele.Image.ImageDataBase64String) {
      return;
    }
    const raw_image = newRawInitModel("image");
    raw_image.height = ele.Height["#text"] * 1 / 2.8 + this.img_margin;
    raw_image.width = ele.Width["#text"] * 1 / 3.13 + +this.img_margin;
    raw_image.src = "data:image/jpeg;base64," + ele.Image.ImageDataBase64String["#text"];
    if (parent_field) {
      parent_field.children.push(raw_image);
    } else {
      raw_paragraph.children.push(raw_image);
    }
  },
  /**
     * 处理都昌label标签数据
     * @param ele
     * @param parent_field
     * @param raw_paragraph
     */
  handleDcLabelField (ele: any, parent_field: any, labelText: string) {
    if (ele instanceof Array) {
      ele = ele.find((item) => (item.ID || item.Name));
    }
    if (!ele) {
      return;
    }
    const raw_field = newRawInitModel("field");
    const field_id = ele.ID ? ele.ID["#text"] : this.uuid("field");
    const label_id = "lbl_" + field_id;
    if (!labelText) {
      return;
    }
    const label_text = labelText;
    if (this.existFieldIds.includes(label_id)) {
      raw_field.id = this.uuid("field");
    } else {
      raw_field.id = label_id;
      this.existFieldIds.push(label_id);
    }
    raw_field.name = ele.Name ? "lbl_" + ele.Name["#text"] : "";
    raw_field.start_symbol = "";
    raw_field.end_symbol = "";
    raw_field.field_type = "label";
    const style_index = ele["@attributes"].StyleIndex;
    // 判断是否已经删除
    const dcStyle = this.styles.dcStyles[style_index];
    // 如果已经删除不放入父节点中
    if (dcStyle && dcStyle.DeleterIndex && dcStyle.DeleterIndex["#text"]) {
      return;
    }
    this.fieldStyleIdSet(ele, raw_field);
    const raw_text = newRawInitModel("text");
    raw_text.value = label_text ?? "";
    if (!raw_text.value) {
      return;
    }
    raw_text.font_id = raw_field.font_id;
    raw_field.children.push(raw_text);
    parent_field.children.push(raw_field);
    // 用于替换的文本域
    const label_field = newRawInitModel("field");
    if (this.existFieldIds.includes(field_id)) {
      label_field.id = this.uuid("field");
    } else {
      label_field.id = field_id;
      this.existFieldIds.push(field_id);
    }
    label_field.name = ele.Name ? ele.Name["#text"] : "";
    label_field.font_id = raw_field.font_id;
    label_field.start_symbol = "";
    label_field.end_symbol = "";
    parent_field.children.push(label_field);
    parent_field.start_symbol = "";
    parent_field.end_symbol = "";
  },
  /**
     * 处理都昌文本域数据
     */
  handleDcFieldData (ele: any, raw_field: any, parent_field: any, raw_paragraph: any) {
    // BackgroundText 、 BorderVisible['#text'] 、 ContentReadonly 、 EditorActiveMode['#text']、ID['#text']
    // Name['#text'] 、 InnerValue、  XElements 、
    // 如果是有LabelText属性，则说明是携带标签文本域
    if (ele.LabelText && ele.LabelText["#text"]) {
      const labelText = ele.LabelText["#text"];
      this.handleDcLabelField(ele.XElements.Element, raw_field, labelText);
    }
    // 设置级联时用 ，可见性表达式
    if (ele.VisibleExpression && ele.VisibleExpression["#text"]) {
      if (ele.ID && ele.ID["#text"]) {
        this.cascadeFieldInfo[ele.ID["#text"]] = ele.VisibleExpression["#text"];
      }
    }

    // 判断当前文本域类型
    // 下拉列表形式DropdownList = 1、日期类型Date = 2、时间日期类型DateTime = 3、去掉秒数的时间日期类型DateTimeWithoutSecond = 4、时间类型Time = 5和数值型Numeric = 6
    const fieldSetting = ele.FieldSettings;
    if (fieldSetting && !this.isEmptyObj(fieldSetting)) {
      const editStyle = fieldSetting.EditStyle;
      if (editStyle && !this.isEmptyObj(editStyle)) {
        const editStyleVal = editStyle["#text"];
        switch (editStyleVal) {
          case "Date":
            raw_field.field_type = "date";
            raw_field.show_format = 0;
            raw_field.replace_format = 0;
            break;
          case "DateTime":
            raw_field.field_type = "date";
            raw_field.show_format = 2;
            raw_field.replace_format = 2;
            break;
          case "DateTimeWithoutSecond":
            raw_field.field_type = "date";
            raw_field.show_format = 1;
            raw_field.replace_format = 1;
            break;
          case "DropdownList":
            if (fieldSetting.ListSource && fieldSetting.ListSource.Items && fieldSetting.ListSource.Items.Item) {
              raw_field.field_type = "select";
              raw_field.start_symbol = this.field_select_start_symbol;
              raw_field.end_symbol = this.field_select_end_symbol;
              for (let i = 0; i < fieldSetting.ListSource.Items.Item.length; i++) {
                const item = fieldSetting.ListSource.Items.Item[i];
                if (!raw_field.source_list) {
                  raw_field.source_list = [];
                }
                raw_field.source_list.push({ text: item.Text["#text"] });
                if (ele.ID && ele.ID["#text"]) {
                  if (item.Text && item.Text["#text"]) {
                    // 如果this.selectFieldsListValText[ele.ID["#text"]]有值则不重复赋空
                    if (!this.selectFieldsListValText[ele.ID["#text"]]) {
                      this.selectFieldsListValText[ele.ID["#text"]] = {};
                    }
                    if (item.Value && item.Value["#text"]) {
                      this.selectFieldsListValText[ele.ID["#text"]][item.Value["#text"]] = item.Text["#text"];
                    } else {
                      this.selectFieldsListValText[ele.ID["#text"]][item.Text["#text"]] = item.Text["#text"];
                    }
                  }
                }
              }
            }
            // 判断是否为多选
            if (fieldSetting.MultiSelect && fieldSetting.MultiSelect["#text"] === "true") {
              raw_field.multi_select = 1;
            }
            break;
        }
      }
    }
    if (ele.EditorActiveMode && ele.EditorActiveMode["#text"]) {
      raw_field.active_type = ele.EditorActiveMode["#text"].indexOf("MouseClick") > -1 ? 0 : 1;
    }
    // 如果已经存在文本域id则不再给文本域id赋值，防止都昌模板中有多个相同文本域id的情况
    const field_id = ele.ID ? ele.ID["#text"] : this.uuid("field");
    // 隐藏文本域处理
    if (ele.Visible && ele.Visible["#text"] === "false") {
      this.hiddenFieldIds[field_id] = true;
    }
    if (this.existFieldIds.includes(field_id)) {
      raw_field.id = this.uuid("field");
    } else {
      raw_field.id = field_id ?? this.uuid("field");
      this.existFieldIds.push(field_id);
    }
    if (ele.Name) {
      let name = nameVsName[ele.Name["#text"]];
      name || (name = ele.ID && nameVsName[ele.ID["#text"]]);
      name || (name = ele.BackgroundText && placeholderVsName[ele.BackgroundText["#text"]]);
      name && (ele.Name["#text"] = name);
    }

    raw_field.name = ele.Name ? ele.Name["#text"] : "";
    // raw_field.name 如果只允许出现一次，那么后边的所有 name 值都要加上 -re 后缀
    if (onlyOneName[raw_field.name]) {
      const isExist = this.existCantRepeatName[raw_field.name];
      if (isExist) {
        raw_field.name = raw_field.name + "-re";
      } else {
        this.existCantRepeatName[raw_field.name] = true;
      }
    }
    // 如果边框不是一直可见，并且文本域有背景文本或者内容的话，就将其边框隐藏
    if (!ele.BorderVisible || ele.BorderVisible["#text"] !== "AlwaysVisible") {
      if ((ele.BackgroundText && ele.BackgroundText["#text"]) ||
                (ele.InnerValue && ele.InnerValue["#text"])) {
        // 如果存在外层文本域才隐藏，不然不隐藏
        if (parent_field) {
          raw_field.start_symbol = "";
          raw_field.end_symbol = "";
        }
      }
    }
    // 转换时都设为可修改
    // if (ele.ContentReadonly && ele.ContentReadonly["#text"] === "True") {
    //   raw_field.readonly = 1;
    // }
    if (ele.BackgroundText && ele.BackgroundText["#text"]) {
      raw_field.placeholder = ele.BackgroundText["#text"];
    }

    const style_index = ele["@attributes"].StyleIndex;
    // 判断是否已经删除
    const dcStyle = this.styles.dcStyles[style_index];
    // 如果已经删除不放入父节点中
    if (dcStyle && dcStyle.DeleterIndex && dcStyle.DeleterIndex["#text"]) {
      return;
    }
    this.fieldStyleIdSet(ele, raw_field);
    // 如果是指定宽度
    if (ele.SpecifyWidth && ele.SpecifyWidth["#text"]) {
      if (!raw_field.placeholder) {
        raw_field.placeholder = " ";
      }
      raw_field.min_width = ele.SpecifyWidth["#text"] / 4;
    }

    if (parent_field) {
      parent_field.children.push(raw_field);
      // console.log("文本域内的文本域：" + parent_field.name);
    } else {
      raw_paragraph.children.push(raw_field);
    }
  },
  /**
     * 处理都昌label标签数据
     * @param ele
     * @param parent_field
     * @param raw_paragraph
     */
  handleDcLabel (ele: any, parent_field: any, raw_paragraph: any) {
    const raw_field = newRawInitModel("field");
    const label_id = ele?.ID?.["#text"] || this.uuid("label");
    if (!ele.Text) {
      return;
    }
    const label_text = ele.Text["#text"];
    if (this.existFieldIds.includes(label_id)) {
      raw_field.id = this.uuid("field");
    } else {
      raw_field.id = label_id;
      this.existFieldIds.push(label_id);
    }
    raw_field.name = ele.Name ? ele.Name["#text"] : "";
    raw_field.start_symbol = "";
    raw_field.end_symbol = "";
    raw_field.field_type = "label";
    const style_index = ele["@attributes"].StyleIndex;
    // 判断是否已经删除
    const dcStyle = this.styles.dcStyles[style_index];
    // 如果已经删除不放入父节点中
    if (dcStyle && dcStyle.DeleterIndex && dcStyle.DeleterIndex["#text"]) {
      return;
    }
    this.fieldStyleIdSet(ele, raw_field);
    const raw_text = newRawInitModel("text");
    raw_text.value = label_text ?? "";
    if (!raw_text.value) {
      return;
    }
    raw_text.font_id = raw_field.font_id;
    raw_field.children.push(raw_text);
    if (parent_field) {
      parent_field.children.push(raw_field);
    } else {
      raw_paragraph.children.push(raw_field);
    }
  },
  /**
     * 处理都昌表格数据
     */
  handleDcTable (ele: any, rawData: any[]) {
    if (!ele.Columns || !ele.Columns.Element) {
      return;
    }
    const raw_table = newRawInitModel("table");
    raw_table.id = (ele.ID && ele.ID["#text"]) ? ele.ID["#text"] : this.uuid("table");
    // 处理列  ,当只有一列时有可能直接是一个对象
    if (ele.Columns.Element.length) {
      for (let j = 0; j < ele.Columns.Element.length; j++) {
        const column = ele.Columns.Element[j];
        const width = column.Width["#text"];
        raw_table.col_size.push(width / this.devicePixelRatio);
      }
    } else {
      const width = ele.Columns.Element.Width["#text"];
      raw_table.col_size.push(width / this.devicePixelRatio);
    }

    raw_table.row_size = new Array(ele.XElements.Element.length).fill(this.min_row_size);
    raw_table.min_row_size = new Array(ele.XElements.Element.length).fill(this.min_row_size);
    let dc_rows = ele.XElements.Element;
    if (!dc_rows.length) {
      dc_rows = [dc_rows];
    }
    this.tableCellBorderSet(dc_rows, raw_table);
    // 需要跳过的合并单元格
    const needJumpRow: any[] = [];
    // 遍历行数据
    for (let i = 0; i < dc_rows.length; i++) {
      let dc_cells = dc_rows[i].XElements.Element;
      // 当只有一列时 dc_cells不是一个数组
      if (!(dc_cells instanceof Array)) { // Array.isArray(elements) 更强大
        dc_cells = [dc_cells];
      }
      // 遍历单元格数据
      for (let j = 0; j < dc_cells.length; j++) {
        const cell = dc_cells[j];
        const raw_cell = newRawInitModel("cell");
        raw_cell.pos = [i, j];
        if (needJumpRow.find(item => this.pathUtils.equals(item, [i, j]))) {
          continue;
        }
        if (cell.ColSpan && cell.ColSpan["#text"]) {
          raw_cell.colspan = cell.ColSpan["#text"] * 1;
        }
        // 出现rowspan的情况
        for (let k = 0; k < raw_cell.rowspan; k++) {
          for (let m = 0; m < raw_cell.colspan; m++) {
            needJumpRow.push([i + k, j + m]);
          }
        }
        const resRawPara = this.handleDcElements(cell.XElements, raw_cell.children, null, raw_table);
        this.judgeRawDataEmpty(raw_cell.children, resRawPara);
        raw_cell.id = this.uuid("cell-template");
        raw_table.cells.push(raw_cell);
      }
    }
    rawData.push(raw_table);
  },
  /**
     * 设置表格线的显示隐藏
     */
  tableCellBorderSet (dc_rows: any[], raw_table: any) {
    const notDrawRow: any[] = [];
    const notDrawCol: any[] = [];
    const opacityRow: any[] = [];
    const opacityCol: any[] = [];
    // 遍历行数据
    for (let i = 0; i < dc_rows.length; i++) {
      let dc_cells = dc_rows[i].XElements.Element;
      if (!(dc_cells instanceof Array)) { // Array.isArray(elements) 更强大
        dc_cells = [dc_cells];
      }
      // 遍历单元格数据
      for (let j = 0; j < dc_cells.length; j++) {
        const cell = dc_cells[j];
        // 出现rowspan的情况
        if (cell.RowSpan && cell.RowSpan["#text"] &&
                    !notDrawRow.find((item: any) => this.pathUtils.equals(item, [i, j]))) {
          // row_span = i + cell.RowSpan["#text"] * 1;
          for (let k = 1; k < cell.RowSpan["#text"] * 1; k++) {
            notDrawRow.push([i + k, j]);
          }
        }
        if (cell.ColSpan && cell.ColSpan["#text"] &&
                    !notDrawCol.find((item: any) => this.pathUtils.equals(item, [j, i]))) {
          // col_span = j + cell.ColSpan["#text"] * 1;
          for (let k = 1; k < cell.ColSpan["#text"] * 1; k++) {
            notDrawCol.push([j + k, i]);
          }
        }
        const style_index = cell["@attributes"].StyleIndex;
        let style = {
          BorderWidth: {
            "#text": "0"
          },
          BorderTop: {
            "#text": "false"
          },
          BorderLeft: {
            "#text": "false"
          },
          BorderRight: {
            "#text": "false"
          },
          BorderBottom: {
            "#text": "false"
          }
        };
        // 如果没有style_index走默认值， 默认值为边框为不显示
        if (style_index !== undefined) {
          style = this.styles.dcStyles[style_index];
        }
        // let border_width: number = 0;
        if (style && style.BorderWidth && style.BorderWidth["#text"] === "1") {
          // border_width = 1;
          // 只要存在一条要显示的边线，则整个表格边框线全部显示
          this.needShowLineTables[raw_table.id] = true;
        }
      }
    }
    raw_table.notAllowDrawLine = {
      row: notDrawRow, // 这是不让画的线
      col: notDrawCol,
      changeOpacityRow: opacityRow, // 这是修改透明度的线
      changeOpacityCol: opacityCol // 这是修改透明度的线
    };
  },
  /**
     * 遇到段落标记的情况
     * @param ele
     * @param parent_field
     * @param rawData
     * @param raw_paragraph
     */
  handleDcParagraph (ele: any, parent_field: any, raw_paragraph: any) {
    // 如果该换行符在文本域内
    if (parent_field) {
      const raw_text = newRawInitModel("text");
      raw_text.value = this.linebreak_mark;
      parent_field.children.push(raw_text);
    } else {
      const style_index = ele["@attributes"].StyleIndex;
      let raw_style = this.styles.styleMap.default;
      if (style_index !== undefined) {
        const style = this.styles.dcStyles[style_index];
        if (style) {
          raw_style = this.styles.styleMap[style_index];
          if (style.Align) {
            if (style.Align["#text"] === "Distribute") {
              raw_paragraph.dispersed_align = true;
            } else {
              raw_paragraph.align = this.alignMap[style.Align["#text"]];
            }
          }
          if (style.LineSpacingStyle) {
            const lineSpaceStyle = style.LineSpacingStyle["#text"];
            switch (lineSpaceStyle) {
              case "Space1pt5":
                raw_paragraph.row_ratio = 1.5;
                break;
              case "SpaceDouble":
                raw_paragraph.row_ratio = 2;
                break;
              case "SpaceExactly":
                raw_paragraph.row_ratio = 1;
                break;
              case "SpaceMultiple":
              case "SpaceSpecify":
                raw_paragraph.row_ratio = /* style.LineSpacing["#text"] * 1 */ this.config_row_ratio;
                break;
            }
          }
          if (style.FirstLineIndent && style.FirstLineIndent["#text"]) {
            // 控制首行缩进
            raw_paragraph.indentation = style.FirstLineIndent["#text"] / 52;
          }
        }
      }
      if (!raw_paragraph.children.length) {
        const raw_text = newRawInitModel("text");
        raw_text.font_id = raw_style.id;
        raw_paragraph.children.push(raw_text);
      }
      raw_paragraph.id = this.uuid("para-template");
    }
  },
  /**
     * 处理都昌String类型数据
     * @param ele
     * @param parent_field
     * @param raw_paragraph
     */
  handleDcString (ele: any, parent_field: any, raw_paragraph: any) {
    const raw_text = newRawInitModel("text");
    raw_text.value = ele.Text["#text"];
    if (!raw_text.value) {
      return;
    }

    const style_index = ele["@attributes"].StyleIndex;
    // 判断是否已经删除
    const dcStyle = this.styles.dcStyles[style_index];
    // 如果已经删除不放入父节点中
    if (dcStyle && dcStyle.DeleterIndex && dcStyle.DeleterIndex["#text"]) {
      return;
    }

    // 可能出现模板中缺少样式的情况，所以判断存在之后赋值，否则走默认的样式数据
    if (style_index !== undefined && this.styles.styleMap[style_index]) {
      raw_text.font_id = this.styles.styleMap[style_index].id;
    } else {
      raw_text.font_id = this.styles.styleMap.default.id;
    }
    if (parent_field) {
      parent_field.children.push(raw_text);
    } else {
      raw_paragraph.children.push(raw_text);
    }
  },
  /**
     * 处理医学表达式
     */
  handleDcMedicalExpression (ele: any, parent_field: any, raw_paragraph: any) {
    const raw_image = newRawInitModel("image");
    raw_image.height = ele.Height["#text"] * 1 / this.devicePixelRatio / 1.5;
    raw_image.width = ele.Width["#text"] * 1 / this.devicePixelRatio / 1.5;
    if (ele.ExpressionStyle && ele.ExpressionStyle["#text"]) {
      const expression_style = ele.ExpressionStyle["#text"];
      switch (expression_style) {
        case "FourValuesGeneral":
          // 通用公式
          raw_image.meta.formula_type = 0;
          break;
        case "FourValues1":
          // 月经表达式2
          raw_image.meta.formula_type = 2;
          break;
        case "FourValues2":
          // 月经表达式3
          raw_image.meta.formula_type = 3;
          break;
        case "ThreeValues":
          // 月经表达式4
          raw_image.meta.formula_type = 4;
          break;
        case "Pupil":
          // 瞳孔图
          raw_image.meta.formula_type = 5;
          break;
        case "LightPositioning":
          // 光定位图
          raw_image.meta.formula_type = 6;
          break;
        case "FetalHeart":
          // 胎心图
          raw_image.meta.formula_type = 7;
          break;
        case "PermanentTeethBitmap":
          // 恒牙牙位图
          raw_image.meta.formula_type = 12;
          break;
        case "DeciduousTeech":
          // 乳牙牙位图
          raw_image.meta.formula_type = 13;
          break;
        case "PDTeech":
          // PD牙位图
          raw_image.meta.formula_type = 8;
          break;
        case "DiseasedTeethBotton":
          // 病变下牙牙位图
          raw_image.meta.formula_type = 9;
          break;
        case "DiseasedTeethTop":
          // 病变上牙牙位图
          raw_image.meta.formula_type = 10;
          break;
      }
    } else {
      // 月经表达式1
      raw_image.meta.formula_type = 1;
    }

    raw_image.src = this.config.formulaImage[raw_image.meta.formula_type];
    raw_image.meta.params = [];
    raw_image.width = 120;
    raw_image.height = 40;
    if (ele.Values && ele.Values.Value) {
      const values = ele.Values.Value;
      for (let j = 0; j < values.length; j++) {
        raw_image.meta.params.push(values[j]["#text"]);
      }
    }
    if (parent_field) {
      parent_field.children.push(raw_image);
    } else {
      raw_paragraph.children.push(raw_image);
    }
  },
  /**
     * 处理都昌数据中的styles
     * @param contentStyles
     */
  handleDcStyles (contentStyles: any) {
    const styleMap: any = {};
    const rawStyleMap: any = {};
    const dcStyles: any = {};
    // 将都昌样式数据转换为原始数据结构
    const dcStyle2RawData = (dcStyle: any) => {
      const rawStyle = newRawInitModel("style");
      rawStyle.id = this.uuid("font-template");
      rawStyle.family = dcStyle.FontName["#text"];
      if (dcStyle.Bold && dcStyle.Bold["#text"] === "true") {
        rawStyle.bold = true;
      }
      // 是否显示底部横线
      if (dcStyle.BorderBottom && dcStyle.BorderBottom["#text"] === "true") {
        rawStyle.underline = true;
      }
      // 是否显示下划线
      if (dcStyle.Underline && dcStyle.Underline["#text"] === "true") {
        rawStyle.underline = true;
      }
      if (dcStyle.Italic && dcStyle.Italic["#text"] === "true") {
        rawStyle.italic = true;
      }
      // 字体颜色
      if (dcStyle.Color && dcStyle.Color["#text"]) {
        rawStyle.color = dcStyle.Color["#text"];
      }
      // 背景色
      if (dcStyle.BackgroundColor && dcStyle.BackgroundColor["#text"]) {
        rawStyle.bgColor = dcStyle.BackgroundColor["#text"];
      }
      const font = this.fontSizeList.find((item: any) => item.option === dcStyle.FontSize["#text"]);
      if (font) {
        rawStyle.height = font.value;
      } else {
        rawStyle.height = this.default_font_style.height;
      }
      const index = dcStyle["@attributes"].Index;
      if (index === undefined) {
        styleMap.default = rawStyle;
      } else {
        styleMap[index] = rawStyle;
        dcStyles[index] = dcStyle;
      }
      rawStyleMap[rawStyle.id] = rawStyle;
    };

    dcStyle2RawData(contentStyles.Default);
    if (!Array.isArray(contentStyles.Styles.Style)) {
      contentStyles.Styles.Style = [contentStyles.Styles.Style];
    }
    for (let i = 0; i < contentStyles.Styles.Style.length; i++) {
      dcStyle2RawData(contentStyles.Styles.Style[i]);
    }
    this.styles = { styleMap, rawStyleMap, dcStyles };
  },
  handleTooManyLinebreak (rawData:any) {
    const str = JSON.stringify(rawData);
    const char = this.linebreak_mark;
    const escapedChar = char.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const regex = new RegExp(escapedChar, "g");
    const count = (str.match(regex) || []).length;
    if (count < 350) {
      return rawData;
    } else {
      const newStr = str.replace(new RegExp(escapedChar, "g"), "");
      return JSON.parse(newStr);
    }
  },
  /**
     * 加载到编辑器之后做的操作
     */
  afterInitRawHandle () {
    // 处理级联文本域
    const res1 = this.hideNestSelectFieldsSymbol();
    const res2 = this.handleCascadeField();
    const res3 = this.handleTableLineShowOrHide();
    if (res1 || res2 || res3) {
      this.editor.refreshDocument(true);
    }
  },
  /**
     * 处理表格线显示隐藏 要么都显示，要么都隐藏
     */
  handleTableLineShowOrHide () {
    // 处理表格线显示隐藏
    const tables = this.editor.root_cell.paragraph.filter((ele: any) => this.TypeJudgment.isTable(ele));
    if (!tables.length) {
      return false;
    }
    for (let i = 0; i < tables.length; i++) {
      const table = tables[i];
      if (this.needShowLineTables[table.id]) {
        // 若干单元格
        table.children.forEach((cell: any) => {
          cell.toggleLineShowHide(null, true);
        });
      } else {
        // 若干单元格
        table.children.forEach((cell: any) => {
          cell.toggleLineShowHide(null, false);
        });
      }
    }
    return true;
  },
  /**
     * 处理级联文本域
     */
  handleCascadeField () {
    const editor = this.editor;
    const fieldValText = this.selectFieldsListValText;
    const casInfo = this.cascadeFieldInfo;
    if (this.isEmptyObj(casInfo) || this.isEmptyObj(fieldValText)) {
      return false;
    }
    let res: boolean = false;
    const mkFieldIds:any = [];
    const needRemoveFields:any[] = [];
    for (const item in casInfo) {
      let visExpStr = casInfo[item];
      visExpStr = visExpStr.replace(/\[/g, "");
      visExpStr = visExpStr.replace(/\]/g, "");
      visExpStr = visExpStr.replace(/'/g, "");
      visExpStr = visExpStr.replace(/ /g, "");
      const visExpArr = visExpStr.split("=");
      if (visExpArr.length === 2) {
        const c_field = editor.getFieldById(item); // 子文本域
        if (!c_field) {
          continue;
        }
        if (!c_field.name) {
          c_field.name = c_field.id;
        }

        const p_field = editor.getFieldById(visExpArr[0]);
        if (!p_field) {
          // 如果主控文本域不存在，当前文本域又是隐藏状态，应该删除当前文本域
          if (this.hiddenFieldIds[c_field.id]) {
            needRemoveFields.push(c_field);
          }
          continue;
        }
        if (!p_field.name) {
          p_field.name = p_field.id;
        }
        if (!fieldValText[visExpArr[0]]) {
          // 此种情况应该是删除了父文本域的联动效果
          continue;
        }
        const text = fieldValText[visExpArr[0]][visExpArr[1]];
        const listOne = p_field.cascade_list.find((it: any) => {
          return it.text === text;
        });
        if (listOne) {
          listOne.show_field_names.push(c_field.name);
        } else {
          p_field.cascade_list.push({
            text: text,
            show_field_names: [c_field.name]
          });
        }
        mkFieldIds.push(p_field.id);
        res = true;
      }
    }
    if (needRemoveFields.length) {
      editor.removeFields(needRemoveFields);
    }
    while (mkFieldIds.length) {
      const fId:string = mkFieldIds.pop();
      const f:XField = editor.getFieldById(fId);
      // 有可能在上一步删除时已经把外层文本域删掉了
      if (!f) {
        continue;
      }
      const rawData = editor.getRawData();
      editor.showOrHideField(f);
      let isNo = false;
      for (let i = 0, len = mkFieldIds.length; i < len; i++) {
        if (!editor.getFieldById(mkFieldIds[i])) {
          isNo = true;
        }
      }
      if (isNo) {
        editor.reInitRaw(rawData);
        editor.update();
        mkFieldIds.unshift(fId);
      }
    }
    return res;
  },
  /**
     选择框文本域如果有父文本域，并且父文本域中只有这个选择框文本域，则将选择框文本域边框隐藏
     */
  hideNestSelectFieldsSymbol () {
    const editor = this.editor;
    const all_select_fields = editor.getFieldsByType("select");
    let res: boolean = false;
    for (let i = 0; i < all_select_fields.length; i++) {
      const field = all_select_fields[i];
      if (field.parent && field.parent.children.length === 1) {
        field.start_symbol = "";
        field.end_symbol = "";
        res = true;
      }
    }
    return res;
  }

};
