<template>
  <div class="menstruation4">
    <div class="row_menstruation4">
      <div class="row-left">
        <div>初潮年龄</div>
        <a-input type="text" class="input" v-model="meta.params[0]" />
      </div>
    </div>
    <div class="slash_menstruation4"></div>
    <div class="row_menstruation4">
      <div class="row-right-down">
        <div>经期（天）</div>
        <a-input type="text" class="input" v-model="meta.params[1]" />
      </div>
      <div class="horizontal_menstruation4"></div>
      <div class="row-right-down">
        <div>周期（天）</div>
        <a-input type="text" class="input" v-model="meta.params[2]" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "menstruation4",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style scoped>
.menstruation4 {
  width: 100%;
  height: 100%;
  display: flex;
}
.horizontal_menstruation4 {
  height: 5px;
  width: 176px;
  margin-left: 27px;
  margin-top: 20px;
  background-color: black;
}
.slash_menstruation4 {
  height: 30px;
  width: 30px;
  margin-top: 65px;
  box-sizing: border-box;
  background: linear-gradient(
    120deg,
    transparent 49.5%,
    #000 49.5%,
    #000 53.5%,
    transparent 50.5%
  );
}
.row_menstruation4 {
  text-align: center;
}
.row-left {
  width: 230px;
  margin-top: 40px;
}
.row-right {
  width: 30px;
}
.row-right-down {
  width: 230px;
}
.input {
  margin-top: 5px;
  width: 170px;
}
</style>
