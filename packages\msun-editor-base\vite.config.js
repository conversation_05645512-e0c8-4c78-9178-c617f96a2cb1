import { resolve } from "path";
import { defineConfig } from "vite";
import { visualizer } from "rollup-plugin-visualizer";
// import dts from "vite-plugin-dts";
const args = process.argv.slice(2);
const debugMode = args[1] && args[1] === "--mode=debug";

export default defineConfig({
  server: {
    port: 8088,
    host: "0.0.0.0",
    // 配置解决无法在控制台中对链接的包添加断点的问题
    fs: {
      strict: false
    },
    hmr: true
  },
  build: {
    target: "es2017",
    sourcemap: true,
    rollupOptions: {
      external: [
      ],
      output: {
        exports: "named",
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
        }
      }
    },
    lib: {
      entry: resolve(__dirname, "src/index.ts"),
      name: "initEditor",
      fileName: "initEditor"
    },
    minify: !debugMode
  },
  plugins: [
    visualizer({ open: false })
  ]
});
