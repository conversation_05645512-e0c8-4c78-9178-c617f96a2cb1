<template>
  <div class="normal_normal">
    <div class="row_normal">
      <div class="row-left">
        <div>第一个值</div>
        <a-input type="text" class="input_normal" v-model="meta.params[0]" />
      </div>
      <div class="vertical_normal"></div>
      <div class="row-right">
        <div>第二个值</div>
        <a-input type="text" class="input_normal" v-model="meta.params[1]" />
      </div>
    </div>
    <div class="horizontal_normal"></div>
    <div class="row_normal">
      <div class="row-left-down">
        <div>第三个值</div>
        <a-input type="text" class="input_normal" v-model="meta.params[2]" />
      </div>
      <div class="vertical_normal"></div>
      <div class="row-right-down">
        <div>第四个值</div>
        <a-input type="text" class="input_normal" v-model="meta.params[3]" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "normal",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style scoped>
.normal_normal {
  width: 100%;
  height: 100%;
}
.horizontal_normal {
  height: 5px;
  width: 100%;
  background-color: black;
}
.vertical_normal {
  height: 80px;
  width: 5px;
  background-color: rgb(85, 81, 81);
}
.row_normal {
  display: flex;
  text-align: center;
}
.row-left {
  width: 242.5px;
}
.row-right {
  width: 242.5px;
}
.row-left-down,
.row-right-down {
  width: 242.5px;
  margin-top: 12px;
}

.input_normal {
  margin-top: 10px;
  width: 170px;
}
</style>
