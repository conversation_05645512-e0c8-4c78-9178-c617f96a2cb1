<template>
  <div class="toothBitmap_toothBitmap">
    <a-radio-group v-model="radioValue">
      <a-radio :value="1" @click="permanentTeeth(radioValue)">
        全部恒牙</a-radio
      >
      <a-radio :value="2" @click="deciduousTeeth(radioValue)">
        全部乳牙</a-radio
      >
    </a-radio-group>
    <div class="direction_toothBitmap">上</div>
    <div class="toothBitmap-quadrant-top">
      <div class="left_toothBitmap">
        <div class="quadrant-right">
          <div
            v-for="(item, index) in meta.params[0] || datas[0]"
            :key="index"
            :class="
              item.click || permanent ? 'button-click' : 'button_toothBitmap'
            "
            @click="buttonClick(item)"
          >
            {{ item.value }}
          </div>
        </div>
        <div class="quadrant-right">
          <div
            v-for="(item, index) in meta.params[1] || datas[1]"
            :key="index"
            :class="
              item.click || deciduous ? 'button-click' : 'button_toothBitmap'
            "
            @click="buttonClick(item)"
          >
            {{ item.value }}
          </div>
        </div>
      </div>
      <div class="right_toothBitmap">
        <div class="quadrant-left">
          <div
            v-for="(item, index) in meta.params[2] || datas[2]"
            :key="index"
            :class="
              item.click || permanent ? 'button-click' : 'button_toothBitmap'
            "
            @click="buttonClick(item)"
          >
            {{ item.value }}
          </div>
        </div>
        <div class="quadrant-left">
          <div
            v-for="(item, index) in meta.params[3] || datas[3]"
            :key="index"
            :class="
              item.click || deciduous ? 'button-click' : 'button_toothBitmap'
            "
            @click="buttonClick(item)"
          >
            {{ item.value }}
          </div>
        </div>
      </div>
    </div>
    <div class="toothBitmap-quadrant-bottom">
      <div class="left_toothBitmap">
        <div class="quadrant-right">
          <div
            v-for="(item, index) in meta.params[4] || datas[4]"
            :key="index"
            :class="
              item.click || permanent ? 'button-click' : 'button_toothBitmap'
            "
            @click="buttonClick(item)"
          >
            {{ item.value }}
          </div>
        </div>
        <div class="quadrant-right">
          <div
            v-for="(item, index) in meta.params[5] || datas[5]"
            :key="index"
            :class="
              item.click || deciduous ? 'button-click' : 'button_toothBitmap'
            "
            @click="buttonClick(item)"
          >
            {{ item.value }}
          </div>
        </div>
      </div>
      <div class="right_toothBitmap">
        <div class="quadrant-left">
          <div
            v-for="(item, index) in meta.params[6] || datas[6]"
            :key="index"
            :class="
              item.click || permanent ? 'button-click' : 'button_toothBitmap'
            "
            @click="buttonClick(item)"
          >
            {{ item.value }}
          </div>
        </div>
        <div class="quadrant-left">
          <div
            v-for="(item, index) in meta.params[7] || datas[7]"
            :key="index"
            :class="
              item.click || deciduous ? 'button-click' : 'button_toothBitmap'
            "
            @click="buttonClick(item)"
          >
            {{ item.value }}
          </div>
        </div>
      </div>
    </div>
    <div class="direction_toothBitmap">下</div>
    <div class="line-vertical"></div>
    <div class="line-horizontal"></div>
  </div>
</template>

<script>
export default {
  name: "toothBitmap",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      radioValue: 0,
      permanent: false,
      deciduous: false,
      datas: [
        [
          { value: 8, click: false },
          { value: 7, click: false },
          { value: 6, click: false },
          { value: 5, click: false },
          { value: 4, click: false },
          { value: 3, click: false },
          { value: 2, click: false },
          { value: 1, click: false },
        ],
        [
          { value: "E", click: false },
          { value: "D", click: false },
          { value: "C", click: false },
          { value: "B", click: false },
          { value: "A", click: false },
        ],
        [
          { value: 1, click: false },
          { value: 2, click: false },
          { value: 3, click: false },
          { value: 4, click: false },
          { value: 5, click: false },
          { value: 6, click: false },
          { value: 7, click: false },
          { value: 8, click: false },
        ],
        [
          { value: "A", click: false },
          { value: "B", click: false },
          { value: "C", click: false },
          { value: "D", click: false },
          { value: "E", click: false },
        ],
        [
          { value: 8, click: false },
          { value: 7, click: false },
          { value: 6, click: false },
          { value: 5, click: false },
          { value: 4, click: false },
          { value: 3, click: false },
          { value: 2, click: false },
          { value: 1, click: false },
        ],
        [
          { value: "E", click: false },
          { value: "D", click: false },
          { value: "C", click: false },
          { value: "B", click: false },
          { value: "A", click: false },
        ],
        [
          { value: 1, click: false },
          { value: 2, click: false },
          { value: 3, click: false },
          { value: 4, click: false },
          { value: 5, click: false },
          { value: 6, click: false },
          { value: 7, click: false },
          { value: 8, click: false },
        ],
        [
          { value: "A", click: false },
          { value: "B", click: false },
          { value: "C", click: false },
          { value: "D", click: false },
          { value: "E", click: false },
        ],
      ],
    };
  },
  watch: {
    datas: {
      handler(e) {
        this.meta.params = e;
      },
      deep: true,
    },
  },
  methods: {
    permanentTeeth(e) {
      if (e === 1) {
        this.radioValue = 0;
      } else {
        this.radioValue = 1;
      }
      this.permanent = !this.permanent;
      this.deciduous = false;
      if (this.permanent) {
        this.datas[0].forEach((e) => {
          e.click = true;
        });
        this.datas[2].forEach((e) => {
          e.click = true;
        });
        this.datas[4].forEach((e) => {
          e.click = true;
        });
        this.datas[6].forEach((e) => {
          e.click = true;
        });
        this.datas[1].forEach((e) => {
          e.click = false;
        });
        this.datas[3].forEach((e) => {
          e.click = false;
        });
        this.datas[5].forEach((e) => {
          e.click = false;
        });
        this.datas[7].forEach((e) => {
          e.click = false;
        });
      } else {
        this.datas[0].forEach((e) => {
          e.click = false;
        });
        this.datas[2].forEach((e) => {
          e.click = false;
        });
        this.datas[4].forEach((e) => {
          e.click = false;
        });
        this.datas[6].forEach((e) => {
          e.click = false;
        });
      }
    },
    deciduousTeeth(e) {
      if (e === 2) {
        this.radioValue = 0;
      } else {
        this.radioValue = 2;
      }
      this.deciduous = !this.deciduous;
      this.permanent = false;
      if (this.deciduous) {
        this.datas[1].forEach((e) => {
          e.click = true;
        });
        this.datas[3].forEach((e) => {
          e.click = true;
        });
        this.datas[5].forEach((e) => {
          e.click = true;
        });
        this.datas[7].forEach((e) => {
          e.click = true;
        });
        this.datas[0].forEach((e) => {
          e.click = false;
        });
        this.datas[2].forEach((e) => {
          e.click = false;
        });
        this.datas[4].forEach((e) => {
          e.click = false;
        });
        this.datas[6].forEach((e) => {
          e.click = false;
        });
      } else {
        this.datas[1].forEach((e) => {
          e.click = false;
        });
        this.datas[3].forEach((e) => {
          e.click = false;
        });
        this.datas[5].forEach((e) => {
          e.click = false;
        });
        this.datas[7].forEach((e) => {
          e.click = false;
        });
      }
    },
    buttonClick(e) {
      e.click = !e.click;
    },
  },
};
</script>
<style scoped>
.toothBitmap_toothBitmap {
  width: 100%;
  height: 260px;
  background-color: #fff;
  position: relative;
}
.toothBitmap-quadrant-top {
  width: 490px;
  height: 70px;
  margin-bottom: 30px;
  margin-top: 10px;
  display: flex;
}
.toothBitmap-quadrant-bottom {
  width: 490px;
  height: 70px;
  margin-bottom: 5px;
  display: flex;
}
.quadrant-left {
  display: flex;
  justify-content: flex-start;
}
.quadrant-right {
  display: flex;
  justify-content: flex-end;
}
.button_toothBitmap {
  width: 20px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border: 1px solid rgb(214, 214, 214);
  border-radius: 3px;
  margin-left: 4px;
  margin-right: 4px;
  margin-bottom: 5px;
}
.button-click {
  width: 20px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border: 1px solid rgb(214, 214, 214);
  background-color: rgb(64, 169, 255);
  color: #fff;
  border-radius: 3px;
  margin-left: 4px;
  margin-right: 4px;
  margin-bottom: 5px;
}
.direction_toothBitmap {
  text-align: center;
}
.left_toothBitmap {
  width: 230px;
  height: 70px;
  margin-right: 15px;
}
.right_toothBitmap {
  width: 230px;
  height: 70px;
  margin-left: 15px;
}
.line-horizontal {
  position: absolute;
  height: 2px;
  width: 490px;
  top: 135px;
  background-color: rgb(214, 214, 214);
}
.line-vertical {
  position: absolute;
  height: 180px;
  width: 2px;
  left: 245px;
  top: 45px;
  background-color: rgb(214, 214, 214);
}
</style>
