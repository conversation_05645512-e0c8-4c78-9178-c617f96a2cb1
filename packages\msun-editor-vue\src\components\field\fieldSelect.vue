<template>
  <div>
    <div
      v-show="focusShow"
      :id="'field_select' + editorId"
      class="field_select_class"
      :style="{
        position: 'absolute',
        width: '200px',
        left: position.left + 'px',
        top: position.top + 'px',
      }"
    >
      <a-select
        ref="field_select"
        dropdownClassName="xeditor-input-up"
        class="editor_select_css_field_select"
        :show-search="showSearch"
        v-model="value"
        :mode="mode"
        :auto-focus="focusShow"
        :open="focusShow"
        :show-arrow="false"
        placeholder="检索"
        :allowClear="true"
        option-filter-prop="children"
        :style="{
          width: calcWidth,
        }"
        :filter-option="filterOption"
        @select="handleSelect"
        @change="handleChange"
        @search="handleSearch"
        :getPopupContainer="
          (triggerNode) => {
            return triggerNode.parentNode || document.body;
          }
        "
      >
        <a-select-option
          class="option"
          v-for="(item, index) in data"
          :key="index"
          :value="item.text.toString()"
          :formulaValue="item.value"
          :text="item.text.toString()"
          :code="item.code && item.code.toString()"
          :title="item.text.toString()"
        >
          {{ item.text }}
        </a-select-option>
      </a-select>
    </div>
    <van-action-sheet
      v-if="isMobile2"
      v-model="mobileSelectShow"
      cancel-text="取消"
      :actions="actions"
      @select="onSelect"
    />
  </div>
</template>

<script>
import { arrUnique } from "../../assets/js/utils";

export default {
  name: "fieldSelect",
  data() {
    return {
      numberSelectPrefix: "common_number_",
      value: undefined,
      focusShow: false,
      selectPanelStatus: 0, //0关闭状态 1激活但未展开状态 2展开状态
      activeFieldId: 0,
      data: [],
      mode: "",
      showSearch: false,
      select_data_temp: {}, //用于缓存已经查询出的下拉项数据
      mobileSelectShow: false,
      actions: [],
      isMobile2: false,
      calcWidth: "200px",
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    position: {
      type: Object,
      default: () => {},
    },
    editorId: {
      type: String,
      default: "",
    },
    separatorGroups: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    show(val) {
      if (this.editor.editor.isMobileTerminal()) {
        this.isMobile2 = true;
      }
      let sourceData = [];
      const fieldInfo = this.editor.editor._curFieldInfo;
      if (fieldInfo.multi_select) {
        this.mode = "multiple";
      } else {
        this.mode = "";
      }
      if (val) {
        this.showSearch = true;
        this.selectPanelStatus = 1;
        this.activeFieldId = fieldInfo ? fieldInfo.id : 0;
        let source_id = fieldInfo.source_id;
        if (source_id) {
          const result = this.select_data_temp[source_id];
          if (result) {
            sourceData = result;
          } else {
            const handleResult = (dataKey, res_list, noCache) => {
              let handleSourceData;
              if (res_list && res_list.length && res_list instanceof Array) {
                handleSourceData = res_list;
              } else {
                //当未根据id检索到内容，并且文本域本身携带源列表数据时展示列表数据
                handleSourceData = fieldInfo.source_list;
              }
              if (!noCache) {
                this.select_data_temp[dataKey] = handleSourceData;
              }
              return handleSourceData;
            };
            const resolve = (resData, noCache) => {
              if (
                this.selectPanelStatus === 1 &&
                this.activeFieldId === fieldInfo.id
              ) {
                sourceData = handleResult(source_id, resData, noCache);
                // 因为解决异步问题，所以要在resolve中调用
                this.showSelectPanelAfterGetData(fieldInfo, sourceData);
              }
            };
            const res_list = this.editor.editor.event.emit(
              "fieldSelectItemsRequest",
              source_id,
              resolve,
              fieldInfo
            );
            if (res_list instanceof Array) {
              resolve(res_list, true);
            }
            return;
          }
        } else {
          //当未根据id检索到内容，并且文本域本身携带源列表数据时展示列表数据
          sourceData = fieldInfo.source_list;
        }
        this.showSelectPanelAfterGetData(fieldInfo, sourceData);
      } else {
        this.showSearch = false;
        this.selectPanelStatus = 0;
        this.activeFieldId = fieldInfo ? fieldInfo.id : 0;
        this.focusShow = false;
        this.$nextTick(() => {
          //清空检索框内容
          if (!fieldInfo.multi_select) {
            const element = document.getElementById(
              "field_select" + this.editorId
            );
            const searchClearBtn = element.getElementsByClassName(
              "ant-select-selection__clear"
            );
            if (searchClearBtn[0]) {
              searchClearBtn[0].click();
            }
          }
        });
      }
    },
  },
  methods: {
    onSelect(item) {
      // 默认情况下，点击选项时不会自动关闭菜单
      // 可以通过 close-on-click-action 属性开启自动关闭
      if (this.editor.editor._curFieldInfo.multi_select) return;
      this.$emit("select", item.text, "select", false, item.formulaValue);
      this.mobileSelectShow = false;
    },
    handleSearch(v) {
      if (v === "") {
        this.data = [...this.data];
      }
    },
    getTextWidth(text) {
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");
      context.font = "14px Arial"; // 根据实际字体设置
      return context.measureText(text).width;
    },
    showSelectPanelAfterGetData(fieldInfo, sourceData) {
      //处理一下回填数据，保证可以正确识别
      const text = fieldInfo.text;
      if (fieldInfo.multi_select) {
        const map = new Map();
        this.separatorGroups.forEach((item) => {
          map.set(item.index, text.split(item.separator));
        });
        this.value = map.get(fieldInfo.separator);
      }
      this.data = arrUnique(sourceData, "text");
      const { editor } = this.editor;
      if (editor.isMobileTerminal()) {
        const data = this.data;
        if (Array.isArray(data)) {
          this.actions = [];
          for (let i = 0; i < data.length; i++) {
            this.actions.push({
              ...data[i],
              name: data[i].text,
            });
          }
          this.actions.length > 0 && (this.mobileSelectShow = true);
        }
      } else {
        const elements = document
          .getElementById("field_select" + this.editorId)
          .getElementsByClassName("ant-select-selection");
        const searchInput = elements[0].getElementsByClassName(
          "ant-select-search__field"
        );
        // 列表项目大于6条则显示检索框，否则隐藏
        if (this.data.length > 7 && !fieldInfo.multi_select) {
          elements[0].style.display = "block";
        } else {
          // eslint-disable-next-line vue/no-mutating-props
          this.position.top = this.position.top - 20;
          elements[0].style.display = "none";
        }
        this.selectPanelSeatSet(this.data);
        this.focusShow = true;

        // 修改宽度 ↓
        let maxWidth = 200;
        this.data.forEach((item) => {
          const width = this.getTextWidth(item.text);
          maxWidth = Math.max(maxWidth, width);
        });

        this.calcWidth = Math.min(maxWidth, 400) + "px";
        // 修改宽度 ↑

        this.$nextTick(() => {
          this.selectPanelStatus = 2;
          searchInput[0].focus();
          searchInput[0].onkeyup = null;
          searchInput[0].onkeyup = (e) => {
            //回车时直接使用文本框中的内容
            if (e.keyCode === 13) {
              console.log("走这个");
              this.$emit("select", searchInput[0].value, "select");
            }
          };
          searchInput[0].onkeydown = null;
          searchInput[0].onkeydown = (e) => {
            //回车时直接使用文本框中的内容
            if (e.keyCode === 27) {
              console.log("还是这个");
              this.$emit("select", "Quit");
            }
            if (e.keyCode === 8 && !searchInput[0].value) {
              this.editor.editor.focus();
            }
          };
        });
      }
    },
    //下拉面板位置调整
    selectPanelSeatSet(data) {
      // eslint-disable-next-line vue/no-mutating-props
      this.position.top += 50; //为与父组件统一，补充父组件减掉的，放置弹出时滚动条闪动
      const { selection, viewScale } = this.editor.editor;
      const view_height =
        this.editor.editor.init_canvas.clientHeight +
        document.getElementById(this.editorId).offsetTop;
      const select_div_height = data.length < 8 ? data.length * 32 : 32 * 8; // 32 是每一项的高度 8是总共显示8个
      if (this.position.top + select_div_height + 32 > view_height) {
        // eslint-disable-next-line vue/no-mutating-props
        this.position.top -=
          select_div_height +
          selection.getFocusRow().height * viewScale +
          (data.length < 8 ? 15 : 38);
      }
    },
    filterOption(input, option) {
      return (
        option.data.attrs.text.toLowerCase().indexOf(input.toLowerCase()) >=
          0 ||
        (option.data.attrs.code &&
          option.data.attrs.code.toLowerCase().indexOf(input.toLowerCase()) >=
            0)
      );
    },

    handleSelect(index, option) {
      // 文本域的下拉列表点击选项的时候走这个
      if (this.editor.editor._curFieldInfo.multi_select) return;
      this.$emit(
        "select",
        option.data.attrs.text,
        "select",
        false,
        option.data.attrs.formulaValue,
        option.data.attrs.code
      );
    },
    handleChange(value) {
      if (!this.editor.editor._curFieldInfo.multi_select) return;
      for (let i = 0; i < value.length; i++) {
        if (!value[i]) {
          value.splice(i, 1);
        }
      }
      const separator = this.editor.editor._curFieldInfo.separator;
      const map = new Map();
      this.separatorGroups.forEach((item) => {
        map.set(item.index, value.join(item.separator));
      });
      let val = map.get(separator);
      this.$emit("select", val, "select", true);
    },
  },
};
</script>
<style>
.field_select_class .ant-select-open .ant-select-selection,
.field_select_class .ant-select-focused .ant-select-selection,
.field_select_class .ant-select-selection:focus,
.field_select_class .ant-select-selection:active {
  box-shadow: 0 0 0 1px rgba(144, 141, 136, 0.2);
  border-color: #d9d9d9;
}
.field_select_class .ant-select-selection:hover {
  border-color: #d9d9d9;
}
.field_select_class .option {
  white-space: normal;
  word-wrap: break-word; /* 强制长单词断行 */
}
</style>
<style lang="less" scoped>
.editor_select_css_field_select {
  ::v-deep input:focus {
    border: none;
    box-shadow: none;
  }
}

.xeditor-input-up {
  z-index: 99999 !important;
}
</style>
