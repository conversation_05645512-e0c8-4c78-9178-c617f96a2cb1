import Editor from "../../../msun-lib-editor-common/src/editor/Editor";
import { isImage } from "../../../msun-lib-editor-common/src/editor/Helper";
import { isCharacter, isRow, isTable } from "../../../msun-lib-editor-common/src/editor/Utils";

export const M2W = {
  getType (c: any) {
    if (c.field_id) return "field";
    if (isCharacter(c)) return "text";
    if (isImage(c)) return "image";
  },
  getOrigin (obj: any) {
    let origin = obj;
    while (origin.parent) {
      origin = origin.parent;
    }
    return origin;
  },
  getDescription (editor: Editor) {
    // 一切以快速完成为目标
    const descripton:any = []; // 二维数组 里边的每一个数组 代表一行内容 一行里边的每一个对象代表一个元素的类型
    // 我要拿一个口袋往后收同类型的内容 遇到不是同类型的 我就收口袋 然后换新口袋 这个口袋就是 obj
    let obj: any = {}; // 记录同类型的元素
    const fieldStack: any[] = [];
    const arr = [];
    for (let rowNum = 0; rowNum < editor.current_cell.children.length; rowNum++) {
      const content = editor.current_cell.children[rowNum];
      let row = [];
      if (isRow(content)) {
        for (let i = 0; i < content.children.length; i++) {
          const c = content.children[i];
          if (c.field_id) {
            // 遇到文本域 如果文本域在一行 咋都好说
            // 如果文本域不在一行了 我应该怎么记呢 先记 rowNum 后边再想怎么处理
            if (c.field_position === "start") {
              const field = editor.getFieldById(c.field_id)!;
              if (fieldStack.length) {
                // 如果堆栈里边已经有了 就说明这是个嵌套文本域的开始
                // 创建个新的
                arr.push(obj);
                const newObj = {
                  parent: obj
                };
                obj.children.push(newObj);
                obj = newObj;
              }
              obj.type = field.type;
              obj.startSymBol = c.value;
              obj.children = [{ type: "text", value: c.value, rowNum }];
              fieldStack.push(field);
            } else if (c.field_position === "end") {
              obj.children.push({ type: "text", value: c.value, rowNum });
              fieldStack.pop();
              if (!fieldStack.length) {
                // 完整的文本域结束了
                const origin = this.getOrigin(obj);
                if (origin.block) {
                  descripton.push([origin]);
                  row = [];
                } else {
                  row.push(origin);
                }
                obj = {}; // 一个完整的文本域结束了 重置 obj
              } else {
                obj = obj.parent;
              }
            } else {
              obj.children.push({ type: "text", value: c.value, rowNum });
            }
          } else {
            // 如果不是文本域 id 的话
            row.push({
              type: "text",
              value: c.value
            });
          }

          if (i === content.children.length - 1) {
            if (fieldStack.length) {
              // 这一行都已经循环完了 堆栈还没有清空 说明这个文本域跨行了
              const origin = this.getOrigin(obj);
              origin.block = true;
            } else {
              descripton.push(row);
              row = [];
            }
          }
        }
      } else if (isTable(content)) {
        console.log("这是 table");
      }
    }
    return descripton;
  }
};
