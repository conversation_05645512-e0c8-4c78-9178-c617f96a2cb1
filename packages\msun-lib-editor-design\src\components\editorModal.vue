<template>
  <modal title="众阳编辑器" :visible="visible" @close="cancel" @resize="resize">
    <div id="editorGrfDom" :style="editorStyle">
      <msun-editor-vue
        style="width: 100%; height: 100%"
        @init="init"
        :upConfig="config"
      ></msun-editor-vue>
    </div>
  </modal>
</template>

<script>
import modal from "../components/modal.vue";
import msunEditorVue from "msun-editor-vue";
import config from "../js/config";
const modalConfig = JSON.parse(JSON.stringify(config));
modalConfig.showDesignFloatingMenu = false;
modalConfig.systemConfig.source = "modalEditor";
modalConfig.systemConfig.editor_padding_top = 5;
export default {
  name: "editorModal",
  data() {
    return {
      visible: false,
      config: modalConfig,
      editorStyle: {
        width: "100%",
        height: "450px",
      },
    };
  },
  props: ["show"],
  components: { modal, msunEditorVue },
  watch: {
    show: {
      handler(val) {
        this.visible = val;
        if (this.instance) {
          if (!val) {
            // 每次关闭记录上次打开内容
            localStorage.setItem(
              "EditorModalSaveData",
              JSON.stringify(this.instance.editor.getRawData())
            );
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    bindEvent() {
      this.instance.editor.event.on("reInitRaw", () => {
        this.instance.editor.adaptSize();
        // 每次关闭记录上次打开内容
        localStorage.setItem(
          "EditorModalSaveData",
          JSON.stringify(this.instance.editor.getRawData())
        );
      });
    },
    init(instance) {
      this.instance = instance;
      this.bindEvent();
      const raw = localStorage.getItem("EditorModalSaveData");
      if (raw) {
        this.instance.editor.reInitRawByConfig(raw);
        this.instance.editor.refreshDocument();
      }
    },
    resize(height) {
      requestAnimationFrame(() => {
        this.editorStyle.height = parseInt(height) - 50 + "px";
        this.instance.editor.adaptSize();
      });
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style lang="less">
#editorGrfDom .editor-content-x {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #c8c8c8;
    border-radius: 6px;
  }
  /* 鼠标悬停时滑块样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #c0c0c0;
  }
  &::-webkit-scrollbar-track {
    background-color: white;
    -webkit-box-shadow: inset 0 0 4px rgba(100, 100, 100, 0.01);
  }
}
</style>
