<template>
  <modal
    title="水印"
    :show="show"
    @cancel="cancel"
    @submit="submit"
    :width="modal_width"
  >
    <div style="display: flex; line-height: 32px">
      <div class="text">文字：</div>
      <a-input v-model="text" style="width: 405px" />
    </div>
    <br />
    <div style="display: flex; line-height: 32px">
      <div class="half-div">
        <div class="text">方向：</div>
        <a-select
          dropdownClassName="xeditor-input-up"
          style="width: 150px"
          :value="choiceDirection"
          @change="changeDirection"
        >
          <a-select-option
            v-for="(item, index) in direction"
            :key="index"
            :value="item.type"
            :title="item.name"
          >
            {{ item.name }}
          </a-select-option>
        </a-select>
      </div>
      <div class="half-div" style="margin-left: 32px">
        <div class="text">字体：</div>
        <a-select
          dropdownClassName="xeditor-input-up"
          style="width: 150px"
          default-value="宋体"
          @change="changeFontFamily"
        >
          <a-select-option
            v-for="(item, index) in fontFamily"
            :key="index"
            :value="item"
            :title="item"
          >
            {{ item }}
          </a-select-option>
        </a-select>
        <br />
      </div>
    </div>
    <br />
    <div style="display: flex; line-height: 32px">
      <div class="half-div">
        <div class="text">字号：</div>
        <a-input-number v-model="fontSize" style="width: 150px" />
        <br />
      </div>
      <div class="half-div" style="margin-left: 32px">
        <div class="text">颜色：</div>
        <colorPicker
          v-model="color"
          v-on:change="handleChangeColor"
        ></colorPicker>
        <br />
      </div>
    </div>
    <br />

    <div style="display: flex">
      <div class="text">展示类型：</div>
      <a-checkbox
        :checked="isChecked(item)"
        :label="item"
        v-for="(item, index) in module"
        :key="index"
        @change="handleChange(item)"
        >{{ item.name }}</a-checkbox
      >
      <br />
    </div>
  </modal>
</template>

<script>
import modal from "./common/modal.vue";
export default {
  name: "waterMark",
  data() {
    return {
      modal_width: 500,
      text: "水印测试",
      checkedList: ["pageWrite"],
      choiceDirection: "left",
      choiceFontFamily: "宋体",

      direction: [
        { name: "左下倾斜", type: "left" },
        { name: "右下倾斜", type: "right" },
        { name: "水平", type: "horizontal" },
      ],
      fontSize: 16,
      module: [
        { name: "页面显示", type: "pageWrite" },
        { name: "打印预览", type: "printView" },
        { name: "纸张打印", type: "printPaper" },
      ],
      fontFamily: ["宋体", "黑体", "幼圆", "楷体", "隶书", "仿宋"],
      color: "#7F7F7F",
    };
  },
  components: {
    modal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {},
  methods: {
    isChecked(option) {
      return this.checkedList.includes(option.type);
    },
    handleChange(option) {
      if (this.checkedList.includes(option.type)) {
        this.checkedList = this.checkedList.filter(
          (item) => item !== option.type
        );
      } else {
        this.checkedList.push(option.type);
      }
    },
    changeDirection(val) {
      this.choiceDirection = val;
    },
    changeFontFamily(val) {
      this.choiceFontFamily = val;
    },
    handleChangeColor(e) {
      this.color = e;
    },
    submit() {
      const params = {
        text: this.text,
        direction: this.choiceDirection,
        module: this.checkedList,
        font: {
          fontSize: this.fontSize,
          fontFamily: this.choiceFontFamily,
          opacity: 0.5,
          color: this.color,
        },
      };
      this.$emit("submit", params);
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style scoped>
.text {
  width: 72px;
  text-align: center;
}

.half-div {
  display: flex;
  width: 50%;
}

.m-colorPicker {
  position: relative;
}

.half-div /deep/.colorBtn {
  width: 150px;
  height: 25px;
  border-radius: 4px;
  margin-top: 3px;
}
</style>
