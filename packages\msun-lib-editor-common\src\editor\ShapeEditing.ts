
// 只是编辑模式下的 小方块
import Renderer from "./Renderer";
export default class ShapeEditing {
  x:number;
  y:number;
  type:string;
  color:string;
  constructor (x: number, y: number, type:string = "line", color:string = "rgb(150,150,150)") {
    this.x = x;
    this.y = y;
    this.type = type;
    this.color = color;
  }

  /**
   * canvas画小方块
   */
  draw () {
    if (this.type === "line") {
      Renderer.drawArc(
        this.x, this.y, 4, 0, Math.PI * 2, "rgb(33,122,201)", 1);
    } else if (this.type === "circle") {
      const width = 7;
      Renderer.draw_stroke_rect(this.x - width / 2, this.y - width / 2, width, width, "rgb(33,122,201)");
    }
  }
}
