# msun-editor-vue

> 基于msun-editor与vue构建的一款专用于病历书写的编辑器。具备了基础的操作ui。

### 启动流程

- 切换npm源
```
npm config set registry http://nexus.chis.msunsoft.com:8081/repository/npm-group/
```

- 安装依赖
```
npm install
```

- 启动项目
```
npm run serve
```

### 发布流程

- 升级版本
```
// dev分支
npm version patch // 升级末尾版本号，用于补丁修复。
npm version minor // 升级中间版本号，用于功能新增
npm version major // 升级头部版本号，用于无法向下兼容的升级调整
```
- 将dev分支合并到master并将master分支推送到远程
- gitlab-ci自动化部署自动完成打包发版流程
