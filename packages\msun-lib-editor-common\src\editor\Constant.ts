export const TABLE_NAME_FOR_PACS_IMAGE_LAYOUT = "pacs-image-table";
export const PIXEL_CONVERSION_FORMULA_RATIO = 0.749; // 护理测出来的一个值 在 PDF 上签名获取位置他们需要的值跟我们计算的 px 值之间的关系
export const DISTANCE_TO_LINE = 1.6; // 距离线的距离 在该范围内 鼠标样式才会变成能拖动的
export const DEFINITION = 0.5; // definition 定义;   清晰度 两层意思 这里是清晰度 canvas 会有 0.5 像素的问题

export enum TypeInImageMeta {
  HUMAN_BODY_DIAGRAM_TYPE = 1
}


export enum ScriptType {
    SUPERSCRIPT = 1, // 上标
    SUBSCRIPT = 2, // 下标
    NORMAL = 3, // 正常
}
export enum ViewMode {
    NORMAL = "normal",
    FORM = "form", // 表单模式
    VIEW = "view", // 简洁模式
    PERSON = "person", // 痕迹视图
}

export enum SkipMode{
  ROW="row",
  COL="col"
}

export enum VerticalAlign {
    TOP = "top",
    CENTER = "center",
    BOTTOM = "bottom",
}
export enum ShapeMode {
    NoShape = 0,
    Line = 1,
    Circle = 2,
    Cross = 3,
    FoldLine = 4,
    ContinueLine = 5,
    Rect = 6
}

// 稀有的上标
export const rareSuperscript = new Map([
  ["⁶", "6"],
  ["⁻", "-"],
  ["⁹", "9"],
  ["³", "3"]
]);

// 稀有的下标
export const rareSubscript = new Map([
  ["ₓ", "x"],
  ["₀", "o"],
  ["₃", "3"],
  ["ₐ", "a"],
  ["₂", "2"],
  ["₁", "1"],
  ["₄", "4"]
]);

// 月经表达式
export const MENSTRUAL_EXPRESSION = new Map([
  [1, ["初潮年龄", "经期（天）", "周期（天）", "末次月经/绝经年龄"]],
  [2, ["初潮年龄", "经期（天）", "末次月经/绝经年龄", "周期（天）"]],
  [3, ["初潮年龄", "经期（天）", "末次月经/绝经年龄", "周期（天）"]],
  [4, ["初潮年龄", "经期（天）", "周期（天）"]],
])

export const COMMENT_LIST_MIN_WIDTH = 290; // 批注列表宽度可配置 但是也不能太小了 如果低于 290 右侧的叉号 还有少部分的批注就展示不全了
export const COMMENT_WIDTH = 260; // 批注的宽度 就是那个批注卡片的宽度
export const COMMENT_LIST_TITLE_HEIGHT = 50; // 标题的高度(未登录、叉号之类的那个标题) 
export const COMMENT_LIST_ITEM_TITLE_HEIGHT = 50; // 批注列表每个子项 也就是折叠起来的每个子项的高度
export const COMMENT_LIST_ITEM_SPACE_HEIGHT = 20; // 批注列表每个子项之间的间距
export const COMMENT_TITLE_PADDING_LEFT = 5;
export const COMMENT_SCROLL_MARGIN = 5;
export const COMMENT_TITLE_PADDING_RIGHT = 5;
export const COMMENT_TITLE_CROSS_WIDTH = 10; // 批注列表标题叉号的宽度
export const COMMENT_TITLE_CROSS_HEIGHT = 10; // 批注列表标题叉号的高度
export const COMMENT_TITLE_CROSS_TOP = 15; // 批注列表标题叉号距离该页顶部的位置
export const COMMENT_SCROLL_BAR_WIDTH = 6; // 批注滚动条的宽度
export const COMMENT_BORDER_COLOR = "#CFCFCF";
export const COMMENT_SWITCH_WIDTH = 13;
export const COMMENT_SWITCH_DISTANCE_TO_CROSS = 10;

export const HUMAN_BODY_DIAGRAM_POINT_RADIUS = 5; // 人体图点位半径
export const HUMAN_BODY_DIAGRAM_POINT_RADIUS_SQUARE = HUMAN_BODY_DIAGRAM_POINT_RADIUS ** 2;
export const HUMAN_BODY_DIAGRAM_POINT_COLOR = "red"; 
