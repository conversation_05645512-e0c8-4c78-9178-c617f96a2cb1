/**
 * 表格相关方法
 */
const tableMixIn = {
  data() {
    return {
      cellBgColor: "", // 初始值为 null 报错
      showNoCellBgColor: true,
      isShowTableAttr: false, // 展示表格属性弹窗
      setCellLock: false,
      cellNoWrap: false,
      minRowSize: 0,
      aggregationMode: true,
    };
  },
  methods: {
    handleCellLockChange() {
      this.setCellLock = !this.setCellLock;
    },
    // 展示表格属性
    showTableAttr() {
      this.minRowSize = 0;
      this.tableName = this.curClickInfo.table.name;
      this.editableInFormMode = this.curClickInfo.table.editableInFormMode;
      this.tableFiexedStyle = this.curClickInfo.table.tableFiexedStyle;
      this.tableNewPage = this.curClickInfo.table.newPage;
      const rowLine = this.curClickInfo.table.getOrigin().rowLineType;
      if (rowLine === 0) {
        this.tableRowLineType = "tableRowLineTypeNone";
      } else if (rowLine === 1) {
        this.tableRowLineType = "tableRowLineTypeSolid";
      }
      this.fullPage = this.curClickInfo.table.fullPage || false;
      if (
        this.curClickInfo &&
        this.curClickInfo.table &&
        this.curClickInfo.table.skipMode === "col"
      ) {
        this.tableSkipMode = true;
      } else {
        this.tableSkipMode = false;
      }
      this.isShowTableAttr = true;
    },
    // 关闭表格属性弹窗
    closeTableAttr() {
      this.tableName = "";
      this.editableInFormMode = false;
      this.tableFiexedStyle = false;
      this.tableNewPage = false;
      this.tableSkipMode = false;
      this.isShowTableAttr = false;
      this.fullPage = false;
    },
    // 确认修改表格属性
    confirmTableAttr() {
      const table = this.curClickInfo.table;
      const originTable = table.getOrigin();
      if (!originTable) return;

      const { editor, config } = this.instance;
      const configMinRowSize = config.getConfig().min_row_size;
      if (this.minRowSize >= configMinRowSize) {
        const cells = editor.selection.selected_cells;
        const minRowSizes = [];
        if (cells.length > 0) {
          for (const { cell } of cells) {
            if (cell.rowspan === 1) {
              minRowSizes.push(cell.start_row_index, cell.end_row_index);
            } else {
              minRowSizes.push(cell.end_row_index);
            }
          }
        } else {
          const currentCell = editor.selection.getFocusCell();
          minRowSizes.push(currentCell.end_row_index);
        }
        if (minRowSizes.length > 0) {
          const startRowIndex = Math.min(...minRowSizes);
          const endRowIndex = Math.max(...minRowSizes);

          for (let i = 0; i < originTable.min_row_size.length; i++) {
            if (i >= startRowIndex && i <= endRowIndex) {
              originTable.min_row_size[i] = this.minRowSize;
            }
          }
        }
      }
      originTable.skipMode = this.tableSkipMode ? "col" : "row";
      originTable.fullPage = this.fullPage;
      originTable.name = this.tableName;
      originTable.editableInFormMode = this.editableInFormMode;
      originTable.tableFiexedStyle = this.tableFiexedStyle;
      originTable.newPage = this.tableNewPage;
      if (this.tableRowLineType === "tableRowLineTypeNone") {
        originTable.rowLineType = 0;
      } else if (this.tableRowLineType === "tableRowLineTypeSolid") {
        originTable.rowLineType = 1;
      }

      this.isShowTableAttr = false;
      // 设置新页的情况下需要，因为是单次调用，此处直接传true
      this.editor.refreshDocument(true);
    },
    // 展示插入表格的模态框
    showInsertTblModal() {
      this.isShowModal = true;
      this.insertTableWithNewPage = false;
    },

    showCellAttr() {
      const cell = this.curClickInfo.cell;
      this.padding_top = cell.padding_top;
      this.padding_bottom = cell.padding_bottom;
      this.padding_left = cell.padding_left;
      this.padding_right = cell.padding_right;
      this.cell_height = cell.set_cell_height.height
        ? cell.set_cell_height.height
        : 0;
      this.setCellLock = cell.lock;
      this.cellNoWrap = cell.noWrap;
      this.isShowCellAttr = true;

      if (cell.style.bgColor) {
        this.cellBgColor = cell.style.bgColor;
        this.showNoCellBgColor = false;
      } else {
        this.cellBgColor = "";
        this.showNoCellBgColor = true;
      }

      const originCell = cell.getOrigin();
      this.aggregationMode = originCell.aggregationMode === 1 ? true : false;
      const rowLine = originCell.rowLineType;
      if (rowLine === 0) {
        this.cellRowLineType = "cellRowLineTypeNone";
      } else if (rowLine === 1) {
        this.cellRowLineType = "cellRowLineTypeSolid";
      }
    },

    handleNoCellBgColorClick() {
      this.showNoCellBgColor = false;
      this.$refs.cellBgColorPicker.openPanel();
    },

    handleChangeBgColor(color) {
      this.cellBgColor = color;
    },

    handleNoCellBgColorBtnClick() {
      this.showNoCellBgColor = true;
      this.cellBgColor = "";
    },

    // 关闭插入表格的模态框
    closeInsertTblModal() {
      this.isShowModal = false;
      this.insertTableWithNewPage = false;
    },

    showSplitCellModal() {
      this.isShowSplitCellModal = true;
    },
    closeCellAttr() {
      this.padding_top = 0;
      this.padding_bottom = 0;
      this.padding_left = 0;
      this.padding_right = 0;
      this.cell_height = 0;
      this.isShowCellAttr = false;
      this.setCellLock = false;
      this.cellNoWrap = false;
    },

    confirmCellAttr() {
      // if (this.setCellLock) {
      //   this.editor.setCellsLock();
      // } else {
      //   this.editor.setCellsLock({ isLock: false });
      // }
      //
      const rowLineType =
        this.cellRowLineType === "cellRowLineTypeSolid" ? 1 : 0;
      const aggregationMode = this.aggregationMode === true ? 1 : 0;
      const attr = {
        lock: !!this.setCellLock,
        noWrap: !!this.cellNoWrap,
        rowLineType,
        aggregationMode,
        padding_left: this.padding_left,
        padding_right: this.padding_right,
        padding_top: this.padding_top,
        padding_bottom: this.padding_bottom,
        cell_height: this.cell_height,
        style: {
          ...this.curClickInfo.cell.style,
          bgColor: this.cellBgColor || null,
        },
      };
      this.editor.updateCellsAttr({ attr });

      this.setCellLock = false;
      this.cellNoWrap = false;
      this.isShowCellAttr = false;
    },
    confirmSplitCellModal() {
      this.editor.splitCellNotHaveBeenMerged(
        this.split_row_num,
        this.split_col_num
      );
      this.closeSplitCellModal();
    },
    closeSplitCellModal() {
      this.isShowSplitCellModal = false;
    },
    // 确认插入表格
    confirmInsertTbl() {
      if (this.row_num && this.col_num) {
        this.editor.insertTable(this.row_num, this.col_num, {
          newPage: this.insertTableWithNewPage,
        });
      }
      this.closeInsertTblModal();
    },
  },
};
export default tableMixIn;
