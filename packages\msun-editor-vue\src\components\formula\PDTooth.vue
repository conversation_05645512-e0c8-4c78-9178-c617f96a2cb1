<template>
  <div class="PDTooth_PDTooth">
    <div class="row_PDTooth">
      <a-input type="text" class="input" v-model="meta.params[0]" />
      <div class="vertical_PDTooth"></div>
      <a-input type="text" class="input" v-model="meta.params[1]" />
      <div class="vertical_PDTooth"></div>
      <a-input type="text" class="input" v-model="meta.params[2]" />
    </div>
    <div class="horizontal_PDTooth"></div>
    <div class="row_PDTooth">
      <a-input type="text" class="input" v-model="meta.params[3]" />
      <div class="vertical_PDTooth"></div>
      <a-input type="text" class="input" v-model="meta.params[4]" />
      <div class="vertical_PDTooth"></div>
      <a-input type="text" class="input" v-model="meta.params[5]" />
    </div>
  </div>
</template>

<script>
export default {
  name: "PDTooth",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style scoped>
.PDTooth_PDTooth {
  width: 100%;
  height: 100%;
}
.horizontal_PDTooth {
  height: 5px;
  width: 100%;
  background-color: #000;
}
.vertical_PDTooth {
  height: 80px;
  width: 5px;
  background-color: #000;
}
.row_PDTooth {
  display: flex;
}

.input {
  margin-top: 10px;
  width: 120px;
  margin: 25px 20px 0 20px;
}
</style>
