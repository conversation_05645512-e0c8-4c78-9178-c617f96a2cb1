<template>
  <Drawer :visible.sync="visible" @closeDrawer="closeDrawer" title="批注列表">
    <template v-for="(item, index) in comments">
      <a-card
        hoverable
        style="width: 250px"
        :key="index"
        :ref="'comment' + item.id"
        @dblclick="editComment(item, index)"
        @click="selectComment(item, index)"
        size="small"
        :headStyle="cardHeaderStyle(item, index)"
      >
        <span slot="title">
          {{ item.name }}
          <span style="color: #5f6368; font-size: 12px; margin-left: 5px">
            {{ dateShow(item) }}
          </span>
        </span>
        <a
          v-if="!hideReplaceBtn"
          class="replaceContent"
          slot="extra"
          style="font-size: 10px"
          href="#"
          @dblclick.stop="() => {}"
          @click="replaceContent($event, item, index)"
          >替换</a
        >
        <a
          slot="extra"
          style="font-size: 10px"
          href="#"
          @dblclick.stop="() => {}"
          @click="deleteComment($event, item, index)"
          >删除</a
        >
        <a-card-meta :description="item.value"> </a-card-meta>
      </a-card>
      <a-divider
        style="border-color: #7cb305; margin: 15px 0"
        dashed
        :key="item.name + index"
      />
    </template>
  </Drawer>
</template>
<script>
import moment from "moment";
import { isElementInViewport } from "../../assets/js/utils";
import Drawer from "../common/drawer.vue";
export default {
  data() {
    return {
      selectCommentId: "-1",
    };
  },
  components: { Drawer },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
    comments: {
      type: Array,
      default: () => [],
    },
    selectId: {
      type: String,
      default: "",
    },
    hideReplaceBtn: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    selectId: {
      handler(val) {
        this.selectCommentId = val;
        this.scrollInView(val);
      },
    },
    visible: {
      handler(val) {
        if (val) {
          setTimeout(() => {
            this.scrollInView(this.selectCommentId);
          }, 1);
        }
      },
    },
  },
  methods: {
    closeDrawer() {
      this.$emit("close");
    },
    dateShow(item) {
      return moment(item.date).format("YYYY-MM-DD HH:mm");
    },
    editComment(item, index) {
      this.$emit("editComment", item, index);
    },
    deleteComment(e, item, index) {
      this.$emit("delete", item, index);
    },
    selectComment(item) {
      this.selectCommentId = item.id;
      this.$emit("select", item);
    },
    cardHeaderStyle(item) {
      if (item.id !== this.selectCommentId) {
        return { background: "#fafafa" };
      } else {
        return {
          background: "#fafafa",
          "border-top": "3px solid #f1a100",
        };
      }
    },
    scrollInView(selectId) {
      if (!this.visible) return;
      this.$nextTick(() => {
        const els = this.$refs["comment" + selectId];
        if (!els || !els.length) return;
        const el = els[0].$el;
        if (!isElementInViewport(el)) el.scrollIntoView(false);
      });
    },
    replaceContent(e, item, index) {
      item;
      this.$emit("replace", item, index);
    },
  },
};
</script>

<style scoped>
.replaceContent {
  margin-right: 5px;
}
</style>
