export const medicalFormula = {
  // 获取医学表达式的图片
  getMedicalFormulaImage(params: any) {
    const canvas = document.createElement("canvas");
    let canvas_size: any = {};
    const ctx = canvas.getContext("2d")!;
    let font_size = 24;
    const obj = medicalFormula;
    switch (Number(params.formula_type)) {
      case 0:
        canvas_size = obj.normal(ctx, params.params, canvas, font_size);
        break;
      case 1:
        canvas_size = obj.menstruation1(
          ctx,
          params.params,
          canvas,
          font_size
        );
        break;
      case 2:
        canvas_size = obj.menstruation2(
          ctx,
          params.params,
          canvas,
          font_size
        );
        break;
      case 3:
        canvas_size = obj.menstruation3(
          ctx,
          params.params,
          canvas,
          font_size
        );
        break;
      case 4:
        canvas_size = obj.menstruation4(
          ctx,
          params.params,
          canvas,
          font_size
        );
        break;
      case 5:
        canvas_size = obj.pupil(ctx, params.params, canvas, font_size);
        break;
      case 6:
        canvas_size = obj.optical(ctx, params.params, canvas, font_size);
        break;
      case 7:
        canvas_size = obj.fetalHeart(ctx, params.params, canvas, font_size);
        break;
      case 8:
        canvas_size = obj.PDTooth(ctx, params.params, canvas, font_size);
        break;
      case 9:
        canvas_size = obj.diseasedLowerTooth(
          ctx,
          params.params,
          canvas,
          font_size
        );
        break;
      case 10:
        canvas_size = obj.diseasedUpperTooth(
          ctx,
          params.params,
          canvas,
          font_size
        );
        break;
      case 11:
        canvas_size = obj.toothBitmap(ctx, params.params, canvas, font_size);
        break;
      case 12:
        canvas_size = obj.permanentTeethBitmap(
          ctx,
          params.params,
          canvas,
          font_size
        );
        break;
      case 13:
        canvas_size = obj.primaryTeethBitmap(
          ctx,
          params.params,
          canvas,
          font_size
        );
        break;

      default:
        break;
    }
    const imgData = ctx.getImageData(
      0,
      0,
      canvas_size.canvas_width,
      canvas_size.canvas_height
    );
    ctx.putImageData(imgData, 0, 0);
    const src = canvas.toDataURL(); // image URL
    const image_element = document.createElement("img");
    let width = canvas_size.canvas_width;
    let height = canvas_size.canvas_height;
    image_element.src = src;
    image_element.style.width = width + "px";
    image_element.style.height = height + "px";
    image_element.width = width;
    image_element.height = height;
    return image_element;
  },
  //普通医学表达式
  normal(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width =
          ctx.measureText(params[0]).width > ctx.measureText(params[2]).width
            ? ctx.measureText(params[0]).width
            : ctx.measureText(params[2]).width;
    let right_width =
          ctx.measureText(params[1]).width > ctx.measureText(params[3]).width
            ? ctx.measureText(params[1]).width
            : ctx.measureText(params[3]).width;
    canvas.width = left_width + right_width + 80;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(40 + left_width, 0);
    ctx.lineTo(40 + left_width, canvas.height);
    ctx.moveTo(0, canvas.height / 2);
    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 3;
    ctx.stroke();
    for (let i = 0; i < 4; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "center";
    ctx.fillText(params[0], (40 + left_width) / 2, canvas.height / 4);
    ctx.fillText(
      params[1],
      60 + left_width + right_width / 2,
      canvas.height / 4
    );
    ctx.fillText(params[2], (40 + left_width) / 2, canvas.height / 2 + 20);
    ctx.fillText(
      params[3],
      60 + left_width + right_width / 2,
      canvas.height / 2 + 20
    );
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //月经表达式1
  menstruation1(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = ctx.measureText(params[0]).width;
    let center_top = ctx.measureText(params[1]).width;
    let center_bottom = ctx.measureText(params[2]).width;
    let right_width = ctx.measureText(params[3]).width;
  
    if (!params[0]) {
      left_width = 0;
    }
    if (!params[1]) {
      center_top = 0;
    }
    if (!params[2]) {
      center_bottom = 0;
    }
    if (!params[3]) {
      right_width = 0;
    }
    let center_width =
          center_top > center_bottom ? center_top + 10 : center_bottom + 10;
    canvas.width = left_width + right_width + center_width + 40;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
  
    ctx.beginPath();
    ctx.moveTo(20 + left_width, canvas.height / 2);
    ctx.lineTo(20 + left_width + center_width, canvas.height / 2);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 2;
    ctx.stroke();
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    for (let i = 0; i < 4; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "center";
    ctx.fillText(params[0], (20 + left_width) / 2, canvas.height / 2);
    ctx.textAlign = "center";
    ctx.fillText(
      params[1],
      20 + left_width + center_width / 2,
      canvas.height / 5
    );
    ctx.textAlign = "center";
    ctx.fillText(
      params[3],
      30 + left_width + center_width + right_width / 2,
      canvas.height / 2
    );
    ctx.textAlign = "center";
    ctx.fillText(
      params[2],
      20 + left_width + center_width / 2,
      (canvas.height / 6) * 5
    );
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //月经表达式2
  menstruation2(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width =
          ctx.measureText(params[0]).width > ctx.measureText(params[2]).width
            ? ctx.measureText(params[0]).width
            : ctx.measureText(params[2]).width;
    let right_width =
          ctx.measureText(params[1]).width > ctx.measureText(params[3]).width
            ? ctx.measureText(params[1]).width
            : ctx.measureText(params[3]).width;
    canvas.width = left_width + right_width + 80;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(40 + left_width, 0);
    ctx.lineTo(40 + left_width, canvas.height);
    ctx.moveTo(0, canvas.height / 2);
    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 3;
    ctx.stroke();
    for (let i = 0; i < 4; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "center";
    ctx.fillText(params[0], (40 + left_width) / 2, canvas.height / 4);
    ctx.fillText(
      params[1],
      60 + left_width + right_width / 2,
      canvas.height / 4
    );
    ctx.fillText(params[2], (40 + left_width) / 2, canvas.height / 2 + 20);
    ctx.fillText(
      params[3],
      60 + left_width + right_width / 2,
      canvas.height / 2 + 20
    );
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //月经表达式3
  menstruation3(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = ctx.measureText(params[0]).width + 60;
    let right_width = ctx.measureText(params[3]).width + 60;
    canvas.width =
          left_width > right_width ? left_width * 2 + 100 : right_width * 2 + 100;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(canvas.width, canvas.height);
    ctx.moveTo(canvas.width, 0);
    ctx.lineTo(0, canvas.height);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 3;
    ctx.stroke();
    for (let i = 0; i < 4; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "center";
    ctx.fillText(params[0], canvas.width / 5, canvas.height / 2);
    ctx.textAlign = "center";
    ctx.fillText(params[1], canvas.width / 2, canvas.height / 5);
    ctx.textAlign = "center";
    ctx.fillText(params[2], canvas.width / 2, (canvas.height / 5) * 4);
    ctx.textAlign = "center";
    ctx.fillText(params[3], (canvas.width / 5) * 4, canvas.height / 2);
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //月经表达式4
  menstruation4(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = ctx.measureText(params[0]).width + 10;
    let right_width =
          ctx.measureText(params[1]).width > ctx.measureText(params[2]).width
            ? ctx.measureText(params[1]).width
            : ctx.measureText(params[2]).width;
    canvas.width = left_width + right_width + 60;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    for (let i = 0; i < 3; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.beginPath();
    ctx.moveTo(20 + left_width, canvas.height / 4 + 25);
    ctx.lineTo(30 + left_width, (canvas.height / 4) * 3 - 25);
    ctx.moveTo(40 + left_width, canvas.height / 2);
    ctx.lineTo(canvas.width - 20, canvas.height / 2);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 3;
    ctx.stroke();
    ctx.textAlign = "left";
    ctx.fillText(params[0], 20, canvas.height / 2);
    ctx.fillText(params[1], 40 + left_width, canvas.height / 4);
    ctx.fillText(params[2], 40 + left_width, (canvas.height / 4) * 3);
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //瞳孔图
  pupil(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = Math.max(
      ctx.measureText(params[0]).width,
      ctx.measureText(params[1]).width,
      ctx.measureText(params[2]).width
    );
    let right_width = Math.max(
      ctx.measureText(params[4]).width,
      ctx.measureText(params[5]).width,
      ctx.measureText(params[6]).width
    );
    let center_width = ctx.measureText(params[3]).width;
    canvas.width =
          left_width > right_width
            ? left_width * 2 + center_width + 40
            : right_width * 2 + center_width + 40;
    canvas.height = font_size * 3 + 18;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    for (let i = 0; i < 7; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "center";
    ctx.fillText(
      params[0],
      (canvas.width / 2 - center_width / 2) / 2 - 10,
      canvas.height / 6
    );
    ctx.fillText(
      params[1],
      (canvas.width / 2 - center_width / 2) / 2 - 10,
      canvas.height / 2
    );
    ctx.fillText(
      params[2],
      (canvas.width / 2 - center_width / 2) / 2 - 10,
      (canvas.height / 6) * 5
    );
    ctx.fillText(params[3], canvas.width / 2, canvas.height / 2);
    ctx.fillText(
      params[4],
      (canvas.width / 4) * 3 + center_width / 4 + 10,
      canvas.height / 6
    );
    ctx.fillText(
      params[5],
      (canvas.width / 4) * 3 + center_width / 4 + 10,
      canvas.height / 2
    );
    ctx.fillText(
      params[6],
      (canvas.width / 4) * 3 + center_width / 4 + 10,
      (canvas.height / 6) * 5
    );
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //光定位图
  optical(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = Math.max(
      ctx.measureText(params[0]).width,
      ctx.measureText(params[1]).width,
      ctx.measureText(params[2]).width
    );
    let center_width = Math.max(
      ctx.measureText(params[3]).width,
      ctx.measureText(params[4]).width,
      ctx.measureText(params[5]).width
    );
    let right_width = Math.max(
      ctx.measureText(params[6]).width,
      ctx.measureText(params[7]).width,
      ctx.measureText(params[8]).width
    );
    let long_width = left_width > right_width ? left_width : right_width;
    canvas.width = long_width * 2 + center_width + 40;
    canvas.height = font_size * 3 + 18;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    for (let i = 0; i < 9; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "center";
    ctx.fillText(
      params[0],
      (canvas.width / 2 - center_width / 2) / 2 - 10,
      canvas.height / 6
    );
    ctx.fillText(
      params[1],
      (canvas.width / 2 - center_width / 2) / 2 - 10,
      canvas.height / 2
    );
    ctx.fillText(
      params[2],
      (canvas.width / 2 - center_width / 2) / 2 - 10,
      (canvas.height / 6) * 5
    );
    ctx.fillText(params[3], canvas.width / 2, canvas.height / 6);
    ctx.fillText(params[4], canvas.width / 2, canvas.height / 2);
    ctx.fillText(params[5], canvas.width / 2, (canvas.height / 6) * 5);
    ctx.fillText(
      params[6],
      (canvas.width / 4) * 3 + center_width / 4 + 10,
      canvas.height / 6
    );
    ctx.fillText(
      params[7],
      (canvas.width / 4) * 3 + center_width / 4 + 10,
      canvas.height / 2
    );
    ctx.fillText(
      params[8],
      (canvas.width / 4) * 3 + center_width / 4 + 10,
      (canvas.height / 6) * 5
    );
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //胎心图
  fetalHeart(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = Math.max(
      ctx.measureText(params[0]).width,
      ctx.measureText(params[1]).width,
      ctx.measureText(params[2]).width
    );
    let right_width = Math.max(
      ctx.measureText(params[3]).width,
      ctx.measureText(params[4]).width,
      ctx.measureText(params[5]).width
    );
    let long_width = left_width > right_width ? left_width : right_width;
    canvas.width = long_width * 2 + 60;
    canvas.height = font_size * 3 + 18;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(canvas.width / 2, 0);
    ctx.lineTo(canvas.width / 2, canvas.height);
    ctx.moveTo(canvas.width / 2 - 10, canvas.height / 2);
    ctx.lineTo(canvas.width / 2 + 10, canvas.height / 2);
    ctx.moveTo(0, canvas.height / 2);
    ctx.lineTo(canvas.width / 2 - long_width - 20, canvas.height / 2);
    ctx.moveTo(canvas.width, canvas.height / 2);
    ctx.lineTo(canvas.width / 2 + long_width + 20, canvas.height / 2);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 3;
    ctx.stroke();
    for (let i = 0; i < 6; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "right";
    ctx.fillText(params[0], canvas.width / 2 - 15, canvas.height / 6);
    ctx.textAlign = "right";
    ctx.fillText(params[1], canvas.width / 2 - 15, canvas.height / 2);
    ctx.textAlign = "right";
    ctx.fillText(params[2], canvas.width / 2 - 15, (canvas.height / 6) * 5);
    ctx.textAlign = "left";
    ctx.fillText(params[3], canvas.width / 2 + 15, canvas.height / 6);
    ctx.textAlign = "left";
    ctx.fillText(params[4], canvas.width / 2 + 15, canvas.height / 2);
    ctx.textAlign = "left";
    ctx.fillText(params[5], canvas.width / 2 + 15, (canvas.height / 6) * 5);
  
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //PD牙位图
  PDTooth(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = Math.max(
      ctx.measureText(params[0]).width,
      ctx.measureText(params[3]).width
    );
    let center_width = Math.max(
      ctx.measureText(params[1]).width,
      ctx.measureText(params[4]).width
    );
    let right_width = Math.max(
      ctx.measureText(params[2]).width,
      ctx.measureText(params[5]).width
    );
    let long_width = left_width > right_width ? left_width : right_width;
    canvas.width = long_width * 2 + center_width + 40;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(canvas.width / 2 - center_width / 2 - 10, 0);
    ctx.lineTo(canvas.width / 2 - center_width / 2 - 10, canvas.height);
    ctx.moveTo(canvas.width / 2 + center_width / 2 + 10, 0);
    ctx.lineTo(canvas.width / 2 + center_width / 2 + 10, canvas.height);
    ctx.moveTo(0, canvas.height / 2);
    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 3;
    ctx.stroke();
    for (let i = 0; i < 9; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "center";
    ctx.fillText(
      params[0],
      (canvas.width / 2 - center_width / 2) / 2 - 10,
      canvas.height / 4
    );
    ctx.fillText(
      params[3],
      (canvas.width / 2 - center_width / 2) / 2 - 10,
      (canvas.height / 4) * 3
    );
    ctx.fillText(params[1], canvas.width / 2, canvas.height / 4);
    ctx.fillText(params[4], canvas.width / 2, (canvas.height / 4) * 3);
    ctx.fillText(
      params[2],
      (canvas.width / 4) * 3 + center_width / 4 + 10,
      canvas.height / 4
    );
    ctx.fillText(
      params[5],
      (canvas.width / 4) * 3 + center_width / 4 + 10,
      (canvas.height / 4) * 3
    );
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //病变下牙牙位图
  diseasedLowerTooth(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = ctx.measureText(params[0]).width;
    let right_width =
          ctx.measureText(params[1]).width > ctx.measureText(params[2]).width
            ? ctx.measureText(params[1]).width
            : ctx.measureText(params[2]).width;
    canvas.width = left_width + right_width + 20;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    for (let i = 0; i < 3; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.beginPath();
    ctx.moveTo(left_width + 10, canvas.height / 2);
    ctx.lineTo(left_width + right_width + 10, canvas.height / 2);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 3;
    ctx.stroke();
    ctx.textAlign = "right";
    ctx.fillText(params[0], left_width, canvas.height / 2);
    ctx.textAlign = "left";
    ctx.fillText(params[1], left_width + 10, canvas.height / 4);
    ctx.textAlign = "left";
    ctx.fillText(params[2], left_width + 10, (canvas.height / 4) * 3);
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  //病变上牙牙位图
  diseasedUpperTooth(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let left_width = ctx.measureText(params[1]).width;
    let right_width = ctx.measureText(params[2]).width;
    canvas.width = left_width + right_width + 80;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(0, 0);
    ctx.lineTo(canvas.width / 2, canvas.height / 2 + 5);
    ctx.moveTo(canvas.width, 0);
    ctx.lineTo(canvas.width / 2, canvas.height / 2 + 5);
    ctx.moveTo(canvas.width / 2, canvas.height / 2 + 5);
    ctx.lineTo(canvas.width / 2, canvas.height);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 3;
    ctx.stroke();
    for (let i = 0; i < 3; i++) {
      if (params[i] === undefined) {
        params[i] = "";
      }
    }
    ctx.textAlign = "center";
    ctx.fillText(params[0], canvas.width / 2, canvas.height / 5);
    ctx.fillText(params[1], canvas.width / 4, (canvas.height / 4) * 3);
    ctx.fillText(params[2], (canvas.width / 4) * 3, (canvas.height / 4) * 3);
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  toothBitmap(ctx: any, params: any, canvas: any, font_size: any) {
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let leftUpText = "";
    let leftDownText = "";
    let rightUpText = "";
    let rightDownText = "";
    if (params.length > 0) {
      params[0].forEach((e: any) => {
        if (e.click) {
          leftUpText = leftUpText + e.value;
        }
      });
      params[1].forEach((e: any) => {
        if (e.click) {
          leftUpText = leftUpText + e.value;
        }
      });

      params[4].forEach((e: any) => {
        if (e.click) {
          leftDownText = leftDownText + e.value;
        }
      });
      params[5].forEach((e: any) => {
        if (e.click) {
          leftDownText = leftDownText + e.value;
        }
      });

      params[2].forEach((e: any) => {
        if (e.click) {
          rightUpText = rightUpText + e.value;
        }
      });
      params[3].forEach((e: any) => {
        if (e.click) {
          rightUpText = rightUpText + e.value;
        }
      });

      params[6].forEach((e: any) => {
        if (e.click) {
          rightDownText = rightDownText + e.value;
        }
      });
      params[7].forEach((e: any) => {
        if (e.click) {
          rightDownText = rightDownText + e.value;
        }
      });
    }

    let left_width =
      ctx.measureText(leftUpText).width > ctx.measureText(leftDownText).width
        ? ctx.measureText(leftUpText).width + 30
        : ctx.measureText(leftDownText).width + 30;

    let right_width =
      ctx.measureText(rightUpText).width >
      ctx.measureText(rightDownText).width
        ? ctx.measureText(rightUpText).width + 30
        : ctx.measureText(rightDownText).width + 30;
    canvas.width = left_width + right_width + 60;
    canvas.height = font_size * 2 + 20;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(0, canvas.height / 2);
    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.moveTo(canvas.width / 2, 0);
    ctx.lineTo(canvas.width / 2, canvas.height);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 1;
    ctx.stroke();
    ctx.textAlign = "center";
    ctx.fillText(leftUpText, canvas.width / 4, canvas.height / 5);
    ctx.fillText(leftDownText, canvas.width / 4, (canvas.height / 5) * 4);
    ctx.fillText(rightUpText, (canvas.width / 4) * 3, canvas.height / 5);
    ctx.fillText(
      rightDownText,
      (canvas.width / 4) * 3,
      (canvas.height / 5) * 4
    );
    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  permanentTeethBitmap(ctx: any, params: any, canvas: any, font_size: any) {
    const quadrant1: any = [];
    const quadrant2: any = [];
    const quadrant3: any = [];
    const quadrant4: any = [];
    params.upToothList?.forEach((e: any, i: any) => {
      if (e && e.click) {
        let surfaceList = "";
        params.upBitmapData[i].surface.forEach((m: any) => {
          if (m && m.click) {
            surfaceList = surfaceList + m.name;
          }
        });
        if (i < params.upToothList.length / 2) {
          quadrant1.push({
            name: e.name,
            surface: surfaceList,
          });
        } else {
          quadrant2.push({
            name: e.name,
            surface: surfaceList,
          });
        }
      }
    });
    params.downToothList?.forEach((e: any, i: any) => {
      if (e && e.click) {
        let surfaceList = "";
        params.downBitmapData[i].surface.forEach((m: any) => {
          if (m && m.click) {
            surfaceList = surfaceList + m.name;
          }
        });
        if (i < params.downToothList.length / 2) {
          quadrant3.push({
            name: e.name,
            surface: surfaceList,
          });
        } else {
          quadrant4.push({
            name: e.name,
            surface: surfaceList,
          });
        }
      }
    });
    let mainQuadrant1 = "";
    let surfaceQuadrant1 = "";
    quadrant1.forEach((e: any) => {
      mainQuadrant1 = mainQuadrant1 + e.name;
      surfaceQuadrant1 = surfaceQuadrant1 + e.surface;
    });
    let mainQuadrant2 = "";
    let surfaceQuadrant2 = "";
    quadrant2.forEach((e: any) => {
      mainQuadrant2 = mainQuadrant2 + e.name;
      surfaceQuadrant2 = surfaceQuadrant2 + e.surface;
    });
    let mainQuadrant3 = "";
    let surfaceQuadrant3 = "";
    quadrant3.forEach((e: any) => {
      mainQuadrant3 = mainQuadrant3 + e.name;
      surfaceQuadrant3 = surfaceQuadrant3 + e.surface;
    });
    let mainQuadrant4 = "";
    let surfaceQuadrant4 = "";
    quadrant4.forEach((e: any) => {
      mainQuadrant4 = mainQuadrant4 + e.name;
      surfaceQuadrant4 = surfaceQuadrant4 + e.surface;
    });
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let main1 = ctx.measureText(mainQuadrant1).width;
    let main2 = ctx.measureText(mainQuadrant2).width;
    let main3 = ctx.measureText(mainQuadrant3).width;
    let main4 = ctx.measureText(mainQuadrant4).width;
    ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '黑体'";
    let surface1 = ctx.measureText(surfaceQuadrant1).width;
    let surface2 = ctx.measureText(surfaceQuadrant2).width;
    let surface3 = ctx.measureText(surfaceQuadrant3).width;
    let surface4 = ctx.measureText(surfaceQuadrant4).width;
    let left_width =
      main1 + surface1 > main3 + surface3
        ? main1 + surface1 + 30
        : main3 + surface3 + 30;
    let right_width =
      main2 + surface2 > main4 + surface4
        ? main2 + surface2 + 30
        : main4 + surface4 + 30;
    canvas.width = left_width + right_width;
    canvas.height = font_size * 2 + 30;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";

    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(0, canvas.height / 2);
    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.moveTo(left_width, 0);
    ctx.lineTo(left_width, canvas.height);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 1;
    ctx.stroke();
    ctx.textAlign = "left";
    let quadrant1Width = 15;
    let quadrant2Width = left_width + 15;
    let quadrant3Width = 15;
    let quadrant4Width = left_width + 15;

    quadrant1.forEach((e: any) => {
      ctx.font = "400 " + `${font_size}` + "px '宋体'";
      ctx.fillText(e.name, quadrant1Width, canvas.height / 4);
      quadrant1Width = quadrant1Width + ctx.measureText(e.name).width;
      ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '宋体'";
      ctx.fillText(e.surface, quadrant1Width, canvas.height / 7);
      quadrant1Width = quadrant1Width + ctx.measureText(e.surface).width;
    });
    quadrant2.forEach((e: any) => {
      ctx.font = "400 " + `${font_size}` + "px '宋体'";
      ctx.fillText(e.name, quadrant2Width, canvas.height / 4);
      quadrant2Width = quadrant2Width + ctx.measureText(e.name).width;
      ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '宋体'";
      ctx.fillText(e.surface, quadrant2Width, canvas.height / 7);
      quadrant2Width = quadrant2Width + ctx.measureText(e.surface).width;
    });
    quadrant3.forEach((e: any) => {
      ctx.font = "400 " + `${font_size}` + "px '宋体'";

      ctx.fillText(e.name, quadrant3Width, (canvas.height / 5) * 4);
      quadrant3Width = quadrant3Width + ctx.measureText(e.name).width;
      ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '宋体'";
      ctx.fillText(e.surface, quadrant3Width, (canvas.height / 10) * 7);
      quadrant3Width = quadrant3Width + ctx.measureText(e.surface).width;
    });
    quadrant4.forEach((e: any) => {
      ctx.font = "400 " + `${font_size}` + "px '宋体'";
      ctx.fillText(e.name, quadrant4Width, (canvas.height / 5) * 4);
      quadrant4Width = quadrant4Width + ctx.measureText(e.name).width;
      ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '宋体'";
      ctx.fillText(e.surface, quadrant4Width, (canvas.height / 10) * 7);
      quadrant4Width = quadrant4Width + ctx.measureText(e.surface).width;
    });

    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
  primaryTeethBitmap(ctx: any, params: any, canvas: any, font_size: any) {
    const quadrant1: any = [];
    const quadrant2: any = [];
    const quadrant3: any = [];
    const quadrant4: any = [];
    params.upToothList?.forEach((e: any, i: any) => {
      if (e && e.click) {
        let surfaceList = "";
        params.upBitmapData[i].surface.forEach((m: any) => {
          if (m && m.click) {
            surfaceList = surfaceList + m.name;
          }
        });
        if (i < params.upToothList.length / 2) {
          quadrant1.push({
            name: e.name,
            surface: surfaceList,
          });
        } else {
          quadrant2.push({
            name: e.name,
            surface: surfaceList,
          });
        }
      }
    });
    params.downToothList?.forEach((e: any, i: any) => {
      if (e && e.click) {
        let surfaceList = "";
        params.downBitmapData[i].surface.forEach((m: any) => {
          if (m && m.click) {
            surfaceList = surfaceList + m.name;
          }
        });
        if (i < params.downToothList.length / 2) {
          quadrant3.push({
            name: e.name,
            surface: surfaceList,
          });
        } else {
          quadrant4.push({
            name: e.name,
            surface: surfaceList,
          });
        }
      }
    });
    let mainQuadrant1 = "";
    let surfaceQuadrant1 = "";
    quadrant1.forEach((e: any) => {
      mainQuadrant1 = mainQuadrant1 + e.name;
      surfaceQuadrant1 = surfaceQuadrant1 + e.surface;
    });
    let mainQuadrant2 = "";
    let surfaceQuadrant2 = "";
    quadrant2.forEach((e: any) => {
      mainQuadrant2 = mainQuadrant2 + e.name;
      surfaceQuadrant2 = surfaceQuadrant2 + e.surface;
    });
    let mainQuadrant3 = "";
    let surfaceQuadrant3 = "";
    quadrant3.forEach((e: any) => {
      mainQuadrant3 = mainQuadrant3 + e.name;
      surfaceQuadrant3 = surfaceQuadrant3 + e.surface;
    });
    let mainQuadrant4 = "";
    let surfaceQuadrant4 = "";
    quadrant4.forEach((e: any) => {
      mainQuadrant4 = mainQuadrant4 + e.name;
      surfaceQuadrant4 = surfaceQuadrant4 + e.surface;
    });
    ctx.font = "400 " + `${font_size}` + "px '黑体'";
    let main1 = ctx.measureText(mainQuadrant1).width;
    let main2 = ctx.measureText(mainQuadrant2).width;
    let main3 = ctx.measureText(mainQuadrant3).width;
    let main4 = ctx.measureText(mainQuadrant4).width;
    ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '黑体'";
    let surface1 = ctx.measureText(surfaceQuadrant1).width;
    let surface2 = ctx.measureText(surfaceQuadrant2).width;
    let surface3 = ctx.measureText(surfaceQuadrant3).width;
    let surface4 = ctx.measureText(surfaceQuadrant4).width;
    let left_width =
      main1 + surface1 > main3 + surface3
        ? main1 + surface1 + 30
        : main3 + surface3 + 30;
    let right_width =
      main2 + surface2 > main4 + surface4
        ? main2 + surface2 + 30
        : main4 + surface4 + 30;
    canvas.width = left_width + right_width;
    canvas.height = font_size * 2 + 30;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";

    ctx.fillStyle = "#000";
    ctx.textBaseline = "middle";
    ctx.textAlign = "center";
    ctx.beginPath();
    ctx.moveTo(0, canvas.height / 2);
    ctx.lineTo(canvas.width, canvas.height / 2);
    ctx.moveTo(left_width, 0);
    ctx.lineTo(left_width, canvas.height);
    ctx.strokeStyle = "#000";
    ctx.lineWidth = 1;
    ctx.stroke();
    ctx.textAlign = "left";
    let quadrant1Width = 15;
    let quadrant2Width = left_width + 15;
    let quadrant3Width = 15;
    let quadrant4Width = left_width + 15;

    quadrant1.forEach((e: any) => {
      ctx.font = "400 " + `${font_size}` + "px '宋体'";
      ctx.fillText(e.name, quadrant1Width, canvas.height / 4);
      quadrant1Width = quadrant1Width + ctx.measureText(e.name).width;
      ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '宋体'";
      ctx.fillText(e.surface, quadrant1Width, canvas.height / 7);
      quadrant1Width = quadrant1Width + ctx.measureText(e.surface).width;
    });
    quadrant2.forEach((e: any) => {
      ctx.font = "400 " + `${font_size}` + "px '宋体'";
      ctx.fillText(e.name, quadrant2Width, canvas.height / 4);
      quadrant2Width = quadrant2Width + ctx.measureText(e.name).width;
      ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '宋体'";
      ctx.fillText(e.surface, quadrant2Width, canvas.height / 7);
      quadrant2Width = quadrant2Width + ctx.measureText(e.surface).width;
    });
    quadrant3.forEach((e: any) => {
      ctx.font = "400 " + `${font_size}` + "px '宋体'";

      ctx.fillText(e.name, quadrant3Width, (canvas.height / 5) * 4);
      quadrant3Width = quadrant3Width + ctx.measureText(e.name).width;
      ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '宋体'";
      ctx.fillText(e.surface, quadrant3Width, (canvas.height / 10) * 7);
      quadrant3Width = quadrant3Width + ctx.measureText(e.surface).width;
    });
    quadrant4.forEach((e: any) => {
      ctx.font = "400 " + `${font_size}` + "px '宋体'";
      ctx.fillText(e.name, quadrant4Width, (canvas.height / 5) * 4);
      quadrant4Width = quadrant4Width + ctx.measureText(e.name).width;
      ctx.font = "400 " + `${(font_size / 3) * 2}` + "px '宋体'";
      ctx.fillText(e.surface, quadrant4Width, (canvas.height / 10) * 7);
      quadrant4Width = quadrant4Width + ctx.measureText(e.surface).width;
    });

    return { canvas_width: canvas.width, canvas_height: canvas.height };
  },
}