// import { JSEncrypt } from "jsencrypt";

const jsePublicKey = ``
// const jsePublicKey = `MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJ/BdcOEXCX0YSfvlkTwwviEcAKAlqJ6
// d/+VILtiGsblbpPuGfse5uwfPW8hWGEpcJ/rou6KraoJ+fZl1XDRUUsCAwEAAQ==`
const jsePrivateKey = `MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEAn8F1w4RcJfRhJ++W
RPDC+IRwAoCWonp3/5Ugu2IaxuVuk+4Z+x7m7B89byFYYSlwn+ui7oqtqgn59mXV
cNFRSwIDAQABAkAYr51R9A8FMmkc8bHHv2hxXI2uZU5hlwkZ7mJbckJu/2Bzb<PERSON>D
KMYeNf4AbpmfOWuS5h+MQDJGEzVb4v2tpVzpAiEA0wn1QMAV3gjg99jGwxJIhtQI
ms8dvx0hPjVsYh5JnbUCIQDByoVyCTjhemib1BHhLLnC4lEitO6GF0Yg0RF5+tyS
/wIgATlBjsqOVec/RrCfFmp4uS9pjVw+E+mbU70Dj94VFzECIFcgj5iyrsEpQMj2
ssczTZ1iFSpugGMHKb9tX9cgw3TPAiBw0xKGihoaMo5l7Kx1BYj8lCN89ix+eMlB
YWL1D3HGHw==`

/**
 * 加密登录密码
 * @param pw 输入的密码字符串
 * @returns {PromiseLike<ArrayBuffer>}
 */
// export function encrypt (pw) {
//   const jse = new JSEncrypt()
//   jse.setPublicKey(jsePublicKey)
//   return jse.encrypt(pw)
// }

/**
 * 解密登录密码
 * @param pw 输入的密码字符串
 * @returns {PromiseLike<ArrayBuffer>}
 */
// export function decrypt (pw) {
//   const jse = new JSEncrypt()
//   jse.setPrivateKey(jsePrivateKey)
//   return jse.decrypt(pw)
// }


//格式化时间
export function formatDateTime(d, formatStr) {
  let str = formatStr;
  let month = d.getMonth() + 1;
  let Week = ["日", "一", "二", "三", "四", "五", "六"];
  str = str.replace(/yyyy|YYYY/, d.getFullYear());
  str = str.replace(
    /yy|YY/,
    d.getYear() % 100 > 9
      ? (d.getYear() % 100).toString()
      : "0" + (d.getYear() % 100)
  );
  str = str.replace(/MM/, month > 9 ? month.toString() : "0" + month);
  str = str.replace(/M/g, d.getMonth());
  str = str.replace(/w|W/g, Week[d.getDay()]);
  str = str.replace(
    /dd|DD/,
    d.getDate() > 9 ? d.getDate().toString() : "0" + d.getDate()
  );
  str = str.replace(/d|D/g, d.getDate());
  str = str.replace(
    /hh|HH/,
    d.getHours() > 9 ? d.getHours().toString() : "0" + d.getHours()
  );
  str = str.replace(/h|H/g, d.getHours());
  str = str.replace(
    /mm/,
    d.getMinutes() > 9 ? d.getMinutes().toString() : "0" + d.getMinutes()
  );
  str = str.replace(/m/g, d.getMinutes());
  str = str.replace(
    /ss|SS/,
    d.getSeconds() > 9 ? d.getSeconds().toString() : "0" + d.getSeconds()
  );
  str = str.replace(/s|S/g, d.getSeconds());
  return str;
}

//将特定字符串格式日期传入，返回日期格式
//如：parseDateTime('yyyy-MM-dd        HH:mm:ss','2017-10-11         10:20:33');
export function parseDatetime(format, dateStr, keepSecond) {
  let newdate = new Date();
  let MONTH_RANGE1 = ["0", "1"];
  let MONTH_RANGE2 = ["0", "1", "2"];
  let DAY_RANGE1 = ["0", "1", "2", "3"];
  let DAY_RANGE2 = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
  let HOUR_RANGE1 = ["0", "1", "2"];
  let HOUR_RANGE2 = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
  let MINUTE_RANGE1 = ["0", "1", "2", "3", "4", "5"];
  let MINUTE_RANGE2 = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];
  let SECOND_RANGE1 = ["0", "1", "2", "3", "4", "5"];
  let SECOND_RANGE2 = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"];

  let dateBuffer = [];

  let year;
  let month;
  let day;
  let hour;
  let minute;
  let second;

  if (format.indexOf("YYYY") >= 0 || format.indexOf("yyyy") >= 0) {
    const year_index = format.toLowerCase().indexOf("yyyy");
    dateBuffer.push({
      partName: "year",
      formatIndex: year_index,
      formatLength: 4,
    });
  } else if (format.indexOf("YY") >= 0 || format.indexOf("yy") >= 0) {
    const year_index = format.toLowerCase().indexOf("yy");
    dateBuffer.push({
      partName: "year",
      formatIndex: year_index,
      formatLength: 2,
    });
  }
  if (format.indexOf("MM") >= 0) {
    const month_index = format.indexOf("MM");
    dateBuffer.push({
      partName: "month",
      formatIndex: month_index,
      formatLength: 2,
    });
  } else if (format.indexOf("M") >= 0) {
    const month_index = format.indexOf("M");
    dateBuffer.push({
      partName: "month",
      formatIndex: month_index,
      formatLength: 1,
    });
  }
  if (format.indexOf("DD") >= 0 || format.indexOf("dd") >= 0) {
    const day_index = format.toLowerCase().indexOf("dd");
    dateBuffer.push({
      partName: "day",
      formatIndex: day_index,
      formatLength: 2,
    });
  } else if (format.indexOf("D") >= 0 || format.indexOf("d") >= 0) {
    const day_index = format.toLowerCase().indexOf("d");
    dateBuffer.push({
      partName: "day",
      formatIndex: day_index,
      formatLength: 1,
    });
  }
  if (format.indexOf("HH") >= 0 || format.indexOf("hh") >= 0) {
    const hour_index = format.toLowerCase().indexOf("hh");
    dateBuffer.push({
      partName: "hour",
      formatIndex: hour_index,
      formatLength: 2,
    });
  } else if (format.indexOf("H") >= 0 || format.indexOf("h") >= 0) {
    const hour_index = format.toLowerCase().indexOf("h");
    dateBuffer.push({
      partName: "hour",
      formatIndex: hour_index,
      formatLength: 1,
    });
  }
  if (format.indexOf("mm") >= 0) {
    const minute_index = format.indexOf("mm");
    dateBuffer.push({
      partName: "minute",
      formatIndex: minute_index,
      formatLength: 2,
    });
  } else if (format.indexOf("m") >= 0) {
    const minute_index = format.indexOf("m");
    dateBuffer.push({
      partName: "minute",
      formatIndex: minute_index,
      formatLength: 1,
    });
  }
  if (format.indexOf("SS") >= 0 || format.indexOf("ss") >= 0) {
    const second_index = format.toLowerCase().indexOf("ss");
    dateBuffer.push({
      partName: "second",
      formatIndex: second_index,
      formatLength: 2,
    });
  } else if (format.indexOf("S") >= 0 || format.indexOf("s") >= 0) {
    const second_index = format.toLowerCase().indexOf("s");
    dateBuffer.push({
      partName: "second",
      formatIndex: second_index,
      formatLength: 1,
    });
  }

  let getMinItem = function (arr, l_item) {
    let tmp_item;
    for (let i = 0; i < arr.length; i++) {
      let current_item = arr[i];
      if (l_item && l_item["formatIndex"] >= current_item["formatIndex"]) {
        continue;
      }
      if (!tmp_item) {
        tmp_item = current_item;
      } else {
        if (tmp_item["formatIndex"] > current_item["formatIndex"]) {
          tmp_item = current_item;
        }
      }
    }
    return tmp_item;
  };
  let minFormatItem = getMinItem(dateBuffer, null);
  let indexOffset = 0;
  while (minFormatItem) {
    let part_name = minFormatItem["partName"];
    let format_index = minFormatItem["formatIndex"];
    let format_length = minFormatItem["formatLength"];

    switch (part_name) {
      case "year":
        year = dateStr.substring(
          format_index + indexOffset,
          format_index + format_length + indexOffset
        );
        break;
      case "month":
        month = dateStr.substring(
          format_index + indexOffset,
          format_index + format_length + indexOffset
        );
        if (format_length == 1 && MONTH_RANGE1.indexOf(month) >= 0) {
          let month2 = dateStr.substring(
            format_index + indexOffset + 1,
            format_index + format_length + indexOffset + 1
          );
          if (MONTH_RANGE2.indexOf(month2) >= 0) {
            month = month + month2;
            indexOffset = indexOffset + 1;
          }
        }
        month = month * 1 - 1;
        break;
      case "day":
        day = dateStr.substring(
          format_index + indexOffset,
          format_index + format_length + indexOffset
        );
        if (format_length == 1 && DAY_RANGE1.indexOf(day) >= 0) {
          let day2 = dateStr.substring(
            format_index + indexOffset + 1,
            format_index + format_length + indexOffset + 1
          );
          if (DAY_RANGE2.indexOf(day2) >= 0) {
            day = day + day2;
            indexOffset = indexOffset + 1;
          }
        }
        break;
      case "hour":
        hour = dateStr.substring(
          format_index + indexOffset,
          format_index + format_length + indexOffset
        );
        if (format_length == 1 && HOUR_RANGE1.indexOf(hour) >= 0) {
          let hour2 = dateStr.substring(
            format_index + indexOffset + 1,
            format_index + format_length + indexOffset + 1
          );
          if (HOUR_RANGE2.indexOf(hour2) >= 0) {
            hour = hour + hour2;
            indexOffset = indexOffset + 1;
          }
        }
        break;
      case "minute":
        minute = dateStr.substring(
          format_index + indexOffset,
          format_index + format_length + indexOffset
        );
        if (format_length == 1 && MINUTE_RANGE1.indexOf(minute) >= 0) {
          let minute2 = dateStr.substring(
            format_index + indexOffset + 1,
            format_index + format_length + indexOffset + 1
          );
          if (MINUTE_RANGE2.indexOf(minute2) >= 0) {
            minute = minute + minute2;
            indexOffset = indexOffset + 1;
          }
        }
        break;
      case "second":
        second = dateStr.substring(
          format_index + indexOffset,
          format_index + format_length + indexOffset
        );
        if (format_length == 1 && SECOND_RANGE1.indexOf(second) >= 0) {
          let second2 = dateStr.substring(
            format_index + indexOffset + 1,
            format_index + format_length + indexOffset + 1
          );
          if (SECOND_RANGE2.indexOf(second2) >= 0) {
            second = second + second2;
            indexOffset = indexOffset + 1;
          }
        }
        break;
    }
    minFormatItem = getMinItem(dateBuffer, minFormatItem);
  }
  let year_new, month_new, day_new, hour_new, minute_new, second_new;

  if (year) {
    //newdate.setYear(year);
    year_new = year;
  } else {
    year_new = newdate.getFullYear();
  }
  if (month || month == 0) {
    //newdate.setMonth(month);
    month_new = month;
  } else {
    month_new = newdate.getMonth();
  }
  if (day) {
    //newdate.setDate(day);
    day_new = day;
  } else {
    day_new = newdate.getDate();
  }
  if (hour || hour == 0) {
    //newdate.setHours(hour);
    hour_new = hour;
  } else {
    hour_new = newdate.getHours();
  }
  if (minute || minute == 0) {
    //newdate.setMinutes(minute);
    minute_new = minute;
  } else {
    minute_new = newdate.getMinutes();
  }
  if (second || second == 0) {
    //newdate.setSeconds(second);
    second_new = second;
  } else if (!keepSecond) {
    newdate.setSeconds(0);
    second_new = 0;
  } else {
    second_new = newdate.getSeconds();
  }
  //newdate.setMilliseconds(0);// 毫秒设置为0，
  return new Date(
    year_new,
    month_new,
    day_new,
    hour_new,
    minute_new,
    second_new
  );
}

/** 判断是否是函数 */
export const isFunction = (func) => typeof func === "function";

/**
 * 在文本框光标位置插入字符
 * @param insertTxt
 */
export function insertInputTxt (elInput,insertTxt) {
  let startPos = elInput.selectionStart
  let endPos = elInput.selectionEnd
  if (startPos === undefined || endPos === undefined) return
  let txt = elInput.value
  let result = txt.substring(0, startPos) + insertTxt + txt.substring(endPos)
  elInput.value = result
  elInput.focus()
  elInput.selectionStart = startPos + insertTxt.length
  elInput.selectionEnd = startPos + insertTxt.length
}

//处理粘贴板中的数据
// export function handleClipBoardData(event,instance) {
//   // 获取解析 粘贴的文本
//   let text = (event.clipboardData || window.clipboardData).getData("text");
//   if (text.startsWith("$$editor$$")) {
//     event.preventDefault();
//     let { cell } = instance.helper.handleCopyDataToParas(text);
//     let pasteText = instance.helper.getParagraphsText(cell.paragraph);
//     if (pasteText.endsWith("\n")) {
//       pasteText = pasteText.replace(/\n/g, "");
//       insertInputTxt(event.target, pasteText);
//     }
//   }
// }
/**
 * 数组去重
 * @param {*} arr 接收的原数组
 * @param {*} key 如果是对象数组[{id: 1}, {id: 2}, {id: 3}]，则需要以什么key作为重复的标准，普通数组[1,2,3,2]不需要
 */
export function arrUnique(arr, key) {
  let returnArr = [];
  if (key) {
    // 对象数组去重
    const obj = {};
    returnArr = arr.reduce((cur, next) => {
      obj[next[key]] ? '' : obj[next[key]] = true && cur.push(next);
      return cur;
    }, []);
    return returnArr;
  }
  // 普通数组去重
  returnArr = arr.reduce((cur, next) => {
    !cur.includes(next) && cur.push(next);
    return cur;
  }, []);
  return returnArr;
}

export function getUUID(prefix) {
  const uuid = "xxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === "x" ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });

  return prefix + "-" + uuid;
}

export function isElementInViewport(el) {
  //获取元素是否在可视区域
  const rect = el.getBoundingClientRect();
  return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <=
      (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <=
      (window.innerWidth || document.documentElement.clientWidth)
  );
}

/**
 * 保留n位小数
 * @param number
 * @param n 需要保留的位数
 */

export function keepDecimal (number, n = 0) {
  return Math.round(number * Math.pow(10, n)) / Math.pow(10, n);
}

// 把image 转换为 canvas对象
export function getImageSrcWithWhiteBgc(image, type = 'image/jpeg') {
  const canvas = document.createElement('canvas')
  canvas.width = image.width
  canvas.height = image.height
  const context = canvas.getContext('2d')
  context.fillStyle = '#fff';
  context.fillRect(0, 0, canvas.width, canvas.height);
  context.drawImage(image, 0, 0)
  const src = canvas.toDataURL(type)
  return src;
}


export function isNumeric(str) {
  // 使用正则表达式判断是否为有效的数字
  return !isNaN(str) && !isNaN(parseFloat(str));
}

export function convertToNumber(str) {
  if (isNumeric(str)) {
    return parseFloat(str);
  } else {
    return str; // 如果不是数字，返回原始字符串
  }
}

export function deleteUndefinedProperties(obj) {
  Object.keys(obj).forEach(key => {
    if (obj[key] === undefined) {
      delete obj[key];
    }
  });
}
