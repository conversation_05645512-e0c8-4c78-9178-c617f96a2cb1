<!doctype html>
<html style="width: 100%; height: 100%" lang="en">
<head>
  <meta charset="utf-8">
  <meta>
  <title></title>
  <style>
    html,body{
      margin:0px;
      width: 100%;
      height: 100%;
      overflow-Y: hidden;
    }
    .button_list{
      width: 350px;
      position: fixed;
      background: white;
      padding: 10px;
      height: calc(100% - 40px);
      overflow-Y: scroll;
      margin: 0 0 0 10px;
      top: 10px;
      border: 1px solid rgb(203,203,203);
    }
    .content{
      /* height: 930px;
      width: 1920px; */
      display: flex;
      width: 100%;
      height: 100%;
      margin:0 0 0 0px;
      overflow-Y: hidden;
    }
    .fieldset{
      border: 1px solid rgb(24, 144, 255);
    }
    .fieldset-color{
      font-family: "微软雅黑";
      color: red;
      font-weight: 600;
    }
    #btn-show-hide {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 99999;
    }
    #testChangeFontSize {
      width: 140px;
      height: 40px;
      position: fixed;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #ccc;
      text-align: center;
      line-height: 40px;
      display: none;
    }
  </style>
</head>
<body style="width: 100%; height: 100%" >

  <button>测试</button>
  <div style="width: 100%; height: 100%;display: flex;">
    <div style="height: 100%;width: 100px;"></div>
    <div class="content" id="content"></div>
  </div>

  <script>
    
  </script>
  <script type="module">
    import initEditor from "./src/index.ts"
    import {R2M} from "./src/main/R2M.ts"
  
    
    // let localStorage_rawData=localStorage.getItem("rawData")
    // if (!localStorage_rawData) {
    //   localStorage_rawData=rawData
    // }else{
    //   localStorage_rawData = localStorage_rawData.replace(/^"|"$/g, '');
    // }
    const instance = initEditor("#content")

    const {editor} = instance
    const localData = localStorage.getItem("rawData");
    localData && editor.reInitRaw(JSON.parse(localData));
    editor.refreshDocument();
    const testBtn = document.getElementsByTagName("button")[0];
    testBtn.addEventListener("click", () => {
      const data = JSON.parse(localStorage.getItem("data1"));
      const {headerCell, footerCell, rootCell} = R2M.rawData2ModelData(editor, data);
      editor.current_cell = editor.root_cell = rootCell;
      editor.headerCell = headerCell;
      editor.footerCell = footerCell;
      editor.selection.setCursorByRootCell("start");
      // 此时更新的文档样式 跟更新之前光标位置处的 fontState 一样
      editor.refreshDocument();
    })
  </script>
</body>
</html>
