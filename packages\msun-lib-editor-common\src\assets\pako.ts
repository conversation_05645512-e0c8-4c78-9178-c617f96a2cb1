// @ts-nocheck
/* eslint-disable */
function MsunEditorPako () { function t (t) { let e = t.length; for (;--e >= 0;)t[e] = 0; } const e = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0]); const a = new Uint8Array([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13]); const i = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7]); const n = new Uint8Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]); const s = new Array(576); t(s); const r = new Array(60); t(r); const o = new Array(512); t(o); const l = new Array(256); t(l); const h = new Array(29); t(h); const d = new Array(30); function _ (t, e, a, i, n) { this.static_tree = t, this.extra_bits = e, this.extra_base = a, this.elems = i, this.max_length = n, this.has_stree = t && t.length; } let f, c, u; function w (t, e) { this.dyn_tree = t, this.max_code = 0, this.stat_desc = e; }t(d); const m = t => t < 256 ? o[t] : o[256 + (t >>> 7)]; const b = (t, e) => { t.pending_buf[t.pending++] = 255 & e, t.pending_buf[t.pending++] = e >>> 8 & 255; }; const g = (t, e, a) => { t.bi_valid > 16 - a ? (t.bi_buf |= e << t.bi_valid & 65535, b(t, t.bi_buf), t.bi_buf = e >> 16 - t.bi_valid, t.bi_valid += a - 16) : (t.bi_buf |= e << t.bi_valid & 65535, t.bi_valid += a); }; const p = (t, e, a) => { g(t, a[2 * e], a[2 * e + 1]); }; const k = (t, e) => { let a = 0; do { a |= 1 & t, t >>>= 1, a <<= 1; } while (--e > 0); return a >>> 1; }; const y = (t, e, a) => { const i = new Array(16); let n; let s; let r = 0; for (n = 1; n <= 15; n++)r = r + a[n - 1] << 1, i[n] = r; for (s = 0; s <= e; s++) { const e = t[2 * s + 1]; e !== 0 && (t[2 * s] = k(i[e]++, e)); } }; const v = t => { let e; for (e = 0; e < 286; e++)t.dyn_ltree[2 * e] = 0; for (e = 0; e < 30; e++)t.dyn_dtree[2 * e] = 0; for (e = 0; e < 19; e++)t.bl_tree[2 * e] = 0; t.dyn_ltree[512] = 1, t.opt_len = t.static_len = 0, t.sym_next = t.matches = 0; }; const x = t => { t.bi_valid > 8 ? b(t, t.bi_buf) : t.bi_valid > 0 && (t.pending_buf[t.pending++] = t.bi_buf), t.bi_buf = 0, t.bi_valid = 0; }; const z = (t, e, a, i) => { const n = 2 * e; const s = 2 * a; return t[n] < t[s] || t[n] === t[s] && i[e] <= i[a]; }; const A = (t, e, a) => { const i = t.heap[a]; let n = a << 1; for (;n <= t.heap_len && (n < t.heap_len && z(e, t.heap[n + 1], t.heap[n], t.depth) && n++, !z(e, i, t.heap[n], t.depth));)t.heap[a] = t.heap[n], a = n, n <<= 1; t.heap[a] = i; }; const E = (t, i, n) => { let s; let r; let o; let _; let f = 0; if (t.sym_next !== 0) do { s = 255 & t.pending_buf[t.sym_buf + f++], s += (255 & t.pending_buf[t.sym_buf + f++]) << 8, r = t.pending_buf[t.sym_buf + f++], s === 0 ? p(t, r, i) : (o = l[r], p(t, o + 256 + 1, i), (_ = e[o]) !== 0 && (r -= h[o], g(t, r, _)), o = m(--s), p(t, o, n), (_ = a[o]) !== 0 && (s -= d[o], g(t, s, _))); } while (f < t.sym_next); p(t, 256, i); }; const R = (t, e) => { const a = e.dyn_tree; const i = e.stat_desc.static_tree; const n = e.stat_desc.has_stree; const s = e.stat_desc.elems; let r; let o; let l; let h = -1; for (t.heap_len = 0, t.heap_max = 573, r = 0; r < s; r++)a[2 * r] !== 0 ? (t.heap[++t.heap_len] = h = r, t.depth[r] = 0) : a[2 * r + 1] = 0; for (;t.heap_len < 2;)a[2 * (l = t.heap[++t.heap_len] = h < 2 ? ++h : 0)] = 1, t.depth[l] = 0, t.opt_len--, n && (t.static_len -= i[2 * l + 1]); for (e.max_code = h, r = t.heap_len >> 1; r >= 1; r--)A(t, a, r); l = s; do { r = t.heap[1], t.heap[1] = t.heap[t.heap_len--], A(t, a, 1), o = t.heap[1], t.heap[--t.heap_max] = r, t.heap[--t.heap_max] = o, a[2 * l] = a[2 * r] + a[2 * o], t.depth[l] = (t.depth[r] >= t.depth[o] ? t.depth[r] : t.depth[o]) + 1, a[2 * r + 1] = a[2 * o + 1] = l, t.heap[1] = l++, A(t, a, 1); } while (t.heap_len >= 2); t.heap[--t.heap_max] = t.heap[1], ((t, e) => { const a = e.dyn_tree; const i = e.max_code; const n = e.stat_desc.static_tree; const s = e.stat_desc.has_stree; const r = e.stat_desc.extra_bits; const o = e.stat_desc.extra_base; const l = e.stat_desc.max_length; let h; let d; let _; let f; let c; let u; let w = 0; for (f = 0; f <= 15; f++)t.bl_count[f] = 0; for (a[2 * t.heap[t.heap_max] + 1] = 0, h = t.heap_max + 1; h < 573; h++)(f = a[2 * a[2 * (d = t.heap[h]) + 1] + 1] + 1) > l && (f = l, w++), a[2 * d + 1] = f, d > i || (t.bl_count[f]++, c = 0, d >= o && (c = r[d - o]), u = a[2 * d], t.opt_len += u * (f + c), s && (t.static_len += u * (n[2 * d + 1] + c))); if (w !== 0) { do { for (f = l - 1; t.bl_count[f] === 0;)f--; t.bl_count[f]--, t.bl_count[f + 1] += 2, t.bl_count[l]--, w -= 2; } while (w > 0); for (f = l; f !== 0; f--) for (d = t.bl_count[f]; d !== 0;)(_ = t.heap[--h]) > i || (a[2 * _ + 1] !== f && (t.opt_len += (f - a[2 * _ + 1]) * a[2 * _], a[2 * _ + 1] = f), d--); } })(t, e), y(a, h, t.bl_count); }; const Z = (t, e, a) => { let i; let n; let s = -1; let r = e[1]; let o = 0; let l = 7; let h = 4; for (r === 0 && (l = 138, h = 3), e[2 * (a + 1) + 1] = 65535, i = 0; i <= a; i++)n = r, r = e[2 * (i + 1) + 1], ++o < l && n === r || (o < h ? t.bl_tree[2 * n] += o : n !== 0 ? (n !== s && t.bl_tree[2 * n]++, t.bl_tree[32]++) : o <= 10 ? t.bl_tree[34]++ : t.bl_tree[36]++, o = 0, s = n, r === 0 ? (l = 138, h = 3) : n === r ? (l = 6, h = 3) : (l = 7, h = 4)); }; const U = (t, e, a) => { let i; let n; let s = -1; let r = e[1]; let o = 0; let l = 7; let h = 4; for (r === 0 && (l = 138, h = 3), i = 0; i <= a; i++) if (n = r, r = e[2 * (i + 1) + 1], !(++o < l && n === r)) { if (o < h) do { p(t, n, t.bl_tree); } while (--o != 0); else n !== 0 ? (n !== s && (p(t, n, t.bl_tree), o--), p(t, 16, t.bl_tree), g(t, o - 3, 2)) : o <= 10 ? (p(t, 17, t.bl_tree), g(t, o - 3, 3)) : (p(t, 18, t.bl_tree), g(t, o - 11, 7)); o = 0, s = n, r === 0 ? (l = 138, h = 3) : n === r ? (l = 6, h = 3) : (l = 7, h = 4); } }; let S = !1; const D = (t, e, a, i) => { g(t, 0 + (i ? 1 : 0), 3), x(t), b(t, a), b(t, ~a), a && t.pending_buf.set(t.window.subarray(e, e + a), t.pending), t.pending += a; }; const T = { _tr_init: t => { S || ((() => { let t, n, w, m, b; const g = new Array(16); for (w = 0, m = 0; m < 28; m++) for (h[m] = w, t = 0; t < 1 << e[m]; t++)l[w++] = m; for (l[w - 1] = m, b = 0, m = 0; m < 16; m++) for (d[m] = b, t = 0; t < 1 << a[m]; t++)o[b++] = m; for (b >>= 7; m < 30; m++) for (d[m] = b << 7, t = 0; t < 1 << a[m] - 7; t++)o[256 + b++] = m; for (n = 0; n <= 15; n++)g[n] = 0; for (t = 0; t <= 143;)s[2 * t + 1] = 8, t++, g[8]++; for (;t <= 255;)s[2 * t + 1] = 9, t++, g[9]++; for (;t <= 279;)s[2 * t + 1] = 7, t++, g[7]++; for (;t <= 287;)s[2 * t + 1] = 8, t++, g[8]++; for (y(s, 287, g), t = 0; t < 30; t++)r[2 * t + 1] = 5, r[2 * t] = k(t, 5); f = new _(s, e, 257, 286, 15), c = new _(r, a, 0, 30, 15), u = new _(new Array(0), i, 0, 19, 7); })(), S = !0), t.l_desc = new w(t.dyn_ltree, f), t.d_desc = new w(t.dyn_dtree, c), t.bl_desc = new w(t.bl_tree, u), t.bi_buf = 0, t.bi_valid = 0, v(t); }, _tr_stored_block: D, _tr_flush_block: (t, e, a, i) => { let o; let l; let h = 0; t.level > 0 ? (t.strm.data_type === 2 && (t.strm.data_type = (t => { let e; let a = 4093624447; for (e = 0; e <= 31; e++, a >>>= 1) if (1 & a && t.dyn_ltree[2 * e] !== 0) return 0; if (t.dyn_ltree[18] !== 0 || t.dyn_ltree[20] !== 0 || t.dyn_ltree[26] !== 0) return 1; for (e = 32; e < 256; e++) if (t.dyn_ltree[2 * e] !== 0) return 1; return 0; })(t)), R(t, t.l_desc), R(t, t.d_desc), h = (t => { let e; for (Z(t, t.dyn_ltree, t.l_desc.max_code), Z(t, t.dyn_dtree, t.d_desc.max_code), R(t, t.bl_desc), e = 18; e >= 3 && t.bl_tree[2 * n[e] + 1] === 0; e--);return t.opt_len += 3 * (e + 1) + 5 + 5 + 4, e; })(t), o = t.opt_len + 3 + 7 >>> 3, (l = t.static_len + 3 + 7 >>> 3) <= o && (o = l)) : o = l = a + 5, a + 4 <= o && e !== -1 ? D(t, e, a, i) : t.strategy === 4 || l === o ? (g(t, 2 + (i ? 1 : 0), 3), E(t, s, r)) : (g(t, 4 + (i ? 1 : 0), 3), ((t, e, a, i) => { let s; for (g(t, e - 257, 5), g(t, a - 1, 5), g(t, i - 4, 4), s = 0; s < i; s++)g(t, t.bl_tree[2 * n[s] + 1], 3); U(t, t.dyn_ltree, e - 1), U(t, t.dyn_dtree, a - 1); })(t, t.l_desc.max_code + 1, t.d_desc.max_code + 1, h + 1), E(t, t.dyn_ltree, t.dyn_dtree)), v(t), i && x(t); }, _tr_tally: (t, e, a) => (t.pending_buf[t.sym_buf + t.sym_next++] = e, t.pending_buf[t.sym_buf + t.sym_next++] = e >> 8, t.pending_buf[t.sym_buf + t.sym_next++] = a, e === 0 ? t.dyn_ltree[2 * a]++ : (t.matches++, e--, t.dyn_ltree[2 * (l[a] + 256 + 1)]++, t.dyn_dtree[2 * m(e)]++), t.sym_next === t.sym_end), _tr_align: t => { g(t, 2, 3), p(t, 256, s), (t => { t.bi_valid === 16 ? (b(t, t.bi_buf), t.bi_buf = 0, t.bi_valid = 0) : t.bi_valid >= 8 && (t.pending_buf[t.pending++] = 255 & t.bi_buf, t.bi_buf >>= 8, t.bi_valid -= 8); })(t); } }; const O = (t, e, a, i) => { let n = 65535 & t | 0; let s = t >>> 16 & 65535 | 0; let r = 0; for (;a !== 0;) { a -= r = a > 2e3 ? 2e3 : a; do { s = s + (n = n + e[i++] | 0) | 0; } while (--r); n %= 65521, s %= 65521; } return n | s << 16 | 0; }; const F = new Uint32Array((() => { let t; const e = []; for (let a = 0; a < 256; a++) { t = a; for (let e = 0; e < 8; e++)t = 1 & t ? 3988292384 ^ t >>> 1 : t >>> 1; e[a] = t; } return e; })()); const I = (t, e, a, i) => { const n = F; const s = i + a; t ^= -1; for (let a = i; a < s; a++)t = t >>> 8 ^ n[255 & (t ^ e[a])]; return -1 ^ t; }; const L = { 2: "need dictionary", 1: "stream end", 0: "", "-1": "file error", "-2": "stream error", "-3": "data error", "-4": "insufficient memory", "-5": "buffer error", "-6": "incompatible version" }; const N = { Z_NO_FLUSH: 0, Z_PARTIAL_FLUSH: 1, Z_SYNC_FLUSH: 2, Z_FULL_FLUSH: 3, Z_FINISH: 4, Z_BLOCK: 5, Z_TREES: 6, Z_OK: 0, Z_STREAM_END: 1, Z_NEED_DICT: 2, Z_ERRNO: -1, Z_STREAM_ERROR: -2, Z_DATA_ERROR: -3, Z_MEM_ERROR: -4, Z_BUF_ERROR: -5, Z_NO_COMPRESSION: 0, Z_BEST_SPEED: 1, Z_BEST_COMPRESSION: 9, Z_DEFAULT_COMPRESSION: -1, Z_FILTERED: 1, Z_HUFFMAN_ONLY: 2, Z_RLE: 3, Z_FIXED: 4, Z_DEFAULT_STRATEGY: 0, Z_BINARY: 0, Z_TEXT: 1, Z_UNKNOWN: 2, Z_DEFLATED: 8 }; const { _tr_init: B, _tr_stored_block: C, _tr_flush_block: M, _tr_tally: H, _tr_align: j } = T; const { Z_NO_FLUSH: K, Z_PARTIAL_FLUSH: P, Z_FULL_FLUSH: Y, Z_FINISH: G, Z_BLOCK: X, Z_OK: W, Z_STREAM_END: q, Z_STREAM_ERROR: J, Z_DATA_ERROR: Q, Z_BUF_ERROR: V, Z_DEFAULT_COMPRESSION: $, Z_FILTERED: tt, Z_HUFFMAN_ONLY: et, Z_RLE: at, Z_FIXED: it, Z_DEFAULT_STRATEGY: nt, Z_UNKNOWN: st, Z_DEFLATED: rt } = N; const ot = 286; const lt = 30; const ht = 19; const dt = 2 * ot + 1; const _t = 15; const ft = (t, e) => (t.msg = L[e], e); const ct = t => 2 * t - (t > 4 ? 9 : 0); const ut = t => { let e = t.length; for (;--e >= 0;)t[e] = 0; }; const wt = t => { let e, a, i; const n = t.w_size; i = e = t.hash_size; do { a = t.head[--i], t.head[i] = a >= n ? a - n : 0; } while (--e); i = e = n; do { a = t.prev[--i], t.prev[i] = a >= n ? a - n : 0; } while (--e); }; const mt = (t, e, a) => (e << t.hash_shift ^ a) & t.hash_mask; const bt = t => { const e = t.state; let a = e.pending; a > t.avail_out && (a = t.avail_out), a !== 0 && (t.output.set(e.pending_buf.subarray(e.pending_out, e.pending_out + a), t.next_out), t.next_out += a, e.pending_out += a, t.total_out += a, t.avail_out -= a, e.pending -= a, e.pending === 0 && (e.pending_out = 0)); }; const gt = (t, e) => { M(t, t.block_start >= 0 ? t.block_start : -1, t.strstart - t.block_start, e), t.block_start = t.strstart, bt(t.strm); }; const pt = (t, e) => { t.pending_buf[t.pending++] = e; }; const kt = (t, e) => { t.pending_buf[t.pending++] = e >>> 8 & 255, t.pending_buf[t.pending++] = 255 & e; }; const yt = (t, e, a, i) => { let n = t.avail_in; return n > i && (n = i), n === 0 ? 0 : (t.avail_in -= n, e.set(t.input.subarray(t.next_in, t.next_in + n), a), t.state.wrap === 1 ? t.adler = O(t.adler, e, n, a) : t.state.wrap === 2 && (t.adler = I(t.adler, e, n, a)), t.next_in += n, t.total_in += n, n); }; const vt = (t, e) => { let a; let i; let n = t.max_chain_length; let s = t.strstart; let r = t.prev_length; let o = t.nice_match; const l = t.strstart > t.w_size - 262 ? t.strstart - (t.w_size - 262) : 0; const h = t.window; const d = t.w_mask; const _ = t.prev; const f = t.strstart + 258; let c = h[s + r - 1]; let u = h[s + r]; t.prev_length >= t.good_match && (n >>= 2), o > t.lookahead && (o = t.lookahead); do { if (h[(a = e) + r] === u && h[a + r - 1] === c && h[a] === h[s] && h[++a] === h[s + 1]) { s += 2, a++; do {} while (h[++s] === h[++a] && h[++s] === h[++a] && h[++s] === h[++a] && h[++s] === h[++a] && h[++s] === h[++a] && h[++s] === h[++a] && h[++s] === h[++a] && h[++s] === h[++a] && s < f); if (i = 258 - (f - s), s = f - 258, i > r) { if (t.match_start = e, r = i, i >= o) break; c = h[s + r - 1], u = h[s + r]; } } } while ((e = _[e & d]) > l && --n != 0); return r <= t.lookahead ? r : t.lookahead; }; const xt = t => { const e = t.w_size; let a, i, n; do { if (i = t.window_size - t.lookahead - t.strstart, t.strstart >= e + (e - 262) && (t.window.set(t.window.subarray(e, e + e - i), 0), t.match_start -= e, t.strstart -= e, t.block_start -= e, t.insert > t.strstart && (t.insert = t.strstart), wt(t), i += e), t.strm.avail_in === 0) break; if (a = yt(t.strm, t.window, t.strstart + t.lookahead, i), t.lookahead += a, t.lookahead + t.insert >= 3) for (n = t.strstart - t.insert, t.ins_h = t.window[n], t.ins_h = mt(t, t.ins_h, t.window[n + 1]); t.insert && (t.ins_h = mt(t, t.ins_h, t.window[n + 3 - 1]), t.prev[n & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = n, n++, t.insert--, !(t.lookahead + t.insert < 3));); } while (t.lookahead < 262 && t.strm.avail_in !== 0); }; const zt = (t, e) => { let a; let i; let n; let s = t.pending_buf_size - 5 > t.w_size ? t.w_size : t.pending_buf_size - 5; let r = 0; let o = t.strm.avail_in; do { if (a = 65535, n = t.bi_valid + 42 >> 3, t.strm.avail_out < n) break; if (n = t.strm.avail_out - n, a > (i = t.strstart - t.block_start) + t.strm.avail_in && (a = i + t.strm.avail_in), a > n && (a = n), a < s && (a === 0 && e !== G || e === K || a !== i + t.strm.avail_in)) break; r = e === G && a === i + t.strm.avail_in ? 1 : 0, C(t, 0, 0, r), t.pending_buf[t.pending - 4] = a, t.pending_buf[t.pending - 3] = a >> 8, t.pending_buf[t.pending - 2] = ~a, t.pending_buf[t.pending - 1] = ~a >> 8, bt(t.strm), i && (i > a && (i = a), t.strm.output.set(t.window.subarray(t.block_start, t.block_start + i), t.strm.next_out), t.strm.next_out += i, t.strm.avail_out -= i, t.strm.total_out += i, t.block_start += i, a -= i), a && (yt(t.strm, t.strm.output, t.strm.next_out, a), t.strm.next_out += a, t.strm.avail_out -= a, t.strm.total_out += a); } while (r === 0); return (o -= t.strm.avail_in) && (o >= t.w_size ? (t.matches = 2, t.window.set(t.strm.input.subarray(t.strm.next_in - t.w_size, t.strm.next_in), 0), t.strstart = t.w_size, t.insert = t.strstart) : (t.window_size - t.strstart <= o && (t.strstart -= t.w_size, t.window.set(t.window.subarray(t.w_size, t.w_size + t.strstart), 0), t.matches < 2 && t.matches++, t.insert > t.strstart && (t.insert = t.strstart)), t.window.set(t.strm.input.subarray(t.strm.next_in - o, t.strm.next_in), t.strstart), t.strstart += o, t.insert += o > t.w_size - t.insert ? t.w_size - t.insert : o), t.block_start = t.strstart), t.high_water < t.strstart && (t.high_water = t.strstart), r ? 4 : e !== K && e !== G && t.strm.avail_in === 0 && t.strstart === t.block_start ? 2 : (n = t.window_size - t.strstart, t.strm.avail_in > n && t.block_start >= t.w_size && (t.block_start -= t.w_size, t.strstart -= t.w_size, t.window.set(t.window.subarray(t.w_size, t.w_size + t.strstart), 0), t.matches < 2 && t.matches++, n += t.w_size, t.insert > t.strstart && (t.insert = t.strstart)), n > t.strm.avail_in && (n = t.strm.avail_in), n && (yt(t.strm, t.window, t.strstart, n), t.strstart += n, t.insert += n > t.w_size - t.insert ? t.w_size - t.insert : n), t.high_water < t.strstart && (t.high_water = t.strstart), n = t.bi_valid + 42 >> 3, s = (n = t.pending_buf_size - n > 65535 ? 65535 : t.pending_buf_size - n) > t.w_size ? t.w_size : n, ((i = t.strstart - t.block_start) >= s || (i || e === G) && e !== K && t.strm.avail_in === 0 && i <= n) && (a = i > n ? n : i, r = e === G && t.strm.avail_in === 0 && a === i ? 1 : 0, C(t, t.block_start, a, r), t.block_start += a, bt(t.strm)), r ? 3 : 1); }; const At = (t, e) => { let a, i; for (;;) { if (t.lookahead < 262) { if (xt(t), t.lookahead < 262 && e === K) return 1; if (t.lookahead === 0) break; } if (a = 0, t.lookahead >= 3 && (t.ins_h = mt(t, t.ins_h, t.window[t.strstart + 3 - 1]), a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = t.strstart), a !== 0 && t.strstart - a <= t.w_size - 262 && (t.match_length = vt(t, a)), t.match_length >= 3) if (i = H(t, t.strstart - t.match_start, t.match_length - 3), t.lookahead -= t.match_length, t.match_length <= t.max_lazy_match && t.lookahead >= 3) { t.match_length--; do { t.strstart++, t.ins_h = mt(t, t.ins_h, t.window[t.strstart + 3 - 1]), a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = t.strstart; } while (--t.match_length != 0); t.strstart++; } else t.strstart += t.match_length, t.match_length = 0, t.ins_h = t.window[t.strstart], t.ins_h = mt(t, t.ins_h, t.window[t.strstart + 1]); else i = H(t, 0, t.window[t.strstart]), t.lookahead--, t.strstart++; if (i && (gt(t, !1), t.strm.avail_out === 0)) return 1; } return t.insert = t.strstart < 2 ? t.strstart : 2, e === G ? (gt(t, !0), t.strm.avail_out === 0 ? 3 : 4) : t.sym_next && (gt(t, !1), t.strm.avail_out === 0) ? 1 : 2; }; const Et = (t, e) => { let a, i, n; for (;;) { if (t.lookahead < 262) { if (xt(t), t.lookahead < 262 && e === K) return 1; if (t.lookahead === 0) break; } if (a = 0, t.lookahead >= 3 && (t.ins_h = mt(t, t.ins_h, t.window[t.strstart + 3 - 1]), a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = t.strstart), t.prev_length = t.match_length, t.prev_match = t.match_start, t.match_length = 2, a !== 0 && t.prev_length < t.max_lazy_match && t.strstart - a <= t.w_size - 262 && (t.match_length = vt(t, a), t.match_length <= 5 && (t.strategy === tt || t.match_length === 3 && t.strstart - t.match_start > 4096) && (t.match_length = 2)), t.prev_length >= 3 && t.match_length <= t.prev_length) { n = t.strstart + t.lookahead - 3, i = H(t, t.strstart - 1 - t.prev_match, t.prev_length - 3), t.lookahead -= t.prev_length - 1, t.prev_length -= 2; do { ++t.strstart <= n && (t.ins_h = mt(t, t.ins_h, t.window[t.strstart + 3 - 1]), a = t.prev[t.strstart & t.w_mask] = t.head[t.ins_h], t.head[t.ins_h] = t.strstart); } while (--t.prev_length != 0); if (t.match_available = 0, t.match_length = 2, t.strstart++, i && (gt(t, !1), t.strm.avail_out === 0)) return 1; } else if (t.match_available) { if ((i = H(t, 0, t.window[t.strstart - 1])) && gt(t, !1), t.strstart++, t.lookahead--, t.strm.avail_out === 0) return 1; } else t.match_available = 1, t.strstart++, t.lookahead--; } return t.match_available && (i = H(t, 0, t.window[t.strstart - 1]), t.match_available = 0), t.insert = t.strstart < 2 ? t.strstart : 2, e === G ? (gt(t, !0), t.strm.avail_out === 0 ? 3 : 4) : t.sym_next && (gt(t, !1), t.strm.avail_out === 0) ? 1 : 2; }; function Rt (t, e, a, i, n) { this.good_length = t, this.max_lazy = e, this.nice_length = a, this.max_chain = i, this.func = n; } const Zt = [new Rt(0, 0, 0, 0, zt), new Rt(4, 4, 8, 4, At), new Rt(4, 5, 16, 8, At), new Rt(4, 6, 32, 32, At), new Rt(4, 4, 16, 16, Et), new Rt(8, 16, 32, 32, Et), new Rt(8, 16, 128, 128, Et), new Rt(8, 32, 128, 256, Et), new Rt(32, 128, 258, 1024, Et), new Rt(32, 258, 258, 4096, Et)]; const Ut = t => { if (!t) return 1; const e = t.state; return !e || e.strm !== t || e.status !== 42 && e.status !== 57 && e.status !== 69 && e.status !== 73 && e.status !== 91 && e.status !== 103 && e.status !== 113 && e.status !== 666 ? 1 : 0; }; const St = t => { if (Ut(t)) return ft(t, J); t.total_in = t.total_out = 0, t.data_type = st; const e = t.state; return e.pending = 0, e.pending_out = 0, e.wrap < 0 && (e.wrap = -e.wrap), e.status = e.wrap === 2 ? 57 : e.wrap ? 42 : 113, t.adler = e.wrap === 2 ? 0 : 1, e.last_flush = -2, B(e), W; }; const Dt = t => { const e = St(t); return e === W && (t => { t.window_size = 2 * t.w_size, ut(t.head), t.max_lazy_match = Zt[t.level].max_lazy, t.good_match = Zt[t.level].good_length, t.nice_match = Zt[t.level].nice_length, t.max_chain_length = Zt[t.level].max_chain, t.strstart = 0, t.block_start = 0, t.lookahead = 0, t.insert = 0, t.match_length = t.prev_length = 2, t.match_available = 0, t.ins_h = 0; })(t.state), e; }; const Tt = (t, e, a, i, n, s) => { if (!t) return J; let r = 1; if (e === $ && (e = 6), i < 0 ? (r = 0, i = -i) : i > 15 && (r = 2, i -= 16), n < 1 || n > 9 || a !== rt || i < 8 || i > 15 || e < 0 || e > 9 || s < 0 || s > it || i === 8 && r !== 1) return ft(t, J); i === 8 && (i = 9); const o = new function () { this.strm = null, this.status = 0, this.pending_buf = null, this.pending_buf_size = 0, this.pending_out = 0, this.pending = 0, this.wrap = 0, this.gzhead = null, this.gzindex = 0, this.method = rt, this.last_flush = -1, this.w_size = 0, this.w_bits = 0, this.w_mask = 0, this.window = null, this.window_size = 0, this.prev = null, this.head = null, this.ins_h = 0, this.hash_size = 0, this.hash_bits = 0, this.hash_mask = 0, this.hash_shift = 0, this.block_start = 0, this.match_length = 0, this.prev_match = 0, this.match_available = 0, this.strstart = 0, this.match_start = 0, this.lookahead = 0, this.prev_length = 0, this.max_chain_length = 0, this.max_lazy_match = 0, this.level = 0, this.strategy = 0, this.good_match = 0, this.nice_match = 0, this.dyn_ltree = new Uint16Array(2 * dt), this.dyn_dtree = new Uint16Array(2 * (2 * lt + 1)), this.bl_tree = new Uint16Array(2 * (2 * ht + 1)), ut(this.dyn_ltree), ut(this.dyn_dtree), ut(this.bl_tree), this.l_desc = null, this.d_desc = null, this.bl_desc = null, this.bl_count = new Uint16Array(_t + 1), this.heap = new Uint16Array(2 * ot + 1), ut(this.heap), this.heap_len = 0, this.heap_max = 0, this.depth = new Uint16Array(2 * ot + 1), ut(this.depth), this.sym_buf = 0, this.lit_bufsize = 0, this.sym_next = 0, this.sym_end = 0, this.opt_len = 0, this.static_len = 0, this.matches = 0, this.insert = 0, this.bi_buf = 0, this.bi_valid = 0; }(); return t.state = o, o.strm = t, o.status = 42, o.wrap = r, o.gzhead = null, o.w_bits = i, o.w_size = 1 << o.w_bits, o.w_mask = o.w_size - 1, o.hash_bits = n + 7, o.hash_size = 1 << o.hash_bits, o.hash_mask = o.hash_size - 1, o.hash_shift = ~~((o.hash_bits + 3 - 1) / 3), o.window = new Uint8Array(2 * o.w_size), o.head = new Uint16Array(o.hash_size), o.prev = new Uint16Array(o.w_size), o.lit_bufsize = 1 << n + 6, o.pending_buf_size = 4 * o.lit_bufsize, o.pending_buf = new Uint8Array(o.pending_buf_size), o.sym_buf = o.lit_bufsize, o.sym_end = 3 * (o.lit_bufsize - 1), o.level = e, o.strategy = s, o.method = a, Dt(t); }; const Ot = { deflateInit: (t, e) => Tt(t, e, rt, 15, 8, nt), deflateInit2: Tt, deflateReset: Dt, deflateResetKeep: St, deflateSetHeader: (t, e) => Ut(t) || t.state.wrap !== 2 ? J : (t.state.gzhead = e, W), deflate: (t, e) => { if (Ut(t) || e > X || e < 0) return t ? ft(t, J) : J; const a = t.state; if (!t.output || t.avail_in !== 0 && !t.input || a.status === 666 && e !== G) return ft(t, t.avail_out === 0 ? V : J); const i = a.last_flush; if (a.last_flush = e, a.pending !== 0) { if (bt(t), t.avail_out === 0) return a.last_flush = -1, W; } else if (t.avail_in === 0 && ct(e) <= ct(i) && e !== G) return ft(t, V); if (a.status === 666 && t.avail_in !== 0) return ft(t, V); if (a.status === 42 && a.wrap === 0 && (a.status = 113), a.status === 42) { let e = rt + (a.w_bits - 8 << 4) << 8; let i = -1; if (e |= (i = a.strategy >= et || a.level < 2 ? 0 : a.level < 6 ? 1 : a.level === 6 ? 2 : 3) << 6, a.strstart !== 0 && (e |= 32), kt(a, e += 31 - e % 31), a.strstart !== 0 && (kt(a, t.adler >>> 16), kt(a, 65535 & t.adler)), t.adler = 1, a.status = 113, bt(t), a.pending !== 0) return a.last_flush = -1, W; } if (a.status === 57) if (t.adler = 0, pt(a, 31), pt(a, 139), pt(a, 8), a.gzhead)pt(a, (a.gzhead.text ? 1 : 0) + (a.gzhead.hcrc ? 2 : 0) + (a.gzhead.extra ? 4 : 0) + (a.gzhead.name ? 8 : 0) + (a.gzhead.comment ? 16 : 0)), pt(a, 255 & a.gzhead.time), pt(a, a.gzhead.time >> 8 & 255), pt(a, a.gzhead.time >> 16 & 255), pt(a, a.gzhead.time >> 24 & 255), pt(a, a.level === 9 ? 2 : a.strategy >= et || a.level < 2 ? 4 : 0), pt(a, 255 & a.gzhead.os), a.gzhead.extra && a.gzhead.extra.length && (pt(a, 255 & a.gzhead.extra.length), pt(a, a.gzhead.extra.length >> 8 & 255)), a.gzhead.hcrc && (t.adler = I(t.adler, a.pending_buf, a.pending, 0)), a.gzindex = 0, a.status = 69; else if (pt(a, 0), pt(a, 0), pt(a, 0), pt(a, 0), pt(a, 0), pt(a, a.level === 9 ? 2 : a.strategy >= et || a.level < 2 ? 4 : 0), pt(a, 3), a.status = 113, bt(t), a.pending !== 0) return a.last_flush = -1, W; if (a.status === 69) { if (a.gzhead.extra) { let e = a.pending; let i = (65535 & a.gzhead.extra.length) - a.gzindex; for (;a.pending + i > a.pending_buf_size;) { const n = a.pending_buf_size - a.pending; if (a.pending_buf.set(a.gzhead.extra.subarray(a.gzindex, a.gzindex + n), a.pending), a.pending = a.pending_buf_size, a.gzhead.hcrc && a.pending > e && (t.adler = I(t.adler, a.pending_buf, a.pending - e, e)), a.gzindex += n, bt(t), a.pending !== 0) return a.last_flush = -1, W; e = 0, i -= n; } const n = new Uint8Array(a.gzhead.extra); a.pending_buf.set(n.subarray(a.gzindex, a.gzindex + i), a.pending), a.pending += i, a.gzhead.hcrc && a.pending > e && (t.adler = I(t.adler, a.pending_buf, a.pending - e, e)), a.gzindex = 0; }a.status = 73; } if (a.status === 73) { if (a.gzhead.name) { let e; let i = a.pending; do { if (a.pending === a.pending_buf_size) { if (a.gzhead.hcrc && a.pending > i && (t.adler = I(t.adler, a.pending_buf, a.pending - i, i)), bt(t), a.pending !== 0) return a.last_flush = -1, W; i = 0; }e = a.gzindex < a.gzhead.name.length ? 255 & a.gzhead.name.charCodeAt(a.gzindex++) : 0, pt(a, e); } while (e !== 0); a.gzhead.hcrc && a.pending > i && (t.adler = I(t.adler, a.pending_buf, a.pending - i, i)), a.gzindex = 0; }a.status = 91; } if (a.status === 91) { if (a.gzhead.comment) { let e; let i = a.pending; do { if (a.pending === a.pending_buf_size) { if (a.gzhead.hcrc && a.pending > i && (t.adler = I(t.adler, a.pending_buf, a.pending - i, i)), bt(t), a.pending !== 0) return a.last_flush = -1, W; i = 0; }e = a.gzindex < a.gzhead.comment.length ? 255 & a.gzhead.comment.charCodeAt(a.gzindex++) : 0, pt(a, e); } while (e !== 0); a.gzhead.hcrc && a.pending > i && (t.adler = I(t.adler, a.pending_buf, a.pending - i, i)); }a.status = 103; } if (a.status === 103) { if (a.gzhead.hcrc) { if (a.pending + 2 > a.pending_buf_size && (bt(t), a.pending !== 0)) return a.last_flush = -1, W; pt(a, 255 & t.adler), pt(a, t.adler >> 8 & 255), t.adler = 0; } if (a.status = 113, bt(t), a.pending !== 0) return a.last_flush = -1, W; } if (t.avail_in !== 0 || a.lookahead !== 0 || e !== K && a.status !== 666) { const i = a.level === 0 ? zt(a, e) : a.strategy === et ? ((t, e) => { let a; for (;;) { if (t.lookahead === 0 && (xt(t), t.lookahead === 0)) { if (e === K) return 1; break; } if (t.match_length = 0, a = H(t, 0, t.window[t.strstart]), t.lookahead--, t.strstart++, a && (gt(t, !1), t.strm.avail_out === 0)) return 1; } return t.insert = 0, e === G ? (gt(t, !0), t.strm.avail_out === 0 ? 3 : 4) : t.sym_next && (gt(t, !1), t.strm.avail_out === 0) ? 1 : 2; })(a, e) : a.strategy === at ? ((t, e) => { let a, i, n, s; const r = t.window; for (;;) { if (t.lookahead <= 258) { if (xt(t), t.lookahead <= 258 && e === K) return 1; if (t.lookahead === 0) break; } if (t.match_length = 0, t.lookahead >= 3 && t.strstart > 0 && (i = r[n = t.strstart - 1]) === r[++n] && i === r[++n] && i === r[++n]) { s = t.strstart + 258; do {} while (i === r[++n] && i === r[++n] && i === r[++n] && i === r[++n] && i === r[++n] && i === r[++n] && i === r[++n] && i === r[++n] && n < s); t.match_length = 258 - (s - n), t.match_length > t.lookahead && (t.match_length = t.lookahead); } if (t.match_length >= 3 ? (a = H(t, 1, t.match_length - 3), t.lookahead -= t.match_length, t.strstart += t.match_length, t.match_length = 0) : (a = H(t, 0, t.window[t.strstart]), t.lookahead--, t.strstart++), a && (gt(t, !1), t.strm.avail_out === 0)) return 1; } return t.insert = 0, e === G ? (gt(t, !0), t.strm.avail_out === 0 ? 3 : 4) : t.sym_next && (gt(t, !1), t.strm.avail_out === 0) ? 1 : 2; })(a, e) : Zt[a.level].func(a, e); if (i !== 3 && i !== 4 || (a.status = 666), i === 1 || i === 3) return t.avail_out === 0 && (a.last_flush = -1), W; if (i === 2 && (e === P ? j(a) : e !== X && (C(a, 0, 0, !1), e === Y && (ut(a.head), a.lookahead === 0 && (a.strstart = 0, a.block_start = 0, a.insert = 0))), bt(t), t.avail_out === 0)) return a.last_flush = -1, W; } return e !== G ? W : a.wrap <= 0 ? q : (a.wrap === 2 ? (pt(a, 255 & t.adler), pt(a, t.adler >> 8 & 255), pt(a, t.adler >> 16 & 255), pt(a, t.adler >> 24 & 255), pt(a, 255 & t.total_in), pt(a, t.total_in >> 8 & 255), pt(a, t.total_in >> 16 & 255), pt(a, t.total_in >> 24 & 255)) : (kt(a, t.adler >>> 16), kt(a, 65535 & t.adler)), bt(t), a.wrap > 0 && (a.wrap = -a.wrap), a.pending !== 0 ? W : q); }, deflateEnd: t => { if (Ut(t)) return J; const e = t.state.status; return t.state = null, e === 113 ? ft(t, Q) : W; }, deflateSetDictionary: (t, e) => { let a = e.length; if (Ut(t)) return J; const i = t.state; const n = i.wrap; if (n === 2 || n === 1 && i.status !== 42 || i.lookahead) return J; if (n === 1 && (t.adler = O(t.adler, e, a, 0)), i.wrap = 0, a >= i.w_size) { n === 0 && (ut(i.head), i.strstart = 0, i.block_start = 0, i.insert = 0); const t = new Uint8Array(i.w_size); t.set(e.subarray(a - i.w_size, a), 0), e = t, a = i.w_size; } const s = t.avail_in; const r = t.next_in; const o = t.input; for (t.avail_in = a, t.next_in = 0, t.input = e, xt(i); i.lookahead >= 3;) { let t = i.strstart; let e = i.lookahead - 2; do { i.ins_h = mt(i, i.ins_h, i.window[t + 3 - 1]), i.prev[t & i.w_mask] = i.head[i.ins_h], i.head[i.ins_h] = t, t++; } while (--e); i.strstart = t, i.lookahead = 2, xt(i); } return i.strstart += i.lookahead, i.block_start = i.strstart, i.insert = i.lookahead, i.lookahead = 0, i.match_length = i.prev_length = 2, i.match_available = 0, t.next_in = r, t.input = o, t.avail_in = s, i.wrap = n, W; }, deflateInfo: "pako deflate (from Nodeca project)" }; const Ft = (t, e) => Object.prototype.hasOwnProperty.call(t, e); const It = { assign: function (t) { const e = Array.prototype.slice.call(arguments, 1); for (;e.length;) { const a = e.shift(); if (a) { if (typeof a !== "object") throw new TypeError(a + "must be non-object"); for (const e in a)Ft(a, e) && (t[e] = a[e]); } } return t; }, flattenChunks: t => { let e = 0; for (let a = 0, i = t.length; a < i; a++)e += t[a].length; const a = new Uint8Array(e); for (let e = 0, i = 0, n = t.length; e < n; e++) { const n = t[e]; a.set(n, i), i += n.length; } return a; } }; let Lt = !0; try { String.fromCharCode.apply(null, new Uint8Array(1)); } catch (t) { Lt = !1; } const Nt = new Uint8Array(256); for (let t = 0; t < 256; t++)Nt[t] = t >= 252 ? 6 : t >= 248 ? 5 : t >= 240 ? 4 : t >= 224 ? 3 : t >= 192 ? 2 : 1; Nt[254] = Nt[254] = 1; const Bt = { string2buf: t => { if (typeof TextEncoder === "function" && TextEncoder.prototype.encode) return (new TextEncoder()).encode(t); let e, a, i, n, s; const r = t.length; let o = 0; for (n = 0; n < r; n++)(64512 & (a = t.charCodeAt(n))) == 55296 && n + 1 < r && (64512 & (i = t.charCodeAt(n + 1))) == 56320 && (a = 65536 + (a - 55296 << 10) + (i - 56320), n++), o += a < 128 ? 1 : a < 2048 ? 2 : a < 65536 ? 3 : 4; for (e = new Uint8Array(o), s = 0, n = 0; s < o; n++)(64512 & (a = t.charCodeAt(n))) == 55296 && n + 1 < r && (64512 & (i = t.charCodeAt(n + 1))) == 56320 && (a = 65536 + (a - 55296 << 10) + (i - 56320), n++), a < 128 ? e[s++] = a : a < 2048 ? (e[s++] = 192 | a >>> 6, e[s++] = 128 | 63 & a) : a < 65536 ? (e[s++] = 224 | a >>> 12, e[s++] = 128 | a >>> 6 & 63, e[s++] = 128 | 63 & a) : (e[s++] = 240 | a >>> 18, e[s++] = 128 | a >>> 12 & 63, e[s++] = 128 | a >>> 6 & 63, e[s++] = 128 | 63 & a); return e; }, buf2string: (t, e) => { const a = e || t.length; if (typeof TextDecoder === "function" && TextDecoder.prototype.decode) return (new TextDecoder()).decode(t.subarray(0, e)); let i, n; const s = new Array(2 * a); for (n = 0, i = 0; i < a;) { let e = t[i++]; if (e < 128) { s[n++] = e; continue; } let r = Nt[e]; if (r > 4)s[n++] = 65533, i += r - 1; else { for (e &= r === 2 ? 31 : r === 3 ? 15 : 7; r > 1 && i < a;)e = e << 6 | 63 & t[i++], r--; r > 1 ? s[n++] = 65533 : e < 65536 ? s[n++] = e : (e -= 65536, s[n++] = 55296 | e >> 10 & 1023, s[n++] = 56320 | 1023 & e); } } return ((t, e) => { if (e < 65534 && t.subarray && Lt) return String.fromCharCode.apply(null, t.length === e ? t : t.subarray(0, e)); let a = ""; for (let i = 0; i < e; i++)a += String.fromCharCode(t[i]); return a; })(s, n); }, utf8border: (t, e) => { (e = e || t.length) > t.length && (e = t.length); let a = e - 1; for (;a >= 0 && (192 & t[a]) == 128;)a--; return a < 0 ? e : a === 0 ? e : a + Nt[t[a]] > e ? a : e; } }; const Ct = function () { this.input = null, this.next_in = 0, this.avail_in = 0, this.total_in = 0, this.output = null, this.next_out = 0, this.avail_out = 0, this.total_out = 0, this.msg = "", this.state = null, this.data_type = 2, this.adler = 0; }; const Mt = Object.prototype.toString; const { Z_NO_FLUSH: Ht, Z_SYNC_FLUSH: jt, Z_FULL_FLUSH: Kt, Z_FINISH: Pt, Z_OK: Yt, Z_STREAM_END: Gt, Z_DEFAULT_COMPRESSION: Xt, Z_DEFAULT_STRATEGY: Wt, Z_DEFLATED: qt } = N; function Jt (t) { this.options = It.assign({ level: Xt, method: qt, chunkSize: 16384, windowBits: 15, memLevel: 8, strategy: Wt }, t || {}); const e = this.options; e.raw && e.windowBits > 0 ? e.windowBits = -e.windowBits : e.gzip && e.windowBits > 0 && e.windowBits < 16 && (e.windowBits += 16), this.err = 0, this.msg = "", this.ended = !1, this.chunks = [], this.strm = new Ct(), this.strm.avail_out = 0; let a = Ot.deflateInit2(this.strm, e.level, e.method, e.windowBits, e.memLevel, e.strategy); if (a !== Yt) throw new Error(L[a]); if (e.header && Ot.deflateSetHeader(this.strm, e.header), e.dictionary) { let t; if (t = typeof e.dictionary === "string" ? Bt.string2buf(e.dictionary) : Mt.call(e.dictionary) === "[object ArrayBuffer]" ? new Uint8Array(e.dictionary) : e.dictionary, (a = Ot.deflateSetDictionary(this.strm, t)) !== Yt) throw new Error(L[a]); this._dict_set = !0; } } function Qt (t, e) { const a = new Jt(e); if (a.push(t, !0), a.err) throw a.msg || L[a.err]; return a.result; }Jt.prototype.push = function (t, e) { const a = this.strm; const i = this.options.chunkSize; let n, s; if (this.ended) return !1; for (s = e === ~~e ? e : !0 === e ? Pt : Ht, typeof t === "string" ? a.input = Bt.string2buf(t) : Mt.call(t) === "[object ArrayBuffer]" ? a.input = new Uint8Array(t) : a.input = t, a.next_in = 0, a.avail_in = a.input.length; ;) if (a.avail_out === 0 && (a.output = new Uint8Array(i), a.next_out = 0, a.avail_out = i), (s === jt || s === Kt) && a.avail_out <= 6) this.onData(a.output.subarray(0, a.next_out)), a.avail_out = 0; else { if ((n = Ot.deflate(a, s)) === Gt) return a.next_out > 0 && this.onData(a.output.subarray(0, a.next_out)), n = Ot.deflateEnd(this.strm), this.onEnd(n), this.ended = !0, n === Yt; if (a.avail_out !== 0) { if (s > 0 && a.next_out > 0) this.onData(a.output.subarray(0, a.next_out)), a.avail_out = 0; else if (a.avail_in === 0) break; } else this.onData(a.output); } return !0; }, Jt.prototype.onData = function (t) { this.chunks.push(t); }, Jt.prototype.onEnd = function (t) { t === Yt && (this.result = It.flattenChunks(this.chunks)), this.chunks = [], this.err = t, this.msg = this.strm.msg; }; const Vt = { Deflate: Jt, deflate: Qt, deflateRaw: function (t, e) { return (e = e || {}).raw = !0, Qt(t, e); }, gzip: function (t, e) { return (e = e || {}).gzip = !0, Qt(t, e); }, constants: N }; const $t = function (t, e) { let a, i, n, s, r, o, l, h, d, _, f, c, u, w, m, b, g, p, k, y, v, x, z, A; const E = t.state; a = t.next_in, z = t.input, i = a + (t.avail_in - 5), n = t.next_out, A = t.output, s = n - (e - t.avail_out), r = n + (t.avail_out - 257), o = E.dmax, l = E.wsize, h = E.whave, d = E.wnext, _ = E.window, f = E.hold, c = E.bits, u = E.lencode, w = E.distcode, m = (1 << E.lenbits) - 1, b = (1 << E.distbits) - 1; t:do { c < 15 && (f += z[a++] << c, c += 8, f += z[a++] << c, c += 8), g = u[f & m]; e:for (;;) { if (f >>>= p = g >>> 24, c -= p, (p = g >>> 16 & 255) === 0)A[n++] = 65535 & g; else { if (!(16 & p)) { if ((64 & p) == 0) { g = u[(65535 & g) + (f & (1 << p) - 1)]; continue e; } if (32 & p) { E.mode = 16191; break t; }t.msg = "invalid literal/length code", E.mode = 16209; break t; }k = 65535 & g, (p &= 15) && (c < p && (f += z[a++] << c, c += 8), k += f & (1 << p) - 1, f >>>= p, c -= p), c < 15 && (f += z[a++] << c, c += 8, f += z[a++] << c, c += 8), g = w[f & b]; a:for (;;) { if (f >>>= p = g >>> 24, c -= p, !(16 & (p = g >>> 16 & 255))) { if ((64 & p) == 0) { g = w[(65535 & g) + (f & (1 << p) - 1)]; continue a; }t.msg = "invalid distance code", E.mode = 16209; break t; } if (y = 65535 & g, c < (p &= 15) && (f += z[a++] << c, (c += 8) < p && (f += z[a++] << c, c += 8)), (y += f & (1 << p) - 1) > o) { t.msg = "invalid distance too far back", E.mode = 16209; break t; } if (f >>>= p, c -= p, y > (p = n - s)) { if ((p = y - p) > h && E.sane) { t.msg = "invalid distance too far back", E.mode = 16209; break t; } if (v = 0, x = _, d === 0) { if (v += l - p, p < k) { k -= p; do { A[n++] = _[v++]; } while (--p); v = n - y, x = A; } } else if (d < p) { if (v += l + d - p, (p -= d) < k) { k -= p; do { A[n++] = _[v++]; } while (--p); if (v = 0, d < k) { k -= p = d; do { A[n++] = _[v++]; } while (--p); v = n - y, x = A; } } } else if (v += d - p, p < k) { k -= p; do { A[n++] = _[v++]; } while (--p); v = n - y, x = A; } for (;k > 2;)A[n++] = x[v++], A[n++] = x[v++], A[n++] = x[v++], k -= 3; k && (A[n++] = x[v++], k > 1 && (A[n++] = x[v++])); } else { v = n - y; do { A[n++] = A[v++], A[n++] = A[v++], A[n++] = A[v++], k -= 3; } while (k > 2); k && (A[n++] = A[v++], k > 1 && (A[n++] = A[v++])); } break; } } break; } } while (a < i && n < r); a -= k = c >> 3, f &= (1 << (c -= k << 3)) - 1, t.next_in = a, t.next_out = n, t.avail_in = a < i ? i - a + 5 : 5 - (a - i), t.avail_out = n < r ? r - n + 257 : 257 - (n - r), E.hold = f, E.bits = c; }; const te = new Uint16Array([3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0]); const ee = new Uint8Array([16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18, 18, 19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72, 78]); const ae = new Uint16Array([1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193, 257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145, 8193, 12289, 16385, 24577, 0, 0]); const ie = new Uint8Array([16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 26, 27, 27, 28, 28, 29, 29, 64, 64]); const ne = (t, e, a, i, n, s, r, o) => { const l = o.bits; let h; let d; let _; let f; let c; let u; let w = 0; let m = 0; let b = 0; let g = 0; let p = 0; let k = 0; let y = 0; let v = 0; let x = 0; let z = 0; let A = null; const E = new Uint16Array(16); const R = new Uint16Array(16); let Z; let U; let S; let D = null; for (w = 0; w <= 15; w++)E[w] = 0; for (m = 0; m < i; m++)E[e[a + m]]++; for (p = l, g = 15; g >= 1 && E[g] === 0; g--);if (p > g && (p = g), g === 0) return n[s++] = 20971520, n[s++] = 20971520, o.bits = 1, 0; for (b = 1; b < g && E[b] === 0; b++);for (p < b && (p = b), v = 1, w = 1; w <= 15; w++) if (v <<= 1, (v -= E[w]) < 0) return -1; if (v > 0 && (t === 0 || g !== 1)) return -1; for (R[1] = 0, w = 1; w < 15; w++)R[w + 1] = R[w] + E[w]; for (m = 0; m < i; m++)e[a + m] !== 0 && (r[R[e[a + m]]++] = m); if (t === 0 ? (A = D = r, u = 20) : t === 1 ? (A = te, D = ee, u = 257) : (A = ae, D = ie, u = 0), z = 0, m = 0, w = b, c = s, k = p, y = 0, _ = -1, f = (x = 1 << p) - 1, t === 1 && x > 852 || t === 2 && x > 592) return 1; for (;;) { Z = w - y, r[m] + 1 < u ? (U = 0, S = r[m]) : r[m] >= u ? (U = D[r[m] - u], S = A[r[m] - u]) : (U = 96, S = 0), h = 1 << w - y, b = d = 1 << k; do { n[c + (z >> y) + (d -= h)] = Z << 24 | U << 16 | S | 0; } while (d !== 0); for (h = 1 << w - 1; z & h;)h >>= 1; if (h !== 0 ? (z &= h - 1, z += h) : z = 0, m++, --E[w] == 0) { if (w === g) break; w = e[a + r[m]]; } if (w > p && (z & f) !== _) { for (y === 0 && (y = p), c += b, v = 1 << (k = w - y); k + y < g && !((v -= E[k + y]) <= 0);)k++, v <<= 1; if (x += 1 << k, t === 1 && x > 852 || t === 2 && x > 592) return 1; n[_ = z & f] = p << 24 | k << 16 | c - s | 0; } } return z !== 0 && (n[c + z] = w - y << 24 | 64 << 16 | 0), o.bits = p, 0; }; const { Z_FINISH: se, Z_BLOCK: re, Z_TREES: oe, Z_OK: le, Z_STREAM_END: he, Z_NEED_DICT: de, Z_STREAM_ERROR: _e, Z_DATA_ERROR: fe, Z_MEM_ERROR: ce, Z_BUF_ERROR: ue, Z_DEFLATED: we } = N; const me = 16209; const be = t => (t >>> 24 & 255) + (t >>> 8 & 65280) + ((65280 & t) << 8) + ((255 & t) << 24); const ge = t => { if (!t) return 1; const e = t.state; return !e || e.strm !== t || e.mode < 16180 || e.mode > 16211 ? 1 : 0; }; const pe = t => { if (ge(t)) return _e; const e = t.state; return t.total_in = t.total_out = e.total = 0, t.msg = "", e.wrap && (t.adler = 1 & e.wrap), e.mode = 16180, e.last = 0, e.havedict = 0, e.flags = -1, e.dmax = 32768, e.head = null, e.hold = 0, e.bits = 0, e.lencode = e.lendyn = new Int32Array(852), e.distcode = e.distdyn = new Int32Array(592), e.sane = 1, e.back = -1, le; }; const ke = t => { if (ge(t)) return _e; const e = t.state; return e.wsize = 0, e.whave = 0, e.wnext = 0, pe(t); }; const ye = (t, e) => { let a; if (ge(t)) return _e; const i = t.state; return e < 0 ? (a = 0, e = -e) : (a = 5 + (e >> 4), e < 48 && (e &= 15)), e && (e < 8 || e > 15) ? _e : (i.window !== null && i.wbits !== e && (i.window = null), i.wrap = a, i.wbits = e, ke(t)); }; const ve = (t, e) => { if (!t) return _e; const a = new function () { this.strm = null, this.mode = 0, this.last = !1, this.wrap = 0, this.havedict = !1, this.flags = 0, this.dmax = 0, this.check = 0, this.total = 0, this.head = null, this.wbits = 0, this.wsize = 0, this.whave = 0, this.wnext = 0, this.window = null, this.hold = 0, this.bits = 0, this.length = 0, this.offset = 0, this.extra = 0, this.lencode = null, this.distcode = null, this.lenbits = 0, this.distbits = 0, this.ncode = 0, this.nlen = 0, this.ndist = 0, this.have = 0, this.next = null, this.lens = new Uint16Array(320), this.work = new Uint16Array(288), this.lendyn = null, this.distdyn = null, this.sane = 0, this.back = 0, this.was = 0; }(); t.state = a, a.strm = t, a.window = null, a.mode = 16180; const i = ye(t, e); return i !== le && (t.state = null), i; }; let xe; let ze; let Ae = !0; const Ee = t => { if (Ae) { xe = new Int32Array(512), ze = new Int32Array(32); let e = 0; for (;e < 144;)t.lens[e++] = 8; for (;e < 256;)t.lens[e++] = 9; for (;e < 280;)t.lens[e++] = 7; for (;e < 288;)t.lens[e++] = 8; for (ne(1, t.lens, 0, 288, xe, 0, t.work, { bits: 9 }), e = 0; e < 32;)t.lens[e++] = 5; ne(2, t.lens, 0, 32, ze, 0, t.work, { bits: 5 }), Ae = !1; }t.lencode = xe, t.lenbits = 9, t.distcode = ze, t.distbits = 5; }; const Re = (t, e, a, i) => { let n; const s = t.state; return s.window === null && (s.wsize = 1 << s.wbits, s.wnext = 0, s.whave = 0, s.window = new Uint8Array(s.wsize)), i >= s.wsize ? (s.window.set(e.subarray(a - s.wsize, a), 0), s.wnext = 0, s.whave = s.wsize) : ((n = s.wsize - s.wnext) > i && (n = i), s.window.set(e.subarray(a - i, a - i + n), s.wnext), (i -= n) ? (s.window.set(e.subarray(a - i, a), 0), s.wnext = i, s.whave = s.wsize) : (s.wnext += n, s.wnext === s.wsize && (s.wnext = 0), s.whave < s.wsize && (s.whave += n))), 0; }; const Ze = { inflateReset: ke, inflateReset2: ye, inflateResetKeep: pe, inflateInit: t => ve(t, 15), inflateInit2: ve, inflate: (t, e) => { let a; let i; let n; let s; let r; let o; let l; let h; let d; let _; let f; let c; let u; let w; let m; let b; let g; let p; let k; let y; let v; let x; let z = 0; const A = new Uint8Array(4); let E, R; const Z = new Uint8Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]); if (ge(t) || !t.output || !t.input && t.avail_in !== 0) return _e; (a = t.state).mode === 16191 && (a.mode = 16192), r = t.next_out, n = t.output, l = t.avail_out, s = t.next_in, i = t.input, o = t.avail_in, h = a.hold, d = a.bits, _ = o, f = l, x = le; t:for (;;) switch (a.mode) { case 16180:if (a.wrap === 0) { a.mode = 16192; break; } for (;d < 16;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if (2 & a.wrap && h === 35615) { a.wbits === 0 && (a.wbits = 15), a.check = 0, A[0] = 255 & h, A[1] = h >>> 8 & 255, a.check = I(a.check, A, 2, 0), h = 0, d = 0, a.mode = 16181; break; } if (a.head && (a.head.done = !1), !(1 & a.wrap) || (((255 & h) << 8) + (h >> 8)) % 31) { t.msg = "incorrect header check", a.mode = me; break; } if ((15 & h) !== we) { t.msg = "unknown compression method", a.mode = me; break; } if (d -= 4, v = 8 + (15 & (h >>>= 4)), a.wbits === 0 && (a.wbits = v), v > 15 || v > a.wbits) { t.msg = "invalid window size", a.mode = me; break; }a.dmax = 1 << a.wbits, a.flags = 0, t.adler = a.check = 1, a.mode = 512 & h ? 16189 : 16191, h = 0, d = 0; break; case 16181:for (;d < 16;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if (a.flags = h, (255 & a.flags) !== we) { t.msg = "unknown compression method", a.mode = me; break; } if (57344 & a.flags) { t.msg = "unknown header flags set", a.mode = me; break; }a.head && (a.head.text = h >> 8 & 1), 512 & a.flags && 4 & a.wrap && (A[0] = 255 & h, A[1] = h >>> 8 & 255, a.check = I(a.check, A, 2, 0)), h = 0, d = 0, a.mode = 16182; case 16182:for (;d < 32;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }a.head && (a.head.time = h), 512 & a.flags && 4 & a.wrap && (A[0] = 255 & h, A[1] = h >>> 8 & 255, A[2] = h >>> 16 & 255, A[3] = h >>> 24 & 255, a.check = I(a.check, A, 4, 0)), h = 0, d = 0, a.mode = 16183; case 16183:for (;d < 16;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }a.head && (a.head.xflags = 255 & h, a.head.os = h >> 8), 512 & a.flags && 4 & a.wrap && (A[0] = 255 & h, A[1] = h >>> 8 & 255, a.check = I(a.check, A, 2, 0)), h = 0, d = 0, a.mode = 16184; case 16184:if (1024 & a.flags) { for (;d < 16;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }a.length = h, a.head && (a.head.extra_len = h), 512 & a.flags && 4 & a.wrap && (A[0] = 255 & h, A[1] = h >>> 8 & 255, a.check = I(a.check, A, 2, 0)), h = 0, d = 0; } else a.head && (a.head.extra = null); a.mode = 16185; case 16185:if (1024 & a.flags && ((c = a.length) > o && (c = o), c && (a.head && (v = a.head.extra_len - a.length, a.head.extra || (a.head.extra = new Uint8Array(a.head.extra_len)), a.head.extra.set(i.subarray(s, s + c), v)), 512 & a.flags && 4 & a.wrap && (a.check = I(a.check, i, c, s)), o -= c, s += c, a.length -= c), a.length)) break t; a.length = 0, a.mode = 16186; case 16186:if (2048 & a.flags) { if (o === 0) break t; c = 0; do { v = i[s + c++], a.head && v && a.length < 65536 && (a.head.name += String.fromCharCode(v)); } while (v && c < o); if (512 & a.flags && 4 & a.wrap && (a.check = I(a.check, i, c, s)), o -= c, s += c, v) break t; } else a.head && (a.head.name = null); a.length = 0, a.mode = 16187; case 16187:if (4096 & a.flags) { if (o === 0) break t; c = 0; do { v = i[s + c++], a.head && v && a.length < 65536 && (a.head.comment += String.fromCharCode(v)); } while (v && c < o); if (512 & a.flags && 4 & a.wrap && (a.check = I(a.check, i, c, s)), o -= c, s += c, v) break t; } else a.head && (a.head.comment = null); a.mode = 16188; case 16188:if (512 & a.flags) { for (;d < 16;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if (4 & a.wrap && h !== (65535 & a.check)) { t.msg = "header crc mismatch", a.mode = me; break; }h = 0, d = 0; }a.head && (a.head.hcrc = a.flags >> 9 & 1, a.head.done = !0), t.adler = a.check = 0, a.mode = 16191; break; case 16189:for (;d < 32;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }t.adler = a.check = be(h), h = 0, d = 0, a.mode = 16190; case 16190:if (a.havedict === 0) return t.next_out = r, t.avail_out = l, t.next_in = s, t.avail_in = o, a.hold = h, a.bits = d, de; t.adler = a.check = 1, a.mode = 16191; case 16191:if (e === re || e === oe) break t; case 16192:if (a.last) { h >>>= 7 & d, d -= 7 & d, a.mode = 16206; break; } for (;d < 3;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } switch (a.last = 1 & h, d -= 1, 3 & (h >>>= 1)) { case 0:a.mode = 16193; break; case 1:if (Ee(a), a.mode = 16199, e === oe) { h >>>= 2, d -= 2; break t; } break; case 2:a.mode = 16196; break; case 3:t.msg = "invalid block type", a.mode = me; }h >>>= 2, d -= 2; break; case 16193:for (h >>>= 7 & d, d -= 7 & d; d < 32;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if ((65535 & h) != (h >>> 16 ^ 65535)) { t.msg = "invalid stored block lengths", a.mode = me; break; } if (a.length = 65535 & h, h = 0, d = 0, a.mode = 16194, e === oe) break t; case 16194:a.mode = 16195; case 16195:if (c = a.length) { if (c > o && (c = o), c > l && (c = l), c === 0) break t; n.set(i.subarray(s, s + c), r), o -= c, s += c, l -= c, r += c, a.length -= c; break; }a.mode = 16191; break; case 16196:for (;d < 14;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if (a.nlen = 257 + (31 & h), h >>>= 5, d -= 5, a.ndist = 1 + (31 & h), h >>>= 5, d -= 5, a.ncode = 4 + (15 & h), h >>>= 4, d -= 4, a.nlen > 286 || a.ndist > 30) { t.msg = "too many length or distance symbols", a.mode = me; break; }a.have = 0, a.mode = 16197; case 16197:for (;a.have < a.ncode;) { for (;d < 3;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }a.lens[Z[a.have++]] = 7 & h, h >>>= 3, d -= 3; } for (;a.have < 19;)a.lens[Z[a.have++]] = 0; if (a.lencode = a.lendyn, a.lenbits = 7, E = { bits: a.lenbits }, x = ne(0, a.lens, 0, 19, a.lencode, 0, a.work, E), a.lenbits = E.bits, x) { t.msg = "invalid code lengths set", a.mode = me; break; }a.have = 0, a.mode = 16198; case 16198:for (;a.have < a.nlen + a.ndist;) { for (;b = (z = a.lencode[h & (1 << a.lenbits) - 1]) >>> 16 & 255, g = 65535 & z, !((m = z >>> 24) <= d);) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if (g < 16)h >>>= m, d -= m, a.lens[a.have++] = g; else { if (g === 16) { for (R = m + 2; d < R;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if (h >>>= m, d -= m, a.have === 0) { t.msg = "invalid bit length repeat", a.mode = me; break; }v = a.lens[a.have - 1], c = 3 + (3 & h), h >>>= 2, d -= 2; } else if (g === 17) { for (R = m + 3; d < R;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }d -= m, v = 0, c = 3 + (7 & (h >>>= m)), h >>>= 3, d -= 3; } else { for (R = m + 7; d < R;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }d -= m, v = 0, c = 11 + (127 & (h >>>= m)), h >>>= 7, d -= 7; } if (a.have + c > a.nlen + a.ndist) { t.msg = "invalid bit length repeat", a.mode = me; break; } for (;c--;)a.lens[a.have++] = v; } } if (a.mode === me) break; if (a.lens[256] === 0) { t.msg = "invalid code -- missing end-of-block", a.mode = me; break; } if (a.lenbits = 9, E = { bits: a.lenbits }, x = ne(1, a.lens, 0, a.nlen, a.lencode, 0, a.work, E), a.lenbits = E.bits, x) { t.msg = "invalid literal/lengths set", a.mode = me; break; } if (a.distbits = 6, a.distcode = a.distdyn, E = { bits: a.distbits }, x = ne(2, a.lens, a.nlen, a.ndist, a.distcode, 0, a.work, E), a.distbits = E.bits, x) { t.msg = "invalid distances set", a.mode = me; break; } if (a.mode = 16199, e === oe) break t; case 16199:a.mode = 16200; case 16200:if (o >= 6 && l >= 258) { t.next_out = r, t.avail_out = l, t.next_in = s, t.avail_in = o, a.hold = h, a.bits = d, $t(t, f), r = t.next_out, n = t.output, l = t.avail_out, s = t.next_in, i = t.input, o = t.avail_in, h = a.hold, d = a.bits, a.mode === 16191 && (a.back = -1); break; } for (a.back = 0; b = (z = a.lencode[h & (1 << a.lenbits) - 1]) >>> 16 & 255, g = 65535 & z, !((m = z >>> 24) <= d);) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if (b && (240 & b) == 0) { for (p = m, k = b, y = g; b = (z = a.lencode[y + ((h & (1 << p + k) - 1) >> p)]) >>> 16 & 255, g = 65535 & z, !(p + (m = z >>> 24) <= d);) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }h >>>= p, d -= p, a.back += p; } if (h >>>= m, d -= m, a.back += m, a.length = g, b === 0) { a.mode = 16205; break; } if (32 & b) { a.back = -1, a.mode = 16191; break; } if (64 & b) { t.msg = "invalid literal/length code", a.mode = me; break; }a.extra = 15 & b, a.mode = 16201; case 16201:if (a.extra) { for (R = a.extra; d < R;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }a.length += h & (1 << a.extra) - 1, h >>>= a.extra, d -= a.extra, a.back += a.extra; }a.was = a.length, a.mode = 16202; case 16202:for (;b = (z = a.distcode[h & (1 << a.distbits) - 1]) >>> 16 & 255, g = 65535 & z, !((m = z >>> 24) <= d);) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if ((240 & b) == 0) { for (p = m, k = b, y = g; b = (z = a.distcode[y + ((h & (1 << p + k) - 1) >> p)]) >>> 16 & 255, g = 65535 & z, !(p + (m = z >>> 24) <= d);) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }h >>>= p, d -= p, a.back += p; } if (h >>>= m, d -= m, a.back += m, 64 & b) { t.msg = "invalid distance code", a.mode = me; break; }a.offset = g, a.extra = 15 & b, a.mode = 16203; case 16203:if (a.extra) { for (R = a.extra; d < R;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; }a.offset += h & (1 << a.extra) - 1, h >>>= a.extra, d -= a.extra, a.back += a.extra; } if (a.offset > a.dmax) { t.msg = "invalid distance too far back", a.mode = me; break; }a.mode = 16204; case 16204:if (l === 0) break t; if (c = f - l, a.offset > c) { if ((c = a.offset - c) > a.whave && a.sane) { t.msg = "invalid distance too far back", a.mode = me; break; }c > a.wnext ? (c -= a.wnext, u = a.wsize - c) : u = a.wnext - c, c > a.length && (c = a.length), w = a.window; } else w = n, u = r - a.offset, c = a.length; c > l && (c = l), l -= c, a.length -= c; do { n[r++] = w[u++]; } while (--c); a.length === 0 && (a.mode = 16200); break; case 16205:if (l === 0) break t; n[r++] = a.length, l--, a.mode = 16200; break; case 16206:if (a.wrap) { for (;d < 32;) { if (o === 0) break t; o--, h |= i[s++] << d, d += 8; } if (f -= l, t.total_out += f, a.total += f, 4 & a.wrap && f && (t.adler = a.check = a.flags ? I(a.check, n, f, r - f) : O(a.check, n, f, r - f)), f = l, 4 & a.wrap && (a.flags ? h : be(h)) !== a.check) { t.msg = "incorrect data check", a.mode = me; break; }h = 0, d = 0; }a.mode = 16207; case 16207:if (a.wrap && a.flags) { for (;d < 32;) { if (o === 0) break t; o--, h += i[s++] << d, d += 8; } if (4 & a.wrap && h !== (4294967295 & a.total)) { t.msg = "incorrect length check", a.mode = me; break; }h = 0, d = 0; }a.mode = 16208; case 16208:x = he; break t; case me:x = fe; break t; case 16210:return ce; case 16211:default:return _e; } return t.next_out = r, t.avail_out = l, t.next_in = s, t.avail_in = o, a.hold = h, a.bits = d, (a.wsize || f !== t.avail_out && a.mode < me && (a.mode < 16206 || e !== se)) && Re(t, t.output, t.next_out, f - t.avail_out), _ -= t.avail_in, f -= t.avail_out, t.total_in += _, t.total_out += f, a.total += f, 4 & a.wrap && f && (t.adler = a.check = a.flags ? I(a.check, n, f, t.next_out - f) : O(a.check, n, f, t.next_out - f)), t.data_type = a.bits + (a.last ? 64 : 0) + (a.mode === 16191 ? 128 : 0) + (a.mode === 16199 || a.mode === 16194 ? 256 : 0), (_ === 0 && f === 0 || e === se) && x === le && (x = ue), x; }, inflateEnd: t => { if (ge(t)) return _e; const e = t.state; return e.window && (e.window = null), t.state = null, le; }, inflateGetHeader: (t, e) => { if (ge(t)) return _e; const a = t.state; return (2 & a.wrap) == 0 ? _e : (a.head = e, e.done = !1, le); }, inflateSetDictionary: (t, e) => { const a = e.length; let i, n, s; return ge(t) ? _e : (i = t.state).wrap !== 0 && i.mode !== 16190 ? _e : i.mode === 16190 && (n = O(n = 1, e, a, 0)) !== i.check ? fe : (s = Re(t, e, a, a)) ? (i.mode = 16210, ce) : (i.havedict = 1, le); }, inflateInfo: "pako inflate (from Nodeca project)" }; const Ue = function () { this.text = 0, this.time = 0, this.xflags = 0, this.os = 0, this.extra = null, this.extra_len = 0, this.name = "", this.comment = "", this.hcrc = 0, this.done = !1; }; const Se = Object.prototype.toString; const { Z_NO_FLUSH: De, Z_FINISH: Te, Z_OK: Oe, Z_STREAM_END: Fe, Z_NEED_DICT: Ie, Z_STREAM_ERROR: Le, Z_DATA_ERROR: Ne, Z_MEM_ERROR: Be } = N; function Ce (t) { this.options = It.assign({ chunkSize: 65536, windowBits: 15, to: "" }, t || {}); const e = this.options; e.raw && e.windowBits >= 0 && e.windowBits < 16 && (e.windowBits = -e.windowBits, e.windowBits === 0 && (e.windowBits = -15)), !(e.windowBits >= 0 && e.windowBits < 16) || t && t.windowBits || (e.windowBits += 32), e.windowBits > 15 && e.windowBits < 48 && (15 & e.windowBits) == 0 && (e.windowBits |= 15), this.err = 0, this.msg = "", this.ended = !1, this.chunks = [], this.strm = new Ct(), this.strm.avail_out = 0; let a = Ze.inflateInit2(this.strm, e.windowBits); if (a !== Oe) throw new Error(L[a]); if (this.header = new Ue(), Ze.inflateGetHeader(this.strm, this.header), e.dictionary && (typeof e.dictionary === "string" ? e.dictionary = Bt.string2buf(e.dictionary) : Se.call(e.dictionary) === "[object ArrayBuffer]" && (e.dictionary = new Uint8Array(e.dictionary)), e.raw && (a = Ze.inflateSetDictionary(this.strm, e.dictionary)) !== Oe)) throw new Error(L[a]); } function Me (t, e) { const a = new Ce(e); if (a.push(t), a.err) throw a.msg || L[a.err]; return a.result; }Ce.prototype.push = function (t, e) { const a = this.strm; const i = this.options.chunkSize; const n = this.options.dictionary; let s, r, o; if (this.ended) return !1; for (r = e === ~~e ? e : !0 === e ? Te : De, Se.call(t) === "[object ArrayBuffer]" ? a.input = new Uint8Array(t) : a.input = t, a.next_in = 0, a.avail_in = a.input.length; ;) { for (a.avail_out === 0 && (a.output = new Uint8Array(i), a.next_out = 0, a.avail_out = i), (s = Ze.inflate(a, r)) === Ie && n && ((s = Ze.inflateSetDictionary(a, n)) === Oe ? s = Ze.inflate(a, r) : s === Ne && (s = Ie)); a.avail_in > 0 && s === Fe && a.state.wrap > 0 && t[a.next_in] !== 0;)Ze.inflateReset(a), s = Ze.inflate(a, r); switch (s) { case Le:case Ne:case Ie:case Be:return this.onEnd(s), this.ended = !0, !1; } if (o = a.avail_out, a.next_out && (a.avail_out === 0 || s === Fe)) if (this.options.to === "string") { const t = Bt.utf8border(a.output, a.next_out); const e = a.next_out - t; const n = Bt.buf2string(a.output, t); a.next_out = e, a.avail_out = i - e, e && a.output.set(a.output.subarray(t, t + e), 0), this.onData(n); } else this.onData(a.output.length === a.next_out ? a.output : a.output.subarray(0, a.next_out)); if (s !== Oe || o !== 0) { if (s === Fe) return s = Ze.inflateEnd(this.strm), this.onEnd(s), this.ended = !0, !0; if (a.avail_in === 0) break; } } return !0; }, Ce.prototype.onData = function (t) { this.chunks.push(t); }, Ce.prototype.onEnd = function (t) { t === Oe && (this.options.to === "string" ? this.result = this.chunks.join("") : this.result = It.flattenChunks(this.chunks)), this.chunks = [], this.err = t, this.msg = this.strm.msg; }; const He = { Inflate: Ce, inflate: Me, inflateRaw: function (t, e) { return (e = e || {}).raw = !0, Me(t, e); }, ungzip: Me, constants: N }; const { Deflate: je, deflate: Ke, deflateRaw: Pe, gzip: Ye } = Vt; const { Inflate: Ge, inflate: Xe, inflateRaw: We, ungzip: qe } = He; return { Deflate: je, deflate: Ke, deflateRaw: Pe, gzip: Ye, Inflate: Ge, inflate: Xe, inflateRaw: We, ungzip: qe, constants: N }; } export default MsunEditorPako();
