<template>
  <transition name="modal-fade">
    <div
      class="EditorDragModal"
      v-if="visible"
      style="position: fixed; z-index: 999999"
    >
      <div class="modal-content" :style="modalStyle" @mousedown="startDrag">
        <div class="modal-header">
          <span class="close" @click="close">&times;</span>
          <h3>{{ title }}</h3>
        </div>
        <div class="modal-body">
          <slot></slot>
        </div>
        <!-- 左下角和右下角用于拖动调整大小 -->
        <div
          class="resize-handle resize-handle-bottom-right"
          @mousedown.prevent.stop="startResize('bottom-right')"
        ></div>
        <div
          class="resize-handle resize-handle-bottom-left"
          @mousedown.prevent.stop="startResize('bottom-left')"
        ></div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: "CustomModal",
  props: {
    visible: {
      type: Boolean,
      required: true,
    },
    title: {
      type: String,
      default: "数据对比",
    },
  },
  data() {
    return {
      isDragging: false,
      isResizing: false,
      dragStart: { x: 0, y: 0 },
      resizeStart: { width: 0, height: 0 },
      resizeDirection: "",
      modalStyle: {
        position: "fixed",
        width: "820px",
        height: "500px", // 添加默认高度
        top: "0px",
        left: "1000px",
        minWidth: "300px", // 添加最小宽度
        minHeight: "200px", // 添加最小高度
      },
    };
  },
  methods: {
    close() {
      this.$emit("close");
    },
    startDrag(event) {
      this.isDragging = true;
      this.dragStart = { x: event.clientX, y: event.clientY };
      document.addEventListener("mousemove", this.onDrag);
      document.addEventListener("mouseup", this.stopDrag);
    },
    stopDrag() {
      this.isDragging = false;
      this.isResizing = false;
      document.removeEventListener("mousemove", this.onDrag);
      document.removeEventListener("mousemove", this.onResize);
      document.removeEventListener("mouseup", this.stopDrag);
    },
    onDrag(event) {
      if (!this.isDragging) return;
      const dx = event.clientX - this.dragStart.x;
      const dy = event.clientY - this.dragStart.y;
      this.modalStyle.top = `${parseInt(this.modalStyle.top) + dy}px`;
      this.modalStyle.left = `${parseInt(this.modalStyle.left) + dx}px`;
      this.dragStart = { x: event.clientX, y: event.clientY };
    },
    startResize(direction) {
      this.isResizing = true;
      this.resizeDirection = direction;
      this.resizeStart = {
        width: parseInt(this.modalStyle.width),
        height: parseInt(this.modalStyle.height),
        x: event.clientX,
        y: event.clientY,
      };
      document.addEventListener("mousemove", this.onResize);
      document.addEventListener("mouseup", this.stopDrag);
    },
    onResize(event) {
      if (!this.isResizing) return;
      const dx = event.clientX - this.resizeStart.x;
      const dy = event.clientY - this.resizeStart.y;

      if (this.resizeDirection === "bottom-right") {
        // 右下角拖动调整大小
        this.modalStyle.width = `${Math.max(
          this.resizeStart.width + dx,
          parseInt(this.modalStyle.minWidth)
        )}px`;
        this.modalStyle.height = `${Math.max(
          this.resizeStart.height + dy,
          parseInt(this.modalStyle.minHeight)
        )}px`;
      } else if (this.resizeDirection === "bottom-left") {
        // 左下角拖动调整大小，宽度变小，左侧位置右移
        this.modalStyle.width = `${Math.max(
          this.resizeStart.width - dx,
          parseInt(this.modalStyle.minWidth)
        )}px`;
        this.modalStyle.height = `${Math.max(
          this.resizeStart.height + dy,
          parseInt(this.modalStyle.minHeight)
        )}px`;
        // 根据宽度的变化调整 left，让右上角保持不动
        this.modalStyle.left = `${this.resizeStart.x + dx}px`;
      }
      this.$emit("resize", this.modalStyle.height, this.modalStyle.width);
    },
  },
};
</script>

<style scoped>
.modal-content {
  z-index: 999999;
  background: white;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100%;
  cursor: move; /* 设置光标为可拖拽 */
  position: relative;
}
.modal-header {
  display: flex;
  padding: 5px 0px 0px 10px;
}
.modal-body {
  padding: 0px 10px 10px 10px;
}
.close {
  font-size: 20px;
  position: absolute;
  right: 20px;
  cursor: pointer;
}
.resize-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  background: transparent;
}
.resize-handle-bottom-right {
  right: 0;
  bottom: 0;
  cursor: nwse-resize;
}
.resize-handle-bottom-left {
  left: 0;
  bottom: 0;
  cursor: ne-resize;
}

/* 过渡动画 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}
.modal-fade-enter,
.modal-fade-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

/* 强制留出过渡动画时间 */
.modal-fade-enter-active,
.modal-fade-leave-active {
  will-change: opacity, transform;
}
</style>
