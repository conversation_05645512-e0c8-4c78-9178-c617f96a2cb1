export type PlainObj = {
  [propName: string]: any
}

export const nameVsName: PlainObj = {
  B0168: "B0503",
  B0533: "B0505",
  B0280: "B0506",
  B0013_T: "B0024",
  B0014_T: "B0025",
  B0024_T: "B0026",
  B0002_T: "B0027",
  B0015: "B0015",
  B0024: "B0005",
  B0370: "B0410",
  B0371: "B0411",
  B0208: "P000303",
  B0207: "P000302",
  B1067: "B0502",
  B1066: "B0501",
  B0209: "P000304",
  B0278: "P000301",
  B0112: "B010304",
  B0108: "B010303",
  B0107: "B010302",
  B0110: "B010301",
  B1069: "B0504",
  B6200: "B0017",
  B0019: "B0005",
  B01605: "B0007",
  B01607: "B0009",
  B6201: "B0028",
  B0021: "B0028",
  B0016: "B0029",
  B01613: "B0030",
  B0302: "B0302",
  B0019_T: "B0070",
  B0069: "B0069",
  B0309: "B0309",
  B0310: "B0310",
  B0703: "B0703",
  B0704: "B0704",
  B0725: "B0725",
  B0708: "B0708",
  B0714: "B0714",
  B0817: "B0817",
  B0717: "B0717",
  B0718: "B0718",
  B0316: "B0408",
  B5001: "B0409",
  B0910: "B0910",
  B0917: "B0917",
  B0819: "B0819",
  B0820: "B0820",
  B0916: "B0916",
  B0020: "B0914",
  B0835: "B0835",
  B0823: "B0823",
  B0824: "B0824",
  B0002: "B0002",
  B0312: "B0312",
  B0014: "B0014",
  B0013: "B0013",
  B0815: "B0815",
  B0315: "B0407",
  B0702: "B0702",
  B0064: "B0064",
  B0068: "B0068",
  B0839: "B0842",
  B0033: "B0033",
  B0308: "B0308",
  B0811: "B0811",
  B0813: "B0813",
  B0836: "B0836",
  B0826: "B0826",
  B0827: "B0827",
  B0828: "B0828",
  B0833: "B0833",
  B0830: "B0830",
  B0831: "B0831",
  B0832: "B0832",
  B2175: "B2175",
  B0313: "B2000",
  B0304: "B2001",
  B0314: "B2001",
  B0131: "B2003",
  B0902: "B2005",
  B0032: "B2006",
  B0903: "B2008",
  P0010: "P0010",
  B0115: "B0115",
  B0806: "B0806",
  B0814: "B0814",
  B0808: "B0808",
  B0807: "B0807",
  B0809: "B0809",
  B0810: "B0810",
  B0816: "B0816",
  B0812: "B0812",
  B1070: "B1070",
  B0311: "B0311",
  B0050: "B0050",
  B0303: "B0303",
  P0003: "P0003",
  B0905: "B0905",
  B0004: "B0004",
  B0102: "B0102",
  B0103: "B0103",
  B0113: "B0113",
  B0116: "B0116",
  B0106: "B0106",
  P0004: "P0004",
  B0001: "B0001",
  B0401: "B0401",
  B0402: "B0402",
  B0105: "B0105",
  B1432: "B4012",
  B0301: "B0301",
  B6200_T: "B0021",
  B0104: "B0104",
  B6178: "B4201",
  B0908: "B0908"
};

export const onlyOneName: PlainObj = {
  B0104: true,
  B0102: true,
  B0103: true,
  B0113: true,
  B0116: true,
  B0106: true,
  P0004: true,
  B0105: true,
  B0301: true,
  B0408: true,
  B0409: true,
  B0407: true,
  B0410: true,
  B0411: true,
  P0003: true,
  P0010: true,
  B0115: true,
  B0503: true,
  P000303: true,
  P000302: true,
  B0502: true,
  B0501: true,
  P000304: true,
  P000301: true,
  B010304: true,
  B010303: true,
  B010302: true,
  B010301: true,
  B0504: true,
  B0001: true,
  B0401: true,
  B0402: true,
  B0302: true,
  B0311: true,
  B0908: true,
  B0004: true,
  B0303: true,
  B0905: true,
  B0050: true
};

export const placeholderVsName: PlainObj = {
  安床时间: "B0818",
  取血人签名: "B0006",
  送交人签名: "B0011",
  交接人签名: "B0012",
  取血人签名时间: "B0071",
  申请医师签名时间: "B0072",
  医务科主任签名时间: "B0073",
  输血科会诊医师签名时间: "B0074",
  科室主任签名时间: "B0075",
  送交人签名时间: "B0076",
  交接人签名时间: "B0077",
  患者委托人签名时间: "B0078",
  手术者签名时间: "B0079",
  医疗机构负责人签名时间: "B0080",
  患者指纹: "B1099",
  亲属签名指纹: "B1100",
  代理人签名指纹: "B1101",
  患者委托人签名指纹: "B1102",
  ABO血型: "B4001",
  RhD血型: "B4002",
  输血日期: "B4003",
  血红蛋白: "B4004",
  HCT: "B4005",
  一助: "B0726",
  二助: "B0727",
  三助: "B0728",
  血小板: "B4006",
  ALT: "B4007",
  HBsAg: "B4009",
  "Anti-HCV": "B4010",
  "Anti-HIV1/2": "B4011",
  医生签名时间: "B0020",
  出院科室: "B0841",
  诊疗意见: "B2002",
  签名: "B2004",
  风险评估: "B2007",
  治疗结果: "B2009",
  第一次入院病史: "B2010",
  全血: "B4013",
  滤白悬浮红细胞: "B4014",
  洗涤红细胞: "B4015",
  RhD阴性红细胞: "B4016",
  RhD阴性解冻红细胞: "B4017",
  RhD阴性血小板: "B4018",
  正常人血浆冷沉淀: "B4019",
  白细胞: "B4020",
  血浆: "B4021",
  父亲姓名: "B0855",
  母亲姓名: "B0856",
  麻醉医师签名: "B0081",
  代理人签名时间: "B0022",
  亲属签名时间: "B0023",
  麻醉医师签名时间: "B0082",
  输血血型: "B4213",
  输血成分: "B4212",
  不良反应处置措施: "B4211",
  输血不良反应: "B4210",
  输血后体征数据: "B4209",
  输血前体征数据: "B4208",
  血袋编号: "B4207",
  输血核对人: "B4206",
  传染病检查结果: "B4205",
  输血量: "B4204",
  输血结束时间: "B4203",
  输血开始时间: "B4202",
  出院医嘱时间: "B0909"
};
