import floatButton from "../components/floatButton.vue";
const floatBtnMixIn = {
  components: {
    floatButton,
  },
  data() {
    return {
      floatBtnPosition: {
        left: "0px",
        top: "0px",
      },
      floatBtnShow: false,
      floatBtnData: [
        {
          name: "常规",
          type: "normal",
          children: [
            { name: "常规", format: 0, default: true },
            { name: "标签", format: 4 },
            { name: "锚点", format: 5 },
            { name: "图片", format: 1 },
            { name: "二维码", format: 2 },
            { name: "条形码", format: 3 },
          ],
        },
        {
          name: "数字",
          type: "number",
          children: [
            { name: "无限制", format: 0, default: true },
            { name: "整数", format: 1 },
            { name: "一位小数", format: 2 },
            { name: "两位小数", format: 3 },
            { name: "三位小数", format: 4 },
            { name: "四位小数", format: 5 },
          ],
        },
        {
          name: "下拉框",
          type: "select",
          children: [
            { name: "单选", format: 0, default: true },
            { name: "多选", format: 1 },
          ],
        },
        {
          name: "单选框",
          type: "radio",
          children: [
            { name: "样式一", icon: "icon-radio-checked", format: 0 },
            {
              name: "样式二",
              icon: "icon-fuxuankuang",
              format: 1,
              default: true,
            },
            { name: "样式三", icon: "icon-danyehuaban", format: 2 },
          ],
        },
        {
          name: "多选框",
          type: "checkbox",
          children: [
            {
              name: "样式一",
              icon: "icon-radio-checked",
              format: 0,
            },
            {
              name: "样式二",
              icon: "icon-fuxuankuang",
              format: 1,
              default: true,
            },
            { name: "样式三", icon: "icon-danyehuaban", format: 2 },
          ],
        },
        {
          name: "日期(YMD)",
          type: "date",
          children: [
            { name: "M-D", format: 6 },
            { name: "Y-M-D", format: 0 },
            { name: "H:m", format: 8 },
            { name: "M-D H:m", format: 11 },
            { name: "Y-M-D H:m", format: 1, default: true },
            { name: "Y-M-D H:m:s", format: 2 },
          ],
        },
        {
          name: "日期(年月日)",
          type: "date",
          children: [
            { name: "月日", format: 7 },
            { name: "年月日", format: 3 },
            { name: "时分", format: 9 },
            { name: "年月日时", format: 4 },
            { name: "年月日时分", format: 5, default: true },
            { name: "年月日时分秒", format: 10 },
          ],
        },
      ],
      fieldTypeMap: {
        string: 0,
        number: 1,
        date: 5,
      },
    };
  },
  methods: {
    handlePlaceholder(treeData) {
      let placeholder = treeData.desc ? treeData.desc : treeData.title;
      if (placeholder.length > 10) {
        return placeholder.slice(0, 10) + "…";
      } else {
        return placeholder;
      }
    },
    floatBtnClick(item, index, pItem) {
      if (item.children && item.children.length) {
        return;
      }
      // 此处根据id重新获取，因为在显示真实值时会改变文本域的引用导致删除不掉从而报错
      const selectedField = this.editor.getFieldById(this.insertedField.id);
      const treeData = this.selectedTreeData;
      const pTreeData = this.selectedParentTreeData;
      let code = treeData.code ?? treeData.mrNodeCode ?? treeData.title ?? "";
      code = this.handleInnerValCode(code);
      if (!selectedField) {
        return;
      }
      let fields = [selectedField];
      // 如果插入的内容没有关联字段文本域，则需要判断
      if (
        treeData.vsField === false ||
        (pTreeData && pTreeData.vsField === false)
      ) {
        // 如果选择的文本域本身就是一个box，则直接将其替换
        if (selectedField.parent && selectedField.type !== "box") {
          code = selectedField.parent.name;
          // 先删除原插入的文本域
          this.editor.removeFields([selectedField.parent]);
          fields = [];
        } else if (selectedField.type === "box") {
          code = selectedField.name;
          // 先删除原插入的文本域
          this.editor.removeFields([selectedField]);
          fields = [];
        } else {
          // 先删除原插入的文本域
          this.editor.removeFields(fields);
          fields = [];
          this.$editor.warning(
            "未绑定字段文本域[常规类型]，如需绑定请从左侧选中字段拖拽到当前位置！"
          );
          // return;
        }
      }
      // 如果是放到了一个条码文本域中，则将条码信息中的文本域名称合并
      if (selectedField.parent && selectedField.parent.meta.qrCodeType) {
        // 然后将条码信息合并到父文本域中的图片信息中
        const image = selectedField.parent.children.find((ele) =>
          this.instance.TypeJudgment.isImage(ele)
        );
        if (image && image.meta.qrCodeType) {
          // 先删除原插入的文本域
          this.editor.removeFields(fields);
          image.meta.qrCodeInfo += "[" + code + "]";
          this.$editor.success("已合并条码信息");
          return;
        }
      }

      let newInsertedField;

      let type = item.type;
      if (pItem) {
        type = pItem.type;
      }
      let extendMsg = "";
      if (type === "select") {
        let sourceList = [];
        if (treeData.children && treeData.children.length) {
          sourceList = treeData.children.map((ele) => {
            return {
              text: ele.title.split("-")[0],
              value: ele.value,
            };
          });
        }
        // 先删除原插入的文本域，然后重新插入一个
        fields.length && this.editor.removeFields(fields);
        newInsertedField = this.editor.insertField({
          type,
          placeholder: this.handlePlaceholder(treeData),
          start_symbol: "(",
          end_symbol: ")",
          multi_select: item.format,
          source_list: sourceList,
          name: code,
          tip: treeData.title,
          style: {
            bold: false,
          },
        });
      } else if (type === "date") {
        // 先删除原插入的文本域，然后重新插入一个
        fields.length && this.editor.removeFields(fields);
        newInsertedField = this.editor.insertField({
          type,
          placeholder: this.handlePlaceholder(treeData),
          replace_format: item.format,
          name: code,
          tip: treeData.title,
          style: {
            bold: false,
          },
        });
      } else if (type === "radio" || type === "checkbox") {
        let sourceList;
        let groupName = code;
        if (treeData.children && treeData.children.length) {
          sourceList = treeData.children.map((node) => {
            return {
              key: this.instance.utils.getUUID("choice"),
              name: "",
              value: node.title.split("-")[0],
              formula_value: node.value,
              disabled: 0,
            };
          });
        } else {
          if (pTreeData) {
            groupName =
              pTreeData.code ?? pTreeData.mrNodeCode ?? pTreeData.title ?? "";
          }
          sourceList = [
            {
              key: this.instance.utils.getUUID("choice"),
              name: "",
              value: treeData.title.split("-")[0],
              formula_value: treeData.value,
              disabled: 0,
            },
          ];
        }
        // 先删除原插入的文本域，然后重新插入一个
        fields.length && this.editor.removeFields(fields);
        newInsertedField = this.editor.insertWidget({
          isGroup: true,
          groupName,
          isMulti: type === "radio" ? false : true,
          widgetType: item.format === 0 ? "radio" : "checkbox",
          deletable: 1,
          hideBorder: 1,
          border: item.format === 2 ? "dotted" : "solid",
          items: sourceList,
        });
      } else if (type === "number") {
        // 先删除原插入的文本域，然后重新插入一个
        fields.length && this.editor.removeFields(fields);
        newInsertedField = this.editor.insertField({
          type,
          placeholder: this.handlePlaceholder(treeData),
          number_format: item.format,
          name: code,
          tip: treeData.title,
          style: {
            bold: false,
          },
        });
      } else {
        // 先删除原插入的文本域，然后重新插入一个
        fields.length && this.editor.removeFields(fields);
        if (item.format === 4) {
          newInsertedField = this.editor.insertField({
            type: "label",
            start_symbol: "",
            end_symbol: "",
            label_text: treeData.desc ? treeData.desc : treeData.title,
            name: code + "-label",
            style: {
              bold: true,
            },
          });
        } else if (item.format === 5) {
          newInsertedField = this.editor.insertField({
            type: "anchor",
            name: code,
            style: {
              bold: false,
            },
          });
        } else {
          newInsertedField = this.editor.insertField({
            type: "normal",
            placeholder: this.handlePlaceholder(treeData),
            tip: treeData.title,
            name: code,
            style: {
              bold: false,
            },
          });
          // 如果是图片文本域则其中默认插入一张占位图片
          if (item.format) {
            let wh = [100, 50];
            let showText = "图片位";
            let typeStr = "";
            let meta = { placeholder: true };
            if (item.format === 2) {
              showText = "二维码";
              typeStr = "qrcode";
              wh = [100, 100];
            } else if (item.format === 3) {
              showText = "条形码";
              typeStr = "barcode";
              wh = [200, 65];
            }
            if (item.format === 2 || item.format === 3) {
              meta = {
                placeholder: true,
                qrCodeType: typeStr,
                qrCodeInfo: "[" + code + "]",
                barcodeFormat: "CODE128",
                displayValue: "hide",
              };
            }
            const src = this.instance.utils.createImagePlaceholder(showText);
            let imageInfo = { src, width: wh[0], height: wh[1], meta };
            this.editor.replaceFieldsImage([newInsertedField], imageInfo);
            newInsertedField.meta = {
              ...meta,
              placeImageWh: wh,
            };
            extendMsg = "【通过拖拽占位图片调整大小】";
            if (item.format > 1) {
              extendMsg += "字段拖拽到条码位置会自动合并";
            }
          }
        }
      }
      if (newInsertedField) {
        this.insertedField = newInsertedField;
        this.$editor.success("转换【" + type + "】类型成功。" + extendMsg);
        this.editor.event.emit("quickInsertFieldAfter", {
          field: newInsertedField,
          data: treeData,
        });
        return newInsertedField;
      }
    },
    // 调整完图片宽高后，将信息记录到文本域内
    handlePlaceImageField(image) {
      if (image.meta && image.meta.placeholder) {
        const field = this.editor.selection.getFocusField();
        if (field && field.meta.placeholder) {
          field.meta.placeImageWh = [image.width, image.height];
        }
      }
      // const field =
      // if(field.meta.type)
    },
    handleInnerValCode(code) {
      const key = "内置变量>";
      if (code.startsWith(key)) {
        return code.replace(key, "");
      } else {
        return code;
      }
    },
    hideFloatBtn() {
      this.floatBtnShow = false;
      this.insertedField = null;
      this.selectedTreeData = null;
    },
    showFloatBtn(field, treeData, pTreeData) {
      this.insertedField = field;
      this.selectedTreeData = treeData;
      this.selectedParentTreeData = pTreeData;

      // 根据文本域设置位置
      if (field) {
        const modelPath = this.editor.paraPath2ModelPath(field.start_para_path);
        const viewPath = this.editor.modelPath2viewPath(modelPath);
        const { x: left, y: top } =
          this.editor.getElementAbsolutePositionByViewPath(viewPath);
        this.floatBtnPosition = {
          left:
            left -
            this.instance.editor.scrollX +
            this.$refs.content.offsetLeft -
            130 +
            "px",
          top:
            top -
            this.editor.scroll_top +
            this.$refs.content.offsetTop -
            40 +
            "px",
        };
      }
      if (treeData.slots) {
        if ((treeData.children && treeData.children.length) || pTreeData) {
          this.$refs.editorFloatButton.selectedIndex = 4; // 默认插入字典集自定义多选框多选
        } else if (treeData.fieldType) {
          this.$refs.editorFloatButton.selectedIndex =
            this.fieldTypeMap[treeData.fieldType];
        }
      }
      const selIndex = this.$refs.editorFloatButton.selectedIndex;
      // 记录上一次的选择
      const item = this.floatBtnData[selIndex];
      const selChildIndex =
        this.$refs.editorFloatButton.getSelectedChildIndex();
      let resInsetField;
      if (item.children) {
        const cItem = item.children[selChildIndex];
        resInsetField = this.floatBtnClick(cItem, selChildIndex, item);
      } else {
        resInsetField = this.floatBtnClick(
          item,
          this.$refs.editorFloatButton.selectedIndex
        );
      }
      if (resInsetField) {
        this.floatBtnShow = true;
      }
    },
  },
};
export default floatBtnMixIn;
