/**
 * 模型数据转html数据
 */

import Editor from "msun-lib-editor-common/src/editor/Editor";
import Paragraph from "msun-lib-editor-common/src/editor/Paragraph";
import Table from "msun-lib-editor-common/src/editor/Table";
import Character from "msun-lib-editor-common/src/editor/Character";
import Image from "msun-lib-editor-common/src/editor/Image";
import Widget from "msun-lib-editor-common/src/editor/Widget";
import Line from "msun-lib-editor-common/src/editor/line";

export const M2H = {
  config: {},

  initEditorConfig (instance: any) {
    const editor = instance.editor;
    const config = editor.config;
    const pageSize = config.getPageSize();
    this.TypeJudgment = instance.TypeJudgment;
    this.config = {
      pageSize: pageSize,
      page_padding_left: config.page_padding_left,
      page_padding_right: config.page_padding_right,
      contentWidth: pageSize.width - config.page_padding_right - config.page_padding_left
    };
  },

  modelData2Html (instance: any, paras:Paragraph[]) {
    const editor = instance.editor;
    this.initEditorConfig(instance);
    const content = this.modelData2Dom(editor, paras);
    const style = this.styleToCss(editor);
    const htmlStr = this.getModelHtml(content.outerHTML, style.outerHTML);
    return htmlStr;
  },
  getModelHtml (content: string, style: string) {
    const htmlModel = [];
    htmlModel.push("<!DOCTYPE html>");
    htmlModel.push("<html>");
    htmlModel.push("<head>");
    htmlModel.push("<meta charset='utf-8'>");
    htmlModel.push(style);
    htmlModel.push("</head>");
    htmlModel.push(content);
    htmlModel.push("</html>");
    return htmlModel.join("");
  },
  /**
     * 获取编辑器Dom对象，如果不存在则创建
     * @param editorId
     */
  getEditorDom () {
    const editorDom = document.createElement("body");
    // editorDom.setAttribute("id", editorId);
    // editorDom.setAttribute("contentEditable", "true");
    editorDom.style.cssText = `
    width:${this.config.pageSize.width}px;
    margin:0 auto;
    background:white;
    box-shadow:0 0 10px rgba(0,0,0,0.1);
    padding-left:${this.config.page_padding_left}px;
    padding-right:${this.config.page_padding_right}px;
    box-sizing:border-box;
    `;
    return editorDom;
  },
  // 将段落数组数据转Dom
  parasDataToDom (paragraphs: Paragraph[], parentDom: HTMLElement) {
    for (let i = 0; i < paragraphs.length; i++) {
      const para = paragraphs[i];
      if (this.TypeJudgment.isParagraph(para)) {
        this.createParagraph(para, parentDom);
      } else {
        this.createTable(para, parentDom);
      }
    }
  },
  /**
     * 模型数据转Dom
     * @param editor
     */
  modelData2Dom (editor: Editor, paras:Paragraph[]) {
    const editorDom = this.getEditorDom();
    this.parasDataToDom(paras, editorDom);
    return editorDom;
  },
  // 创建段落
  createParagraph (paragraph: Paragraph, parentDom: HTMLElement) {
    const paraDom = document.createElement("p");
    paraDom.setAttribute("para_id", paragraph.id);
    paraDom.style.cssText = `line-height:${paragraph.row_ratio};margin-top:0px;margin-bottom:0px;`;
    // 设置对齐方式
    if (paragraph.align) {
      if (paragraph.align === "dispersed") {
        paraDom.style.cssText += "text-align:justify;text-align-last:justify;margin: 0;";
      } else if (paragraph.align === "docuAlign") {
        // 暂时没想要这块 还不知道css样式怎么写这个字符对齐
      } else {
        paraDom.style.cssText += "text-align:" + paragraph.align + ";";
      }
    }
    const characters = paragraph.characters;
    let processSpanDom: any = null;
    let processFontId: any = null;
    for (let i = 0; i < characters.length; i++) {
      const char = characters[i];

      if (this.TypeJudgment.isCharacter(char)) {
        let charValue = char.value;
        if (char.field_position !== "normal" || (charValue === "\n" && parentDom.tagName !== "TD")) {
          continue;
        }
        // 如果字符中含有字体样式，并且与前一个字体样式不同就重新创建一个span标签设置样式
        if (processFontId !== char.font.id) {
          processFontId = char.font.id;
          processSpanDom = this.createSpan(char, paraDom);
          // 当只有一个换行符的时候，此时将td标签设置为换行符的高度
          if (charValue === "\n" && i === 0 && parentDom.tagName === "TD") {
            parentDom.style.cssText += `height:${char.font.height * paragraph.row_ratio}px`;
          }
        }
        if (charValue === "\t") {
          charValue = "&ensp;&ensp;";
        }
        if (charValue === " ") {
          charValue = "&ensp;";
        }
        processSpanDom.innerHTML += charValue;
        processFontId = char.font.id;
      } else if (this.TypeJudgment.isImage(char)) {
        processFontId = null;
        this.createImage(char, paraDom);
      } else if (this.TypeJudgment.isWidget(char)) {
        processFontId = null;
        this.createWidget(char, paraDom);
      } else if (this.TypeJudgment.isLine(char)) {
        processFontId = null;
        this.createLine(char, paraDom);
      }
    }
    parentDom.appendChild(paraDom);
  },
  // 创建表格
  createTable (table: Table, parentDom: HTMLElement) {
    const tableDom = document.createElement("table");
    tableDom.setAttribute("table_id", table.id);
    tableDom.style.cssText = `border-collapse: collapse; width: ${this.config.contentWidth}px;border: 1px solid #000;`;
    const tblWidth = table.col_size.reduce((w, c) => w + c, 0);
    for (let i = 0; i < table.row_size.length; i++) {
      const tr = document.createElement("tr");
      for (let j = 0; j < table.col_size.length; j++) {
        const cell = table.children.find(cell => cell.position[0] === i && cell.position[1] === j);
        if (cell) {
          const td = document.createElement("td");
          const width = table.col_size[j] / tblWidth * this.config.contentWidth;
          td.style.cssText = `border: 1px solid #000;width: ${width};vertical-align:top;`;
          td.rowSpan = cell.rowspan;
          td.colSpan = cell.colspan;
          this.parasDataToDom(cell.paragraph, td);
          tr.appendChild(td);
        }
      }
      tableDom.appendChild(tr);
    }
    parentDom.appendChild(tableDom);
  },
  // 创建文本标签主要用于设置样式
  createSpan (char: Character, parentDom: HTMLElement) {
    const spanDom = document.createElement("span");
    spanDom.className += " character_" + char.field_position;
    spanDom.className += " " + char.font.id;
    // 这样设置才能粘贴到word时携带样式
    spanDom.style.cssText += char.font.getDomStyle();
    if (char.field_id) {
      spanDom.setAttribute("field_id", char.field_id);
    }
    parentDom.appendChild(spanDom);
    return spanDom;
  },
  // 创建图片
  createImage (image: Image, parentDom: HTMLElement) {
    const imageDom = document.createElement("img");
    imageDom.setAttribute("src", image.src);
    imageDom.setAttribute("width", String(image.width));
    imageDom.setAttribute("height", String(image.height));
    parentDom.appendChild(imageDom);
  },
  // 创建单选复选框
  createWidget (widget: Widget, parentDom: HTMLElement) {
    const inputDom = document.createElement("input");
    inputDom.checked = widget.selected;
    if (widget.disabled) {
      inputDom.setAttribute("disabled", "");
    }
    inputDom.setAttribute("type", widget.widgetType);
    parentDom.appendChild(inputDom);
  },
  // 创建水平线
  createLine (line: Line, parentDom: HTMLElement) {
    const hrDom = document.createElement("hr");
    parentDom.appendChild(hrDom);
  },
  styleToCss (editor: Editor) {
    const fontMap = editor.fontMap.get();
    const style = document.createElement("style");
    style.setAttribute("type", "text/css");
    style.setAttribute("rel", "stylesheet");
    // 首先清空其中样式
    style.innerText = "";
    for (const [key, font] of fontMap) {
      // console.log("属性：" + key + ",值：" + font);
      const code = " .{" + key + font.getDomStyle() + "}";
      style.appendChild(document.createTextNode(code));
    }
    // 文本域边框及背景文本
    style.appendChild(document.createTextNode(".character_placeholder{color:gray;} .character_start,.character_end{color:blue;} table p{margin-top:0;margin-bottom:0;}"));
    return style;
  }
};
