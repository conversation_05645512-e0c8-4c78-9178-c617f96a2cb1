<template>
  <div class="editor-version-history-content-wrapper">
    <div class="common-version-switch-action-zone">
      <a-button
        type="primary"
        :disabled="!isVersionBtnClickable"
        @click="onClickRestoreBtn"
      >
        恢复所选版本
      </a-button>
    </div>
    <div class="version-scrollbar-wrapper">
      <div :id="containerId" class="editor-version-history-records">
        <div
          v-for="item in recordList"
          :key="item.dateKey"
          class="version-history-record"
        >
          <div class="version-history-date">
            <span class="version-save-date">{{ item.dateAliasName }}</span>
          </div>
          <ul class="record-pieces-for-cur-date">
            <li
              v-for="record in item.verRecords"
              :key="record.reportFileLogId"
              :data-id="record.reportFileLogId"
              :class="getRecordItemClassName(record.reportFileLogId)"
              @click="onClickVersionRecord(record.reportFileLogId)"
            >
              <div class="version-header-bar">
                <span class="version-name">{{ record.reportFileName }}</span>
              </div>
              <div class="version-change-info">
                <span
                  v-if="record.reportFileLogId === curVersionId"
                  class="current-version-mark"
                  >当前版本</span
                >
                <span
                  v-if="record.restoreDesc"
                  class="revision-tile-descriptor"
                  >{{ record.restoreDesc }}</span
                >
              </div>
              <div class="version-collaborator-list">
                <div
                  v-for="collab in record.collaborators"
                  :key="collab.id"
                  class="version-collaborator"
                >
                  <i class="ver-collaborator-swatch" />
                  <span class="ver-collaborator-name">{{ collab.name }}</span>
                </div>
                <div class="version-logtime">
                  {{ record.logTime }}
                </div>
              </div>
            </li>
          </ul>
        </div>
        <div class="ver_empty_dik">
          <a-empty v-if="recordList.length === 0" />
        </div>
      </div>
    </div>
    <div v-show="recordList.length !== 0" class="version-history-pagenation">
      <a-pagination
        v-model="current"
        simple
        :total="total"
        @change="currentChange"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "VersionHistoryRecord",
  props: {
    // 当前版本的ID
    curVersionId: {
      type: String,
      required: true,
    },
    // 切换版本历史选项时函数回调（接收一个 reportFileLogId 做为参数）
    onSwitchVersionItem: {
      type: Function,
      required: true,
    },
    // 恢复版本历史时的函数回调（接收 selectedVersionId 做为参数）
    onRestoreSelectedVersion: {
      type: Function,
      required: true,
    },
    page: {
      type: Number,
      default: 1,
    },
    total: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      current: 1,
      // 当前选中进行预览的ID
      selectedVersionId: this.curVersionId,
      // 历史版本记录元素的ID
      containerId: "version-history-records",
      // 版本历史项的类名
      recordClassName: "ver-record-piece",
      // 当前所选的版本元素的类名
      selectedRecordClassName: "selected",
      recordList: [],
    };
  },
  computed: {
    // 版本恢复按钮是否可选
    isVersionBtnClickable() {
      return this.selectedVersionId !== this.curVersionId;
    },
  },
  watch: {
    page(val) {
      this.current = val;
    },
    current(val) {
      this.$emit("update:page", val);
    },
  },
  methods: {
    /**
     * 页码发生变化
     * @param {*} page
     * @param {*} pageSize
     */
    currentChange(page, pageSize) {
      // 请求数据
      // console.log(page, pageSize);
      this.$nextTick(() => {
        this.$emit("onPageChange");
      });
    },
    /**
     * 当点击历史版本元素时所触发的一些交互逻辑
     * 如：触发外部回调、更新UI状态等
     */
    onClickVersionRecord(id) {
      const changed = id !== this.selectedVersionId;
      if (typeof this.onSwitchVersionItem === "function") {
        changed && this.onSwitchVersionItem(id);
      }
      if (changed) {
        const dom = document.getElementById(this.containerId);
        if (dom) {
          dom.querySelectorAll("." + this.recordClassName).forEach((record) => {
            record.classList.remove(this.selectedRecordClassName);
            if (record.dataset.id === id) {
              record.classList.add(this.selectedRecordClassName);
            }
          });
        }
        this.selectedVersionId = id;
      }
    },
    /**
     * 试图恢复某个历史版本时的处理逻辑
     */
    onClickRestoreBtn() {
      if (this.selectedVersionId === this.curVersionId) {
        return;
      }
      if (typeof this.onRestoreSelectedVersion === "function") {
        this.onRestoreSelectedVersion(this.selectedVersionId);
      }
    },
    /**
     * 产出历史版本元素的类名（组件自用函数）
     */
    getRecordItemClassName(id) {
      if (id === this.selectedVersionId) {
        return `${this.recordClassName} ${this.selectedRecordClassName}`;
      }
      return this.recordClassName;
    },
  },
};
</script>

<style lang="less">
.version-history-drawer .version-history-pagenation {
  height: 50px;
  text-align: center;
  padding-top: 10px;
}

.version-history-drawer .ant-drawer-body {
  padding: 0;
}

.common-version-switch-action-zone {
  padding: 10px 12px;
  text-align: right;
  border-bottom: 1px solid #eee;
}

.version-scrollbar-wrapper {
  height: calc(~"100vh - 160px");
  overflow: hidden;
  overflow-y: auto;
}

.version-history-date {
  padding: 8px 16px;
  border-bottom: 1px solid #eee;
}

.version-save-date {
  color: #666;
  font-size: 13px;
  font-weight: 600;
}

.ver-record-piece {
  padding: 14px 20px 16px 32px;
  border-bottom: 1px solid #efefef;

  &:hover {
    background-color: #f9f9f9;
    cursor: pointer;
  }

  &.selected {
    background-color: #e2f3eb;

    .version-name {
      color: #137333;
    }

    .current-version-mark,
    .revision-tile-descriptor {
      color: #1967d2;
    }
  }

  .version-header-bar {
    margin-bottom: 2px;
  }

  .version-name {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    line-height: 24px;
  }

  .version-change-info {
    color: #3d4040;
    font-style: italic;
    font-size: 13px;
  }

  .current-version-mark,
  .revision-tile-descriptor {
    display: block;
    line-height: 20px;
  }

  .current-version-mark {
    font-style: normal;
  }

  .version-collaborator-list {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    margin-top: 4px;
  }

  .version-logtime {
    margin-left: 5px;
    white-space: nowrap;
    font-size: 80%;
    color: #aaa;
  }

  .version-collaborator {
    line-height: 18px;

    & + .version-collaborator {
      margin-left: 10px;
    }
  }

  .ver-collaborator-swatch {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #26a69a;
  }

  .ver-collaborator-name {
    margin-left: 6px;
    color: #3c4043;
    font-size: 12px;
  }
}

.ver_empty_dik {
  padding: 15px 0;
}
</style>
