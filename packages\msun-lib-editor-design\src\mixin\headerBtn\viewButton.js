const viewBtnMixIn = {
  data() {
    return {
      viewBtn: [
        {
          type: "textIcon",
          icon: "icon-biao<PERSON><PERSON>hi",
          title: "表单模式",
          key: "view_mode",
          val: "form",
          func: this.formPattern,
        },
        {
          type: "textIcon",
          icon: "icon-jianjie",
          title: "简洁视图",
          key: "view_mode",
          val: "view",
          func: this.simplePattern,
        },
        {
          type: "textIcon",
          icon: "icon-putongshitu",
          title: "普通视图",
          key: "view_mode",
          val: "normal",
          func: this.normalPattern,
        },
        // {
        //   type: "textIcon",
        //   icon: "icon-shitu",
        //   title: "只读模式",
        //   key: "readonly",
        //   val: true,
        //   func: this.onlyReadPattern,
        // },
        {
          type: "textIcon",
          icon: "icon-huyanmoshi",
          title: "护眼模式",
          key: "page_color",
          val: "rgb(199,237,204)",
          func: this.eyeProtectionMode,
        },
      ],
    };
  },
  created() {},
  methods: {
    // 表单模式
    formPattern() {
      const { editor } = this.instance;
      editor.setViewMode("form");
      this.$forceUpdate();
    },
    // 简洁模式
    simplePattern() {
      const { editor } = this.instance;
      editor.setViewMode("view");
      this.$forceUpdate();
    },
    // 只读模式
    onlyReadPattern() {
      const { editor } = this.instance;
      editor.setReadonly(!editor.readonly);
      this.$forceUpdate();
    },
    // 护眼模式
    eyeProtectionMode() {
      const { editor } = this.instance;
      const isOpen = editor.config.page_color === "#ffffff";
      editor.eyeProtectionMode(isOpen);
      this.$forceUpdate();
    },
    // 普通模式
    normalPattern() {
      const { editor } = this.instance;
      editor.setViewMode("normal");
      this.$forceUpdate();
    },
  },
};
export default viewBtnMixIn;
