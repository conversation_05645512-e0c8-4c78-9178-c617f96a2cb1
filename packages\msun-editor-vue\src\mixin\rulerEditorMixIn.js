const rulerEditorMixIn = {
  data() {
    return {
      showRulerEditorModal: false,
      replaceRule: [],
    };
  },
  methods: {
    testRulerEditor(reg, newText) {
      const editor = this.instance.editor;
      const field = editor.createElement("field");
      field.new_text = newText;
      editor.handleFieldNewTextByReplaceRule(field, reg);
      this.formattedText = field.new_text;
    },
    rulerEditorSubmit(reg) {
      const editor = this.instance.editor;
      this.showRulerEditorModal = false;
      if (this.fieldSource === "field") {
        this.reg = reg;
        this.$refs.fieldProperty.$el.style.display = "block";
      } else if (this.fieldSource === "side") {
        if (editor.focusElement["field"]) {
          editor.focusElement["field"].replaceRule = reg;
        }
      }
    },
    rulerEditorCancel() {
      this.showRulerEditorModal = false;
      if (this.fieldSource === "field") {
        this.$refs.fieldProperty.$el.style.display = "block";
      }
    },
  },
};
export default rulerEditorMixIn;
