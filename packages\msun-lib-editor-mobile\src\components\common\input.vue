<template>
  <div
    contenteditable="true"
    class="custom-input"
    @blur="blur"
    @pointerdown="pointerdown"
    @pointermove="pointermove"
    @pointerup="pointerup"
  ></div>
</template>

<script>
export default {
  name: "custom-input",
  data() {
    return {
      hold_mouse_move: false,
      pointer_down_cursor: "",
      before_location: { x: 0, y: 0 },
      clickStyle: {
        left: 0 + "px",
        top: 0 + "px",
      },
    };
  },
  props: {
    fontStyle: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {},
  methods: {
    blur() {
      this.$emit("blur");
    },
    pointerdown(e) {
      const target = e.target;
      target.setPointerCapture(e.pointerId);
      this.hold_mouse_move = true;
      const dom = this.$el;
      if (this.in_move_area) {
        this.pointer_down_cursor = dom.style.cursor = "move";
      } else {
        this.hold_mouse_move = false;
        this.pointer_down_cursor = "";
        dom.style.cursor = "text";
      }
      this.clickStyle = {
        left: this.$el.offsetLeft + "px",
        top: this.$el.offsetTop + "px",
      };
      this.before_location = { x: e.offsetX, y: e.offsetY };
    },
    pointermove(e) {
      e.preventDefault();
      const x = e.offsetX;
      const y = e.offsetY;
      const dom = this.$el;
      const moveList = [
        { x: [0, dom.offsetWidth], y: [0, 5] },
        {
          x: [0, dom.offsetWidth],
          y: [dom.offsetHeight - 7, dom.offsetHeight],
        },
        { x: [0, 5], y: [0, dom.offsetHeight] },
        {
          x: [dom.offsetWidth - 7, dom.offsetWidth],
          y: [0, dom.offsetHeight],
        },
      ];
      if (this.pointer_down_cursor === "") {
        for (let i = 0; i < moveList.length; i++) {
          const move = moveList[i];
          if (
            move.x[0] <= x &&
            x <= move.x[1] &&
            move.y[0] <= y &&
            y <= move.y[1]
          ) {
            dom.style.cursor = "move";
            this.in_move_area = true;
            break;
          } else {
            this.in_move_area = false;
            dom.style.cursor = "text";
          }
        }
      } else {
        const move_x = x + dom.offsetLeft + 1 - this.before_location.x;
        const move_y = y + dom.offsetTop + 1 - this.before_location.y;

        dom.style.left = move_x + "px";
        dom.style.top = move_y + "px";
        this.clickStyle = {
          left: move_x + "px",
          top: move_y + "px",
        };
      }
    },
    pointerup(e) {
      const target = e.target;
      target.releasePointerCapture(e.pointerId);
      this.hold_mouse_move = false;
      this.pointer_down_cursor = "";
      this.$emit("changeLocation", this.clickStyle);
    },
  },
};
</script>

<style lang="less" scoped>
.custom-input {
  -webkit-user-modify: read-write-plaintext-only;
  padding: 5px;
}
</style>
