import Editor from "./Editor";
import Element from "./Element";
import Font from "./Font";
import Table from "./Table";
export default class But<PERSON> extends Element {
  field_id: string | null = null;
  field_position: string = "normal";
  value: string;
  color: string;
  type: string = "button";
  pointer_in: boolean = false;
  meta: any = {
    saveWidth: 0
  };
  font: Font = new Font({ family: "", height: 0 });
  constructor(value: string, width: number, height: number, color: string) {
    super(width, height);
    this.value = value;
    this.color = color;
    this.meta.saveWidth = width
  }

  contain_vertical(y: number) {
    return this.top <= y && y <= this.bottom;
  }

  contain_horizontal(x: number) {
    return this.left <= x && x <= this.right;
  }

  contain(x: number, y: number) {
    return this.contain_horizontal(x) && this.contain_vertical(y);
  }

  copy() {
    const box = new Button(this.value, this.width, this.height, this.color);
    return box;
  }

  static attrJudgeUndefinedAssign(newModel: Button, raw: any) {
    if (raw.field_id !== undefined) newModel.field_id = raw.field_id;
    if (raw.meta !== undefined) newModel.meta = raw.meta;
  }

  static insertButton(editor: Editor, text: string, color?: string, width?: number, height?: number) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    if (!editor.selection.isCollapsed) {
      editor.delete_backward();
    }

    const focus_row = editor.selection.getFocusRow();
    const focus_field = editor.selection.getFocusField();
    if (focus_field && focus_field.isReadonly) {
      return false;
    }

    editor.caret.cache = null;
    if (!editor.delSectionRecordStack()) {
      return false;
    }
    // 只有表单模式下 单元格不能编辑的时候 才要 return 出去
    if (!focus_field && (editor.view_mode === "form" && !editor.adminMode) && !Table.judgeIsEditableInFormMode(editor.selection.focus, editor)) {
      return false;
    }
    // 段落系的坐标,因为下面有方法会更改坐标，所以此处需要重新获取
    const para_path = editor.selection.para_focus;
    const current_paragraph = focus_row.paragraph;

    if (!color) {
      color = "rgb(78,169,252)";
    }
    // 参数为 text 行数 字符数 文字样式
    const button = current_paragraph.insertButton(
      text,
      para_path[para_path.length - 1],
      focus_field,
      color,
      Number(width),
      Number(height)
    );
    editor.update();
    editor.scroll_by_focus();
    editor.render();
    return button;
  }
}
