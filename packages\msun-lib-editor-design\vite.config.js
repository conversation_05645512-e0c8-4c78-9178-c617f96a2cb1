import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue2";
import { viteCommonjs } from "@originjs/vite-plugin-commonjs"; // 让浏览器支持commonjs语法
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";
import antdvFix from "vite-plugin-antdv-fix";
export default defineConfig({
  server: {
    host: "0.0.0.0",
    port: "9999",
    // 配置解决无法在控制台中对链接的包添加断点的问题
    fs: {
      strict: false,
    },
    hmr: {
      overlay: true,
    },
    // watch: ['!**/node_modules/msun-editor-vue/**'],
  },
  optimizeDeps: {
    // exclude: ['msun-editor-vue']
  },
  build: {
    target: "es2016",
    lib: {
      entry: "./src/App.vue",
      name: "msunLibEditorDesign",
      fileName: "msunLibEditorDesign",
      formats: ["esm"], //'esm', 'cjs', 'umd'
    },
    rollupOptions: {
      external: [
        "vue",
        "ant-design-vue",
        "msun-editor-vue",
        "ant-design-vue/dist/antd.css",
        "ant-design-vue/lib/icon",
        "ant-design-vue/dist/antd",
        "vcolorpicker",
        "vite-plugin-antdv-fix",
      ],
      output: {
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
          vue: "Vue",
          "ant-design-vue": "antDesignVue",
        },
      },
    },
  },

  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  plugins: [vue(), antdvFix(), viteCommonjs(), cssInjectedByJsPlugin()],
});
