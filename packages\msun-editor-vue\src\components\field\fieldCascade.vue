<template>
  <modal
    class="table-modal"
    :show="show"
    title="级联规则设置"
    :width="width"
    :freePoint="true"
    :sessionMove="true"
    pointer-events:none
    @cancel="cancel"
  >
    <span class="editor-example-mention"
      ><a-popover title="级联规则说明" :overlayStyle="{ zIndex: 999999 }">
        <template slot="content">
          <div style="width: 760px">
            <div class="textTitle">
              当某个文本域内容为**或者自定义多选框选择某项的时候，控制显示哪些文本域，并将当前规则列表中涉及的其他文本域隐藏。
            </div>
            &nbsp;&nbsp;&nbsp;&nbsp;情景举例：<br />
            &nbsp;&nbsp;&nbsp;&nbsp;一、下拉选择类型的文本域，选项为男、女。选中选项【男】时，显示内容为【婚育史】的文本域（hys），隐藏内容为【月经婚育史】文本域（yjhys）；
            选中选项【女】时，显示内容为【月经婚育史】文本域，隐藏内容为【婚育史】的文本域。
            设置方式：假设hys与yjhys分别为婚育史与月经婚育史文本域的名称。
            第一条规则：【男】-【hys】;第二条规则：【女】-【yjhys】<br />
            &nbsp;&nbsp;&nbsp;&nbsp;二、一组自定义多选框选项为是、否，勾选【是】的时候显示文本域A,隐藏文本域B。
            勾选【否】的时候显示文本域B,隐藏文本域A。
            设置方式：自定义多选框属性面板-选项列表-【是】-【A】，【否】-【B】<br />
            <div class="textTitle">注意：</div>
            <div style="color: red">
              &nbsp;&nbsp;&nbsp;&nbsp;一、自定义多选框设置位置在选项列表中。<br />
              &nbsp;&nbsp;&nbsp;&nbsp;二、打开该弹窗后，文档中右上角带有蓝色方块的文本域可以通过点击的方式插入文本域名称。<br />
              <br />
            </div>
          </div>
        </template>
        <div
          style="margin-bottom: 5px; margin-left: 10px; width: 20px"
          class="edit-btn"
        >
          <icon-common icon="icon-jieshi" style="cursor: pointer"></icon-common>
        </div> </a-popover
    ></span>
    <a-table
      id="cascadeParent"
      :columns="cascadeColumns"
      :data-source="cascade_list"
      bordered
      :pagination="false"
      :scroll="{ y: 224 }"
    >
      <template
        v-for="(col, i) in ['text', 'show_field_names']"
        :slot="col"
        slot-scope="show_field_names, text"
      >
        <template>
          <div :key="i">
            <a-input
              ref="cascadeColumn"
              :id="text.key + i"
              size="small"
              style="margin: -5px 0"
              @focus="getFocusDom($event, i)"
              :value="show_field_names"
              @click="clickCascadeInput(text.key, i)"
              @change="
                (e) => cascadeHandleChange(e.target.value, text.key, col)
              "
            />
          </div>
        </template>
      </template>

      <template slot="operation" slot-scope="show_field_names, record">
        <div class="editable-row-operations">
          <a @click="() => cascadeDeleteRow(record.key)">删除</a>
        </div>
      </template>
    </a-table>
    <div
      v-show="showCascadeSelect"
      class="cascade-select"
      id="cascadeList"
      v-if="
        source_list.length > 0 &&
        (source_list[0].text !== '' ||
          source_list[0].code !== '' ||
          source_list[0].value)
      "
    >
      <div
        v-for="(item, i) in source_list"
        class="cascade-select-div"
        :key="i"
        @click="clickSelectDiv(item)"
      >
        {{ item.text }}
      </div>
    </div>
    <div class="attention">
      注意：插入完所有的级联文本域，对当前文本域进行编辑操作后级联规则生效。
    </div>
    <div slot="editor-modal-footer" class="footer">
      <div>
        <a-button type="default" @click="add">添加</a-button>
      </div>
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
const cascadeColumns = [
  {
    title: "当前内容为...时",
    dataIndex: "text",
    width: "25%",
    scopedSlots: { customRender: "text" },
  },
  {
    title: "显示[name]为...的文本域（以“,”分隔）",
    dataIndex: "show_field_names",
    width: "35%",
    scopedSlots: { customRender: "show_field_names" },
  },
  {
    title: "操作",
    dataIndex: "operation",
    width: "10%",
    scopedSlots: { customRender: "operation" },
  },
];
import modal from "../common/modal.vue";
import IconCommon from "../common/iconCommon.vue";
export default {
  name: "fieldCascade",
  components: { IconCommon, modal },
  data() {
    return {
      cascadeColumns,
      cascade_list: [],
      focusDom: null,
      showCascadeSelect: false,
      source_list: [],
    };
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 524,
    },
    title: {
      type: String,
      default: "",
    },
  },
  watch: {
    show(val) {
      if (val) {
        let list = [];
        let field_names = "";
        this.showCascadeSelect = false;
        this.source_list = this.field.source_list || [];
        this.field.cascade_list.forEach((e) => {
          if (e.show_field_names.length > 0) {
            field_names = e.show_field_names.join(",");
          }

          list.push({
            key: this.editor.utils.getUUID(),
            text: e.text,
            show_field_names: field_names,
          });
        });
        if (list.length === 0) {
          list = [
            {
              key: this.editor.utils.getUUID(),
              text: "",
              show_field_names: "",
            },
          ];
        }
        this.cascade_list = list;
      }
      this.$nextTick(() => {
        this.focusDom = this.$refs.cascadeColumn[1].$el
          ? this.$refs.cascadeColumn[1].$el
          : null;
      });
    },

    immediate: true,
    deep: true,
  },

  methods: {
    add() {
      const new_source_list = {
        key: this.editor.utils.getUUID(),
        text: "",
        show_field_names: "",
      };

      this.cascade_list.push(new_source_list);
    },
    submit() {
      const new_source_list = [];
      this.cascade_list.forEach((e) => {
        const show_names = e.show_field_names.split(/,|，/);
        new_source_list.push({
          text: e.text,
          show_field_names: show_names,
        });
      });

      this.$emit("submit", new_source_list);
    },
    cancel() {
      this.$emit("cancel");
    },
    getFocusDom(event, i) {
      if (i === 1) {
        this.focusDom = event.target;
      }
    },
    insertFieldName(name) {
      //获取当前聚焦的input在整个table的第几行，然后给cascade_list的show_field_names赋值
      const td = this.focusDom.parentNode.parentNode;
      const trs = td.closest("table").getElementsByTagName("tr");
      let tdIndex = -1;
      for (var i = 0; i < trs.length; i++) {
        var tds = trs[i].getElementsByTagName("td");
        // 检查当前行的每个单元格是否与目标单元格相同
        for (var j = 0; j < tds.length; j++) {
          if (tds[j] === td) {
            tdIndex = i; // 找到目标行，记录行数
            break; // 结束内层循环
          }
        }
        if (tdIndex !== -1) {
          break; // 结束外层循环
        }
      }
      this.focusDom.focus();
      let field_name = this.cascade_list[tdIndex].show_field_names;
      const result = field_name.endsWith(",") || field_name.endsWith("，");
      if (result) {
        this.cascade_list[tdIndex].show_field_names += name + ",";
      } else {
        if (!field_name) {
          this.cascade_list[tdIndex].show_field_names += name + ",";
        } else {
          this.cascade_list[tdIndex].show_field_names += "," + name + ",";
        }
      }
    },
    clickCascadeInput(val, i) {
      if (!(i === 0)) return;
      this.inputDomId = val + i;
      this.showCascadeSelect = !this.showCascadeSelect;
      const parentDiv = document.getElementById("cascadeParent");
      const cascadeDiv = document.getElementById("cascadeList");
      if (parentDiv && cascadeDiv) {
        for (let i = 0; i < this.cascade_list.length; i++) {
          const key = this.cascade_list[i].key;
          if (key === val) {
            this.clickSelect = i;
            break;
          }
        }
        const div = document.getElementById(this.inputDomId);
        const parentTop = this.getOffsetTop(parentDiv);
        const top = this.getOffsetTop(div);

        cascadeDiv.style.cssText = `top:${
          top - parentTop + 85
        }px;z-index:999;left:18px`;
      }
    },
    getOffsetTop(el) {
      return el.offsetParent
        ? el.offsetTop + this.getOffsetTop(el.offsetParent)
        : el.offsetTop;
    },

    cascadeHandleChange(value, key, column) {
      const newData = [...this.cascade_list];
      const target = newData.filter((item) => key === item.key)[0];
      if (target) {
        target[column] = value;
        this.cascade_list = newData;
      }
    },
    clickSelectDiv(val) {
      this.showCascadeSelect = false;
      this.cascade_list[this.clickSelect].text = val.text;
    },

    cascadeDeleteRow(key) {
      if (this.cascade_list.length === 1) {
        const newCascadeObj = {
          key: this.editor.utils.getUUID(),
          text: "",
          show_field_names: "",
        };
        this.cascade_list = [newCascadeObj];
        return;
      }
      this.cascade_list = this.cascade_list.filter((item) => item.key !== key);
      this.focusDom = this.$refs.cascadeColumn[1].$el
        ? this.$refs.cascadeColumn[1].$el
        : null;
    },
  },
};
</script>

<style scoped>
.table-modal /deep/ .ant-modal-body {
  height: 260px;
}

.table-modal /deep/.ant-table-tbody > tr > td {
  padding: 5px;
}

.table-modal /deep/.ant-table-row-cell-break-word {
  padding: 5px;
}

.table-modal /deep/.ant-modal-content {
  top: 50px;
}
.editable-row-operations a {
  margin-right: 8px;
}
.cascade-select {
  position: absolute;
  width: 188px;
  border-radius: 4px;
  box-shadow: 0px 0px 5px rgb(177, 174, 174);
  background-color: rgb(255, 255, 255);
}
.attention {
  position: absolute;
  bottom: 45px;
  text-align: center;
  color: red;
}
.cascade-select-div {
  line-height: 22px;
  padding: 5px 12px;
  font-size: 14px;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.65);
}
.footer {
  display: flex;
  justify-content: space-between;
  padding-top: 35px;
}
.editor-example-mention {
  font-size: 10px;
  position: absolute;
  top: 10px;
  left: 90px;
}
.textTitle {
  color: black;
  font-weight: 700;
}
</style>
