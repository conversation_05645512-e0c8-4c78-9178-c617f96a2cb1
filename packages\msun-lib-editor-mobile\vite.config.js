import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue2";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";
import { viteCommonjs } from "@originjs/vite-plugin-commonjs"; // 让浏览器支持commonjs语法
import { visualizer } from "rollup-plugin-visualizer";
import antdvFix from "vite-plugin-antdv-fix";
// const isMobile3 =
//   /Mobi/.test(navigator.userAgent) ||
//   /Android/i.test(navigator.userAgent) ||
//   /iPhone|iPad|iPod/i.test(navigator.userAgent);
export default defineConfig({
  server: {
    host: "0.0.0.0",
    port: "9527",
    // 配置解决无法在控制台中对链接的包添加断点的问题
    fs: {
      strict: false,
    },
    watch: ["!**/node_modules/msun-editor/**"],
  },
  publicDir: false, // 静态资源目录 ，  本地调试pdf打印时改为true
  optimizeDeps: {
    exclude: ["msun-editor"],
  },
  build: {
    target: "es6",
    sourcemap: true,
    lib: {
      entry: "./src/App.vue",
      name: "msunEditorMobile",
      fileName: "msunEditorMobile",
      formats: ["esm"], //'esm', 'cjs', 'umd'
    },
    rollupOptions: {
      external: [
        "vue",
        "ant-design-vue",
        "vite-plugin-antdv-fix",
        "msun-editor",
        "vcolorpicker",
        "moment",
        "diff_match_patch",
        "vant",
        "js-pinyin",
        // ...(isMobile3 ? ["vant"] : []), // 只在移动端加载 Vant
        "jspdf",
      ],
      output: {
        // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
        globals: {
          vue: "Vue",
          "ant-design-vue": "antDesignVue",
          "vite-plugin-antdv-fix": "vitePluginAntdvFix",
          "msun-editor": "initEditor",
          vcolorpicker: "vcolorpicker",
          moment: "moment",
          "js-pinyin": "jsPinyin",
          diff_match_patch: "diff_match_patch",
          jspdf: "jspdf",
        },
      },
    },
  },
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
  },
  plugins: [
    vue(),
    antdvFix(),
    viteCommonjs(),
    cssInjectedByJsPlugin(),
    visualizer({ open: false }), //依赖分析插件
  ],
});
