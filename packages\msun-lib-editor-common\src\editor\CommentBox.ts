import Comment from "./Comment";
import { COMMENT_BORDER_COLOR, COMMENT_LIST_ITEM_SPACE_HEIGHT, COMMENT_LIST_ITEM_TITLE_HEIGHT, COMMENT_SWITCH_WIDTH, COMMENT_TITLE_CROSS_HEIGHT, COMMENT_WIDTH } from "./Constant";
import { Direction } from "./Definition";
import Editor from "./Editor";
import Font, { FontStyle } from "./Font";
import Page from "./Page";
import Renderer from "./Renderer";
import { isCharacter, isRow, isTable } from "./Utils";

export default class CommentBox {
  editor: Editor;
  commentId:string;
  left:number;
  top:number;
  width:number = COMMENT_WIDTH;
  height:number;
  comment:any;

  page: Page | undefined; // 该批注所在的页
  
  fontStyle: FontStyle = {
    family: "黑体", // 默认字体 宋体 勿动
    height: 16,
    bold: false,
    italic: false,
    underline: false,
    strikethrough: false,
    dblUnderLine: false,
    script: 3,
    characterSpacing: 0,
    color: "#000",
    bgColor: null,
    highLight: null
  };

  lineHeight:number = 25;

  get bottom() {
    return this.top + this.height;
  }

  static createCommentBox (editor:Editor) {
    let commentArr = Comment.getCommentInfo(editor);
    const left = editor.page_left + editor.page_size.width + 15;
    let top = editor.scroll_top ? editor.scroll_top : editor.pages[0].top;

    const lineHeight = 25;
    const fontStyleOfSymbol = editor.config.default_font_style;
    const font = editor.fontMap.add(fontStyleOfSymbol);
    const canvasHeight =(editor.init_canvas.height / editor.config.devicePixelRatio - 50)/editor.viewScale;

    const originTop = editor.scroll_top ? Math.round(editor.scroll_top) : editor.pages[0].top;
    if (editor.internal.currentCommentPosition) {
      // 如果已经移动过了,现在的定点应该等于，top减去上次移动的距离-这次移动的距离
      top = top - editor.internal.commentMovedHeight - editor.internal.currentCommentPosition;
    } else {
      top = top - editor.internal.commentMovedHeight;
    }
    // 控制滚动的上边界
    if (canvasHeight < editor.internal.totalCommentHeight) {
      top = top > originTop ? originTop : top;
      // 控制滚动的下边界
      top = top < (canvasHeight - editor.internal.totalCommentHeight + editor.scroll_top) ? canvasHeight - editor.internal.totalCommentHeight + editor.scroll_top : top;
    } else { top = originTop; }

    for (let i = 0; i < commentArr.length; i++) {
      const comment = commentArr[i];
      const lineNum = editor.internal.currentComment === comment ?  CommentBox.countRows(comment.value, 230, font, editor) : 0;
      const height = 50 + lineNum * lineHeight;
      const commentBox = new CommentBox(left, top + 50, height, editor, commentArr[i]);
      editor.commentBox.push(commentBox);
      top += commentBox.height + 30;
    }
    let totalHeight = editor.commentBox.length * 30;
    const commentBox = editor.commentBox;
    commentBox.forEach((comment:any) => {
      totalHeight += comment.height;
    });
    editor.internal.totalCommentHeight = totalHeight;
    // 中间绿色的线
    Renderer.draw_line(
      [left - 13, originTop + 45],
      [left - 13 + 300, originTop + 45],
      "rgb(150,150,150)",
      1,
      0.5,
      "solid"
    );
    if(!editor.config.comment.hideCloseBtn){
      Renderer.draw_line(
        [left + 250, originTop + 15],
        [left + 260, originTop + 25],
        "black",
        1,
        1.5,
        "solid"
      );
      Renderer.draw_line(
        [left + 260, originTop + 15],
        [left + 250, originTop + 25],
        "black",
        1,
        1.5,
        "solid"
      );
      Renderer.drawSwitch({ start: [left + 230, originTop + 15], width: COMMENT_SWITCH_WIDTH, height: COMMENT_TITLE_CROSS_HEIGHT })
    }
    const titleFontStyle = {
      family: "微软雅黑",
      height: 20,
      bold: true,
      italic: false,
      underline: false,
      strikethrough: false,
      dblUnderLine: false,
      script: 3,
      characterSpacing: 0,
      color: "#000",
      bgColor: null,
      highLight: null
    };
    const titleFont = new Font(titleFontStyle);
    Renderer.draw_text(titleFont, editor.config.comment.title ?? "批注列表", left + 5, originTop + 33, editor);
    Renderer.clipCell(left, originTop + 50, 300, totalHeight + 50);
    for (let i = 0; i < editor.commentBox.length; i++) {
      const commentBox = editor.commentBox[i];
      commentBox.draw();
    }
  }

  static sort(editor: Editor, commentArr: {data: string, id: string, name: string, selected: boolean, value: string}[]) {
    if (!commentArr || commentArr.length < 2) return commentArr
    const commentId = new Map();
    const newCommentBox = [];
    OUT_FOR: for (let i = 0; i < editor.current_cell.children.length; i++) {
      const content = editor.current_cell.children[i];
      if (isRow(content)) {
        for (const c of content.children) {
          if (isCharacter(c) && c.comment_id) {
            if (!commentId.has(c.comment_id)) {
              for (let j = 0; j < commentArr.length; j++) {
                if (commentArr[j].id === c.comment_id) {
                  const box = commentArr.splice(j, 1)[0];
                  newCommentBox.push(box);
                  break
                }
              }
            }

          }
        }
      } else if (isTable(content)) {
        for (const cell of content.children) {
          for (const row of cell.children) {
            for (const c of row.children) {
              if (isCharacter(c) && c.comment_id) {
                if (!commentId.has(c.comment_id)) {
                  for (let j = 0; j < commentArr.length; j++) {
                    if (commentArr[j].id === c.comment_id) {
                      const box = commentArr.splice(j, 1)[0];
                      newCommentBox.push(box);
                      break
                    }
                  }
                }
              }
            }
          }
        }
      }
      if (!commentArr.length) {
        break OUT_FOR;
      }
    }
    return newCommentBox;
  }

  // 计算有多少行，后边换行用
  static countRows (text:string, maxWidth:number, font:Font, editor:Editor) {
    const { width } = Renderer.measure(font, text, editor);
    let line;
    if (width < maxWidth) {
      line = 1;
    } else {
      line = Math.ceil(width / maxWidth);
    }
    return line;
  }

  constructor (left: number, top: number, height: number, editor: Editor, comment:any) {
    this.top = top;
    this.left = left;
    this.height = height;
    this.editor = editor;
    this.comment = comment;
    this.commentId = comment.id;
  }

  drawUserName({ font, commentInfoPositionY, restWidth }: {font: Font, commentInfoPositionY: number, restWidth: number}) {
    const originFamily = font.family;
    const origintHeight = font.height;
    const originColor = font.color;
    font.family = "宋体";
    font.height = 14;
    font.color = "#4F4F4F"

    
    const commentUser = this.comment.name; // 标题啊 这是 打印出来看 就是月经史 辅助检查 初步诊断 主诉 那些
    const ellipsis = Renderer.measure(font, "…", this.editor);
    let showName = "未登录"
    restWidth = restWidth || 240; // 看的绘制小三角的位置 是 245
    let ok = false;
    let n = 0;
    while(!ok) {
      const { width } = Renderer.measure(font, commentUser.slice(0, commentUser.length - n), this.editor);
      if (width + (n > 0 ? ellipsis.width : 0) <= restWidth){
        ok = true;
      } else {
        n++;
      }
    }
    showName = commentUser.slice(0, commentUser.length - n) + (n > 0 ? "…" : "");
    

    Renderer.draw_text(font, showName, this.left + 5, commentInfoPositionY, this.editor);
    
    font.family = originFamily;
    font.height = origintHeight;
    font.color = originColor;
  }

  // 先写字，因为要根据字的行数来就算总的高度
  drawCommentText (page?: Page) {
    // 填充上一点颜色
    Renderer.draw_rectangle(
      this.left,
      Math.round(this.top),
      this.width,
      this.height,
      this.editor.config.comment.listItemBgColor,
      5
    );
    const font = new Font(this.fontStyle);
    const text = this.comment.value;
    const commentInfoPositionY = Math.round(this.top + 30);
    // 更新写上字后的高度
    const commentDate = this.comment.date
    let restWidth = 0;
    if(commentDate && !this.editor.config.comment.hideDate){
      const timestamp = commentDate;
      const date = new Date(timestamp);
  
      // 提取日期和时间的各个部分
      const year = date.getFullYear();
      const month = (String(date.getMonth() + 1) as any).padStart(2, "0");
      const day = (String(date.getDate()) as any).padStart(2, "0");
      const hours = (String(date.getHours()) as any).padStart(2, "0");
      const minutes = (String(date.getMinutes()) as any).padStart(2, "0");
  
      // 格式化日期和时间
      const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}`;
      font.height = 12;
      font.color = "rgb(140,140,140)";
      Renderer.draw_text(font, formattedDate, this.left + 75, commentInfoPositionY, this.editor);
      restWidth = 75;
    }
    
    if (!this.comment.abolished) {
      font.color = "rgb(29,144,255)";
      font.height=12
      if(!this.editor.config.comment.hideReplaceBtn){
        if (restWidth === 0) {
          restWidth = 180;
        }
        Renderer.draw_text(font, "替换", this.left + 180, commentInfoPositionY, this.editor);
      }
      if(!this.editor.config.comment.hideDeleteBtn){
        if (restWidth === 0) {
          restWidth = 210;
        }
        Renderer.draw_text(font, "删除", this.left + 210, commentInfoPositionY, this.editor);
        
      }
    } else {
      font.color = "red";
      font.height=12
      if (this.comment.processMode === 1) {
        if (restWidth === 0) {
          restWidth = 180;
        }
        Renderer.draw_text(font, "(已替换)", this.left + 180, commentInfoPositionY, this.editor);
      } else {
        if (restWidth === 0) {
          restWidth = 180;
        }
        Renderer.draw_text(font, "(已删除)", this.left + 180, commentInfoPositionY, this.editor);
      }
    }
    this.drawUserName({ font, commentInfoPositionY, restWidth });
    
    if ((this.editor.useNewVersionCommentList && page && this.editor.newVersionOpenCommentMap.has(this.comment.id)) || this.comment === this.editor.internal.currentComment) {
      Renderer.drawChevron({
        position: [this.left + 245, commentInfoPositionY - 10],
        direction: Direction.down
      });
      // // 绘制批注内容
      font.height = 14;
      font.color = "rgb(140,140,140)";
      font.family = "微软雅黑";
      Renderer.drawWrapText(text, 230, this.lineHeight, font, this.left + 10, Math.round(this.top + 70), this.editor);
    } else {
      Renderer.drawChevron({
        position: [this.left + 245, commentInfoPositionY - 10],
        direction: Direction.left
      });
    }
  }

  drawCommentBox (page?: Page) {
    let boxColor;
    let lineWidth;
    const { wordSelectedBgColor, listItemSelectedBorderColor } = this.editor.config.comment;
    if (this.comment.selected) {
      boxColor = listItemSelectedBorderColor ?? wordSelectedBgColor;
      lineWidth = 1.5;
    } else {
      boxColor = COMMENT_BORDER_COLOR;
      lineWidth = 1;
    }

    // 先画个大框框;
    Renderer.draw_stroke_rect(
      this.left + 0.5,
      Math.ceil(this.top) + 0.5,
      this.width,
      this.height,
      boxColor,
      "line",
      lineWidth,
      5
    );

    // // 中间绿色的线
    // Renderer.draw_line(
    //   [this.left, this.top + this.height + 15],
    //   [this.left + this.width, this.top + this.height + 15],
    //   "green",
    //   1,
    //   0.5,
    //   "dash"
    // );

    if ((this.editor.useNewVersionCommentList && page  && this.editor.newVersionOpenCommentMap.has(this.comment.id)) || this.comment === this.editor.internal.currentComment) { 
      // 分割的线
      Renderer.draw_line(
        [this.left, this.top + COMMENT_LIST_ITEM_TITLE_HEIGHT],
        [this.left + this.width, this.top + COMMENT_LIST_ITEM_TITLE_HEIGHT],
        "rgb(150,150,150)",
        1,
        0.5,
        "solid"
      );
  
      
    }
  }

  draw (page?: Page) {
    this.drawCommentText(page);
    this.drawCommentBox(page);
  }

  // 其实这个判断的不是是不是在视口内 而是 是不是在该页里边展示了 没有跑到该页外边去 就是说 this.top 要大于 page.top this.bottom 要小于 page.bottom
  checkIsInPageport(): {isVisible?: boolean, distance?: number} {
    const page = this.page
    if (!page) return {};
    let distance = 0;
    let isVisible = true;
    const top = this.top;
    const bottom = this.bottom;
    if (top >= page.top && bottom <= page.bottom) {
      isVisible = true
    } else if (top < page.top) {
      distance = top - page.top - COMMENT_LIST_ITEM_SPACE_HEIGHT; 
      isVisible = false;
    } else if (bottom > page.bottom) {
      distance = bottom - page.bottom + COMMENT_LIST_ITEM_SPACE_HEIGHT; 
      isVisible = false;
    }
    return { isVisible, distance, }
  }
}
