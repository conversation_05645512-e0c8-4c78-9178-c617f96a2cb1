<template>
  <a-modal
    class="editor-modal-x"
    :class="simpleClass"
    v-model="visible"
    :width="width"
    :mask="masks"
    :centered="centered"
    :maskClosable="false"
    :bodyStyle="bodyStyle"
    @cancel="cancel"
    @ok="submit"
    destroyOnClose
  >
    <div slot="title" class="editor-title-x">
      <div style="font-size: 14px">{{ title }}</div>
    </div>
    <template>
      <slot></slot>
    </template>
    <template slot="footer">
      <slot name="editor-modal-footer">
        <!--        <a-button type="defalut" @click="submit">确认</a-button>-->
        <!--        <a-button type="defalut" @click="cancel">取消</a-button>-->
      </slot>
    </template>
  </a-modal>
</template>

<script>
export default {
  name: "current-modal",
  components: {},
  data() {
    return {
      visible: false,
      masks: false,
      modalMove: false,
      changedX: 0, // modal已经改变的位置
      changedY: 0,
      mouseDownX: 0,
      mouseDownY: 0,
      initBounding: null, // 初始的modal left top ... 改变位置的trnasform应该就是相对于这个位置来移动的
      bounding: null,
    };
  },
  computed: {
    simpleClass() {
      return Math.random().toString(36).substring(2);
    },
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 524,
    },
    title: {
      type: String,
      default: "",
    },
    freePoint: {
      type: Boolean,
      default: false,
    },
    centered: {
      type: Boolean,
      default: false,
    },
    bodyStyle: {
      type: Object,
      default: () => {},
    },
    noBlink: {
      type: Boolean,
      default: false,
    },
    sessionMove: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    show(val) {
      this.visible = val;
      if (val) {
        this.$nextTick(() => {
          if (!this.freePoint) {
            this.maskOnClick();
          } else {
            const doms = this.$el.getElementsByClassName("ant-modal-wrap");
            const modalDoms = this.$el.getElementsByClassName("ant-modal");
            if (modalDoms && modalDoms.length) {
              for (let i = 0; i < modalDoms.length; i++) {
                const modalDom = modalDoms[i];
                modalDom.style.top = "20px";
              }
            }
            if (doms && doms.length) {
              for (let i = 0; i < doms.length; i++) {
                const dom = doms[i];
                dom.style.pointerEvents = "none";
              }
            }
          }
          //使用全局变量导致混乱，全部改成私有变量
          this.contain = document.getElementsByClassName(this.simpleClass)[0]; // 获取的是a-modal
          if (this.contain) {
            this.header =
              this.contain.getElementsByClassName("ant-modal-header")[0]; // 获取的是header
            this.header.style.cursor = "move";
            this.modalContent =
              this.contain.getElementsByClassName("ant-modal-content")[0]; // 获取的是整体
            this.initBounding = this.modalContent.getBoundingClientRect();
            const move = JSON.parse(sessionStorage.getItem("modalMove"));
            if (move && this.sessionMove) {
              move.y = Math.max(move.y, 0);
              this.changedX = move.x;
              this.changedY = move.y;
              this.modalContent.style.transform = `translate(${move.x}px, ${move.y}px)`;
            }

            this.initialEvent(); // 此时this.visible永远为真啊
          }
        });
      } else {
        this.bounding = null;
        this.initBounding = null;
        this.changedX = 0;
        this.changedY = 0;
        this.mouseDownX = 0;
        this.mouseDownY = 0;
      }
    },

    immediate: true,
    deep: true,
  },
  beforeDestroy() {
    window.removeEventListener("mousedown", this.handleMouseDown, false);
    window.removeEventListener("mousemove", this.handleMouseMove, false);
    window.removeEventListener("mouseup", this.handleMouseUp, false);
  },
  methods: {
    resetNum() {
      this.mouseDownX = 0;
      this.mouseDownY = 0;
    },
    handleMouseDown(e) {
      //不显示时就不用触发鼠标事件
      if (this.$el.style.display == "none" || !this.show) return;
      // this.header.onselectstart = () => false;
      this.header.style.userSelect = "none";
      if (e.target === this.header || this.header.contains(e.target)) {
        // 说明点击在了header上
        this.mouseDownX = e.pageX; // 记录起始落点坐标
        this.mouseDownY = e.pageY;
        this.modalMove = true;
      }
    },
    handleMouseMove(e) {
      if (this.$el.style.display == "none" || !this.show) return;
      if (!this.modalMove) return;
      let tmpX = e.pageX;
      let tmpY = e.pageY;
      // e.pageX 不能大于 (window.innerWidth - 点击位置距离modalContent右侧边界的距离(bounding.right - mouseDownX))
      if (e.pageX < 30) {
        tmpX = 30;
      }
      if (e.pageY < 30) {
        tmpY = 30;
      }
      if (e.pageX > window.innerWidth - 20) {
        tmpX = window.innerWidth - 20;
      }
      if (e.pageY > window.innerHeight - 20) {
        tmpY = window.innerHeight - 20;
      }

      let distanceX = tmpX - this.mouseDownX;
      let distanceY = tmpY - this.mouseDownY;

      let translateX = distanceX + this.changedX;
      let translateY = distanceY + this.changedY;
      this.modalContent.style.transform = `translate(${translateX}px, ${translateY}px)`;
    },
    handleMouseUp() {
      if (this.$el.style.display == "none" || !this.show) return;
      if (!this.modalMove) return;
      this.modalMove = false;
      this.mouseDownX = 0;
      this.mouseDownY = 0;
      this.bounding = this.modalContent.getBoundingClientRect();
      this.changedX = (this.bounding?.left ?? 0) - this.initBounding?.left || 0;
      this.changedY = (this.bounding?.top ?? 0) - this.initBounding?.top || 0;
      const move = {
        x: this.changedX,
        y: this.changedY,
      };
      sessionStorage.setItem("modalMove", JSON.stringify(move));
    },
    initialEvent() {
      window.addEventListener("mousedown", this.handleMouseDown, true);
      window.addEventListener("mousemove", this.handleMouseMove, true);
      window.addEventListener("mouseup", this.handleMouseUp, true);
    },
    submit() {
      this.$emit("submit");
    },
    cancel() {
      this.$emit("cancel");
    },
    maskOnClick() {
      this.$nextTick(() => {
        document.getElementsByClassName("ant-modal-wrap")[
          document.getElementsByClassName("ant-modal-wrap").length - 1
        ].onclick = function (ev) {
          if (ev.target != this) return;
          let classDom =
            document.getElementsByClassName("ant-modal-content")[0].className;

          document.getElementsByClassName("ant-modal-content")[0].className =
            "ant-modal-content arrow_box";
          setTimeout(() => {
            document.getElementsByClassName("ant-modal-content")[0].className =
              classDom;
          }, 300);
        };
      });
    },
  },
};
</script>

<style scoped>
.editor-modal-x /deep/ .ant-btn-defalut {
  color: #000;
}

.editor-modal-x /deep/ .ant-modal-header {
  padding: 8px 10px;
}

.editor-modal-x /deep/ .ant-modal-close-x {
  height: 40px;
  line-height: 40px;
}

.editor-modal-x /deep/ .ant-modal-body {
  background-color: rgb(240, 240, 240);
  padding: 12px;
}

.editor-modal-x /deep/.ant-modal-content {
  box-shadow: 0 0px 12px rgb(0 0 0 / 40%);
}

.editor-modal-x /deep/ .ant-modal-footer {
  background-color: rgb(240, 240, 240);
  border-top: none;
  padding: 0 16px 10px 16px;
}

::v-deep .ant-modal-wrap {
  z-index: 9999;
}

.editor-title-x {
  height: 23px;
  display: flex;
}

.editor-modal-x /deep/.arrow_box {
  animation: glow 100ms ease-out 3 backwards;
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(100, 100, 100, 0.2),
      inset 0 0 5px rgba(100, 100, 100, 0.1);
  }

  100% {
    box-shadow: 0 0 20px rgba(100, 100, 100, 0.6),
      inset 0 0 10px rgba(100, 100, 100, 0.4);
  }
}
</style>
