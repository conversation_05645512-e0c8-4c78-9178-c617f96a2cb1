<template>
  <div class="pupil_pupil">
    <div class="col_pupil">
      <a-input type="text" class="input_pupil" v-model="meta.params[0]" />
      <a-input type="text" class="input_pupil" v-model="meta.params[1]" />
      <a-input type="text" class="input_pupil" v-model="meta.params[2]" />
    </div>
    <div class="col_pupil">
      <a-input
        type="text"
        class="input_center_pupil"
        v-model="meta.params[3]"
      />
    </div>
    <div class="col_pupil">
      <a-input type="text" class="input_pupil" v-model="meta.params[4]" />
      <a-input type="text" class="input_pupil" v-model="meta.params[5]" />
      <a-input type="text" class="input_pupil" v-model="meta.params[6]" />
    </div>
  </div>
</template>

<script>
export default {
  name: "pupil",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style scoped>
.pupil_pupil {
  width: 100%;
  height: 100%;
  display: flex;
}
.col_pupil {
  width: 166.5px;
}
.input_pupil {
  margin-top: 10px;
  margin-bottom: 10px;
  width: 150px;
}
.input_center_pupil {
  margin-top: 62px;
  margin-bottom: 10px;
  width: 150px;
}
</style>
