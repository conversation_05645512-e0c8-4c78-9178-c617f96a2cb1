<template>
  <div style="display: flex">
    <div v-for="(items, index) in headerBtn" :key="index">
      <a-dropdown overlay-class-name="func-menu-dropdown">
        <span class="func-menu">{{ items.name }}</span>
        <a-menu slot="overlay">
          <template v-for="(item, index) in items.data">
            <a-menu-item
              v-if="!item.children"
              :key="index"
              @click="clickButton(items.name, item)"
            >
              <my-icon :type="item.icon" :title="item.title" />
              <span class="func-overlay-menu">{{ item.title }}</span>
            </a-menu-item>

            <a-sub-menu v-else :key="index">
              <span slot="title">
                <my-icon :type="item.icon" :title="item.title" />
                <span class="func-overlay-menu">{{ item.title }}</span>
              </span>
              <a-menu-item
                v-for="(it, i) in item.children"
                :key="i"
                @click="clickButton(items.name, it)"
              >
                <my-icon :type="it.icon" />
                <span class="func-overlay-menu">{{ it.title }}</span>
              </a-menu-item>
            </a-sub-menu>
          </template>
        </a-menu>
      </a-dropdown>
    </div>
  </div>
</template>

<script>
export default {
  name: "headerMenuItem",
  mixins: [],
  props: ["headerBtn"],
  data() {
    return {};
  },
  created() {},
  methods: {
    clickButton(name, e) {
      this.$emit("onMenuClick", name, e);
    },
  },
};
</script>
<style scoped>
.func-menu {
  cursor: pointer;
  padding: 0 6px 0 6px;
  color: black;
}
</style>
