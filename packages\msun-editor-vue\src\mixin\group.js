// import BUS from "@/assets/js/eventBus";
const groupMixin = {
  data() {
    return {
      // group: {},
      isShowGroupModal: false, // 是否展示分组弹框
      modifyProperty: false,
    };
  },
  methods: {
    // 展示段落属性的模态框
    showGroupModal(modifyProperty) {
      // const group = this.editor.selection.getFocusGroup() ?? null;
      // this.$refs.group.group = group;
      // BUS.$emit("editor_" + this.editorId, { group: group });
      if (modifyProperty) {
        this.modifyProperty = modifyProperty;
      }
      this.isShowGroupModal = true;
    },
    closeGroupModal() {
      this.isShowGroupModal = false;
      this.modifyProperty = false;
      this.$refs.group.clearTemplateState();
      this.editor.focus();
    },
    confirmGroupModal(data) {
      const editor = this.editor;
      const group = editor.selection.getFocusGroup();
      const date = data.date.valueOf();
      const id = data.id;
      const direction = data.insert_direction;
      const new_page = data.new_page;
      const name = data.name;
      if (data.modifyProperty) {
        editor.modifyGroupTime(date);
        group.modifyId(id);
        group.new_page = new_page;
        group.name = name;
        group.lock = data.lock;
        if (data.insertBy === "time") {
          editor.sortGroup();
        }
      } else {
        //有ID的时候使用填入的ID，没有的时候给一个默认的
        if (data.insertBy === "position") {
          id
            ? editor.insertGroup(direction, date, id)
            : editor.insertGroup(direction, date);
        } else if (data.insertBy === "time") {
          id
            ? editor.insertGroupWithDateSort(date, id)
            : editor.insertGroupWithDateSort(date);
          editor.sortGroup();
        }
        const focusGroup = editor.selection.getFocusGroup();
        focusGroup.new_page = new_page;
        focusGroup.name = name;
        focusGroup.lock = data.lock;
      }
      //设置分组表单模式
      if (data.isForm) {
        editor.setGroupFormMode(true);
      } else editor.setGroupFormMode(false);
      this.editor.focus();
      this.isShowGroupModal = false;
      this.modifyProperty = false;
      this.$refs.group.clearTemplateState();
      this.$refs.group.showDirectionOption();
      this.editor.update();
      this.editor.scroll_by_focus();
      this.editor.render();
      // }
    },
  },
};
export default groupMixin;
