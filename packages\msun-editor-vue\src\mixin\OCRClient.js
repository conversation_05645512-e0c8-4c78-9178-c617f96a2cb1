import axios from "axios";

export default class OCRClient {
  constructor() {
    this.appId = "864049f7b8cda68bec081d72669428d4";
    this.secretCode = "058933e616c6ec39f10f95ae85bcf939";
    this.baseUrl = "https://api.textin.com/ai/service/v1/pdf_to_markdown";
  }

  async recognize(fileContent, options) {
    const params = new URLSearchParams();
    for (const [key, value] of Object.entries(options)) {
      params.append(key, value.toString());
    }

    const url = `${this.baseUrl}${
      params.toString() ? "?" + params.toString() : ""
    }`;

    const response = await axios({
      method: "post",
      url: url,
      data: fileContent,
      headers: {
        "x-ti-app-id": this.appId,
        "x-ti-secret-code": this.secretCode,
        // 读取本地文件
        "Content-Type": "application/octet-stream",
      },
      responseType: "text",
    });

    return response.data;
  }
}
