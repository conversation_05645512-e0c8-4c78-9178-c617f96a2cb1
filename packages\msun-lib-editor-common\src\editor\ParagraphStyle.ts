import { alignType } from "./Definition";

export interface ParagraphStyle {
  align: alignType;
  before_paragraph_spacing: number; // 段落前的间距 表示1倍的行高
  after_paragraph_spacing: number; // 段落后的间距 表示1倍的行高
  indentation: number; // 首行缩进
  row_ratio: number; // 行间距
  isOrder: boolean;
  islist: boolean;
  level: number;
}

export default class ParaStyle {
  align: alignType;
  before_paragraph_spacing: number; // 段落前的间距 表示1倍的行高
  after_paragraph_spacing: number; // 段落后的间距 表示1倍的行高
  indentation: number; // 首行缩进
  row_ratio: number; // 行间距
  isOrder: boolean;
  islist: boolean;
  level: number;

  constructor (ParagraphStyle: ParagraphStyle) {
    this.align = ParagraphStyle.align;
    this.before_paragraph_spacing = ParagraphStyle.before_paragraph_spacing;
    this.after_paragraph_spacing = ParagraphStyle.after_paragraph_spacing;
    this.indentation = ParagraphStyle.indentation;
    this.row_ratio = ParagraphStyle.row_ratio;
    this.isOrder = ParagraphStyle.isOrder;
    this.islist = ParagraphStyle.islist;
    this.level = ParagraphStyle.level;
  }

  static isEqual (paraStyle: ParaStyle, another: ParaStyle) {
    return (
      paraStyle.before_paragraph_spacing === another.before_paragraph_spacing &&
      paraStyle.after_paragraph_spacing === another.after_paragraph_spacing &&
      paraStyle.indentation === another.indentation &&
      paraStyle.row_ratio === another.row_ratio &&
      paraStyle.isOrder === another.isOrder &&
      paraStyle.islist === another.islist &&
      paraStyle.level === another.level
    );
  }
}
