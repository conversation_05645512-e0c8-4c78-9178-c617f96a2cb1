import Cell from "./Cell";
import Editor from "./Editor";
import ImageRect from "./ImageRect";
import Renderer from "./Renderer";
import { uuid } from "./Utils";

export default class FloatModel extends Cell {
  originPosition: [number, number] = [0, 0]; // TODO 原点应该是相对于页面左上角 而不是相对于编辑器左上角 现在还没改

  padding_left: number;
  padding_right: number;

  #_width: number = 400;
  #_height: number = 200;

  orderOfCreation: number = 0; // 创建时的顺序
  zIndex: number = 0; // 层级 默认为 0 越高层级越高

  constructor (
    editor: Editor,
    width: number,
    height: number,
    originPosition: [number, number]
  ) {
    super(editor, [0, 0], 1, 1, null, uuid("float-model"));
    this.width = width;
    this.height = height;

    this.padding_left = 0;
    this.padding_right = 0;
    this.padding_top = 0;
    this.padding_bottom = 0;

    this.originPosition = [...originPosition];
    this.orderOfCreation = editor.floatModels.length;
  }

  set width (width: number) {
    this.#_width = width;
  }

  get width () {
    return this.#_width;
  }

  set height (height: number) {
    this.#_height = height;
  }

  get height () {
    return this.#_height;
  }

  draw () {
    Renderer.save();
    Renderer.draw_rectangle(
      this.originPosition[0],
      this.originPosition[1],
      this.width,
      this.height,
      "rgba(200,200,200,0.1)"
    );
    Renderer.clipCell(
      this.originPosition[0],
      this.originPosition[1],
      this.width,
      this.height
    );
    if (this.editor.currentFloatModel === this) {
      const aroundRect = new ImageRect(
        this.originPosition[0],
        this.originPosition[1],
        this.width,
        this.height
      );
      aroundRect.draw();
    }

    Renderer.save();
    Renderer.translate(this.originPosition[0], this.originPosition[1]);
    this.children.forEach(rowOrTable => rowOrTable.draw(this.editor));
    Renderer.restore();

    Renderer.restore();
  }

  contain (x: number, y: number): boolean {
    if (x > this.originPosition[0] && x < this.originPosition[0] + this.width) {
      if (y > this.originPosition[1] && y < this.originPosition[1] + this.height) {
        return true;
      }
    }
    return false;
  }
}
