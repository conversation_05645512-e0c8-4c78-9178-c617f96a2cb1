<template>
  <div class="tree-menu">
    <div v-show="dataSetInfo.dataSets.length">
      <a-card
        ref="dataSetPanel"
        size="small"
        class="header-card"
        :body-style="{
          'padding-left': '0px',
          'padding-right': '0px',
          'padding-bottom': '0px',
        }"
      >
        <label class="header-card-title">{{ dataSetInfo.title }}</label>
        <div class="header-content msun-editor-forms-scrollbar">
          <p
            v-for="(item, index) in dataSetInfo.dataSets"
            v-show="!item.name.endsWith('_print')"
            :key="index"
            @click="
              () => {
                handleDataSetChange(item.value, index);
              }
            "
            :class="selectedDtaSetIndex === index ? 'dataset-selected' : ''"
          >
            {{ item.name }}
          </p>
        </div>
      </a-card>
    </div>
    <!-- <a-select
      v-if="dataSets.length > 1"
      style="width: 100%; margin-bottom: 4px; padding: 0px 5px 0px 5px"
      :default-value="dataSets[0].value"
      @change="handleDataSetChange"
    >
      <a-select-option
        v-for="item in dataSets"
        :value="item.value"
        :key="item.value"
      >
        {{ item.name }}
      </a-select-option>
    </a-select>-->
    <a-card
      size="small"
      ref="editorDesignTreeCard"
      class="header-card tree-card"
      :body-style="{
        'padding-left': '0px',
        'padding-right': '0px',
      }"
    >
      <a-tooltip
        v-if="dataSetInfo.optionTip"
        placement="top"
        :title="dataSetInfo.optionTip"
        :get-popup-container="getPopupContainer"
      >
        <a-icon type="question-circle" class="header-card-title-icon" />
      </a-tooltip>
      <span @click="sortFields" class="header-card-title-sort-icon">
        <a-icon v-if="sortType === 1" type="sort-ascending" title="升序排序" />
        <a-icon
          v-else-if="sortType === 2"
          type="sort-descending"
          title="降序排序"
        />
        <a-icon v-else type="pause" title="原始顺序" />
      </span>

      <label v-if="dataSetInfo.optionTitle" class="header-card-title">{{
        dataSetInfo.optionTitle
      }}</label>
      <a-input-search
        placeholder="关键字检索"
        @change="onChange"
        style="padding: 5px 5px 0px 5px"
        @keydown="onKeyDown"
      />
      <div class="msun-editor-forms-scrollbar tree-menu-tree">
        <a-tree
          show-icon
          :expanded-keys="expandedKeys"
          :auto-expand-parent="autoExpandParent"
          :tree-data="selectTreeData"
          :selectable="false"
          @expand="onExpand"
          :draggable="draggable"
          @dragstart="handleDragStart"
          @click="click"
        >
          <template slot="title" slot-scope="{ title, code }">
            <a-tooltip>
              <span
                :style="
                  selectNode && code === selectNode.code
                    ? { background: '#C8E9FF' }
                    : {}
                "
                v-if="title.indexOf(searchValue) > -1"
              >
                {{ title.substr(0, title.indexOf(searchValue)) }}
                <span style="background-color: #ffff00">{{ searchValue }}</span>
                {{
                  title.substr(title.indexOf(searchValue) + searchValue.length)
                }}
              </span>
              <span v-else>{{ title }}</span>
              <template slot="title">
                <span> {{ title }} </span>
              </template>
            </a-tooltip>
          </template>
          <icon-common
            slot="dict0"
            :iconStyle="{
              width: '15px',
              height: '12px',
            }"
            icon="icon-ecs-related-resources"
          ></icon-common>
          <icon-common
            slot="dict1"
            :iconStyle="{
              fill: '#19C37D',
              width: '15px',
              height: '12px',
            }"
            icon="icon-ecs-related-resources"
          ></icon-common>
          <icon-common
            slot="date"
            :iconStyle="{
              fill: '#1E80FF',
              width: '15px',
              height: '12px',
            }"
            icon="icon-bianzu00"
          ></icon-common>
          <icon-common
            slot="number"
            :iconStyle="{
              fill: '#00B180',
              width: '15px',
              height: '12px',
            }"
            icon="icon-a-123"
          ></icon-common>
          <icon-common
            slot="string"
            :iconStyle="{
              fill: '#3587E0',
              width: '15px',
              height: '12px',
            }"
            icon="icon-Abc"
          ></icon-common>
        </a-tree>
        <a-empty
          v-if="!selectTreeData.length"
          description="未检索到数据"
          :image="simpleImage"
        />
      </div>
    </a-card>
  </div>
</template>

<script>
import IconCommon from "./iconCommon.vue";
import { Empty } from "ant-design-vue";
export default {
  name: "treeMenu",
  components: { IconCommon },
  props: {
    draggable: {
      type: Boolean,
      default: true,
    },
    treeData: {
      type: Array,
      default: () => [],
    },
    dataSetInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      selectedDtaSetIndex: 0,
      dataList: [],
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE,
      expandedKeys: [],
      selectTreeData: [],
      inputSearchText: "",
      searchValue: "",
      autoExpandParent: true,
      selectNode: null,
      sortType: 1,
    };
  },
  watch: {
    treeData: {
      handler(val) {
        this.dataList = [];
        this.selectNode = null;
        this.generateList(val);
        let resList = [];
        if (this.searchValue) {
          const transList = [];
          resList = this.filterTreeDataByVal(transList, this.searchValue);
        } else {
          resList = [...val];
        }
        this.exeFieldSort(resList);
      },
      immediate: true,
    },
    dataSetInfo: {
      handler() {
        this.selectedDtaSetIndex = 0;
      },
      deep: true,
    },
  },
  mounted() {
    setTimeout(() => {
      this.$refs.editorDesignTreeCard.$el.style.maxHeight =
        this.$el.offsetHeight -
        this.$refs.dataSetPanel.$el.offsetHeight -
        30 +
        "px";
    });
  },
  methods: {
    click(event, item) {
      this.selectNode = null;
      const key = item.eventKey;
      const { data, resArr } = this.getDataByKey(key);
      if (data.children && data.children.length) {
        const index = this.expandedKeys.indexOf(key);
        if (index > -1) {
          this.expandedKeys.splice(index, 1);
        } else {
          this.expandedKeys.push(key);
        }
        this.autoExpandParent = false;
      } else {
        this.$emit("clickTree", data, key, resArr);
      }
    },
    sortFields() {
      if (this.sortType > 1) {
        this.sortType = 0;
      } else {
        this.sortType++;
      }
      this.exeFieldSort(this.selectTreeData);
    },
    exeFieldSort(filterTreeData) {
      if (this.sortType === 0) {
        this.selectTreeData = [...this.treeData];
      } else {
        this.selectTreeData = filterTreeData.sort((a, b) => {
          if (this.sortType === 1) {
            return a.title.localeCompare(b.title);
          } else if (this.sortType === 2) {
            return b.title.localeCompare(a.title);
          }
        });
      }
    },
    handleDragStart(info) {
      const key = info.node.eventKey;
      const { data, resArr } = this.getDataByKey(key);
      this.$emit("dragstart", data, key, resArr);
    },
    getDataByKey(key) {
      const indexArr = key.replace("sub_", "").split("_");
      let data = this.treeData[Number(indexArr[0])];
      const resArr = [];
      resArr.push(data);
      for (let i = 1; i < indexArr.length; i++) {
        if (indexArr[i] !== undefined) {
          data = data.children[Number(indexArr[i])];
          resArr.push(data);
        }
      }
      return { data, resArr };
    },
    onExpand(expandedKeys) {
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    generateList(data, pNode) {
      for (let i = 0; i < data.length; i++) {
        // 生成key
        const node = data[i];
        if (!pNode) {
          node.key = "sub_" + i;
        } else {
          node.key = pNode.key + "_" + i;
        }
        this.dataList.push({ key: node.key, title: node.title });
        if (node.children) {
          this.generateList(node.children, node);
        }
      }
    },
    onChange(e) {
      const value = e.target.value;
      clearTimeout(this.filterTimeout);
      this.filterTimeout = setTimeout(() => {
        this.searchValue = value;
        const transList = [];
        this.selectTreeData = this.filterTreeDataByVal(transList, value);
      }, 200);
    },
    onKeyDown(e) {
      if (e.key.toLowerCase() === "arrowup") {
        const treeData = this.selectTreeData;
        const index = treeData.indexOf(this.selectNode);
        this.selectNode = treeData[index - 1];
        e.preventDefault();
      }
      if (e.key.toLowerCase() === "arrowdown") {
        const treeData = this.selectTreeData;
        const index = treeData.indexOf(this.selectNode);
        this.selectNode = treeData[index + 1];
        e.preventDefault();
      }
      if (e.key.toLowerCase() === "enter") {
        if (this.selectNode) {
          this.$emit("clickTree", this.selectNode);
        }
      }
    },
    filterTreeDataByVal(transList, value) {
      const expandedKeys = this.dataList
        .map((item) => {
          if (item.title.toLowerCase().indexOf(value.toLowerCase()) > -1) {
            this.getSelectData(transList, item);
            return this.getParentKey(item.key, this.treeData);
          }
          return null;
        })
        .filter((item, i, self) => item && self.indexOf(item) === i);
      Object.assign(this, {
        expandedKeys,
        searchValue: value,
        autoExpandParent: true,
      });
      if (!value) {
        this.expandedKeys = [];
        transList = this.treeData;
      }
      return transList;
    },
    getSelectData(transList, item) {
      for (let i = 0; i < this.treeData.length; i++) {
        const data = this.treeData[i];
        if (data.key === item.key) {
          transList.push(data);
        } else {
          this.dealDataChildren(data, item);
        }
      }
    },
    dealDataChildren(transList, data, item) {
      if (data.children) {
        for (let j = 0; j < data.children.length; j++) {
          const element = data.children[j];
          if (element.key === item.key) {
            if (
              !transList.some((info) => {
                return info.key === data.key;
              })
            ) {
              transList.push(data);
            }
          }
        }
      }
    },
    getParentKey(key, tree) {
      let parentKey;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          if (node.children.some((item) => item.key === key)) {
            parentKey = node.key;
          } else if (this.getParentKey(key, node.children)) {
            parentKey = this.getParentKey(key, node.children);
          }
        }
      }
      return parentKey;
    },
    handleDataSetChange(value, index) {
      this.selectedDtaSetIndex = index;
      this.$emit("handleDataSetChange", value);
    },
    getPopupContainer(trigger) {
      return trigger.parentElement;
    },
  },
};
</script>
<style scoped lang="less">
/*.tree-card {
  max-height: calc(~"100% - 145px");
}*/
.tree-menu {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  .tree-menu-tree {
    flex: 1;
    height: 100%;
    margin-top: 10px;
    overflow: auto;
  }
}
/deep/.ant-tree-treenode-switcher-close {
  transition: all 0.2s;
}
/deep/.ant-tree-treenode-switcher-close:hover {
  background-color: rgb(231, 247, 255);
}
/deep/.ant-tree li .ant-tree-node-content-wrapper:hover {
  background-color: rgb(231, 247, 255);
  transition: all 0.2s;
}

/deep/ .ant-tree li .ant-tree-node-content-wrapper {
  padding: 0px 0px 0px 0px;
}
/deep/ .ant-card-body {
  height: calc(~"100% - 35px");
}
/* 定义变量 */
:root {
  --panelHeight: 50px;
}

.dataSetPanel {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  overflow: auto;
}
.panelClass {
  height: var(--panelHeight);
}
.header-card {
  position: relative;
  margin: 10px 5px;
  .header-card-title {
    position: absolute;
    top: -12px;
    left: 5px;
    color: #1f1f1f;
    font-size: 14px;
    font-weight: bold;
    background-color: white;
  }
  .header-card-title-icon {
    position: absolute;
    top: -8px;
    left: 40px;
    color: #f8b600;
    font-size: 14px;
    font-weight: bold;
    background-color: white;
    cursor: pointer;
  }
  .header-card-title-sort-icon {
    position: absolute;
    top: -12px;
    right: 5px;
    /*color: #f8b600;*/
    font-size: 16px;
    font-weight: bold;
    background-color: white;
    cursor: pointer;
  }
}
.msun-editor-forms-scrollbar {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d3e3fd;
    border-radius: 6px;
  }
  /* 鼠标悬停时滑块样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #a8c7fa;
  }
  &::-webkit-scrollbar-track {
    background-color: white;
    -webkit-box-shadow: inset 0 0 4px rgba(100, 100, 100, 0.01);
  }
}
.header-content {
  padding: 0;
  max-height: 100px;
  overflow: auto;
  p {
    margin: 0px 0px 0px 0px;
    padding: 5px 5px 5px 5px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }
  p:hover {
    color: #1890ff;
  }
}
.dataset-selected {
  color: #1890ff;
  background: #e7f7ff;
}
</style>
