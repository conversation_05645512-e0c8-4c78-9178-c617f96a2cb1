
import Renderer from "./Renderer";
export default class ImageRect {
  imageRectList: any = [];
  x: number;
  y: number;
  width: number;
  height: number;
  rect_size:number;
  type:string;
  constructor (x: number, y: number, width: number, height: number, type: string = "rect") {
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
    this.type = type;
    this.rect_size = 6;
    if (type === "rect") {
      this.imageRectList = [
        { x: x, y: y, width: this.rect_size, height: this.rect_size },
        { x: x + width / 2 - this.rect_size / 2, y: y, width: this.rect_size, height: this.rect_size },
        { x: x + width - this.rect_size, y: y, width: this.rect_size, height: this.rect_size },
        { x: x, y: y + height / 2 - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x + width - this.rect_size, y: y + height / 2 - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x, y: y + height - this.rect_size, width: this.rect_size, height: this.rect_size },
        { x: x + width / 2 - this.rect_size / 2, y: y + height - this.rect_size, width: this.rect_size, height: this.rect_size },
        { x: x + width - this.rect_size, y: y + height - this.rect_size, width: this.rect_size, height: this.rect_size }
      ];
    } else if (type === "mark") {
      this.imageRectList = [
        { x: x - this.rect_size / 2, y: y - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x + width / 2 - this.rect_size / 2, y: y - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x + width - this.rect_size / 2, y: y - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x - this.rect_size / 2, y: y + height / 2 - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x + width - this.rect_size / 2, y: y + height / 2 - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x - this.rect_size / 2, y: y + height - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x + width / 2 - this.rect_size / 2, y: y + height - this.rect_size / 2, width: this.rect_size, height: this.rect_size },
        { x: x + width - this.rect_size / 2, y: y + height - this.rect_size / 2, width: this.rect_size, height: this.rect_size }
      ];
    }
  }

  /**
   * canvas画小方块
   */
  draw () {
    if (this.type === "mark") {
      Renderer.draw_stroke_rect(this.x, this.y, this.width, this.height);
    } else {
      Renderer.draw_stroke_rect(this.x + this.rect_size / 2, this.y + this.rect_size / 2, this.width - this.rect_size, this.height - this.rect_size);
    }
    this.imageRectList.forEach((e: any) => {
      Renderer.draw_border_rect(e.x, e.y, e.width, e.height, "#fff");
    });
  }
}
