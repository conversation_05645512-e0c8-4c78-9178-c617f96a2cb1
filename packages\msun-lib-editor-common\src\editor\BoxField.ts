
import XField from "./XField";
import Widget from "./Widget";
import Cell from "./Cell";
import { isRow } from "./Utils";
import { isBoxField, isWidget } from "./Helper";
import Editor from "./Editor";

/**
 * 自定义复选框单选框功能文本域
 */
export default class BoxField extends XField {
  type:string = "box";

  box_checked:number = 0; // 当type为box时，0 未选中， 1 选中

  box_multi:number = 0; // 当type为box时， 0 单选 1 多选,主要用于成组外层文本域，内层文本域默认全部值为0

  required: number = 0; // 是否必填

  get boxType () {
    const boxField = this.parent_box.children.find(item => isBoxField(item));
    const widget = boxField?.children.find(item => isWidget(item));
    return widget?.widgetType
  }

  static attrJudgeUndefinedAssign (newModel:BoxField, raw:any) {
    if (raw.box_checked !== undefined) newModel.box_checked = raw.box_checked;
    if (raw.box_multi !== undefined) newModel.box_multi = raw.box_multi;
    if (raw.required !== undefined) newModel.required = raw.required;
  }

  /**
     * 获取复选框组件的最外层
     */
  get parent_box ():BoxField {
    if (this.parent && this.parent.type === "box") {
      return this.parent as BoxField;
    } else {
      return this;
    }
  }

  /**
     * 获取成组多选框的所有子项
     */
  get box_children ():BoxField[] {
    return this.children.filter((box) => {
      return (isBoxField(box));
    }) as BoxField [];
  }

  /**
     * 判断是否为外层组件
     */
  get is_parent_box ():Boolean {
    return (this.type === "box" && !this.box_widget);
  }

  /**
     * 获取当前分组选中项
     */
  get box_checked_items (): BoxField [] {
    const all_checked_items:any = [];
    // 如果当前多选框有分组属性，则获取所有同名组选中项
    if (this.parent_box && this.parent_box.is_parent_box) {
      const parent_boxes = this.group_boxes;
      parent_boxes.forEach((p_box) => {
        const all_item = p_box.getFieldInChildren() as BoxField[];
        const checked_items = all_item.filter((item:BoxField) => {
          return item.box_checked;
        });
        all_checked_items.push(...checked_items);
      });
    } else if (this.box_checked) {
      all_checked_items.push(this);
    }
    return all_checked_items;
  }

  get group_all_items (): BoxField [] {
    const all_checked_items:any = [];
    // 如果当前多选框有分组属性，则获取所有同名组选中项
    if (this.parent_box && this.parent_box.is_parent_box) {
      const parent_boxes = this.group_boxes;
      parent_boxes.forEach((p_box) => {
        const all_item = p_box.getFieldInChildren() as BoxField[];
        const checked_items = all_item.filter((item:BoxField) => {
          return !item.is_parent_box;
        });
        all_checked_items.push(...checked_items)
      });
    } else{
      all_checked_items.push(this);
    }
    return all_checked_items;
  }

  /**
     * 用于判断box文本域中的复选框单选框是否可设置选中
     */
  get box_widget (): Widget | null {
    const box_widget = this.children.find((item) => (isWidget(item))) as Widget;
    return box_widget;
  }

  /**
     * 当前复选框是否可选
     */
  get disabled () {
    return this.box_widget?.disabled;
  }

  /**
     * 获取所有与当前多选框所在分组的组名相同的父多选框
     */
  get group_boxes ():BoxField[] {
    const group_boxes:any = [];
    if (!this.parent_box.is_parent_box) {
      return group_boxes;
    }
    // 如果未设置分组名，只返回当前分组
    const group_name = this.parent_box.name;
    if (!group_name) {
      return [this.parent_box];
    }
    let root_cell:Cell = this.cell;
    const all_cell:Cell[] = [];
    if (this.cell.parent && this.cell.parent.parent) {
      root_cell = this.cell.parent.parent;
    }
    all_cell.push(root_cell);
    // 收集所有root_cell下的所有cell
    for (let j = 0; j < root_cell.children.length; j++) {
      const row = root_cell.children[j];
      if (isRow(row)) continue;
      all_cell.push(...row.children);
    }
    for (let i = 0; i < all_cell.length; i++) {
      const cell = all_cell[i];
      const filter_cells = cell.getFieldsByName(group_name).filter((field) => {
        return (isBoxField(field)) && field.is_parent_box;
      });
      group_boxes.push(...filter_cells);
    }
    return group_boxes;
  }

  copy (cell:Cell) {
    const field = this.simpleCopy(cell);
    field.generateMainProps();
    return field;
  }

  simpleCopy (cell:Cell) {
    const field = new BoxField(this.id, this.style, cell);
    const propKeyArr = ["name", "type", "placeholder", "tip", "start_symbol", "end_symbol", "display_type", "show_symbol", "deletable", "readonly", "show_format", "show_field", "cascade_list","automation_list", "replace_format", "number_format", "source_id",
      "active_type", "multi_select", "separator", "inputMode", "formula", "formula_value","ext_cell", "valid", "valid_content", "box_checked", "box_multi", "required", "max_width", "min_width", "showAsterisk", "maxHeight"];
    for (let i = 0; i < propKeyArr.length; i++) {
      // @ts-ignore
      field[propKeyArr[i]] = this[propKeyArr[i]];
    }
    return field;
  }

  /**
     * 清空所有选中的复选框与单选框
     */
  clearBoxChecked () {
    const items = this.box_checked_items;
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const widget = item.box_widget;
      if (isWidget(widget)) {
        widget.selected = false;
        item.box_checked = 0;
      }
    }
  }

  /**
     * 设置复选框或单选框选中
     * @param checked 是否选中
     */
  updateBoxChecked (checked:boolean) {
    if (this.parent_box.is_parent_box && !this.parent_box.box_multi) {
      this.clearBoxChecked();
    }
    const box_widget = this.box_widget;
    if (box_widget) {
      box_widget.selected = !!checked;
      this.box_checked = checked ? 1 : 0;
    }
  }
  // 根据val设置 一组复选框的选中状态
  updateGroupCheckedByValue (val:any,selectType?:number) {
    if(Array.isArray(val)){
      val = val.map(ele=>String(ele))
    }
    // 字符串中如果存在英文逗号，则也处理成数组
    if (typeof val === "string" && val.indexOf(",") > -1) {
      val = val.split(",");
    }
    const parentBox = this.parent_box;
    // 不传的时候为自动识别
    if(selectType === undefined){
      selectType = parentBox.meta.selectType
    }
    let valTexts:string[] = []
    parentBox.box_children.forEach((b) => {
      let keyword = String(b.formula_value)
      if(selectType === 1){
        // 根据编码匹配
        keyword = b.name
      }
      const text = b.text;
      if(selectType === 2){
        // 根据选项匹配
        keyword = text
      }
      if (
        (Array.isArray(val) &&
              (val as any).includes(keyword)) ||
            keyword === String(val)
      ) {
        b.updateBoxChecked(true);
        valTexts.push(text)
      }
    });
    if(valTexts.length){
      this.cell.editor.showOrHideField(parentBox,valTexts)
    }
  }


  /**
   * 设置文本域是否只读
   * @param disabled 是否禁用
   * @param field 干嘛滴
   * @returns 设置成功了没
   */
  static setDisabled(
    editor: Editor,
    disabled: boolean,
    field: XField
  ) {
    // 级联
    if (field) {
      const all_field = field.getFieldInChildren();
      for (let i = 0; i < all_field.length; i++) {
        const element = all_field[i];
        if(isBoxField(element)){
          const widget = element.box_widget;
          if(widget){
            widget.disabled = disabled ? 1 : 0;
          }
        }
      }
      return true;
    } else {
      return false;
    }
  }
}
