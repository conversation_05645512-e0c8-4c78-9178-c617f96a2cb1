<template>
  <div
    v-show="show"
    class="editor-float-button"
    :style="mobilePosition"
    id="mobileButton"
  >
    <div class="editor-float-button-level1">
      <div
        v-for="(item, index) in btnData"
        @click="btnClick_(item)"
        :key="index"
      >
        <div>
          <span class="editor-float-button-item-span">
            <span class="icon-text">
              <span style="margin-left: 7px; margin-right: 7px">
                {{ item.name }}</span
              >
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "mobileButton",
  props: {
    show: {
      type: Boolean,
      default: true,
    },
    btnData: {
      type: Array,
      default: () => [],
    },
    mobilePosition: {
      type: Object,
      default: () => {
        return {
          left: "0px",
          top: "0px",
        };
      },
    },
  },
  watch: {
    show() {},
  },
  mounted() {
    const divElement = document.getElementById("mobileButton");
    if (!divElement) return;
    divElement.addEventListener("selectstart", function (e) {
      e.preventDefault();
    });
  },
  data() {
    return {
      customIconStyle: {
        width: "14px",
        height: "14px",
        fill: "rgba(0, 133, 255, 0.8)",
      },
      selectChildMap: {},
      childBtnPosition: {
        top: "-45px",
      },
    };
  },
  methods: {
    btnClick_(item) {
      this.$emit("mobileBtnClick", item.name);
    },
  },
};
</script>

<style scoped>
.editor-float-button {
  position: absolute;
  width: fit-content !important;
  border-radius: 2px;
  display: block;
  opacity: 1;
  color: rgb(78, 90, 112);
}

.editor-float-button-level1 {
  display: flex;
  width: fit-content;
  -webkit-box-pack: start;
  justify-content: center;
  z-index: 1001;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: rgba(90, 109, 122, 0.3) 0px 1px 16px;
  flex-wrap: wrap;
  background: rgb(255, 255, 255);
  padding: 4px;
  position: relative;
  border-radius: 5px;
  top: 0px;
}

.fade-in-item-container {
  display: flex;
  place-content: center flex-start;
  -webkit-box-pack: start;
  z-index: 1001;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: rgba(90, 109, 122, 0.3) 0px 1px 16px;
  flex-wrap: wrap;
  background: rgb(255, 255, 255);
  padding: 4px;
  position: relative;
  border-radius: 5px;
  width: 100%;
  top: 0px;
}

.editor-float-button-item {
  display: flex;
  margin-left: 3px;
  position: relative;
  -webkit-box-pack: start;
  justify-content: start;
  font-size: 12px;
  height: 100%;
  width: auto;
  cursor: pointer;
}

.editor-float-button-item-span {
  display: flex;
  user-select: none;
}

.editor-float-button-item-span:hover,
.editor-float-button-item:hover {
  background-color: rgb(237, 242, 255);
}

.float-button-selected {
  border-radius: 2px;
  align-self: center;
  color: #5b89fe !important;
  background-color: rgb(237, 242, 255);
}

.icon-text {
  position: relative;
  display: flex;
  height: 25px;
  padding: 0px 5px;
  -webkit-box-align: center;
  align-items: center;
  box-sizing: border-box;
  line-height: 1.5;
  white-space: nowrap;
  clear: both;
  font-size: 12px;
  font-weight: normal;
  cursor: pointer;
  opacity: 1;
}
</style>
