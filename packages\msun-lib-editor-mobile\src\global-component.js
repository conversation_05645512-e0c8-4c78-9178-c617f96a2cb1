import Vue from "vue";
import "./styles/modalStyle.less";

async function loadVantComponents() {
  const [
    { default: Popup },
    { default: DatetimePicker },
    { default: ActionSheet },
  ] = await Promise.all([
    import("vant/lib/popup"),
    import("vant/lib/datetime-picker"),
    import("vant/lib/action-sheet"),
    import("vant/lib/popup/style"),
    import("vant/lib/datetime-picker/style"),
    import("vant/lib/action-sheet/style"),
  ]);
  Vue.use(Popup);
  Vue.use(DatetimePicker);
  Vue.use(ActionSheet);
}

if (
  /Mobi/.test(navigator.userAgent) ||
  /Android/i.test(navigator.userAgent) ||
  /iPhone|iPad|iPod/i.test(navigator.userAgent)
) {
  loadVantComponents().catch((error) => {
    console.error("组件加载失败：", error);
  });
}

Vue.config.productionTip = false;
