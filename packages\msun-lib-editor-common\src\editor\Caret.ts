import Box from "./Box";
import Cell from "./Cell";
import Character from "./Character";
import { Config } from "./Config";
import Editor from "./Editor";
import { contextStateSetByCaret, isImage } from "./Helper";
import Image from "./Image";
import { getFocusImage, setFocusImage } from "./ImageEditing";
import Line from "./line";
import Page from "./Page";
import Renderer from "./Renderer";
import Row from "./Row";
import Table from "./Table";
import Widget from "./Widget";
import EditorHelper from "./EditorHelper";
import Button from "./Button";
import PathUtils from "./Path";
import { DirectionType } from "./Definition";
import { ViewMode } from "./Constant";
import { isPage, isRow, isTable } from "./Utils";
import XField from "./XField";
import { FieldShowMode } from "./EditorConfig";
import Group from "./Groups";

export default class Caret {
  cache: number | null = null;

  show: boolean = false;

  x: number = 0;

  y: number = 0;

  height: number = 0;

  width: number = 1.5;

  preY: number = 0;

  preX: number = 0;

  /**
   * 获取光标前后的字符
   * @param editor Editor
   * @returns 哪个字符没有，哪个就是 undefined
   */
  static getCharacters(editor: Editor): (Character | Image | Widget | Line | Box | Button)[] {
    const focusPath = editor.selection.focus;
    const focusRowOrTable = editor.current_cell.children[focusPath[0]];
    const focusIndex = focusPath[focusPath.length - 1];
    let characters = focusRowOrTable.children as (Character | Image | Widget | Line | Box | Button)[];
    if (isTable(focusRowOrTable)) {
      characters = (focusRowOrTable.children[focusPath[1]].children[focusPath[2]] as Row).children;
    }
    return [characters[focusIndex - 1], characters[focusIndex]];
  }

  static drawVirtual(editor: Editor, x: number, y: number) {
    Renderer.save();
    Renderer.get().scale(editor.config.devicePixelRatio * editor.viewScale, editor.config.devicePixelRatio * editor.viewScale);
    Renderer.translate(editor.internal.view_scale_offset, -editor.scroll_top);
    const current_element = editor.getContainerInfoByPointHelper(x, y + editor.scroll_top);

    if (current_element) {
      const { view_path } = current_element;
      const row = current_element.row;
      const para_path = editor.modelPath2ParaPath(current_element.model_path);
      if (editor.view_mode === "form" && !editor.adminMode) {
        const current_field = editor.selection.getFieldByPath(para_path);
        if (!current_field || current_field.readonly) {
          const table = editor.current_cell.paragraph[para_path[0]];
          if (!(isTable(table) && table.editableInFormMode)) {
            // 因为在表格中并且表格是表单模式下可编辑的就得能拖
            // 所以取反
            editor.internal.drag_in_path = [];
            Renderer.restore();
            return;
          }
        }
      }
      editor.internal.drag_in_path = [...view_path];
      const absolute_path = editor.getElementAbsolutePositionByViewPath(view_path);
      Renderer.draw_vertical(absolute_path.x, absolute_path.y, absolute_path.y + row.height, "#000", "dotted");
    } else {
      editor.internal.drag_in_path = [];
    }
    Renderer.restore();
  }

  static drawFieldShadow(editor: Editor, x: number, y: number) {
    const info = editor.internal.field_shadow_xy
    info.endX = x
    if (x > info.maxX) {
      info.endX = info.maxX
    }
    Renderer.save();
    Renderer.get().scale(editor.config.devicePixelRatio * editor.viewScale, editor.config.devicePixelRatio * editor.viewScale);
    Renderer.translate(editor.internal.view_scale_offset, -editor.scroll_top);

    Renderer.draw_vertical(info.endX, 0, y + editor.scroll_top + 1200, "rgb(71,163,252)", "solid");
    const widthNum = Math.round(Math.abs(info.endX - info.x))
    let name = "最大宽度"
    if (info.type === "min") {
      name = "最小宽度"
    }
    Renderer.drawFieldSetWidth(name + " " + String(widthNum), y - 20, x + 20, x + 30, "#FD6300", 16)
    Renderer.restore();
  }
  static setFocusFieldDrawBgColorInfo(editor: Editor, field: XField | null) {
    if (editor.config.fieldShowMode === FieldShowMode.overShowBgColor ||
      editor.config.fieldShowMode === FieldShowMode.showBgColor ||
      editor.config.fieldShowMode === FieldShowMode.mergeSymbol) {
      if (field) {
        editor.internal.focusDrawStyleInfo = {
          fieldId: field.id,
          color: EditorHelper.getFocusOrOverFieldBgColor(editor, field, 0.1)
        };
      } else {
        editor.internal.focusDrawStyleInfo = null;
      }
    }
  }

  static update(editor: Editor) {
    const selection = editor.selection;

    if (!selection.isCollapsed) return;
    // 判断所有分组内是有否包含表单模式的分组，如果有模式置为normal
    Group.groupFormModeResetEditorViewMode(editor, editor.config.useGroupFormMode);
    let view_path = editor.modelPath2viewPath(selection.focus);
    const focus_field = selection.getFieldByPath(selection.para_focus);
    const focus_row = selection.getRowByPath(selection.focus);
    const focus_para = focus_row.paragraph;
    const page = editor.editFloatModelMode ? editor.pages[0] : editor.pages[view_path[0]];
    EditorHelper.setFocusElement(editor, focus_field, focus_row, page);
    this.setFocusFieldDrawBgColorInfo(editor, focus_field);
    editor.setFormReadonly(false);
    const group = editor.focusElement.group;
    if (editor.config.useGroupFormMode && group && group.is_form) {
      editor.view_mode = ViewMode.FORM;
    }
    // 如果当前模式为表单模式，并且当前光标不在文本域中，或者点击的是标签文本域，则隐藏光标展示
    if (!focus_field && (editor.view_mode === ViewMode.FORM && !editor.adminMode)) {
      if (Table.judgeIsEditableInFormMode(selection.focus, editor)) {
        if (editor.editFloatModelMode) {
          view_path.shift();
          editor.updateCaretByViewPath(view_path, editor.currentFloatModel?.children, editor.currentFloatModel!.originPosition[0], editor.currentFloatModel!.originPosition[1]);
        } else {
          editor.updateCaretByViewPath(view_path);
        }
        return;
      }
      // 分组表单模式文本域判断
      const all_fields = group ? group.getGroupFields() : editor.getAllFields(editor.current_cell);
      const field = all_fields.find(field => field.type !== "label");
      if (field) {
        const para_path = [...field.start_para_path];
        para_path[para_path.length - 1] = para_path[para_path.length - 1] + 1;
        const model_path = editor.paraPath2ModelPath(para_path);
        view_path = editor.modelPath2viewPath(model_path);
        selection.setCursorPosition(model_path);
      } else {
        if (editor.editFloatModelMode) {
          view_path.shift();
          editor.updateCaretByViewPath(view_path, editor.currentFloatModel?.children, editor.currentFloatModel!.originPosition[0], editor.currentFloatModel!.originPosition[1]);
        } else {
          editor.updateCaretByViewPath(view_path);
        }
        editor.setFormReadonly(true);
        editor.render();
        return;
      }
    }
    // 当点击文本域内的时候 如果文本域没有内容 将光标定位到开头
    if (focus_field && focus_field.children.length === 0) {
      // 设置hold_mouse为false,防止点击空文本域背景文本时变成设置选区
      // editor.hold_mouse = false;
      const para_path = selection.para_focus;
      const focus_field_start = focus_field.start_sym_char;
      // 确定段落中的坐标
      const start_sym_index = focus_para.characters.findIndex(
        (item) =>
          item.field_id === focus_field_start.field_id &&
          item.field_position === focus_field_start.field_position
      );
      para_path[para_path.length - 1] = start_sym_index + 1;
      const model_path = editor.paraPath2ModelPath(para_path);
      // 设置光标的真实坐标
      selection.setCursorPosition(model_path);
      // 绘制光标位置
      view_path = editor.modelPath2viewPath(model_path);
    }
    if (focus_field && focus_field.type === "label") {
      // editor.hold_mouse = false;
      // 如果是label，设置选区为整个文本域
      const end_para_path = [...focus_field.end_para_path];
      end_para_path[end_para_path.length - 1] = end_para_path[end_para_path.length - 1] + 1;
      // 设置选区样式
      selection.setSelectionByPath(focus_field.start_para_path, end_para_path);
    }
    if (editor.editFloatModelMode) { // 目前测试 点击走的是这儿
      view_path.shift();
      editor.updateCaretByViewPath(view_path, editor.currentFloatModel?.children, editor.currentFloatModel!.originPosition[0], editor.currentFloatModel!.originPosition[1]);
    } else {
      editor.updateCaretByViewPath(view_path);
    }
    const model_path = selection.focus;
    if (
      focus_row.children.length === model_path[model_path.length - 1] &&
      focus_row.paragraph.align === "right"
    ) {
      if (focus_row.parent.parent) {
        editor.caret.preX = editor.caret.x;
        editor.caret.x =
          editor.pages[0].left +
          editor.config.page_padding_left +
          focus_row.width +
          focus_row.parent.left +
          editor.config.table_padding_horizontal;
      } else {
        editor.caret.preX = editor.caret.x;
        editor.caret.x =
          editor.pages[0].left +
          editor.config.page_padding_left +
          focus_row.width +
          focus_row.parent.left;
      }
    }
    if (
      focus_row.children.length === 0 &&
      focus_row.paragraph.align === "center"
    ) {
      editor.caret.preX = editor.caret.x;
      editor.caret.x =
        editor.pages[0].left +
        editor.config.page_padding_left +
        focus_row.width / 2 +
        focus_row.parent.left;
    }
    editor.event.emit("updateSide")
  }

  static updateByViewPath(
    editor: Editor,
    view_path: number[],
    containers: (
      | Page
      | Cell
      | Table
      | Row
      | Character
      | Image
      | Widget
      | Box
    )[],
    x: number = 0,
    y: number = 0
  ) {
    // page_page.length小于3,说明聚焦的元素不是row,不需要更新caret
    const path = view_path;
    const offset = path.shift()!;
    if (path.length > 0) {
      let current_container: any = containers[offset] as Row;

      let children: any = current_container.children;
      if (
        isPage(current_container) &&
        editor.internal.current_page &&
        editor.current_cell.hf_part
      ) {
        current_container = editor.internal.current_page;
        const part = (editor.current_cell.hf_part + "_cell") as
          | "header_cell"
          | "footer_cell";
        children = editor[part].children;
      }
      // current_container 只能为row
      if (path.length === 1) {
        const is_image = current_container.children.findIndex((e: any) => isImage(e));
        if (is_image !== -1) {
          editor.caret.height = current_container.height - Config.img_margin * 2;
          editor.caret.preY = editor.caret.y; // 这个赋值在移动端使用 不能放到最外层 这三个赋值不能抽出来放到最外层 注意
          editor.caret.y = y + current_container.bottom - editor.caret.height - Config.img_margin;
        } else {
          editor.caret.height = current_container.height / current_container.paragraph.row_ratio;
          editor.caret.preY = editor.caret.y;
          editor.caret.y = y + current_container.bottom - editor.caret.height - current_container.padding_vertical; // TODO 不应该减去一半的 padding_vertical 吗
          // 如果是滚动单元格，则减去滚动位置
          if (current_container.parent.set_cell_height.type === "scroll") {
            editor.caret.preY = editor.caret.y;
            editor.caret.y = y + current_container.bottom - editor.caret.height - current_container.padding_vertical - current_container.parent.scroll_cell_top;
          }
        }
        // 设置上下文状态
        contextStateSetByCaret(editor, path, offset, containers as Row[], current_container);
      }

      // 计算光标坐标，遇到listRow的时候，对应的是有这个padding_left的，需要加上
      if (
        isRow(current_container) &&
        current_container.children.length === 0
      ) {
        x += current_container.left + current_container.padding_left;
      } else {
        x += current_container.left;
      }

      y += current_container.top;

      editor.updateCaretByViewPath(path, children, x, y);
    } else { // TODO 这里所有 y += xxx 感觉都是多余的 因为进入了 这个 else 就不会再递归了 也没有给 caret.y 赋值的地方了 所以应该注释掉
      // 当前containers是char数组
      // let height: number;
      let current_container;

      if (containers[offset]) {
        current_container = containers[offset];

        x += current_container.left;

        // height = current_container.height;

        // y += current_container.bottom - height; // TODO 看过所有内容的 bottom 都是 top + height 这里 就直接使用 top 值应该就可以
      } else {
        // containers 长度为空，
        if (containers.length) {
          current_container = containers[containers.length - 1];
          x += current_container.left + current_container.width;

          // height = current_container.height;

          // y += current_container.bottom - height;
        }
      }
      editor.caret.preX = editor.caret.x;
      editor.caret.x = x;
    }
  }

  static move(editor: Editor, direction: DirectionType) {
    if (editor.selection.isCollapsed) {
      if (direction === "left") {
        editor.caret.cache = null;
        editor.selection.stepBackward(
          1,
          editor.selection.getFocusRow()
        );
      } else if (direction === "right") {
        editor.caret.cache = null;
        const focusImage = getFocusImage();
        if (focusImage === null) {
          editor.selection.stepForward(
            1,
            editor.selection.getFocusRow()
          );
        }
      } else {
        // 上下方向键
        editor.caret.cache = null;
        editor.selection.stepVertical(direction, editor.caret);
      }
      const focus_image = getFocusImage();
      if (focus_image) {
        setFocusImage(null);
      }
    } else {
      editor.selection.stepWhenSelected(direction, editor.caret);
    }
    // 判断移动完成是否移至到了文本域中，如果文本域时判断是否为空文本域
    editor.selection.caretMoveAboutField(direction);
    // 以下为表单模式使用
    if (editor.view_mode === "form" && !editor.adminMode) {
      const model_path = [...editor.selection.focus];
      const table = editor.current_cell.children[model_path[0]];
      if (!(isTable(table) && table.editableInFormMode)) {
        direction = editor.selection.caretMoveAboutFormView(direction, model_path);
        if (direction) {
          if (editor.isUseCurrentLogic()) {
            if (this.forbiddenCaretMoveInGroupForm(editor)) return
          }
          return editor.caret_move(direction);
        }
      }
    }
    // 分组表单开启后不允许通过光标移动跳分组
    this.forbiddenCaretMoveInGroupForm(editor)
    editor.updateCaret();
    editor.scroll_by_focus();
    editor.render();
  }

  static forbiddenCaretMoveInGroupForm(editor: Editor) {
    if (editor.config.useGroupFormMode) {
      if (editor.focusElement.group) {
        const para_path = editor.selection.para_focus;
        const para = editor.selection.getParagraphByPath(para_path);
        let group_id = para.group_id;
        if (PathUtils.isTablePath(para_path)) {
          group_id = para.cell.parent!.group_id;
        }
        if (editor.focusElement.group.id !== group_id) {
          const firstPara = editor.focusElement.group.paragraph[0];
          let resPath;
          if (isTable(firstPara)) {
            resPath = PathUtils.getStartPathByTable(firstPara);
          } else {
            resPath = PathUtils.getStartPathByRow(firstPara.children[0]);
          }
          if (editor.focusElement.group.is_form && editor.isUseCurrentLogic()) {
            const resParaPath = editor.modelPath2ParaPath(resPath)
            const field = editor.selection.getFieldByPath(resParaPath)
            if (!field) {
              const fields = editor.focusElement.group.getGroupFields()
              if (fields.length) {
                const firstField = fields.find(field => field.type === "normal");
                if (firstField) {
                  const firstFieldStartModelPath = editor.paraPath2ModelPath(firstField.start_para_path_inner)
                  editor.selection.setCursorPosition(firstFieldStartModelPath);
                  return true
                }
              }
            }

          }
          editor.selection.setCursorPosition(resPath);
          return true
        }
      }
    }

  }

  draw(cell?: Cell) {
    if (cell) {
      Renderer.save();
      Renderer.get().fillStyle = "red";
      const real_top = cell.realTop;
      if (this.y <= real_top && this.y + this.height >= real_top) {
        Renderer.get().rect(this.x, real_top, cell.width, this.y + this.height - real_top);
        Renderer.get().clip();
      } else if (this.y <= real_top + cell.height && this.y + this.height >= real_top + cell.height) {
        Renderer.get().rect(this.x, this.y, cell.width, real_top + cell.height - this.y);
        Renderer.get().clip();
      }
      Renderer.draw_rectangle(this.x, this.y, this.width, this.height, "#000000");
      Renderer.restore();
    } else {
      Renderer.draw_rectangle(this.x, this.y, this.width, this.height, "#000000");
    }
  }

  // 按行高渲染
  _draw(left: number, bottom: number, line_height: number, line_ratio: number) {
    const padding = (line_height * (line_ratio - 1)) / 2;
    Renderer.draw_rectangle(
      left,
      bottom - line_height - padding,
      this.width,
      line_height - padding - padding,
      "#000000"
    );
  }
}
