# msun-editor

> 一款专用于病历书写的编辑器

### 启动流程

- 切换npm源
```
npm config set registry http://nexus.chis.msunsoft.com:8081/repository/npm-group/
新的(或者不执行以下命令直接将host文件中nexus.chis.msunsoft.com映射地址更改为**************)：
npm config set registry http://**************:8081/repository/npm-group/
```

- 安装依赖
```
npm install
```

- 启动项目
```
npm start
```

### 发布流程

- 升级版本
```
// dev分支
npm version patch // 升级末尾版本号，用于补丁修复。
npm version minor // 升级中间版本号，用于功能新增
npm version major // 升级头部版本号，用于无法向下兼容的升级调整
```
- 将dev分支合并到master并将master分支推送到远程
- gitlab-ci自动化部署自动完成打包发版流程

### IE兼容处理
- 使用babel兼容IE的方法  请查看分支 mobile-compatible-with-ie
https://note.youdao.com/s/LcuNTG6D  
