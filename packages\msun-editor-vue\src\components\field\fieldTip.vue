<template>
  <div class="editor_field_tip" v-show="fieldTipMsg">
    <!-- <strong class="editor_tip_title_field_tip"> {{ fieldTipTitle }} </strong>-->
    <span
      :style="
        'color:' +
        (fieldTipTitle.indexOf('错误') > -1 ? 'red' : 'black') +
        ';white-space: pre-wrap;'
      "
      >{{ fieldTipMsg }}</span
    >
  </div>
</template>

<script>
export default {
  name: "fieldTip",
  props: {
    fieldTipMsg: { type: String, default: "" },
    fieldTipTitle: { type: String, default: "提示" },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.editor_field_tip {
  position: absolute;
  color: #000;
  z-index: 9999;
  background-color: #fff;
  box-shadow: 2px 2px 4px 3px rgba(208, 208, 208, 0.6);
  border-radius: 4px;
  padding: 5px 5px 5px 10px;
  min-width: 150px;
  .editor_tip_title_field_tip {
    line-height: 1.5 !important;
    color: black;
    display: block;
  }
}
</style>
