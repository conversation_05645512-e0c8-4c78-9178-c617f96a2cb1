<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="title"
    @cancel="cancel"
    @submit="submit"
  >
    <div style="display: flex; flex-wrap: wrap">
      <div style="display: flex; margin: 10px; line-height: 32px">
        <div style="width: 52px">名称:</div>
        <a-input style="width: 150px" v-model="markImageName"></a-input>
      </div>
      <div style="display: flex; margin: 10px; line-height: 32px">
        <div style="width: 52px">宽度:</div>
        <a-input-number
          style="width: 150px"
          v-model="markImageWidth"
          :min="0"
        ></a-input-number>
      </div>

      <div style="display: flex; margin: 10px; line-height: 32px">
        <div style="width: 52px">层级:</div>
        <a-select
          v-model="markImageLevel"
          style="width: 150px"
          dropdownClassName="xeditor-input-up"
          :defaultValue="1"
        >
          <a-select-option :value="1">文字下方</a-select-option>
          <a-select-option :value="2">页眉上方正文下方</a-select-option>
          <a-select-option :value="3">文字上方</a-select-option>
        </a-select>
      </div>
      <div style="display: flex; margin: 10px; line-height: 32px">
        <div style="width: 52px">高度:</div>
        <a-input-number
          style="width: 150px"
          v-model="markImageHeight"
          :min="0"
        ></a-input-number>
      </div>
      <div style="display: flex">
        <div style="margin: 10px; line-height: 32px">全部/单页</div>
        <a-switch
          style="margin-right: 20px; margin-top: 16px"
          v-model="single"
          @change="onChange"
        />
      </div>
      <div style="display: flex; margin: 10px">
        <div style="line-height: 32px; margin-left: 75px">关联边距</div>
        <a-switch
          style="margin-left: 10px; margin-top: 6px"
          v-model="padding"
          @change="onChangePadding"
        />
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../components/common/modal.vue";
export default {
  name: "customWaterMark",
  data() {
    return {
      modal_width: 524,
      title: "自定义水印图片",
      markImageName: "",
      markImageWidth: 300,
      markImageHeight: 150,
      markImageLevel: 1,
      mode: "repeat",
      padding: false,
      changeMark: false,
      single: false,
    };
  },
  components: {
    modal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          const mark = this.editor.editor.internal.focusMark;
          if (mark) {
            this.markImageName = mark.name ? mark.name : "";
            this.markImageWidth = mark.width ? mark.width : 300;
            this.markImageHeight = mark.height ? mark.height : 150;
            this.markImageLevel = mark.params.level ? mark.params.level : 1;
            this.mode = mark.mode ? mark.mode : "repeat";
            this.padding = mark.params.padding ? !!mark.params.padding : false;
            this.changeMark = true;
          } else {
            this.markImageName = "";
            this.markImageWidth = 300;
            this.markImageHeight = 150;
            this.markImageLevel = 1;
            this.mode = "repeat";
            this.padding = false;
            this.changeMark = false;
          }
          this.single = this.mode === "repeat" ? false : true;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {
    submit() {
      const param = {
        name: this.markImageName,
        width: this.markImageWidth,
        height: this.markImageHeight,
        mode: this.mode,
        padding: this.padding,
        level: this.markImageLevel,
      };
      this.$emit("submit", param, this.changeMark);
    },
    cancel() {
      this.$emit("cancel");
    },
    onChange(checked) {
      if (checked) {
        this.mode = "single";
      } else {
        this.mode = "repeat";
      }
    },
    onChangePadding(checked) {
      if (checked) {
        this.padding = true;
      } else {
        this.padding = false;
      }
    },
  },
};
</script>

<style scoped></style>
