// 分数
// 就相当于 character 按左右方向键会一次就跳过该元素
// 只能通过鼠标点击的时候将光标放到分子上或者分母上
// 字体大小就按照 font 的字体大小 算一个比例 可以跟其他字体一样设置大小

import Character from "./Character";
import { editor_prompt_msg } from "./Config";
import Editor from "./Editor";
import Font from "./Font";
import { insertTextAfterCursorSet } from "./Helper";
import Renderer from "./Renderer";
import XSelection from "./Selection";
import Table from "./Table";

// TODO 光标怎么弄？选区 复制 粘贴
export default class Fraction extends Character {
    int: string = ""; // 整数
    numerator: string = ""; // 分子
    denominator: string = ""; // 分母
    field_id: string | null = null;
    width: number = 0;
    type: string = "fraction";
    editor: Editor;
    get height(): number {
      return this.font.height + 4; // 如果是原来的就会距离上边行比较远
    }

    intWidth: number = 0;
    numeratorWidth: number = 0;
    denominatorWidth: number = 0;
    
    constructor(editor: Editor, int: string, numerator: string, denominator: string, font: Font) {
      super(font, `${int}#${numerator}#${denominator}`)
      this.editor = editor;
      this.int = int + "";
      this.numerator = numerator + "";
      this.denominator = denominator + "";
      this.setWidth(int, numerator, denominator, font);
    }

    getWidth(int: string, numerator: string, denominator: string, font: Font): {intWidth: number, numeratorWidth: number, denominatorWidth: number, totalWidth: number} {
      let totalWidth = 0;
      let intWidth = 0;
      if (int) {
        const { width } = Renderer.measure(font, int + "");
        intWidth = width;
        totalWidth += width;
      }
      const { width: numeratorWidth } = Renderer.measure(font, numerator + "");
      const { width: denominatorWidth } = Renderer.measure(font, denominator + "");
      totalWidth += Math.max(numeratorWidth, denominatorWidth);
      return {
        intWidth,
        numeratorWidth,
        denominatorWidth,
        totalWidth
      }
    }

    setWidth(int: string, numerator: string, denominator: string, font: Font) {
      const { intWidth, numeratorWidth, denominatorWidth, totalWidth } = this.getWidth(int, numerator, denominator, font);
      this.intWidth = intWidth;
      this.numeratorWidth = numeratorWidth;
      this.denominatorWidth = denominatorWidth;
      this.width = totalWidth;
      this.ori_width = totalWidth;
    }

    /**
       * 该该字符设置新的样式
       * @param newFontStyle 要设置的新的 Font
       * @param editor Editor
       */
    setFont(newFontStyle: Font, editor: Editor) {
      this.setWidth(this.int, this.numerator, this.denominator, newFontStyle);
      this.font = newFontStyle;
    }

    draw(baseline: number, row_height: number, line_padding: number, isEndChar: boolean = false) {
      const render = Renderer.get();

      render.fillStyle =
        this.font.temp_valid_color || this.font.temp_word_color || this.font.color;
      render.strokeStyle = render.fillStyle;
      render.font = this.font.getCss(this.editor, true);
      render.globalAlpha = this.transparent;
      
      let left = this.left;
      if (this.int) {
        render.fillText(this.int + "", left, baseline - line_padding, this.intWidth)
        left += this.intWidth;
      }

      const max =  Math.max(this.numeratorWidth, this.denominatorWidth);
      const min = Math.min(this.numeratorWidth, this.denominatorWidth);
      
      render.beginPath()
      render.moveTo(left, baseline - line_padding - this.font.height / 2);
      render.lineTo(left + max, baseline - line_padding - this.font.height / 2);
      
      render.moveTo(left, baseline - line_padding - this.font.height / 2);
      const numeratorStartX = left + (max === this.numeratorWidth ? 0 : (max - min) / 2);
      const numeratorStartY = baseline - line_padding - this.font.height / 2 - 2; // 最后 -2 是要有一点距离 要不不好看
      render.fillText(this.numerator + "", numeratorStartX, numeratorStartY); 
      const denominatorStartX = left + (max === this.denominatorWidth ? 0 : (max - min) / 2);
      const denominatorStartY = baseline - line_padding + this.font.height / 2; // 这里不减 2 了 要不然下边有点超出表格线了
      render.fillText(this.denominator + "", denominatorStartX, denominatorStartY); 
      render.stroke();
      render.closePath();
    }

    copy() {
      return new Fraction(this.editor, this.int, this.numerator, this.denominator, this.font);
    }

    update(parameter: { int?: string, numerator?: string, denominator?: string }) {
      const { int, numerator, denominator } = parameter;
      if (int !== undefined && int !== "undefined") {
        this.int = int + "";
      }
      if (numerator !== undefined) {
        this.numerator = numerator + "";
      }
      if (denominator !== undefined) {
        this.denominator = denominator + "";
      }
      this.setWidth(this.int, this.numerator, this.denominator, this.font);
      const paragraph = this.editor.selection.getFocusParagraph();
      paragraph.updateChildren();
      this.editor.refreshDocument();
    }

    static insert(parameter: {editor: Editor, int: string, numerator: string, denominator: string}) {
      const { editor } = parameter;
      // 以下代码跟 insertText 中是完全重复的↓
      // 可以考虑把这里的判断条件放入到 allowEditing 里边，allowEditing 在调用该方法之前也调用了, 问题是下边使用了 focus_field 挪过去就有点重复
      // 光标所在处是否为 文本域
      if (!XSelection.deleteAndControlRecord(editor)) return false;
      const focusField = editor.selection.getFocusField();
      // 如果是文本域且文本域为只读时停止或者不是文本域中且为表单模式时停止
      if (
        ((focusField && focusField.isReadonly) || focusField?.inputMode) &&
            !editor.adminMode
      ) {
        editor.event.emit("message", editor_prompt_msg.readonly_field);
        return false;
      }
      // 只有表单模式下 单元格不能编辑的时候 才要 return 出去
      if (
        !focusField &&
            editor.view_mode === "form" &&
            !editor.adminMode &&
            !Table.judgeIsEditableInFormMode(editor.selection.focus, editor)
      ) {
        return false;
      }
      
      editor.caret.cache = null;
      // 以下代码跟 insertText 中是完全重复的↑
      
      let start_row = null;
      let focus_row = null;
      focus_row = start_row = editor.selection.getFocusRow();

      const currentParagraph = editor.selection.getFocusParagraph();
      const path = editor.selection.para_focus
      const index = path[path.length - 1];
      currentParagraph.insertFraction({ ...parameter, focusField,  index });
      insertTextAfterCursorSet(editor, " ", currentParagraph, path);

      editor.update(...editor.getUpdateParamsByContainer(start_row));
      editor.scroll_by_focus();
      editor.render();
      return true;
    }
}