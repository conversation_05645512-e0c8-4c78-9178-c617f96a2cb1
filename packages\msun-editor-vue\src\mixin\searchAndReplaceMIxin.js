/**
 * 查找和替换
 * @type {{data(): {}, method: {}}}
 */
import searchAndReplace from "../components/searchAndReplace.vue";
const searchAndReplaceMixin = {
  data() {
    return {
      search: "",
      showTipModal: false,
      resultData: null,
      font_modal_width: 400,
      showSearchAndReplace: false,
      showCurrentGroupReplaceAll: false,
    };
  },
  components: {
    searchAndReplace,
  },
  methods: {
    searchAndReplaceCancel() {
      this.showSearchAndReplace = false;
      this.showCurrentGroupReplaceAll = false;
      this.editor.cancelSearch();
      this.instance.editor.focus();
    },
    searchAndReplace() {
      this.showSearchAndReplace = true;
      const group = this.editor.selection.getFocusGroup();
      if (group) {
        this.showCurrentGroupReplaceAll = true;
      }
      let { all_chars } = this.editor.selection.selected_fields_chars;
      let list = [];
      if (all_chars) {
        all_chars.forEach((e) => {
          list.push(e.value);
        });
      }
      this.search = list.join("");
    },
    changeCaseSensitive() {
      this.editor.caseSensitive();
    },
    replaceOne(e, m) {
      if (e !== "") {
        const next_one = this.editor.replaceFont(e, m);
        if (next_one && next_one.data) {
          this.showTipModal = true;
          this.resultData = next_one.data;
        }
      }
    },
    replaceAll(e, m) {
      if (e !== "") {
        const replace_all = this.editor.replaceAllFont(e, m);
        if (replace_all && replace_all.data) {
          this.showTipModal = true;
          this.resultData = replace_all.data;
        }
      }
    },
    // 当前分组内全部替换
    currentGroupReplaceAll(oldData, newData) {
      const group = this.editor.selection.getFocusGroup();
      if (oldData !== "" && group) {
        const replace_all = this.editor.replaceAllFont(oldData, newData, group);
        if (replace_all && replace_all.data) {
          this.showTipModal = true;
          this.resultData = replace_all.data;
        }
      }
    },
    searchAll(e) {
      if (e !== "") {
        const search_all = this.editor.searchAll(e);
        const data = "已完成" + search_all + "处查找";
        this.showTipModal = true;
        this.resultData = data;
      }
    },
    searchNextOne(e) {
      if (e !== "") {
        const search_next = this.editor.searchNext(e);
        if (search_next && search_next.data) {
          this.showTipModal = true;
          this.resultData = search_next.data;
        }
      }
    },
    closeTipModal() {
      this.showTipModal = false;
      this.instance.editor.focus();
    },
  },
};
export default searchAndReplaceMixin;
