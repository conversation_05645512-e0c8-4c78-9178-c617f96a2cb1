import Font from "./Font";
import ParaStyle, { ParagraphStyle } from "./ParagraphStyle";
import { uuid } from "./Utils";
import Editor from "./Editor";
import PathUtils from "./Path";
import { fontHeightAsc } from "./Config";

export const default_para_style: ParaStyle = {
  align: "left",
  before_paragraph_spacing: 0, // 段落前的间距 表示1倍的行高
  after_paragraph_spacing: 0, // 段落后的间距 表示1倍的行高
  indentation: 0, // 首行缩进
  row_ratio: 1.4, // 行间距
  isOrder: false,
  islist: false,
  level: 0
};

export default class ContextState {
  editor : Editor;

  font : Font = new Font({ family: "", height: 0 });

  pathFont: any = []; // 下标0存储坐标，下标1存储字体。 用于解决在光标处设置字体样式后再次点击光标位置，设置的字体样式失效问题

  paraStyle : ParaStyle = default_para_style;

  constructor (editor:Editor) {
    this.editor = editor;
  }

  /**
   * 设置路径字体，临时存储设置的字体样式
   * @param font
   */
  setPathFont (font : Font) {
    const path = [...this.editor.selection.focus];
    this.pathFont[0] = path;
    this.pathFont[1] = font;
  }

  getState () {
    return this;
  }

  setFontState (font: Font) {
    if(font.height <= 0){
      return;
    }
    // 如果在光标处设置了字体样式，则设置上下文样式时判断该坐标处是否已经设置过了，如果设置过了则获取之前设置的样式
    if (this.pathFont.length && PathUtils.equals(this.pathFont[0], this.editor.selection.focus)) {
      this.font = this.pathFont[1];
    } else {
      this.pathFont = [];
      this.font = font;
    }
  }

  getFontState () {
    return this.font;
  }

  /**
   * 重置字体样式
   */
  resetFontState (defaultVal = this.editor.config.default_font_style) {
    // 解决30迭代上的样式的bug(先打一个字，再调用 reInitRaw 加载数据，再打字，再撤销，所有字体样式就跟光标位置处一样了) 加这两行重置
    // rawDataTrans.newFontArray = []; // 因为打一个字 newFontArray 里边就有样式了 reInitRaw 之后再打字，newFontArray 中已经有样式了，返回了已有的id，但是 FontMap 中没有该 id 对应的样式，就用了 contentstate 的值
    // rawDataTrans.newFontMap = {};
    this.editor.event.emit("clearDataTransTempInfo");
    // 此处必须使用FontMap.add,初始化默认字体用
    this.font = this.editor.fontMap.add(defaultVal, uuid("font-default"));
  }

  getParagraphState () {
    return this.paraStyle;
  }

  setParagraphState (paraStyle: ParagraphStyle) {
    this.paraStyle = paraStyle;
  }

  setCharacterSize(type:"bigger"|"smaller"){
    const originHeight = this.font.height;
    const originHeightIndex = fontHeightAsc.findIndex(fontHeight => fontHeight === originHeight);
    let newHeight = originHeight;
    if (type === "bigger") {
      newHeight = fontHeightAsc[originHeightIndex + 1];
    } else if (type === "smaller") {
      newHeight = fontHeightAsc[originHeightIndex - 1];
    }
    this.editor.change_font_style({ height: newHeight });
  }
}
