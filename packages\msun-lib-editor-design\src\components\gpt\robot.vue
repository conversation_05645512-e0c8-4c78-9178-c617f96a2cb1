<template>
  <div
    class="robot-dik"
    :style="{
      width: size + 'px',
      height: size + 'px',
    }"
  >
    <svg
      version="1.0"
      :width="size"
      :height="size"
      @mouseenter="mouseenter"
      @mouseleave="mouseleave"
    >
      <defs>
        <radialGradient
          id="faceRadial"
          cx="0.5"
          cy="0.3"
          r="0.45"
          fx="0.5"
          fy="0.5"
        >
          <stop stop-color="white" offset="0%" />
          <stop stop-color="#f0f0f0" offset="100%" />
        </radialGradient>
      </defs>
      <g
        :transform="'scale(' + size / 100 + ')'"
        transform-origin="0 0"
        width="100"
        height="100"
      >
        <circle
          cx="50"
          cy="50"
          r="50"
          fill="url(#faceRadial)"
          stroke="#999"
          stroke-width="1"
        />
        <path
          d="M 50 70 A 40 30 0 1 1 51 70 Z"
          fill="#151b29"
          stroke="#000000"
        />
        <path
          :d="
            'M 30 ' + eyePos + ' A 10 ' + eyeSize + ' 0 1 1 31 ' + eyePos + ' Z'
          "
          fill="#34a853"
        />
        <path
          :d="
            'M 70 ' + eyePos + ' A 10 ' + eyeSize + ' 0 1 1 71 ' + eyePos + ' Z'
          "
          fill="#34a853"
        />
        <rect x="40" y="82" width="20" height="6" fill="#34a853" />
        <rect x="47" y="76" width="6" height="20" fill="#34a853" />
        <path
          :d="'M 43 52 A 7 ' + smile + ' 0 1 0 57 52'"
          stroke="#34a853"
          stroke-width="4"
          fill="transparent"
        />
      </g>
    </svg>
  </div>
</template>

<script>
export default {
  name: "GptRobot",
  components: {},
  props: {
    size: {
      type: Number,
      default: 100,
    },
  },
  data() {
    return {
      eyeSize: 10,
      smile: 1,
      animate: null, // 动作
      loop: null,
    };
  },
  computed: {
    eyePos() {
      return this.eyeSize / 2 + 40;
    },
  },
  mounted() {
    this.looping();
  },
  methods: {
    mouseenter() {
      this.clearAll();
      window.clearTimeout(this.loop);
      this.smiling();
    },
    mouseleave() {
      this.clearAll();
      this.looping();
    },
    amazing() {
      // 惊喜
      this.eyeSize = 12;
      this.smile = 5;
      this.animate = setTimeout(() => {
        this.eyeSize = 8;
        this.smile = 1;
        this.animate = setTimeout(this.amazing, 4000);
      }, 2000);
    },
    blink() {
      // 眨眼
      this.eyeSize = 1;
      this.animate = setTimeout(() => {
        this.eyeSize = 12;
        this.animate = setTimeout(this.blink, 3000);
      }, 300);
    },
    smiling() {
      // 保持微笑
      this.eyeSize = 2;
      this.smile = 5;
    },
    clearAll() {
      this.eyeSize = 10;
      this.smile = 1;
      window.clearTimeout(this.animate);
    },
    looping() {
      this.clearAll();
      const animates = ["smiling", "blink", "amazing"];
      const index = Math.floor(Math.random() * animates.length);
      this[animates[index]]();
      this.animate = setTimeout(() => {
        this.clearAll();
      }, 2000);
      this.loop = setTimeout(this.looping, 10000);
    },
  },
};
</script>

<style lang="less" scoped>
// 脸颜色  151b29   十字颜色 34a853
.robot-dik {
  display: inline-block;
  vertical-align: middle;
  overflow: hidden;
}
</style>
