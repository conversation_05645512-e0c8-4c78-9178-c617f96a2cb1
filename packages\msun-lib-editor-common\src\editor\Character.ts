import Font from "./Font";
import { keepDecimal } from "./Utils";
import Renderer from "./Renderer";
import Editor from "./Editor";

export default class Character {
  temp_bgColor: string | undefined;

  left: number = 0;

  top: number = 0;

  width: number;

  font: Font;

  field_id: string | null = null;

  comment_id: string | undefined;

  cusCommentId: string | undefined;

  field_position: string = "normal";

  draw_width: number; // 页面渲染的背景色宽度

  transparent: number = 1;

  value: string;

  ori_width: number;// 原始宽度

  needAsterisk: boolean = false;

  mark: unknown; // 该字符上的标记 由其他团队自定义 目前是 pacs 用 改变过字体字号的字符 在缩放的时候就过滤掉

  type: string = "character";

  isShowAsterisk(editor: Editor) {
    if (!this.needAsterisk || !editor.config.fieldAsteriskNames || !editor.config.fieldAsteriskNames.length) {
      return false;
    } else {
      return true;
    }
  }

  constructor(font: Font, value: string, custom_width?: number) {
    if (custom_width !== undefined) {
      this.width = custom_width;

      this.draw_width = custom_width;
    } else {

      const { width } = Renderer.measure(font, value);
      this.width = width;
      this.draw_width = width;
    }
    this.ori_width = this.width;
    this.font = font;
    this.value = value;
  }

  get height() {
    return this.font.height;
  }

  get right(): number {
    return keepDecimal(this.left + this.width);
  }

  get bottom(): number {
    return keepDecimal(this.top + this.height);
  }

  /**
   * 该该字符设置新的样式
   * @param newFontStyle 要设置的新的 Font
   * @param editor Editor
   */
  setFont(newFontStyle: Font, editor: Editor) {
    let { width } = Renderer.measure(newFontStyle, this.value, editor);
    this.width = width;
    this.ori_width = width;
    this.font = newFontStyle;
  }

  center(): number {
    return this.left + this.width * 0.5;
  }

  contain_vertical(y: number) {
    return this.top <= y && y <= this.bottom;
  }

  contain_horizontal(x: number, offsetX: number) {
    return this.left <= x && x <= this.left + this.draw_width + offsetX;
  }

  contain(x: number, y: number, offsetX: number) {
    return this.contain_horizontal(x, offsetX) && this.contain_vertical(y);
  }

  copy(): Character {
    if (this.type === "fraction") {
      return this.copy();
    } else {
      const char = new Character(this.font, this.value, this.ori_width);
      char.field_id = this.field_id;
      char.field_position = this.field_position;
      char.transparent = this.transparent;
      return char;
    }
  }
}
