import Vue from "vue";
import vcolorpicker from "vcolorpicker";
import "./styles/modalStyle.less";
import "ant-design-vue/dist/antd.css";

// gpt m-icon 使用
// import * as iconFontJs from "./components/gpt/assets/js/iconfont.js";
// const MIcon = Icon.createFromIconfontCN({
//   scriptUrl: iconFontJs,
// });

async function loadVantComponents() {
  const [
    { default: Popup },
    { default: DatetimePicker },
    { default: ActionSheet },
  ] = await Promise.all([
    import("vant/lib/popup"),
    import("vant/lib/datetime-picker"),
    import("vant/lib/action-sheet"),
    import("vant/lib/popup/style"),
    import("vant/lib/datetime-picker/style"),
    import("vant/lib/action-sheet/style"),
  ]);
  Vue.use(Popup);
  Vue.use(DatetimePicker);
  Vue.use(ActionSheet);
}

if (
  /Mobi/.test(navigator.userAgent) ||
  /Android/i.test(navigator.userAgent) ||
  /iPhone|iPad|iPod/i.test(navigator.userAgent)
) {
  loadVantComponents().catch((error) => {
    console.error("组件加载失败：", error);
  });
}

// TODO 样式暂时注掉，其他产品会引
// import "ant-design-vue/dist/antd";
import {
  message,
  Button,
  ConfigProvider,
  DatePicker,
  Input,
  Checkbox,
  Select,
  Menu,
  Divider,
  Modal,
  InputNumber,
  Row,
  Col,
  Radio,
  Alert,
  Table,
  Popconfirm,
  Tooltip,
  Icon,
  Card,
  TimePicker,
  Popover,
  Tabs,
  Dropdown,
  Form,
  Switch,
} from "ant-design-vue";

// gpt m-icon 使用
// Vue.component("MIcon", MIcon);
Vue.component(ConfigProvider.name, ConfigProvider);
Vue.component(DatePicker.name, DatePicker);
Vue.component(TimePicker.name, TimePicker);
Vue.component(Input.name, Input);
Vue.component(Input.TextArea.name, Input.TextArea);
Vue.component(Checkbox.name, Checkbox);
Vue.component(Checkbox.Group.name, Checkbox.Group);
Vue.component(Select.name, Select);
Vue.component(Select.Option.name, Select.Option);
Vue.component(Button.name, Button);
Vue.component(Radio.name, Radio);
Vue.component(Radio.Group.name, Radio.Group);
Vue.component(Menu.name, Menu);
Vue.component(Menu.Item.name, Menu.Item);
Vue.component(Menu.SubMenu.name, Menu.SubMenu);
Vue.component(Divider.name, Divider);
Vue.component(InputNumber.name, InputNumber);
Vue.component(Row.name, Row);
Vue.component(Col.name, Col);
Vue.component(Alert.name, Alert);
Vue.component(Table.name, Table);
Vue.component(Popconfirm.name, Popconfirm);
Vue.component(Icon.name, Icon);
Vue.use(Modal);
Vue.use(Tooltip);
Vue.use(vcolorpicker);
Vue.use(Card);
Vue.use(Popover);
Vue.use(Tabs);
Vue.use(Dropdown);
Vue.use(Form);
Vue.use(Switch);
Vue.config.productionTip = false;
// Vue.prototype.$error = Modal.error;
message.config({
  maxCount: 1,
});
Vue.prototype.$editor = message;
