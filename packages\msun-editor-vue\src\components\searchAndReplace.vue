<template>
  <modal
    :show="visible"
    :width="modal_width"
    :title="title"
    @cancel="cancel"
    class="replace_search_and_replace"
  >
    <div slot="title" class="title_search_and_replace">
      <div style="font-size: 14px">{{ title }}</div>
    </div>
    <tips-modal
      :show="showTipModal"
      :datas="resultData"
      @tipCancel="closeModal"
    ></tips-modal>
    <div class="replaceContentClass">
      <div class="search-content">
        <div class="top-text">查找内容：</div>
        <a-input
          type="text"
          @blur="searchData = $event.target.value"
          :value="searchData"
        ></a-input>
      </div>
      <div class="replace-content">
        <div class="top-text">替换为:</div>
        <a-input
          type="text"
          :value="replace"
          @blur="replace = $event.target.value"
        ></a-input>
      </div>
      <div class="condition">
        <div
          class="click-checkbox"
          @click="changeCaseSensitive"
          onselectstart="return false"
        >
          <input v-model="isCaseSensitive" type="checkbox" class="checkbox" />
          区分大小写
        </div>
      </div>
    </div>
    <div slot="editor-modal-footer" class="footer_search_and_replace">
      <div>
        <a-button size="small" type="defalut" @click="replaceOne"
          >替换</a-button
        >
        <a-button size="small" type="defalut" @click="replaceAll"
          >替换全部</a-button
        >
        <a-button
          v-show="showCurrentGroupReplaceAll"
          size="small"
          type="defalut"
          @click="currentGroupReplaceAll"
          >分组内全部替换</a-button
        >
      </div>
      <div>
        <a-button size="small" type="defalut" @click="searchAll">查找</a-button>
        <a-button size="small" type="defalut" @click="searchNextOne"
          >查找下一处</a-button
        >
        <a-button size="small" type="defalut" @click="cancel">取消</a-button>
      </div>
    </div>
  </modal>
</template>
<script>
import modal from "./common/modal.vue";
import tipsModal from "./common/tipsModal.vue";
// import BUS from "@/assets/js/eventBus";
export default {
  name: "searchAndReplace",
  components: {
    modal,
    tipsModal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    resultData: {
      type: String,
      default: "",
    },
    search: {
      type: String,
      default: "",
    },
    showTipModal: {
      type: Boolean,
      default: false,
    },
    showCurrentGroupReplaceAll: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
    // editor: {
    //   type: Object,
    //   default: () => {},
    // },
  },
  watch: {
    show(val) {
      this.visible = val;
    },
    search(val) {
      this.searchData = val;
    },
    immediate: true,
    deep: true,
  },
  mounted() {
    // BUS.$on("editor_" + this.editorId, ({ instance }) => {
    //   if (instance) this.editor = instance;
    // });
  },
  data() {
    return {
      searchData: "",
      visible: false,
      replace: "",
      masks: false,
      isCaseSensitive: false,
      modal_width: 524,
      title: "查找替换",
    };
  },
  methods: {
    test(e) {
      this.searchData = e.target.value;
    },
    cancel() {
      this.$emit("cancel");
    },
    //区分大小写
    changeCaseSensitive() {
      this.isCaseSensitive = !this.isCaseSensitive;
      this.$emit("changeCaseSensitive");
    },
    //替换
    replaceOne() {
      this.$emit("replaceOne", this.searchData, this.replace);
    },
    //替换全部
    replaceAll() {
      this.$emit("replaceAll", this.searchData, this.replace);
    },
    currentGroupReplaceAll() {
      this.$emit("currentGroupReplaceAll", this.searchData, this.replace);
    },
    //查询全部
    searchAll() {
      this.$emit("searchAll", this.searchData);
    },
    //查询下一个
    searchNextOne() {
      this.$emit("searchNextOne", this.searchData);
    },
    closeModal() {
      this.$emit("closeTip");
    },
  },
};
</script>
<style scoped>
.replace_search_and_replace {
  width: 500px;
  background-color: rgb(255, 255, 255);
  color: #000;
}
.replaceContentClass {
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(217, 217, 217);
  padding: 20px;
}
.search-content {
  display: flex;
  padding-top: 10px;
}
.replace-content {
  padding-top: 20px;
  display: flex;
}
.condition {
  padding-top: 20px;
}
.click-checkbox {
  cursor: pointer;
  display: flex;
}
.replace_search_and_replace /deep/ .ant-input {
  border-radius: 0;
  padding: 0 0 0 5px;
  line-height: 21px;
  height: 21px;
  width: 90%;
  color: #000;
  border: 1px solid rgb(150, 150, 150);
}
.top-text {
  width: 80px;
}
.checkbox {
  margin-top: 4px;
  margin-right: 5px;
  height: 13px;
  width: 13px;
}
.replace_search_and_replace /deep/.ant-btn-defalut {
  color: #000;
}
.replace_search_and_replace /deep/ .ant-modal-header {
  padding: 8px 10px;
}
.replace_search_and_replace /deep/ .ant-modal-close-x {
  height: 40px;
  line-height: 40px;
}
.replace_search_and_replace /deep/ .ant-modal-body {
  background-color: rgb(240, 240, 240);
  padding: 12px;
}
.replace_search_and_replace /deep/.ant-modal-content {
  box-shadow: 0 0 12px rgb(0 0 0 / 40%);
}
.replace_search_and_replace /deep/ .ant-modal-footer {
  background-color: rgb(240, 240, 240);
  border-top: none;
  padding: 0 16px 10px 16px;
}
.replace_search_and_replace /deep/ .ant-modal-wrap {
  pointer-events: none;
}
.title_search_and_replace {
  height: 23px;
  display: flex;
}
.footer_search_and_replace {
  display: flex;
  justify-content: space-between;
}
</style>
