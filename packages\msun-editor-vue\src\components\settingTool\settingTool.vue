<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="title"
    class="setting-tool-modal"
    @cancel="cancel"
    :bodyStyle="{ backgroundColor: '#ffffff', padding: 0 }"
    @submit="cancel"
    :footer="null"
  >
    <div class="setting-container">
      <a-menu
        id="settingToolMenu"
        style="width: 150px"
        mode="inline"
        class="editor-setting-menu"
      >
        <a-sub-menu
          v-for="item in menuData"
          :key="item.key"
          class="editor-setting-sub-menu"
          @titleClick="handleTitleClick"
        >
          <template #title>{{ item.title }}</template>
          <a-menu-item
            v-for="child in item.children"
            :key="child.key"
            @click="clickOptionBtn"
          >
            {{ child.title }}</a-menu-item
          >
        </a-sub-menu>
      </a-menu>
      <div class="option-container">
        <div class="setting-option">
          <template v-for="(item, index) in optionData">
            <template v-if="item.keyWord === currentMenuKey">
              <template v-if="item.type === 'parent'">
                <div class="theme-title" :key="index">{{ item.title }}</div>
                <template v-for="child in item.children">
                  <template v-if="child.type === 'switch'">
                    <div :key="child.key" class="setting-tool-switch">
                      <span class="setting-tool-title clear">
                        {{ child.intro }}
                      </span>
                      <a-switch v-model="editorConfig[child.key]"></a-switch>
                    </div>
                  </template>
                  <template v-if="child.type === 'input'">
                    <div :key="child.key" class="setting-tool-input">
                      <span class="setting-tool-title clear">
                        {{ child.title }}
                      </span>
                      <a-input v-model="editorConfig[child.key]"></a-input>
                    </div>
                  </template>
                  <template v-if="child.type === 'colorPicker'">
                    <div :key="child.key" class="color-picker">
                      <span class="setting-tool-title clear">
                        {{ child.title }}
                      </span>
                      <div class="setting-tool-color-picker-button">
                        <colorPicker v-model="editorConfig[child.key]" />
                      </div>
                    </div>
                  </template>
                </template>
                <hr class="hr-style" :key="item.title" />
              </template>
              <template v-if="item.type === 'radioParent'">
                <div class="theme-title" :key="index">{{ item.title }}</div>
                <div :key="item.key">
                  <span class="setting-tool-title">
                    {{ item.intro }}
                  </span>
                  <a-radio-group
                    style="margin-left: 30px; margin-bottom: 15px"
                    v-model="editorConfig[item.key]"
                    :default-value="1"
                  >
                    <a-radio
                      v-for="child in item.children"
                      :value="child.value"
                      :key="child.key"
                      >{{ child.title }}</a-radio
                    >
                  </a-radio-group>
                  <hr class="hr-style" :key="item.title" />
                </div>
              </template>
              <template v-if="item.type === 'switch'">
                <div class="theme-title" :key="index">{{ item.title }}</div>
                <div :key="item.key" class="setting-tool-switch">
                  <span class="setting-tool-title">
                    {{ item.intro }}
                  </span>
                  <a-switch
                    v-model="editorConfig[item.key]"
                    class="setting-tool-switch-button"
                    checked-children="开"
                    un-checked-children="关"
                  ></a-switch>
                </div>
                <hr class="hr-style" :key="item.title" />
              </template>
              <template v-if="item.type === 'input'">
                <div class="theme-title" :key="index">{{ item.title }}</div>
                <div :key="item.key" class="setting-tool-input">
                  <a-input
                    v-model="editorConfig[item.key]"
                    class="setting-tool-button-input"
                  ></a-input>
                </div>
                <hr class="hr-style" :key="item.title" />
              </template>
              <template v-if="item.type === 'colorPicker'">
                <div class="color-picker-container" :key="index">
                  <div class="theme-title">{{ item.title }}</div>
                  <div :key="item.key" class="color-picker">
                    <span class="setting-tool-title clear">
                      {{ item.intro }}
                    </span>
                    <div class="setting-tool-color-picker-button clear">
                      <colorPicker v-model="editorConfig[item.key]" />
                    </div>
                  </div>
                </div>
                <hr class="hr-style" :key="item.title" />
              </template>
              <template v-if="item.type === 'select'">
                <div :key="index">
                  <div class="theme-title">{{ item.title }}</div>
                  <div :key="item.key">
                    <div v-if="item.key === 'family'">
                      <a-select
                        v-model="editorConfig.family"
                        style="width: 30%"
                        dropdownClassName="xeditor-input-up"
                      >
                        <a-select-option
                          v-for="(child, index) in fontTypeList"
                          :key="index"
                          :value="child.value"
                          >{{ child.option }}
                        </a-select-option>
                      </a-select>
                    </div>
                    <div v-if="item.key === 'height'">
                      <a-select
                        v-model="editorConfig.height"
                        style="width: 30%"
                        dropdownClassName="xeditor-input-up"
                      >
                        <a-select-option
                          v-for="(child, index) in fontSizeList"
                          :key="index"
                          :value="child"
                          >{{ child }}
                        </a-select-option>
                      </a-select>
                    </div>
                  </div>
                </div>
                <hr class="hr-style" :key="item.title" />
              </template>
            </template>
          </template>
          <a-button @click="reSetConfig">重置参数</a-button>
        </div>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../common/modal.vue";

export default {
  name: "settingTool",
  components: {
    modal,
  },
  data() {
    return {
      title: "设置",
      modal_width: 600,
      currentMenuKey: "pageShow",
      radioValue: 1,
      fontSizeList: [
        6.5, 7.5, 8.5, 10, 11, 12, 13.5, 14, 15, 16, 17.3, 19, 20, 21.5, 24.5,
        27.5, 29.5, 30, 32.5, 35, 38, 48.5, 56.5, 65, 76, 97.5,
      ],
      fontTypeList: [],

      initConfig: {
        background_color: "#ececec",
        fastEntryMode: false,
        fieldShowMode: 0,
        field_symbol_color: "blue",
        openEyeProtectMode: false,
        placeholderColor: "gray",
        show_corner_line: true,
        show_field_symbol: true,
        show_footer_line: false,
        show_header_line: true,
        useLetterPlaceholder: false,
        height: 16,
        family: "宋体",
      },
      editorConfig: {
        openEyeProtectMode: false,
        background_color: "#ececec",
        fastEntryMode: false,
        fieldShowMode: 0,
        field_symbol_color: "blue",
        // page_color: "#ffffff",
        placeholderColor: "gray",
        show_corner_line: true,
        show_field_symbol: true,
        show_footer_line: false,
        show_header_line: true,
        useLetterPlaceholder: false,
        height: 16,
        family: "",
      },
      menuData: [
        {
          title: "页面",
          key: "pageSetting",
          children: [
            {
              title: "页面显示",
              key: "pageShow",
            },
            {
              title: "颜色",
              key: "pageColor",
            },
          ],
        },
        {
          title: "文本域",
          key: "field",
          children: [
            {
              title: "边框",
              key: "fieldSymbol",
            },
            {
              title: "颜色",
              key: "fieldColor",
            },
          ],
        },
        {
          title: "文档",
          key: "document",
        },
        {
          title: "其他",
          key: "other",
        },
      ],
      optionData: [
        {
          title: "开启护眼色",
          type: "switch",
          key: "openEyeProtectMode",
          keyWord: "pageColor",
          intro: "根据个人需要开启页面护眼色:",
        },
        {
          title: "背景颜色",
          type: "colorPicker",
          key: "background_color",
          keyWord: "pageColor",
          intro: "根据需要调整编辑器背景颜色:",
        },
        {
          title: "快速录入模式",
          type: "switch",
          key: "fastEntryMode",
          keyWord: "other",
          intro: "开启快速录入模式",
        },
        {
          title: "文本域边框颜色",
          type: "colorPicker",
          key: "field_symbol_color",
          keyWord: "fieldSymbol",
          intro: "根据需求设置文本域边框颜色",
        },
        {
          title: "文本域背景文本颜色",
          type: "colorPicker",
          key: "placeholderColor",
          keyWord: "fieldColor",
          intro: "调整文本域placeholder颜色",
        },
        {
          title: "显示角落边角",
          type: "switch",
          key: "show_corner_line",
          keyWord: "pageShow",
          intro: "设置是否显示页面边角",
        },
        {
          title: "显示文本域边框",
          type: "switch",
          key: "show_field_symbol",
          keyWord: "fieldSymbol",
          intro: "设置是否显示文本域的边框",
        },
        {
          title: "显示页眉线",
          type: "switch",
          key: "show_header_line",
          keyWord: "pageShow",
          intro: "设置是否显示页眉线",
        },
        {
          title: "显示页脚线",
          type: "switch",
          key: "show_footer_line",
          keyWord: "pageShow",
          intro: "设置是否显示页脚线",
        },
        {
          title: "启用输入字母占位",
          type: "switch",
          key: "useLetterPlaceholder",
          keyWord: "other",
          intro: "设置是否启用输入时字母占位",
        },
        {
          title: "设置默认字体",
          type: "select",
          key: "family",
          keyWord: "document",
          intro: "设置文档默认字体",
        },
        {
          title: "设置默认字号",
          type: "select",
          key: "height",
          keyWord: "document",
          intro: "设置文档默认字号",
        },
      ],
    };
  },
  mounted() {},
  watch: {
    show(val) {
      if (val) {
        const editor = this.editor.editor;
        this.editor.builtInVariable.fontTypeList.forEach((e) => {
          this.fontTypeList.push({
            option: e,
            value: e,
          });
        });
        const config = editor.config;
        if (config.page_color === "rgb(199,237,204)") {
          this.editorConfig.openEyeProtectMode = true;
        }
        for (let key in this.editorConfig) {
          if (key in config) {
            this.editorConfig[key] = config[key];
          }
          if (config.default_font_style) {
            if (key === "family") {
              this.editorConfig.family = config.default_font_style.family;
            }
            if (key === "height")
              this.editorConfig.height = config.default_font_style.height;
          }
        }
      }
    },
    editorConfig: {
      handler(val) {
        this.$emit("submit", val);
        this.$editor.info("配置更改成功");
      },
      deep: true,
    },
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    clickOptionBtn(e) {
      this.currentMenuKey = e.key;
    },
    handleTitleClick(e) {
      if (e.key === "other" || e.key === "document") {
        this.currentMenuKey = e.key;
      } else {
        const menuData = this.menuData;
        menuData.forEach((data) => {
          if (data.key === e.key) {
            if (data.children.length) {
              this.currentMenuKey = data.children[0].key;
            }
          }
        });
      }
    },

    cancel() {
      this.fontTypeList = [];
      this.$emit("cancel");
    },
    reSetConfig() {
      let initConfig;
      if (this.upConfig) {
        initConfig = this.upConfig;
      } else {
        initConfig = this.initConfig;
      }
      const currentMenuKey = this.currentMenuKey;
      const optionData = this.optionData;
      optionData.forEach((data) => {
        if (data.keyWord === currentMenuKey) {
          for (let key in initConfig) {
            if (key === data.key) {
              this.editorConfig[key] = initConfig[key];
            }
          }
        }
      });
    },
  },
};
</script>
<style scoped>
.setting-container {
  width: 100%;
  display: flex;
  justify-self: start;
}
.editor-setting-menu {
  background-color: #f0f0f0;
  min-height: 400px;
}
.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical-left .ant-menu-item,
.ant-menu-vertical-right .ant-menu-item,
.ant-menu-inline .ant-menu-item {
  background-color: #f0f0f0;
  margin: 0;
}
.editor-setting-sub-menu {
  background-color: #f0f0f0;
}
.setting-tool-modal {
  background-color: #ffffff;
  float: left;
}
.setting-option {
  position: relative;
  padding: 10px;
  width: 100%;
}
.setting-tool-switch {
  margin: 20px;
  font-size: 16px;
}
.setting-tool-button {
  margin: 20px;
}
.color-picker {
  cursor: pointer;
  margin: 20px;
  font-size: 16px;
}
.setting-tool-input {
  display: flex;
  width: 100%;
  margin: 10px;
  margin: auto;
}
.setting-tool-title {
  width: 35%;
  text-align: center;
  margin-bottom: 5px;
  line-height: 35px;
  font-size: 16px;
}
.clear {
  clear: both;
}
.setting-tool-color-picker-button {
  display: inline-block;
  line-height: 18px;
  margin-left: 20px;
  height: 17px;

  /* line-height: 35px; */
  border: black solid 1px;
}
.theme-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  margin-top: 10px;
}

.setting-tool-switch-button {
  margin-left: 20px;
  margin-bottom: 5px;
}

.setting-tool-button-button {
  margin-left: 20px;
}
.option-container {
  width: 100%;
  max-height: 500px;
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden;
}
</style>
