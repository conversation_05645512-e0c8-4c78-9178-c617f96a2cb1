<template>
  <div
    id="editorNavigationMenu"
    style="width: 100%; height: 100%; overflow: auto; background-color: #fff"
    class="msun-editor-scrollbar"
  >
    <a-menu
      :default-selected-keys="[defaultSelectedKey]"
      :default-open-keys="defaultOpenKeys"
      mode="inline"
      style="width: 100%; background: #fff"
      @click="click"
    >
      <template v-for="(pItem, pIndex) in menuData">
        <template v-if="pItem.children && pItem.children.length">
          <a-sub-menu :key="itemKey(pIndex)">
            <span slot="title"
              ><a-icon v-if="pItem.icon" :type="pItem.icon" /><span>{{
                pItem.title
              }}</span></span
            >
            <template v-for="(cItem, cIndex) in pItem.children">
              <template v-if="cItem.children && cItem.children.length">
                <a-sub-menu :key="itemKey(pIndex, cIndex)">
                  <span slot="title"
                    ><a-icon v-if="cItem.icon" :type="cItem.icon" /><span>{{
                      cItem.title
                    }}</span></span
                  >
                  <template v-for="(ccItem, ccIndex) in cItem.children">
                    <a-menu-item :key="itemKey(pIndex, cIndex, ccIndex)">
                      <a-icon v-if="ccItem.icon" :type="ccItem.icon" />
                      <span>{{ ccItem.title }}</span>
                    </a-menu-item>
                  </template>
                </a-sub-menu>
              </template>
              <template v-else>
                <a-menu-item :key="itemKey(pIndex, cIndex)">
                  <a-icon v-if="cItem.icon" :type="cItem.icon" />
                  <span>{{ cItem.title }}</span>
                </a-menu-item>
              </template>
            </template>
          </a-sub-menu>
        </template>
        <template v-else>
          <a-menu-item :key="itemKey(pIndex)">
            <a-icon v-if="pItem.icon" :type="pItem.icon" />
            <span>{{ pItem.title }}</span>
          </a-menu-item>
        </template>
      </template>
    </a-menu>
  </div>
</template>
<script>
export default {
  name: "navigationMenu",
  data() {
    return {};
  },
  props: {
    defaultSelectedKey: {
      type: String,
      default: "sub_0_0",
    },
    defaultOpenKeys: {
      type: Array,
      default: () => ["sub_0"],
    },
    menuData: {
      type: Array,
      default: () => [],
    },
  },
  created() {},
  methods: {
    itemKey(pIndex, cIndex, ccIndex) {
      let key = "sub_" + pIndex;
      if (!isNaN(cIndex)) {
        key += "_" + cIndex;
      }
      if (!isNaN(ccIndex)) {
        key += "_" + ccIndex;
      }
      return key;
    },
    click({ key }) {
      const { data, resArr } = this.getDataByKey(key);
      this.$emit("clickMenu", data, key, resArr);
    },
    getDataByKey(key) {
      const indexArr = key.replace("sub_", "").split("_");
      const resArr = [];
      let data = this.menuData[Number(indexArr[0])];
      resArr.push(data);
      if (indexArr[1] !== undefined) {
        data = data.children[Number(indexArr[1])];
        resArr.push(data);
      }
      if (indexArr[2] !== undefined) {
        data = data.children[Number(indexArr[2])];
        resArr.push(data);
      }
      return { data, resArr };
    },
  },
};
</script>
<style lang="less">
#editorNavigationMenu .ant-menu-sub.ant-menu-inline {
  background: inherit;
}
.msun-editor-scrollbar {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #eceff7;
    border-radius: 6px;
  }
  /* 鼠标悬停时滑块样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #e0e3ea;
  }
  &::-webkit-scrollbar-track {
    background-color: white;
    -webkit-box-shadow: inset 0 0 4px rgba(100, 100, 100, 0.01);
  }
}
</style>
