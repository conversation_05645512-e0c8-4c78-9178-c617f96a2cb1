<template>
  <div class="side-body" @keydown="sidebarQuickSubmit">
    <div>
      <div v-for="(item, i) in dataSource" :key="i">
        <div v-if="type === item.type">
          <div class="side-title">{{ item.name }}</div>
          <div v-if="item.groupRadio && item.groupRadio.length && item.show">
            <a-radio-group
              :default-value="item.groupRadio[0]"
              @change="clickGroup"
              :style="{
                display: 'flex',
                justifyContent: 'center',
              }"
            >
              <a-radio-button
                v-for="(group, j) in item.groupRadio"
                :key="j"
                :value="group"
                :style="{
                  width: '50%',
                  textAlign: 'center',
                }"
              >
                {{ group }}
              </a-radio-button>
            </a-radio-group>
          </div>

          <div style="flex: 1; /* 中部分内容占据剩余空间 */ overflow-y: auto">
            <div v-for="(child, j) in item.children" :key="j">
              <div v-if="child.type === 'groupRadio'">
                <div v-if="child.name === clickTabGroup">
                  <div
                    v-for="(secondChild, k) in child.children"
                    :key="k"
                    class="header-card"
                  >
                    <div v-if="JSON.parse(secondChild.show)">
                      <a-card
                        size="small"
                        class="header-card"
                        :body-style="{
                          'padding-left': '0px',
                          'padding-right': '0px',
                        }"
                      >
                        <div class="header-card-title">
                          {{ secondChild.name }}
                        </div>
                        <div
                          v-if="
                            secondChild.children && secondChild.children.length
                          "
                        >
                          <div
                            v-for="(eleChild, m) in secondChild.children"
                            :key="m"
                          >
                            <sideRecursion
                              :sideData="eleChild"
                              :type="type"
                            ></sideRecursion>
                          </div>
                        </div>
                      </a-card>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else-if="child.type === 'title'">
                <div v-if="JSON.parse(child.show)">
                  <a-card
                    size="small"
                    class="header-card"
                    :body-style="{
                      'padding-left': '0px',
                      'padding-right': '0px',
                    }"
                  >
                    <div class="header-card-title">
                      {{ child.name }}
                    </div>
                    <div v-if="child.children" class="side-card-info">
                      <div v-for="(select, k) in child.children" :key="k">
                        <sideRecursion
                          :sideData="select"
                          ref="sideCard"
                          @inputChange="handleInputChange"
                          @connectMenu="connectMenu"
                          :type="type"
                        ></sideRecursion>
                      </div>
                    </div>
                  </a-card>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import sideRecursion from "./sideRecursion.vue";
export default {
  name: "editorElement",
  props: {
    type: {
      type: String,
      default: "文本域",
    },
    dataSource: {
      type: Array,
      default: () => [],
    },
    curClickInfo: {
      type: Object,
      default: () => {},
    },
  },

  components: { sideRecursion },
  data() {
    return {
      show: false,
      clickTabGroup: "",
      menuList: ["数字", "下拉列表", "日期"],
    };
  },
  mounted() {},
  watch: {
    type: {
      handler(val) {
        //给点击到的模块的clickTabGroup赋值
        for (let i = 0; i < this.dataSource.length; i++) {
          const element = this.dataSource[i];
          if (this.type === element.type) {
            if (element.groupRadio && element.groupRadio.length) {
              this.clickTabGroup = element.groupRadio[0];
            }
          }
        }
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    uuid() {
      const uuid = "xxxxxxxx".replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
      return uuid;
    },
    clickGroup(item) {
      this.clickTabGroup = item.target.value;
    },
    sidebarQuickSubmit(event) {
      if (event.key === "Enter" && event.ctrlKey) {
        event.preventDefault();
        this.$emit("submit");
      }
    },
    handleInputChange(data) {
      if (data.key === "fix_width") {
        this.dataSource.forEach((dataList) => {
          if (dataList.name === "文本域") {
            dataList.children.forEach((child) => {
              if (child.name === "常规") {
                child.children.forEach((c) => {
                  if (c.key === "max_width" || c.key === "min_width") {
                    c.value = data.e;
                    c.disabled = !!data.e;
                  }
                });
              }
            });
          }
        });
      }
    },
    connectMenu(name) {
      for (let i = 0; i < this.dataSource.length; i++) {
        const data = this.dataSource[i];
        if (data.type === this.type) {
          const connect = data.children;
          this.closeConnect(connect);
          const info = connect.filter((item) => item.name === name);
          if (info[0]) {
            info[0].show = true;
          }
        }
      }
    },
    closeConnect(list) {
      for (let i = 0; i < list.length; i++) {
        const element = list[i];
        if (this.menuList.includes(element.name)) {
          element.show = false;
        }
      }
    },
  },
};
</script>

<style scoped lang="less">
.side-body {
  height: 100%;
  width: 100%;
  padding: 0 10px 10px 10px;
  background-color: white;
  display: flex;
  flex-direction: column;
  /* 垂直方向排列子元素 */
  overflow-y: auto;
}

.side-body /deep/.ant-input-number-input {
  height: 25px;
  line-height: 25px;
}

.side-body/deep/.ant-select-selection--single {
  height: 25px;
}

.side-body/deep/.ant-select-selection__rendered {
  line-height: 25px;
}

.side-body/deep/ .ant-table-tbody > tr > td {
  padding: 5px;
}

.side-body/deep/.ant-table-thead > tr > th {
  padding: 5px;
}

.side-body/deep/.ant-table-hide-scrollbar {
  scrollbar-color: inherit;
}
.side-card-info {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 5px;
  line-height: 25px;
}
.side-title {
  font-size: 15px;
  font-weight: 700;
  color: #1f1f1f;
  line-height: 25px;
}

.side-card-title {
  width: 95px;
  font-size: 13px;
  background-color: white;
  display: flex;
}

.header-card {
  position: relative;
  margin: 15px 0;

  .header-card-title {
    position: absolute;
    top: -12px;
    left: 5px;
    color: #1f1f1f;
    font-size: 14px;
    font-weight: bold;
    background-color: white;
  }
}
</style>
