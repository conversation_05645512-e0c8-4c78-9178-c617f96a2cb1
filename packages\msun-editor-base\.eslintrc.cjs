module.exports = {
  env: {
    browser: true,
    es6: true
  },
  extends: [
    "standard"
  ],
  globals: {
    Atomics: "readonly",
    SharedArrayBuffer: "readonly"
  },
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: "module"
  },
  plugins: ["@typescript-eslint"],
  rules: {
    camelcase: "off",
    semi: [2, "always"],
    quotes: [
      "error",
      "double",
      {
        avoidEscape: true,
        allowTemplateLiterals: true
      }
    ],
    "node/no-callback-literal": "off",
    "no-unused-vars": "off",
    "@typescript-eslint/no-unused-vars": "error"
  }

};
