<template>
  <div
    :style="{
      position: 'absolute',
      left: position.left + 'px',
      top: position.top + 50 + 'px',
      opacity: 0.9,
    }"
  >
    <a-dropdown v-if="visible" class="editor-search-box-dropdown">
      <div class="editor-wrap">
        <div class="editor-searchBoxTitle">您可能要输入的内容</div>
        <div class="editor-titleTop">
          <span class="editor-titleTextShortCut">快捷键</span>
          <span class="editor-titleTextTime">日期</span>
          <span class="editor-titleTextContent">内容</span>
          <span class="editor-titleTextUnit">单位</span>
          <span class="editor-titleTexOperation">操作</span>
        </div>
        <div class="editor-content-wrap">
          <template v-for="(item, index) in data">
            <template>
              <div :key="index" class="editor-searchContentWrap">
                <span class="editor-searchContent">{{ "Alt+" + index }}</span>
                <span class="editor-searchContent">{{ item.time }}</span>
                <span class="editor-searchContent-operation">{{
                  item.content
                }}</span>
                <span class="editor-searchContent">{{ item.unit }}</span>
                <span
                  class="editor-searchContent-content"
                  @click="handleReference(index)"
                  >引用</span
                >
              </div>
            </template>
          </template>
        </div>
        <div class="test"></div>
      </div>
    </a-dropdown>
  </div>
</template>

<script>
export default {
  name: "fieldSearchBox",
  data() {
    return {
      data: [],

      select_data_temp: [
        // {
        //   searchBox_001: [
        //     { time: "2023-12-11", content: "19", unit: "次/分" },
        //     { time: "2023-12-08", content: "26", unit: "次/分" },
        //     { time: "2023-12-11", content: "23", unit: "次/分" },
        //     { time: "2023-12-08", content: "29", unit: "次/分" },
        //   ],
        // },
        // {
        //   searchBox_002: [
        //     { time: "2023-12-11", content: "19", unit: "次/分" },
        //     { time: "2023-12-08", content: "26", unit: "次/分" },
        //   ],
        // },
      ], //用于缓存已经查询出的下拉项数据
    };
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    position: {
      type: Object,
      default: () => {},
    },
    quickSelectIndex: {
      type: Number,
      default: 0,
    },
  },
  mounted() {},
  computed: {},
  watch: {
    visible: {
      handler(val) {
        const fieldInfo = this.editor._curFieldInfo;

        if (val) {
          let source_id = fieldInfo.source_id;
          if (source_id) {
            let result;
            for (let i = 0; i < this.select_data_temp.length; i++) {
              const keys = Object.keys(this.select_data_temp[i]);
              if (keys[0] === source_id) {
                result = Object.values(this.select_data_temp[i])[0];
              }
            }
            if (result) {
              this.data = result;
            } else {
              const handleResult = (dataKey, res_list) => {
                let handleSourceData;
                if (res_list && res_list.length && res_list instanceof Array) {
                  this.select_data_temp = res_list;
                  res_list.forEach((data) => {
                    if (Object.keys(data)[0] === dataKey) {
                      handleSourceData = Object.values(data)[0];
                    }
                  });
                }
                return handleSourceData;
              };
              const resolve = (resData) => {
                this.data = handleResult(source_id, resData);
                // 因为解决异步问题，所以要在resolve中调用
              };
              const res_list = this.editor.event.emit(
                "fieldSelectItemsRequest",
                source_id,
                resolve,
                fieldInfo
              );
              if (res_list instanceof Array) {
                resolve(res_list);
              }
              return;
            }
          }
        }
      },
    },
    quickSelectIndex: {
      handler(val) {
        let content;
        if (this.data[val]) {
          content = this.data[val].content;
        }
        if (content) {
          this.$emit("select", content, "select", false, content);
        }
      },
    },
  },
  methods: {
    handleReference(index) {
      const content = this.data[index].content;
      this.$emit("select", content, "select", false, content);
    },
  },
};
</script>
<style scoped>
.editor-searchBoxTitle {
  line-height: 20px;
  background-color: #ececec;
}
.editor-search-box-dropdown {
  padding: 0;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
}
.editor-wrap {
  width: 400px;
}
.editor-titleTop {
  padding: 0px 20px 0px 20px;
  font-weight: bold;
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
}
.editor-content-wrap {
  margin-top: 0px;
  height: 200px;
  overflow-y: auto;
}
.editor-searchContent-content {
  color: #1890ff;
}
.editor-searchContent-operation {
  color: #1890ff;
}
.editor-searchContentWrap {
  margin-top: 5px;
  display: flex;
  justify-content: space-around;
}
.editor-searchContentWrap:hover {
  background-color: #ececec;
}
.editor-searchContent-content:hover {
  cursor: pointer;
  color: #0f5eff;
}
</style>
