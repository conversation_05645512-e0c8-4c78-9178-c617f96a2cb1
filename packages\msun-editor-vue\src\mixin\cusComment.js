const cusCommentMixIn = {
  data() {
    return {
      selectCusCommentId: "-1", //选中的批注id
      isShowCusCommentList: false, //是否展示批注列表
      isOpenCusCommentEditModal: false, //是否打开批注编辑窗口
      curCusComment: null, //当前激活的批注
      cusComments: [], //批注列表内容
    };
  },
  methods: {
    refreshCusCommentList() {
      //表示从未添加过批注信息
      if (!this.editor.document_meta.cusCommentsIDSet) {
        this.cusComments = [];
        return;
      }
      //展示所有的批注信息
      this.cusComments = Object.values(
        this.editor.document_meta.cusCommentsIDSet
      ).flat();
    },
    //打开批注列表
    openCusCommentList() {
      this.editor.toggleCusCommentMode(true);
      this.refreshCusCommentList();
      if (!this.cusComments.length) {
        this.$editor.info("当前文档不存在自定义批注信息！");
        return;
      }
      this.isShowCusCommentList = true;
      this.editor.render();
    },

    // 批注修改完成确定后
    submitCusComment(value, isEdit, id = "") {
      let readonlyMark = false;
      if (this.editor.readonly) {
        this.editor.setReadonly(false);
        readonlyMark = true;
      }
      if (!value) {
        return this.$editor.warning("内容为空！");
      }
      if (!isEdit) {
        const selectedChars =
          this.editor.selection.selected_fields_chars.all_chars;
        if (
          !selectedChars ||
          !selectedChars.find((char) => char.field_position === "normal")
        ) {
          return this.$editor.warning("请先选中需要添加批注的文本！");
        }
      }
      if (this.curCusComment) {
        this.curCusComment.value = value;
        //更新批注内容
        this.editor.updateCusComments(this.curCusComment.id, value);
      } else {
        //新增批注
        this.selectCusCommentId = this.editor.addCusComment(
          {
            date: Date.now(),
            value,
            name: this.currentUser ? this.currentUser.name : "未登录",
            userId: this.currentUser ? this.currentUser.id : "-1",
          },
          id
        );
        this.editor.toggleCusCommentMode(true);
        this.refreshCusCommentList();
      }
      this.isOpenCusCommentEditModal = false;
      this.curCusComment = null;
      if (readonlyMark) {
        this.editor.setReadonly(true);
      }
    },
    //删除自定义批注
    deleteCusComment(item) {
      let readonlyMark;
      if (this.editor.readonly) {
        this.editor.setReadonly(false);
        readonlyMark = true;
      }
      this.editor.deleteCusComment(item.id);
      this.refreshCusCommentList();
      if (readonlyMark) {
        this.editor.setReadonly(true);
      }
    },
    //从右侧批注列表中选中一条批注
    selectCusComment(item) {
      this.selectCusCommentId = item.id;
      //激活对应的字符
      const res = this.editor.internal.addHighlightByCusCommentId(
        this.selectCusCommentId
      );
      //res 为 true 代表找到了对应的字符并设置好了样式 ， 为false 代表未从文档中找到对应字符
      if (res) {
        this.editor.render();
      } else {
        this.refreshCusCommentList();
        this.$editor.warning("文档中对应内容已删除！列表已刷新");
      }
    },

    replaceCusContent(item) {
      const id = item.id;
      const elements = this.editor.internal.getInfoByCusCommentId(id);
      const paraPath = elements.para_path;
      const endPath = elements.end_path;
      if (paraPath.length && endPath.length) {
        this.editor.selection.setSelectionByPath(
          paraPath,
          endPath,
          "para_path"
        );
        this.editor.delete_backward();
        // this.editor.deleteContentByPath(paraPath, endPath);
        const modelPath = this.editor.paraPath2ModelPath(paraPath);
        this.editor.selection.setCursorPosition(modelPath);
        this.editor.insertText(item.value);
        this.editor.update();
        this.editor.render();
      }
    },
    referenceCusComment(cusCommentId) {
      const editor = this.editor;
      const allChars = editor.selection.selected_fields_chars.all_chars;
      if (allChars) {
        if (!allChars.length) {
          return this.$editor.warning("请先选中需要添加批注的文本");
        }
        allChars.forEach((char) => {
          if (
            char.field_position === "normal" &&
            this.instance.TypeJudgment.isCharacter(char)
          ) {
            char.cusCommentId = cusCommentId;
          }
        });
      } else {
        return this.$editor.warning("请先选中需要添加批注的文本！");
      }
    },
  },
};
export default cusCommentMixIn;
