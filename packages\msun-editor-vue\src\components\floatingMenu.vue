<template>
  <div
    ref="floatDrag"
    class="editor-float-position"
    :style="{ left: initInfoUse.x + 'px', top: initInfoUse.y + 'px' }"
  >
    <div
      class="editor-float-position-drag"
      :style="{
        height: floatPositionDragHeight + 'px',
        'border-bottom': flag ? '0px' : '1px solid #fff',
      }"
      :class="{ dragging: isDragging }"
      @mousedown.stop.prevent="startDrag"
      @mouseup.stop.prevent="stopDrag"
      @mousemove.stop.prevent="dragging"
    ></div>
    <div class="editor-float-position-content">
      <!--      <div class="label">-->
      <!--        <div v-if="flag">展开</div>-->
      <!--        <div v-else>收起</div>-->
      <!--      </div>-->
      <div class="editor-float-item-container">
        <div
          v-for="(item, index) in floatMenuList"
          :key="index"
          :title="item.title"
          @click.stop.prevent="activeHandle(index, item)"
        >
          <a-popover
            :placement="popoverPlacement"
            trigger="click"
            overlayClassName="editor-popover-class"
            :visible.sync="popovers[index].visible"
          >
            <div slot="content" class="editor-popover-content">
              <div class="inner-title">
                <a-icon
                  style="font-size: 13px"
                  v-if="item.icon"
                  :type="item.icon"
                ></a-icon>
                {{ item.title }}
              </div>
              <template v-if="item.children && item.children.length">
                <div v-if="item.type === 'icon'" class="inner-icon">
                  <template v-for="(cItem, cIndex) in item.children">
                    <template v-if="cItem.children">
                      <a-divider
                        :key="generateKey(index, cIndex)"
                        orientation="center"
                        >{{ cItem.title }}</a-divider
                      >
                      <template v-for="(ccItem, ccIndex) in cItem.children">
                        <template v-if="ccItem.icon">
                          <span
                            :key="generateKey(index, cIndex, ccIndex)"
                            class="icon-menu-item"
                            :title="ccItem.title"
                            @mouseup.stop.prevent="
                              () => {
                                btnClickUp(item, cItem, ccItem);
                              }
                            "
                            @mousedown.stop.prevent="
                              () => {
                                btnClick(item, cItem, ccItem);
                              }
                            "
                          >
                            <icon-common
                              v-if="ccItem.icon.indexOf('icon') > -1"
                              :icon-style="customIconStyle"
                              :icon="ccItem.icon"
                              class="icon-menu-icon"
                            />
                            <a-icon
                              v-else
                              :type="ccItem.icon"
                              :style="{
                                color: ccItem.color,
                              }"
                              class="icon-menu-icon"
                            />
                          </span>
                        </template>
                      </template>
                    </template>
                    <template v-else>
                      <template v-for="(cItem, cIndex) in cItem.children">
                        <template v-if="cItem.icon">
                          <span
                            :key="generateKey(index, cIndex)"
                            class="icon-menu-item"
                            :title="cItem.title"
                            @mouseup.stop.prevent="
                              () => {
                                btnClickUp(item, cItem);
                              }
                            "
                            @mousedown.stop.prevent="
                              () => {
                                btnClick(item, cItem);
                              }
                            "
                          >
                            <icon-common
                              v-if="cItem.icon.indexOf('icon') > -1"
                              :icon-style="customIconStyle"
                              :icon="cItem.icon"
                              class="icon-menu-icon"
                            />
                            <a-icon
                              v-else
                              :type="cItem.icon"
                              class="icon-menu-icon"
                            />
                          </span>
                        </template>
                      </template>
                    </template>
                  </template>
                </div>
                <div v-if="item.type === 'menu'" class="menu">
                  <!-- 菜单项 -->
                  <a
                    class="menu-item"
                    v-for="(cItem, cIndex) in item.children"
                    :key="generateKey(index, cIndex)"
                    @mouseup.stop.prevent="
                      () => {
                        btnClickUp(item, cItem);
                      }
                    "
                    @mousedown.stop.prevent="
                      () => {
                        btnClick(item, cItem);
                      }
                    "
                  >
                    <template v-if="cItem.icon">
                      <icon-common
                        v-if="cItem.icon.indexOf('icon') > -1"
                        :icon-style="customIconStyle"
                        :icon="cItem.icon"
                        class="menu-icon"
                      />
                      <a-icon v-else :type="cItem.icon" class="menu-icon" />
                    </template>

                    <span>{{ cItem.title }}</span>
                  </a>
                </div>
                <div v-if="item.type === 'desc'" class="list">
                  <!-- 列表项 -->
                  <div class="list-item">
                    <template v-for="(cItem, cIndex) in item.children">
                      <template v-if="cItem.children">
                        <div
                          :key="generateKey(index, cIndex)"
                          class="list-item-title"
                        >
                          <a-icon
                            style="font-size: 12px"
                            v-if="cItem.icon"
                            :type="cItem.icon"
                          ></a-icon>
                          {{ cItem.title }}
                        </div>
                        <div
                          class="list-item-content"
                          v-for="(ccItem, ccIndex) in cItem.children"
                          :key="generateKey(index, cIndex, ccIndex)"
                        >
                          <a-icon
                            style="font-size: 12px"
                            v-if="ccItem.icon"
                            :type="ccItem.icon"
                          ></a-icon>
                          {{ ccItem.title }}
                        </div>
                      </template>
                      <template v-else>
                        <div
                          :key="generateKey(index, cIndex)"
                          class="list-item-content"
                        >
                          <a-icon
                            style="font-size: 12px"
                            v-if="cItem.icon"
                            :type="cItem.icon"
                          ></a-icon>
                          {{ cItem.title }}
                        </div>
                      </template>
                    </template>
                  </div>
                </div>
              </template>
            </div>
            <div
              :class="
                initInfoUse.activeIndex == index
                  ? 'active power-item'
                  : 'power-item'
              "
            >
              <a-icon
                v-if="item.icon"
                :type="item.icon"
                style="font-size: 16px; color: lightgoldenrodyellow"
              />
            </div>
          </a-popover>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import iconCommon from "./common/iconCommon.vue";
export default {
  name: "floatingMenu",
  components: {
    iconCommon,
  },
  data() {
    return {
      popoverPlacement: "left",
      popoverShow: false,
      isDragging: false,
      mouseState: -1, // 1 按下，2，移动，-1抬起
      startX: 800,
      startY: 110,
      maxX: window.innerWidth - 30,
      maxY: window.innerHeight - 30,
      floatPositionDragHeight: 30,
      timer: null,
      flag: true, // 控制悬浮框是否展开
      box: "", // 悬浮球的dom
      popovers: [],
      customIconStyle: {
        width: "14px",
        height: "14px",
        fill: "rgba(0, 0, 0, 0.8)",
      },
      initInfoUse: {},
      intervalId: -1,
      timeoutId: -1,
      outerFloatTimeout: -1,
    };
  },
  watch: {
    menuCount: {
      handler(val) {
        if (val > 1) {
          this.initInfoUse = JSON.parse(JSON.stringify(this.initInfo));
        } else {
          this.initInfoUse = this.initInfo;
        }
      },
      immediate: true,
    },
  },
  props: {
    menuCount: {
      type: Number,
      default: 1,
    },
    floatMenuList: {
      type: Array,
      default: () => [],
    },
    initInfo: {
      type: Object,
      default: () => {
        return {
          x: 0,
          y: 0,
          activeIndex: -1,
        };
      },
    },
  },
  created() {
    // 在 mounted 生命周期中创建 popovers 数组，用于存储每个 a-popover 实例的状态
    this.popovers = this.floatMenuList.map(() => ({
      visible: false,
    }));
  },
  mounted() {
    window.addEventListener("mouseup", this.stopDrag);
    this.box = this.$refs.floatDrag;
    const { activeIndex } = this.initInfoUse;
    if (!isNaN(activeIndex) && activeIndex > -1) {
      this.handelFlex();
      this.$nextTick(() => {
        this.handlePopoversShow(activeIndex);
      });
    }
  },
  beforeDestroy() {
    window.removeEventListener("mouseup", this.stopDrag);
  },
  methods: {
    generateKey(index, cIndex, ccIndex) {
      const key = [];
      if (!isNaN(index)) {
        key.push(index);
      }
      if (!isNaN(cIndex)) {
        key.push(cIndex);
      }
      if (!isNaN(ccIndex)) {
        key.push(ccIndex);
      }
      return key.join("_");
    },
    startDrag(event) {
      this.mouseState = 1;
      this.isDragging = true;
      try {
        this.startX = event.clientX || event.touches[0].clientX;
        this.startY = event.clientY || event.touches[0].clientY;
        document.addEventListener("mousemove", this.dragging);
        this.$emit("startDrag", event, this.initInfoUse);
        // eslint-disable-next-line no-empty
      } catch (e) {}
    },
    dragging(event) {
      if (this.isDragging) {
        event.preventDefault();
        try {
          const currentX = event.clientX || event.touches[0].clientX;
          const currentY = event.clientY || event.touches[0].clientY;
          const diffX = currentX - this.startX;
          const diffY = currentY - this.startY;
          const newX = this.initInfoUse.x + diffX;
          const newY = this.initInfoUse.y + diffY;
          this.initInfoUse.x = Math.max(20, Math.min(newX, this.maxX));
          this.initInfoUse.y = Math.max(20, Math.min(newY, this.maxY));
          if (this.initInfoUse.x > this.initInfoUse.middleX) {
            this.popoverPlacement = "left";
          } else {
            this.popoverPlacement = "right";
          }
          this.startX = currentX;
          this.startY = currentY;
          this.mouseState = 2;
          // eslint-disable-next-line no-empty
        } catch (e) {}
      }
    },
    stopDrag() {
      if (this.mouseState === 1) {
        this.handelFlex();
      }
      this.mouseState = -1;
      this.isDragging = false;
      document.removeEventListener("mousemove", this.dragging);
    },
    // 伸缩悬浮球
    handelFlex() {
      clearTimeout(this.outerFloatTimeout);
      if (this.flag) {
        this.floatPositionDragHeight = 20;
        const height = this.floatMenuList.length * 35 + 20;
        this.buffer(this.box, "height", height);
      } else {
        this.buffer(this.box, "height", 30);
      }
      this.flag = !this.flag;
      this.handlePopoversShow(-1);
      if (this.flag) {
        this.outerFloatTimeout = setTimeout(() => {
          this.floatPositionDragHeight = 30;
        }, 600);
      }
    },
    // 点击哪个power
    activeHandle(index, item) {
      if (!this.flag) {
        if (item.children && item.children.length) {
          //把我们自定义的下标赋值
          this.initInfoUse.activeIndex = index;
          this.handlePopoversShow(index);
        } else if (item.func) {
          this.$emit("btnClick", item);
        }
      }
    },
    btnClick(item, cItem, ccItem) {
      this.$emit("btnClick", item, cItem, ccItem);
      const handItem = ccItem ?? cItem ?? item;
      if (handItem.continuous) {
        this.timeoutId = setTimeout(() => {
          this.intervalId = setInterval(() => {
            // 按住持续触发的代码
            this.$emit("btnClick", item, cItem, ccItem);
          }, 100);
        }, 500);
      }
    },
    btnClickUp() {
      clearTimeout(this.timeoutId);
      clearInterval(this.intervalId);
    },
    // 控制二级菜单面板显示隐藏
    handlePopoversShow(index) {
      this.popovers.forEach((item, i) => {
        item.visible = index === i ? !this.popovers[index].visible : false;
      });
    },
    // 获取要改变得样式属性
    getStyleAttr(obj, attr) {
      if (obj.currentStyle) {
        // IE 和 opera
        return obj.currentStyle[attr];
      } else {
        return window.getComputedStyle(obj, null)[attr];
      }
    },
    // 动画函数
    buffer(eleObj, attr, target) {
      cancelAnimationFrame(eleObj.timer);
      let speed = 0;
      let begin = 0;
      let _this = this;
      eleObj.timer = requestAnimationFrame(function fn() {
        begin = parseInt(_this.getStyleAttr(eleObj, attr));
        // 动画速度
        speed = (target - begin) * 0.1;
        speed = target > begin ? Math.ceil(speed) : Math.floor(speed);
        eleObj.style[attr] = begin + speed + "px";
        eleObj.timer = requestAnimationFrame(fn);
        if (begin === target) {
          cancelAnimationFrame(eleObj.timer);
        }
      });
    },
  },
};
</script>
<style lang="less">
.editor-float-position:hover {
  box-shadow: 0 0 0 3px rgba(155, 155, 155, 0.4);
  opacity: 0.8;
}
.editor-float-position {
  position: fixed;
  z-index: 1000 !important;
  left: 0;
  top: 20%;
  width: 30px;
  height: 30px;
  border-radius: 32px;
  overflow: hidden;
  user-select: none;
  font-size: 12px;
  display: block;
  background: #9b9b9b;
  margin: 0;
  opacity: 0.6;
  box-shadow: 0 0 0 3px rgba(52, 52, 52, 0.1);
  transform: translateZ(0) rotateX(10deg);
  background: #9b9b9b;
  .editor-float-position-drag {
    width: 30px;
    height: 31px;
    cursor: grab;
    text-align: center;
    line-height: 15px;
  }
  .editor-float-position-drag:hover,
  .editor-float-position-drag:focus {
    outline: none;
  }

  .editor-float-position-drag:active {
    cursor: grabbing;
    background-color: #797d83;
  }
  .editor-float-position-content {
    width: 30px;
    /*height: 15px;*/

    /*.label {*/
    /*  cursor: pointer;*/
    /*  width: 30px;*/
    /*  height: 15px;*/
    /*  text-align: center;*/
    /*  line-height: 15px;*/
    /*  color: white;*/
    /*}*/

    /*.label:hover {*/
    /*  opacity: 0.8;*/
    /*}*/

    .editor-float-item-container {
      width: 30px;
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex-direction: column;
      cursor: pointer;
      .power-item {
        margin-top: 5px;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #69707a;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
      }
      .power-item:hover,
      .power-item:focus {
        background-color: #4c4c4c; /* 使用灰色搭配 */
        color: #333333; /* 将文字颜色改为黑色或深灰色 */
        outline: none;
      }

      .power-item:active {
        background-color: #5c6169; /* 使用深灰色搭配 */
        color: #ffffff; /* 将文字颜色改回白色 */
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
        border: 1px solid #6b7178;
      }
      .des {
        cursor: pointer;
        width: 30px;
        text-align: center;
        margin-bottom: 5px;
        font-size: 10px;
        color: #fff;
      }
    }
  }

  .close {
    width: 20px;
    height: 15px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: #9b9b9b;
    position: absolute;
    right: -10px;
    top: -12px;
    cursor: pointer;
  }

  .cart {
    border-radius: 50%;
    width: 5em;
    height: 5em;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-notice {
    display: inline-block;
    transition: all 0.3s;

    span {
      vertical-align: initial;
    }

    .notice-badge {
      color: inherit;

      .header-notice-icon {
        font-size: 16px;
        padding: 4px;
      }
    }
  }

  &.active {
    background-color: #9b9b9b !important;

    .active-des {
      color: #1a1818 !important;
      font-weight: bold !important;
    }
  }

  .drag-ball {
    .drag-content {
      overflow-wrap: break-word;
      font-size: 14px;
      color: #fff;
      letter-spacing: 2px;
    }
  }
}

.editor-popover-class .ant-popover-inner {
  opacity: 0.9;
  border-radius: 10px;
  background: #ececec;
}
// 重复代码 跟 542 行到699行 跟waterMarkFloating.vue 252到421行
.editor-popover-class .ant-popover-inner-content {
  padding: 5px 2px 2px 5px;
}
.editor-popover-content {
  text-align: center;
  .inner-title {
    font-size: 13px;
    font-weight: 600;
    user-select: none;
  }
  .inner-icon {
    background: white;
    width: 80px;
    .ant-divider {
      margin: 0;
    }
    .ant-divider-inner-text {
      font-size: 10px;
      padding: 0;
      user-select: none;
    }
    .icon-menu-item {
      cursor: pointer;
      padding: 2px 5px 2px 5px;
      font-size: 16px;
      width: 30px;
      height: 30px;
    }
    .icon-menu-item:hover {
      background-color: #eeeeee;
      border-radius: 10px;
    }
    .icon-menu-item:active {
      background-color: #e2e2e2;
      border-radius: 10px;
    }
  }
  .list-item {
    padding: 2px 5px;
    border-radius: 5px;
    background: white;
    white-space: pre-wrap;
    text-align: left;
    white-space: pre-wrap;
  }

  .list-item-title {
    font-size: 12px;
    font-weight: bold;
    color: #333;
    text-align: left;
    user-select: none;
  }

  .list-item-content {
    font-size: 12px;
    line-height: 1.5;
    color: #666;
  }
  .menu {
    background: white;
    border-radius: 5px;
    display: flex;
    flex-direction: column;
  }

  .menu-item {
    display: inline-flex;
    align-items: center;
    padding: 3px 5px;
    color: #333;
    text-decoration: none;
    user-select: none;
    .menu-icon {
      margin: 3px 2px 0px 0px;
      font-size: 12px;
    }
  }

  .menu-item:hover {
    background-color: #eeeeee;
    border-radius: 10px;
  }
  .menu-item:active {
    background-color: #e2e2e2;
    border-radius: 10px;
  }
}

/*.editor-popover-class .ant-popover-arrow {*/
/*  border-top-color: #f0f0f0 !important;*/
/*  border-right-color: #f0f0f0 !important;*/
/*  border-bottom-color: #f0f0f0 !important;*/
/*  border-left-color: #f0f0f0 !important;*/
/*}*/
</style>
