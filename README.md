# changeset 使用流程

## 项目地址：[链接](https://github.com/changesets/changesets#integrating-with-ci)

## 发版流程

1. 确保代码拉到最新代码之后，执行`npm run bump`，如果是新分支需要升级中间的版本号执行`npm run bump:minor`

2. 提交更新，合并到main分支，触发自动发版

3. 自动发版完成之后，复制版本号到运维平台（检查上面的分支号是否对应当前的版本），发布版本

## 从原仓库更新代码

`
git subtree pull --prefix=packages/msun-editor-base https://gitlab.msunhis.com/msunClound/emr-editor-group/msun-editor-base.git  release-V1.47.0

git subtree pull --prefix=packages/msun-editor-vue https://gitlab.msunhis.com/msunClound/emr-editor-group/msun-editor.git release-V1.47.0

git subtree pull --prefix=packages/msun-lib-editor-design https://gitlab.msunhis.com/msunClound/emr-editor-group/msun-lib-editor-design.git main

git subtree pull --prefix=packages/msun-lib-editor-common https://gitlab.msunhis.com/msunClound/emr-editor-group/msun-editor-common-base.git dev

git subtree pull --prefix=packages/msun-lib-editor-transform https://gitlab.msunhis.com/msunClound/emr-editor-group/msun-editor-data-transform.git dev

`

## 问题处理

`
执行以上从原仓库更新代码命令时提示 Working tree has modifications.  Cannot add.

解决方法：1、将本地代码变动全部回滚或全部提交，保持本地代码与远程仓库中一致后再执行。
        2、每执行一次从原仓库更新代码命令后都先将拉取的代码再提交到当前项目仓库中。

`
## 当需要将demo加入到monorepo中时，将demo项目整个copy至packages目录下，删除原node_modules使用pnpm重新安装即可


## 当新版本已经发布，但是需要发旧版本补丁的时候,按照以下步骤进行操作
1.在需要发布的版本上执行，npm run bump,升级完成之后推送到远程仓库
2.切换到main分支，执行 git reset --hard 发版的分支名字    例：git reset --hard release-V1.60.0  
3.执行 git push origin main --force
4.到远端看着流程走完，tags里面拿到版本号，复制一下粘贴到运维平台发版
