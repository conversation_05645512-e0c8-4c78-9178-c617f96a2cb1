<template>
  <modal
    class="table-modal"
    :title="title"
    :show="show"
    :width="width"
    :freePoint="true"
    :sessionMove="true"
    pointer-events:none
    @cancel="cancel"
  >
    <span class="editor-example-mention"
      ><a-popover :title="popTitle" :overlayStyle="{ zIndex: 999999 }">
        <template slot="content">
          <div style="width: 760px">
            <div class="textTitle">
              展示规则主要用于展示值不同于字段真实值的情景。
            </div>
            &nbsp;&nbsp;&nbsp;&nbsp;情景举例：<br />
            &nbsp;&nbsp;&nbsp;&nbsp;(1)将字段值中所有的"&"符号替换为"+"。配置方式：【全局】-【自定义】【&】-【+】<br />
            &nbsp;&nbsp;&nbsp;&nbsp;(2)字段值为“1.第一段2.第二段3.第三段”，将其分成三段展示。配置方式：【全局】-【数字】-【\n*】，其中星号（*）就代表匹配到的内容，这里就是指数字。<br />
            &nbsp;&nbsp;&nbsp;&nbsp;(3)字段值为非固定日期，格式为“YYYY-MM-DD“，隐藏真实日期展示为”某天“。配置方式：【全局】-【自定义】【^\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])$】-【某天】<br />
            &nbsp;&nbsp;&nbsp;&nbsp;(4)只保留匹配到的内容：操作中只留一个*即可，例如12:34:33,只展示12:34.
            配置方式：【全局】-【自定义】【^.{5}】-【*】<br />
            &nbsp;&nbsp;&nbsp;&nbsp;(5)内容脱敏以星号展示，只保留身份证号前两位与后两位，中间部分用三个星号展示。
            配置方式：第一条规则：【全局】-【自定义】【^(.{2}).*(.{2})$】-【*】，第二条规则：【全局】-【自定义】【#】-【\*\*\*】
            <br />
            <div class="textTitle">注意：</div>
            <div style="color: red">
              &nbsp;&nbsp;&nbsp;&nbsp;一、其中*号代表匹配到的内容，当最右侧输入框只保留一个*号时，只展示匹配内容；<br />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;如果匹配到多项，使用#号分割。如需替换#号，则在下方继续添加规则实现；<br />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;当*号跟其他字符一起时是将匹配到的内容与其他字符拼接后再替换匹配到的内容。例如上述情景（2）
              <br />
              &nbsp;&nbsp;&nbsp;&nbsp;二、如果下一条匹配内容是上一条的替换结果，会导致上条规则不生效<br />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;例如：需要实现当字段值为0时展示1，值为1时展示2<br />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;如果这么设置，第一条规则【自定义】-【0】-【1】；第二条规则：【自定义】-【1】-【2】；<br />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;因为执行是从上往下执行，所以当字段值为0时会展示2<br />
              &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;解决办法：把规则设置顺序颠倒：第一条规则【自定义】-【1】-【2】；第二条规则：【自定义】-【0】-【1】
              <br />
            </div>
          </div>
        </template>
        <div
          style="margin-bottom: 5px; margin-left: 10px; width: 20px"
          class="edit-btn"
        >
          <icon-common icon="icon-jieshi" style="cursor: pointer"></icon-common>
        </div> </a-popover
    ></span>
    <div class="editor-ruler-editor">
      <a-table
        :columns="fieldAutoColumns"
        bordered
        :dataSource="dataSource"
        :pagination="false"
        :scroll="{ y: 200 }"
      >
        <template slot="range" slot-scope="text, record">
          <a-select
            class="prop-select rulerEditorSelect"
            v-model="record.matchRange"
            dropdownClassName="xeditor-input-up"
            :style="{ width: '100%' }"
          >
            <a-select-option :value="0">全局</a-select-option>
            <a-select-option :value="1">首项</a-select-option>
            <a-select-option :value="2">尾项</a-select-option>
          </a-select>
        </template>
        <template slot="match" slot-scope="text, record">
          <div
            v-if="record.match === 'custom'"
            style="display: flex; align-items: center"
          >
            <a-select
              class="prop-select rulerEditorSelect"
              v-model="record.match"
              dropdownClassName="xeditor-input-up"
              :style="{ width: '40%' }"
            >
              <a-select-option :value="'number'">数字</a-select-option>
              <a-select-option :value="'letter'">字母</a-select-option>
              <a-select-option :value="'custom'">自定义</a-select-option>
            </a-select>
            <a-input
              v-model="record.matchValue"
              placeholder="匹配字符"
              class="rulerRange"
              :style="{ width: '60%' }"
            ></a-input>
          </div>
          <div v-else>
            <a-select
              class="prop-select rulerEditorSelect"
              v-model="record.match"
              dropdownClassName="xeditor-input-up"
              :style="{ width: '100%' }"
            >
              <a-select-option :value="'number'">数字</a-select-option>
              <a-select-option :value="'letter'">字母</a-select-option>
              <a-select-option :value="'custom'">自定义</a-select-option>
            </a-select>
          </div>
        </template>
        <template slot="operation" slot-scope="text, record">
          <div style="display: flex; align-items: center">
            <!-- <a-input
              style="width: 50%"
              placeholder="*(匹配对象)"
              disabled
            ></a-input>-->
            <span style="width: 20%; text-align: center">=></span>
            <a-input
              style="width: 100%"
              placeholder="替换为"
              v-model="record.handledValue"
            ></a-input>
          </div>
        </template>
        <template slot="delete" slot-scope="text, record">
          <a
            href="#"
            @click="handleDelete(record)"
            style="display: flex; justify-content: center"
            ><span>删除</span></a
          >
        </template>
      </a-table>
    </div>

    <div
      style="
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
      "
    >
      <div
        class="editor-given-example"
        style="width: 45%; display: flex; align-items: center; margin-right: 5%"
      >
        <span style="width: 30%">测试：</span>
        <a-input
          v-model="strUsedForTestRule"
          style="margin-left: 10px"
          @pressEnter="handleTest"
          placeholder="回车看测试结果"
        ></a-input>
      </div>
      <div
        class="editor-handled-example"
        style="width: 50%; display: flex; align-items: center"
      >
        <span style="width: 44%">测试结果：</span>
        <a-input
          :value="formattedText"
          type="textarea"
          :autoSize="{ minRows: 1, maxRows: 6 }"
          disabled
          style="margin-left: 2px"
        ></a-input>
      </div>
    </div>
    <div slot="editor-modal-footer" class="footer" style="padding-top: 0px">
      <div>
        <a-button type="default" @click="add">添加</a-button>
      </div>
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "./common/modal.vue";
import iconCommon from "./common/iconCommon.vue";

export default {
  name: "rulerEditor",
  components: { modal, iconCommon },
  data() {
    return {
      regArr: [],
      matchRange: 0,
      title: "规则编辑器",
      width: 600,
      popTitle: "展示规则说明",
      strUsedForTestRule: "",
      fieldAutoColumns: [
        {
          title: "范围",
          dataIndex: "range",
          width: "12%",
          align: "center",
          scopedSlots: { customRender: "range" },
        },
        {
          title: "匹配",
          dataIndex: "match",
          width: "38%",
          align: "center",
          scopedSlots: { customRender: "match" },
        },
        {
          title: "操作",
          dataIndex: "operation",
          width: "40%",
          align: "center",
          scopedSlots: { customRender: "operation" },
        },
        {
          title: "删除",
          dataIndex: "delete",
          width: "10%",
          align: "center",
          scopedSlots: { customRender: "delete" },
        },
      ],
      dataSource: [
        {
          key: "1",
          matchRange: 0,
          match: "custom",
          matchValue: "",
          range: "",
          operation: "",
          delete: "",
          handledValue: "",
        },
      ],
    };
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    field: {
      type: Object,
      default: () => {},
    },
    replaceRule: {
      type: Array,
      default: () => [],
    },
    formattedText: {
      type: String,
      default: "",
    },
  },
  computed: {},
  watch: {
    show(val) {
      if (val) {
        if (this.replaceRule.length) {
          this.dataSource = [];
          const ruleList = this.replaceRule;
          ruleList.forEach((rule, index) => {
            let match;
            if (rule.rule === "\\d+") {
              match = "number";
            } else if (rule.rule === "[a-zA-Z]") {
              match = "letter";
            } else {
              match = "custom";
            }
            const newData = {
              key: index,
              matchRange: rule.flags === "g" ? 0 : 1,
              match: match,
              matchValue: rule.custom === "custom" ? rule.rule : "",
              range: "",
              operation: "",
              delete: "",
              handledValue: rule.replace,
            };
            this.dataSource.push(newData);
          });
        } else {
          this.dataSource = [
            {
              key: "1",
              matchRange: 0,
              match: "custom",
              matchValue: "",
              range: "",
              operation: "",
              delete: "",
              handledValue: "",
            },
          ];
        }
      }
    },

    immediate: true,
    deep: true,
  },
  methods: {
    handleTest() {
      const reg = this.submit({ editorIsTest: true });
      this.$emit("test", reg, this.strUsedForTestRule);
    },
    cancel() {
      this.$emit("cancel");
    },
    submit(e) {
      const dataList = this.dataSource;
      const reg = [];
      for (let i = 0; i < dataList.length; i++) {
        const dataInfo = dataList[i];
        const curReg = {};
        const range = dataInfo.matchRange;
        const value = dataInfo.matchValue;
        const match = dataInfo.match;
        let handledValue = dataInfo.handledValue;

        if (match === "number" && handledValue !== "*") {
          curReg.rule = "\\d+";
          curReg.flags = range;
          curReg.replace = handledValue;
          reg.push(curReg);
        } else if (match === "letter" && handledValue !== "*") {
          curReg.rule = "[a-zA-Z]";
          curReg.flags = range;
          curReg.replace = handledValue;
          reg.push(curReg);
        } else if (match === "custom") {
          curReg.rule = value;
          curReg.flags = range;
          curReg.replace = handledValue ?? "";
          curReg.custom = "custom";
          reg.push(curReg);
        }
      }
      if (e.editorIsTest) return reg;
      this.$emit("submit", reg);
    },
    handleDelete(item) {
      for (let i = 0; i < this.dataSource.length; i++) {
        if (item.key === this.dataSource[i].key) {
          this.dataSource.splice(i, 1);
          break;
        }
      }
    },
    add() {
      const new_source_list = {
        key: this.editor.utils.getUUID("displayRules"),
        matchRange: 0,
        match: "custom",
        matchValue: "",
        range: "",
        operation: "",
        delete: "",
        handledValue: "*",
      };

      this.dataSource.push(new_source_list);
    },
  },
};
</script>

<style>
.select-input .ant-select-dropdown-menu-item {
  padding: 2px 12px;
}
</style>

<style scoped>
.table-modal /deep/ .ant-input-sm {
  height: 30px;
}

.rulerRange {
  width: 100%;
}

.table-modal /deep/ .ant-table-tbody > tr > td {
  padding: 0px;
}

.table-modal /deep/ .ant-table-thead > tr > th,
.ant-table-tbody > tr > td {
  padding: 6px;
}

.editor-given-example {
  margin-top: 10px;
}

.editor-handled-example {
  margin-top: 10px;
}

.rulerEditorSelect {
  margin: auto;
}
.textTitle {
  color: black;
  font-weight: 700;
}

.footer {
  display: flex;
  justify-content: space-between;
  padding-top: 35px;
}
.editor-ruler-editor {
  margin-bottom: 10px;
}
.editor-example-mention {
  font-size: 10px;
  position: absolute;
  top: 10px;
  left: 78px;
}
</style>
