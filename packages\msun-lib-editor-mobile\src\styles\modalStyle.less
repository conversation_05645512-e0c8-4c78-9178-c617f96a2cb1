.prop-div {
  .prop-input {
    width: calc(~'100% - 120px');
  }
}
.prop {
  display         : flex;
  flex-wrap       : wrap;
  background-color: rgb(255, 255, 255);
  padding-bottom  : 5px;

  .prop-type {
    margin-top : 5px;
    width      : 100%;
    margin-left: 10px;
    height     : 15px;
    display    : flex;
    align-items: center;

    .hr-style {
      margin-top: 10px;
      width     : calc(~'100% - 100px');
      color     : black;
    }
  }
}

.prop-div {
  margin-top: 10px;
  width     : 50%;
}

.prop-div label {
  margin-left: 10px;
  display    : inline-block;
  width      : 100px;
}

.prop_align {
  display   : flex;
  width     : 100%;
  margin-top: 10px;

  label,
  strong {
    margin-left: 20px;
    width      : 100px;
  }

  .align_icon {
    padding: 0px 5px 0 5px;
  }
}

.border_css {
  border-radius: 2px;
  display      : flex;
}

.prop-input_num {
  width       : 100px;
  margin-right: 10px;
}
.xeditor-input-up {
  z-index: 99999 !important;
}
