
export enum MonitorType {
  update,
  render,
}

// 监视器，监视编辑器的运行状态
export default class Monitor {
  result:any = {};

  process:any = {};

  start (mark:string | MonitorType) {
    this.process[mark] = Date.now();
  }

  end (mark:string | MonitorType) {
    if (isNaN(this.process[mark])) {
      this.result[mark] = -1;
    } else {
      const dateNow = Date.now();
      this.result[mark] = dateNow - this.process[mark];
      this.process[mark] = dateNow;
    }
  }
}
