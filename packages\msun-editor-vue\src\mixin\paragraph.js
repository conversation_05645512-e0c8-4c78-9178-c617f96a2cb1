// import BUS from "@/assets/js/eventBus";
const paragraphMixIn = {
  data() {
    return {
      isShowParaAttrModal: false,
      isShowListModal: false,
      // paragraph_prop: {},
    };
  },
  methods: {
    // 展示段落属性的模态框
    showParaAttrModal() {
      let paragraph = this.editor.selection.getFocusParagraph();
      const checkParaPropArr = [
        "align",
        "vertical_align",
        "indentation",
        "before_paragraph_spacing",
        "row_ratio",
        "isOrder",
        "listNumStyle",
      ];
      let paragraph_prop = {};
      checkParaPropArr.forEach((prop) => {
        paragraph_prop[prop] = paragraph[prop];
      });
      const paraArr = this.editor.selection.selected_para_info.paragraphs;
      paragraph_prop = this.checkProp(
        checkParaPropArr,
        paraArr,
        paragraph_prop
      );
      this.$refs.paragraph.paragraph_prop = paragraph_prop;
      // BUS.$emit("editor_" + this.editorId, { paragraph_prop: paragraph_prop });
      this.isShowParaAttrModal = true;
    },
    checkProp(propArr, paraArr, paragraph_prop) {
      for (let i = 0; i < propArr.length; i++) {
        const prop = propArr[i];
        const isSame = paraArr.every((obj) => obj[prop] === paraArr[0][prop]);
        if (!isSame) {
          paragraph_prop[prop] = undefined;
        }
      }
      return paragraph_prop;
    },
    closeParaAttrModal() {
      this.isShowParaAttrModal = false;
      // 点击取消的时候弹窗会出现重影
      setTimeout(() => {
        this.isShowListModal = false;
      }, 100);
      this.editor.focus();
    },
    confirmParaAttrModal(data) {
      const editor = this.editor;

      data.indentation !== undefined &&
        editor.firstRowIndentation(data.indentation); // 首行缩进
      data.before_paragraph_spacing !== undefined &&
        editor.changeParaBeforSpacing(data.before_paragraph_spacing); // 修改段间距
      data.row_ratio && editor.changeRowRatio(data.row_ratio); // 修改行倍距
      if (data.align) {
        editor.changeContentAlign(data.align); // 修改水平对齐方式
      }
      data.vertical_align =
        data.vertical_align === "middle" ? "center" : data.vertical_align;
      data.vertical_align && editor.setVerticalAlign(data.vertical_align); // 修改垂直对齐
      //   修改列表类型以及图标类型
      data.isOrder !== undefined && editor.addList(data.isOrder);
      data.listNumStyle !== undefined &&
        editor.changeListNumStyle(data.listNumStyle);
      this.isShowParaAttrModal = false;
      // 点击确定的时候弹窗会出现重影
      setTimeout(() => {
        this.isShowListModal = false;
      }, 100);
      editor.focus();
    },
    showListModal() {
      const paragraph_prop = this.editor.selection.getFocusParagraph();
      this.$refs.paragraph.paragraph_prop = paragraph_prop;
      // BUS.$emit("editor_" + this.editorId, { paragraph_prop: paragraph_prop });
      this.isShowParaAttrModal = true;
      this.isShowListModal = true;
    },
  },
};
export default paragraphMixIn;
