[{"name": "F0001", "value": "内容1"}, {"name": "C0001", "value": [1, 2, 3, 4]}, {"name": "R0001", "value": 2}, {"name": "I0001", "width": 50, "height": 50, "src": "base64"}, {"type": "table", "fillType": 1, "maxCols": 4, "colSize": [10], "tableRaw": "", "fixedHeaderNum": 1, "repeatRowCells": [[{"content": [{"type": "raw", "value": {"header": [{"id": "para-835baea7", "type": "p", "align": "left", "deepNum": 0, "islist": false, "isOrder": false, "indentation": 0, "dispersed_align": false, "before_paragraph_spacing": 0, "restart_list_index": false, "row_ratio": 1.6, "page_break": false, "children": [{"type": "text", "value": "", "font_id": "font-default-bc28fc66"}], "title_length": 0, "content_padding_left": 0, "vertical_align": "top", "level": 0, "listNumStyle": "number"}], "footer": [{"id": "para-a5fb94f2", "type": "p", "align": "left", "deepNum": 0, "islist": false, "isOrder": false, "indentation": 0, "dispersed_align": false, "before_paragraph_spacing": 0, "restart_list_index": false, "row_ratio": 1.6, "page_break": false, "children": [{"type": "text", "value": "", "font_id": "font-default-bc28fc66"}], "title_length": 0, "content_padding_left": 0, "vertical_align": "top", "level": 0, "listNumStyle": "number"}], "content": [{"id": "para-2514b39c", "type": "p", "align": "left", "deepNum": 0, "islist": false, "isOrder": false, "indentation": 0, "dispersed_align": false, "before_paragraph_spacing": 0, "restart_list_index": false, "row_ratio": 1.5, "page_break": false, "children": [{"type": "text", "value": "适用对象：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-dbc2d4dd", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "适用对象", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "SHIYONGDUIXIANG", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}], "title_length": 0, "content_padding_left": 0, "vertical_align": "top", "level": 0, "listNumStyle": "number", "itemsWidth": []}, {"id": "para-423c41ba", "type": "p", "align": "left", "deepNum": 0, "islist": false, "isOrder": false, "indentation": 0, "dispersed_align": false, "before_paragraph_spacing": 0, "restart_list_index": false, "row_ratio": 1.5, "page_break": false, "children": [{"type": "text", "value": "患者姓名：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-6f555e2d", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "患者姓名", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}, {"type": "text", "value": "       性别：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-753a787d", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "性别", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}, {"type": "text", "value": "     年龄：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-965cef7b", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "年龄", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}, {"type": "text", "value": "      住院号：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-e36a409d", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "住院号", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}], "title_length": 0, "content_padding_left": 0, "vertical_align": "top", "level": 0, "listNumStyle": "number", "itemsWidth": []}, {"id": "para-798c74a4", "type": "p", "align": "left", "deepNum": 0, "islist": false, "isOrder": false, "indentation": 0, "dispersed_align": false, "before_paragraph_spacing": 0, "restart_list_index": false, "row_ratio": 1.5, "page_break": false, "children": [{"type": "text", "value": "住院日期：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-dedbe487", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "住院日期", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}, {"type": "text", "value": "        出院日期：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-6ae02659", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "出院日期", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}, {"type": "text", "value": "         标准住院日：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-ed283f75", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "标准住院日", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}], "title_length": 0, "content_padding_left": 0, "vertical_align": "top", "level": 0, "listNumStyle": "number", "itemsWidth": []}, {"id": "para-4d893a60", "type": "p", "align": "left", "deepNum": 0, "islist": false, "isOrder": false, "indentation": 0, "dispersed_align": false, "before_paragraph_spacing": 0, "restart_list_index": false, "row_ratio": 1.5, "page_break": false, "children": [{"type": "text", "value": "路径结束时间：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-9a45621a", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "路径结束时间", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}, {"type": "text", "value": "     结束状态：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-0d31bcbb", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "结束状态", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}, {"type": "text", "value": "      原因：", "font_id": "font-ad26cfde"}, {"type": "field", "id": "field-paste-d0ba1d18", "field_type": "normal", "start_symbol": "[", "end_symbol": "]", "display_type": "normal", "readonly": 0, "deletable": 1, "placeholder": "原因", "tip": "", "source_id": "", "source_list": [], "show_format": 0, "replace_format": 0, "number_format": 0, "meta": {}, "active_type": 0, "multi_select": 0, "separator": 0, "inputMode": 0, "align": "left", "max_width": 0, "min_width": 0, "style": {}, "valid": 0, "valid_content": {"require": 1, "type": "string", "phone_type": "", "rule": {"max_length": 20, "min_length": 5}, "regex": ""}, "box_checked": 0, "box_multi": 0, "children": [], "name": "XINGMING", "show_symbol": 1, "show_field": true, "cascade_list": [], "automation_list": [], "formula": "", "formula_value": 0, "forbidden": {"start": null, "end": null}, "maxHeight": 0, "font_id": "font-ad26cfde"}], "title_length": 0, "content_padding_left": 0, "vertical_align": "top", "level": 0, "listNumStyle": "number", "itemsWidth": []}, {"id": "para-bd7d6e2a", "type": "p", "align": "left", "deepNum": 0, "islist": false, "isOrder": false, "indentation": 0, "dispersed_align": false, "before_paragraph_spacing": 0, "restart_list_index": false, "row_ratio": 1.5, "page_break": false, "children": [{"type": "text", "value": "", "font_id": "font-ad26cfde"}], "title_length": 0, "content_padding_left": 0, "vertical_align": "top", "level": 0, "listNumStyle": "number", "itemsWidth": []}], "groups": [], "fontMap": {"font-default-bc28fc66": {"id": "font-default-bc28fc66", "height": 16, "family": "宋体", "bold": false, "italic": false, "underline": false, "dblUnderLine": false, "strikethrough": false, "script": 3, "color": "#000", "bgColor": null, "highLight": null, "characterSpacing": 0}}, "imageSrcObj": {}, "bodyText": "", "shapes": [], "waterMarks": [], "config": {"page_info": {"default_font_style": {"family": "宋体", "height": 16, "bold": false, "italic": false, "underline": false, "strikethrough": false, "dblUnderLine": false, "script": 3, "characterSpacing": 0, "color": "#000", "bgColor": null, "highLight": null}, "page_size_type": "A4", "page_direction": "vertical", "row_ratio": 1.6, "editor_padding_top": 20, "page_margin_bottom": 20, "page_padding_left": 40, "page_padding_top": 80, "page_padding_right": 40, "page_padding_bottom": 80, "header_margin_top": 40, "footer_margin_bottom": 42.5, "content_margin_header": 5, "content_margin_footer": 5, "table_padding_horizontal": 5, "page_size": {"width": 793, "height": 1121}}, "direction": "vertical", "header_horizontal": true, "footer_horizontal": false, "rowLineType": 0}, "meta": {"versionList": [{"version": "10.0.9", "time": 1704277708559}, {"version": "10.0.12", "time": 1704781867636}, {"version": "10.0.13", "time": 1704865469467}, {"version": "10.0.17", "time": 1705375214991}], "fieldSymbolWidth": 0, "newDocuAlign": true, "useNewToggleSymbol": true, "handleTableRowSize": true}, "customMeta": {}, "newDesignerType": "1", "floatModelRaws": []}}], "colSpan": 4, "toggleBorder": {"left": true, "top": true, "right": true}, "style": {"align": "middle|center"}}]], "repeatColCells": [[{"content": [{"type": "text", "value": "时间", "style": {"bold": true}}], "style": {"align": "middle|center"}}], [{"content": [{"type": "text", "value": "主\n要\n诊\n疗\n工\n作", "style": {"bold": true}}], "style": {"align": "middle|center"}}], [{"content": [{"type": "text", "value": "重\n要\n医\n嘱", "style": {"bold": true}}], "style": {"align": "middle|center"}}], [{"content": [{"type": "text", "value": "主要\n护理\n工作", "style": {"bold": true}}], "style": {"align": "middle|center"}}], [{"content": [{"type": "text", "value": "病情\n变异\n记录", "style": {"bold": true}}], "style": {"align": "middle|center"}}], [{"content": [{"type": "text", "value": "护士\n签名", "style": {"bold": true}}], "style": {"align": "middle|center"}}], [{"content": [{"type": "text", "value": "医师\n签名", "style": {"bold": true}}], "style": {"align": "middle|center"}}]], "contentCells": [[{"content": [{"type": "text", "value": "住院第1天", "style": {"bold": true}}], "style": {"align": "middle|center"}}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "field", "name": "nurseSign1"}], "style": {"align": "middle|center"}}, {"content": [{"type": "field", "name": "doctorSign1"}], "style": {"align": "middle|center"}}], [{"content": [{"type": "text", "value": "住院第2天至术前1天", "style": {"bold": true}}], "style": {"align": "middle|center"}}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "field", "name": "nurseSign1", "value": "data:image/gif;base64,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", "width": "200", "height": "50"}], "style": {"align": "middle|center"}}, {"content": [{"type": "field", "name": "doctorSign1"}], "style": {"align": "middle|center"}}], [{"content": [{"type": "text", "value": "住院第1~3天\n(手术日)", "style": {"bold": true}}], "style": {"align": "middle|center"}}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}], [{"content": [{"type": "text", "value": "住院第2~7天\n(术后第1~5天)", "style": {"bold": true}}], "style": {"align": "middle|center"}}, {"content": [{"type": "text", "value": "长期医嘱\n", "style": {"bold": true}}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "text", "value": "临时医嘱\n", "style": {"bold": true}}, {"type": "radio", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}], [{"content": [{"type": "text", "value": "住院第3~8天\n(出院日)", "style": {"bold": true}}], "style": {"align": "middle|center"}}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}], [{"content": [{"type": "text", "value": "住院第3~8天\n(出院日)", "style": {"bold": true}}], "style": {"align": "middle|center"}}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}], [{"content": [{"type": "text", "value": "住院第3~8天\n(出院日)", "style": {"bold": true}}], "style": {"align": "middle|center"}}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}], [{"content": [{"type": "text", "value": "住院第3~8天\n(出院日)", "style": {"bold": true}}], "style": {"align": "middle|center"}}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}, {"content": [{"type": "checkbox", "value": 0}, {"type": "text", "value": "询问病史及体格检查\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "完成病历书写\n"}, {"type": "checkbox", "value": 0}, {"type": "text", "value": "上级医师查房与术前评估\n"}]}]]}]