/**
 * 文本域相关逻辑混入
 * @type {{data(): {}, method: {}}}
 */

const chartSettingMixIn = {
  data() {
    return {
      chartType: "bar",
      xAxisData: "",
      yAxisData: [],
      showChartSetting: false,
    };
  },
  components: {},
  methods: {
    onUpdateChartType(val) {
      this.chartType = val;
    },
    showChartSettingWinow(clickInfo) {
      this.chartType = clickInfo.image.meta?.chartType || "bar";
      this.xAxisData = clickInfo.image.meta?.xAxisData || "";
      this.yAxisData = clickInfo.image.meta?.yAxisData || [];
      this.showChartSetting = true;
      const keys = this.getData(clickInfo);
      this.chartKeys = keys || [];
    },
    getData(clickInfo) {
      const fieldName = clickInfo.field?.name;
      if (fieldName) {
        let data = this.editor.event.emit("handleDataSet");
        if (data) {
          for (const key in data) {
            if (key === fieldName) {
              if (Array.isArray(data[key])) {
                if (data[key].length) {
                  return Object.keys(data[key][0]);
                }
              }
            }
          }
        }
      }
    },
    confirmChartEditingModal(data) {
      if (
        this.editor._curClickInfo &&
        this.editor._curClickInfo.field &&
        this.editor._curClickInfo.image.meta &&
        this.editor._curClickInfo.image.meta.name === "chart"
      ) {
        this.editor._curClickInfo.image.meta.chartType =
          data?.chartType || "bar";
        this.editor._curClickInfo.image.meta.xAxisData = data?.xAxisData || "";
        this.editor._curClickInfo.image.meta.yAxisData = data?.yAxisData || [];
      }
      this.showChartSetting = false;
      this.editor.focus();
    },
    closeChartEditingModal() {
      this.showChartSetting = false;
      this.editor.focus();
    },
  },
};
export default chartSettingMixIn;
