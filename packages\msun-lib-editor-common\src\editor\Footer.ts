import Page from "./Page";
import Editor from "./Editor";
import Table from "./Table";
import Row from "./Row";
import { system_variables } from "./Config";
import Renderer from "./Renderer";
import Cell from "./Cell";
import { isTable } from "./Utils";

/**
 * 页脚对象
 */
export default class Footer {
  page: Page;
  editor: Editor;
  children: (Table | Row)[];
  footer_cell!: Cell;
  // 页脚内容高度
  footer_height: number = 0;

  constructor (editor: Editor, page: Page) {
    this.page = page;
    this.editor = editor;
    this.children = this.editor.footer_cell.children;
    this.footer_cell = this.editor.footer_cell;
    if (this.children.length) {
      for (let i = 0; i < this.children.length; i++) {
        const child = this.children[i];
        if (isTable(child)) {
          this.footer_height += child.height;
        } else {
          const height = child.bottom - child.top;
          this.footer_height += height;
        }
      }
      // this.footer_height =
      //     this.children[this.children.length - 1].bottom - this.children[0].top;
    }
    // TODO 这块逻辑再捋捋
    const doc_config = editor.raw?.config || editor.config;
    if (doc_config && doc_config.footer_horizontal !== undefined) {
      editor.config.show_footer_line = !!editor.raw.config.footer_horizontal;
    }
    this.refreshPageIndex();
  }

  // 当前页 页脚占用的开始高度 跟 header_outer_bottom 类似 都是最终用的值
  get footer_outer_top (): number {
    const ori_footer_top = this.page.height - this.page.padding_bottom;
    const footer_outer_top =
      this.footer_top < ori_footer_top ? this.footer_top : ori_footer_top;
    const max_val = this.page.height / 1.5;
    return footer_outer_top < max_val ? max_val : footer_outer_top;
  }

  // 页脚内容top值
  get footer_top (): number {
    return this.page.height - this.footer_height - this.editor.config.footer_margin_bottom;
  }

  /**
   * 重新设置editor中数据top属性。否则针对其内容操作会出现各种问题（定位问题等）
   */
  refreshPageIndex () {
    // 保持原数据与复制数据top值一致
    let offset_top = this.footer_top;
    this.children.forEach((item, index) => {
      item.page_index = index;
      item.page_number = this.page.number;
      // 重置页脚行的高度值
      item.top = offset_top;
      offset_top += item.height;
    });
  }

  /**
   * 判断鼠标是否在页脚内
   * @param x
   * @param y
   */
  contain (x: number, y: number) {
    const content_top = this.page.height - this.page.footer.footer_outer_top;
    return (
      y < this.page.bottom &&
        y > this.page.bottom - content_top &&
        x > this.page.left &&
        x < this.page.right
    );
  }

  draw () {
    if (!this.editor.show_header_footer) {
      return;
    }
    Renderer.save();
    Renderer.translate(this.page.left, this.page.top);
    if(this.editor.is_edit_hf_mode){
      this.drawHeaderOperationBtn()
    }
    // 因为总页数在绘制时才能确定，所以替换信息方法放在此处
    this.children.forEach((item) => item.draw(this.editor));
    Renderer.restore();
    if (this.editor.config.show_footer_line) {
      this.drawHorizontal();
    }
  }

  /**
   * 当绘制的时候设置属性
   */
  initCellData () {
    this.footer_cell = this.editor.footer_cell.copy();
    this.children = this.footer_cell.children;
    // 重新设置数据属性值
    this.refreshPageIndex();
  }

  /**
   * 替换页脚信息
   */
  replacePageInfo () {
    const pageNumFields = this.editor.getFieldsByName(system_variables.page_number,this.footer_cell);
    let differenceValue = 0;
    if (this.editor.config.startPageNumber || this.editor.config.startPageNumber === 0) {
      differenceValue = this.editor.config.startPageNumber - this.editor.pages[0].number;
    }
    if (pageNumFields.length) {
      pageNumFields.forEach((field) => {
        field.replaceText((this.page.number + differenceValue).toString());
      });
    }
    const pageCountFields = this.editor.getFieldsByName(system_variables.page_count,this.footer_cell);
    if (pageCountFields.length) {
      pageCountFields.forEach((field) => {
        field.replaceText(this.editor.pages.length.toString());
      });
    }
    this.refreshPageIndex();
  }

  /**
   * 绘制页脚上横线
   */
  drawHorizontal () {
    Renderer.save();
    Renderer.translate(this.page.left, this.page.top);
    Renderer.draw_horizontal(
      this.footer_top,
      this.editor.config.page_padding_left,
      this.page.width - this.editor.config.page_padding_right,
      "#000"
    );
    Renderer.restore();
  }
  /**
   * 绘制页眉页脚删除增加按钮
   */
  drawHeaderOperationBtn(){
    Renderer.draw_rectangle(0,this.footer_outer_top+5,12,2,"#000")
    if(!this.editor.config.show_footer_line){
      Renderer.draw_rectangle(5,this.footer_outer_top,2,12,"#000")
    }
    Renderer.draw_stroke_rect(0,this.footer_outer_top,12,12,"#000")
  }
}
