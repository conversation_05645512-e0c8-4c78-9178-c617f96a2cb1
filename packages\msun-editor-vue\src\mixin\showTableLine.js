const tableLineMixin = {
  data() {
    return {
      show_table_line: false,
    };
  },
  methods: {
    // 打开边框设置div
    showTableLine(show) {
      this.show_table_line = show;
      if (show) {
        this.$nextTick(() => {
          const height = this.$refs.tableLine.$el.offsetHeight;
          const width = this.$refs.tableLine.$el.offsetWidth;
          this.$refs.tableLine.changeTableLinePosition(
            this.rightMenuEvent,
            height,
            width
          );
        });
      }
    },
    toggleLineShowHide(data) {
      this.editor.cotrolTableLine(data);
    },
  },
};
export default tableLineMixin;
