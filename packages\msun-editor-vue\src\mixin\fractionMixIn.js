/**
 * 表格相关方法
 */
const fractionMixIn = {
  data() {
    return {
      isShowFraction: false,
      fraction: "",
      fractionInt: "",
      fractionNumerator: "",
      fractionDenominator: "",
    };
  },
  methods: {
    openFraction(fraction) {
      this.isShowFraction = true;
      if (fraction) {
        this.fraction = fraction;
        const int = fraction.int;
        this.fractionInt =
          int === undefined || int === "undefined" ? "" : fraction.int;
        this.fractionNumerator = fraction.numerator;
        this.fractionDenominator = fraction.denominator;
      }
    },
    closeFraction() {
      this.isShowFraction = false;
      this.fraction = "";
      this.fractionInt = "";
      this.fractionNumerator = "";
      this.fractionDenominator = "";
    },
    confirmFraction() {
      if (this.fraction) {
        this.fraction.update({
          int: this.fractionInt,
          numerator: this.fractionNumerator,
          denominator: this.fractionDenominator,
        });
      } else {
        this.instance.editor.insertFraction({
          int: this.fractionInt,
          numerator: this.fractionNumerator,
          denominator: this.fractionDenominator,
        });
      }
      this.closeFraction();
    },
  },
};
export default fractionMixIn;
