/**
 *页眉对象
 */
import Page from "./Page";
import Editor from "./Editor";
import Table from "./Table";
import Row from "./Row";
import Renderer from "./Renderer";
import Cell from "./Cell";
import { system_variables } from "./Config";
import { createCustomWidthCharToField } from "./Helper";
import { isEmptyObj } from "./Utils";
import Font from "./Font";

export default class Header {
  page: Page;

  children!: (Table | Row)[];

  header_cell!: Cell;

  editor: Editor;

  constructor (editor: Editor, page: Page) {
    this.page = page;
    this.editor = editor;
    this.children = this.editor.header_cell.children;
    this.header_cell = this.editor.header_cell;
    this.refreshPageIndex();
  }

  contain (x: number, y: number) {
    const content_top = this.header_outer_bottom;
    return (
      y > this.page.top &&
      y < content_top + this.page.top &&
      x > this.page.left &&
      x < this.page.right
    );
  }

  // 页眉底部边界 距离当前页顶部边界的距离(就是页眉那条线到白色区域最顶部的距离)
  get header_bottom () {
    if (this.children.length) {
      return this.children[this.children.length - 1].bottom;
    } else {
      return this.editor.config.header_margin_top;
    }
  }

  // 页眉底部边界距离当前页顶部边界的距离 根据配置的header_bottom计算得出 考虑了 配置的header_bottom有可能比当前页的padding_top还要小(不合理) 取padding_top 还有页眉高度不能无限往下走，这里除以了2.5 就是比页面的一半还要少一点 有条虚线标记了位置
  // 所以他的高度 不会超过虚线的高度
  get header_outer_bottom (): number {
    const header_outer_bottom =
      this.header_bottom > this.page.padding_top
        ? this.header_bottom
        : this.page.padding_top;
    const max_val = this.page.height / 2.5;
    return header_outer_bottom > max_val ? max_val : header_outer_bottom;
  }

  draw () {
    if (!this.editor.show_header_footer) {
      return;
    }
    Renderer.save();
    Renderer.translate(this.page.left, this.page.top);
    if(this.editor.is_edit_hf_mode){
      this.drawHeaderOperationBtn()
    }
    this.children.forEach((item) => {
      item.draw(this.editor);
    });
    Renderer.restore();
    if (this.editor.config.show_header_line) {
      this.drawHorizontal();
    }
  }

  /**
   * 刷新对应的页面信息
   */
  refreshPageIndex () {
    // 保持原数据与复制数据top值一致
    let offset_top = this.editor.config.header_margin_top;
    // 保持原数据与复制数据top值一致
    this.children.forEach((item, index) => {
      item.page_index = index;
      item.page_number = this.page.number;
      // 重置页眉行的高度值
      item.top = offset_top;
      offset_top += item.height;
    });
  }

  /**
   * 初始化应用数据
   */
  initCellData () {
    this.header_cell = this.header_cell.copy(null,false);
    this.children = this.header_cell.children;
    this.refreshPageIndex();
  }

  /**
   * 替换分组中不同的页眉信息
   */
  replaceGroupDiffInfo (fieldMaxLength:any) {
    const groups = this.page.groups;
    let need_group_header_info = {};
    if (this.page.editor.config.group_relate_header_type === "after") {
      if (groups.length) {
        // 取当前页第一个分组，如果没有页眉信息，则按照原来的信息展示(主要为了兼容老数据)
        const group = groups[0];
        if (group.header_info) {
          need_group_header_info = group.header_info;
        }
      }
    } else {
      for (let i = 0; i < groups.length; i++) {
        const group = groups[i];
        if (!group.header_info) continue;
        need_group_header_info = group.header_info;
      }
    }
    // 根据分组信息处理页眉
    const res_info = this.handleHeaderCellByGroup(need_group_header_info);
    // 说明没有不同，则不继续以下逻辑
    if (!res_info.has_diff) {
      return;
    }
    // 修改之前先进行复制
    this.initCellData();
    if (!isEmptyObj(this.editor.header_diff_info)) {
      const fields = this.editor.getAllFields(this.header_cell);
      for (let i = 0; i < fields.length; i++) {
        const field = fields[i];
        let field_key = field.id;
        let replaceVal = this.editor.header_diff_info[field_key];
        if (!replaceVal) {
          field_key = field.name;
          replaceVal = this.editor.header_diff_info[field_key];
        }
        if (replaceVal) {
          // 规则定为与分组关联的文本域自动设置为只读，否则进行编辑删除时会出现bug
          field.readonly = 1;
          if (res_info.diff_info[field_key] /* replaceVal !== field.text */) {
            field.clear();
            const chars = field.textToFieldCharacter(replaceVal);
            const { width } = Renderer.measure(new Font(field.style), replaceVal, this.editor);
            if (width < fieldMaxLength[field_key].width) {
              chars.push(createCustomWidthCharToField(field, replaceVal, fieldMaxLength[field_key].value));
            }
            field.children = chars;
            field.updateChildren();
          }
        }
      }
      this.refreshPageIndex();
    }
  }

  /**
   * 根据分组信息复制页眉
   * @param need_group_header_info
   */
  handleHeaderCellByGroup (need_group_header_info:any) {
    let has_diff = false;
    const diff_info:any = {};
    const header_diff_info = this.editor.header_diff_info;
    for (const item in need_group_header_info) {
      if (header_diff_info[item] !== need_group_header_info[item]) {
        has_diff = true;
        diff_info[item] = true;
      }
    }
    Object.assign(header_diff_info, need_group_header_info);
    this.header_cell = this.page.pre_page.header.header_cell;
    this.children = this.header_cell.children;
    return {
      has_diff, diff_info
    };
  }

  /**
   * 替换页眉信息
   */
  replacePageInfo () {
    const pageNumFields = this.editor.getFieldsByName(system_variables.page_number,this.header_cell);
    if (pageNumFields.length) {
      let differenceValue = 0;
      if (this.editor.config.startPageNumber || this.editor.config.startPageNumber === 0) {
        differenceValue = this.editor.config.startPageNumber - this.editor.pages[0].number;
      }
      pageNumFields.forEach((field) => {
        field.replaceText((this.page.number + differenceValue).toString());
      });
    }
    const pageCountFields = this.editor.getFieldsByName(system_variables.page_count,this.header_cell);
    if (pageCountFields.length) {
      pageCountFields.forEach((field) => {
        field.replaceText(this.editor.pages.length.toString());
      });
    }
    this.refreshPageIndex();
  }

  /**
   * 绘制页眉下划线
   */
  drawHorizontal () {
    Renderer.save();
    Renderer.translate(this.page.left, this.page.top);
    Renderer.draw_horizontal(
      this.header_bottom,
      this.editor.config.page_padding_left,
      this.page.width - this.editor.config.page_padding_right,
      "#000"
    );
    Renderer.restore();
  }

  drawHeaderOperationBtn(){
    Renderer.draw_rectangle(0,this.header_bottom-7,12,2,"#000")
    if(!this.editor.config.show_header_line){
      Renderer.draw_rectangle(5,this.header_bottom-12,2,12,"#000")
    }
    Renderer.draw_stroke_rect(0,this.header_bottom-12,12,12,"#000")
  }
}
