const insertBtnMixIn = {
  data() {
    return {
      insertBtn: [
        {
          type: "textIcon",
          icon: "icon-biaoge",
          title: "插入表格",
          func: this.handleInsertTableBtnClick,
        },
        {
          type: "textIcon",
          icon: "icon-shuipingxian",
          title: "水平线",
          func: this.insertLine,
        },
        {
          type: "textIcon",
          icon: "icon-14xingzhuangjiehe",
          title: "形状",
          func: this.shapeMode,
        },
        {
          type: "textIcon",
          icon: "icon-shuiyin",
          title: "水印",
          func: this.watermarkMode,
        },
        {
          type: "textIcon",
          icon: "icon-wenbenyu1",
          title: "文本域",
          func: this.insertField,
        },
        {
          type: "groupMenu",
          icon: "icon-xia<PERSON>ujian",
          title: "小组件",
          showType: "widget",
          children: [
            // 先注释， 都使用自定义选择框实现
            // {
            //   type: "textIcon",
            //   icon: "icon-radio-checked",
            //   title: "单选框",
            //   func: this.insertRadio,
            // },
            // {
            //   type: "textIcon",
            //   icon: "icon-caidan",
            //   title: "复选框",
            //   func: this.insertCheckbox,
            // },
            {
              type: "textIcon",
              icon: "icon-ceju",
              title: "卡尺",
              func: this.showCaliperModal,
            },
          ],
        },
        {
          type: "textIcon",
          icon: "icon-caidan",
          title: "单选/多选框",
          func: this.insertChoiceBox,
        },
        {
          type: "textIcon",
          icon: "icon-biaodashidingyi",
          title: "医学表达式",
          func: this.insertFormula,
        },
        {
          type: "textIcon",
          icon: "icon-biaodashidingyi",
          title: "计算公式",
          func: this.openMedicalCalcFormula,
        },
        {
          type: "textIcon",
          icon: "icon-charutupian",
          title: "本地图片",
          func: this.insertLocalImage,
        },
        {
          type: "textIcon",
          icon: "icon-fenzu",
          title: "本地模板",
          func: this.insertLocalTemplate,
        },
        {
          type: "textIcon",
          icon: "icon-fenyefu1",
          title: "分页符",
          func: this.insertPageBreak,
        },
        {
          type: "textIcon",
          icon: "icon-fenzu",
          title: "分组",
          func: this.insertGroup,
        },
      ],
    };
  },
  methods: {
    insertLocalTemplate() {
      this.instance.openFile("insert");
    },
    // 插入分组
    insertGroup() {
      this.instance.showGroupModal();
    },
    // 插入输入域
    insertField() {
      this.instance.insertField();
    },
    // 形状编辑模式
    shapeMode() {
      this.instance.shapeMode();
    },
    watermarkMode() {
      this.instance.waterMarkModel();
    },
    // 插入水平线
    insertLine() {
      this.instance.openLineModal();
    },
    // 插入单选框
    insertRadio() {
      this.instance.editor.insertSimpleWidget("radio");
    },
    // 插入复选框
    insertCheckbox() {
      this.instance.editor.insertSimpleWidget("checkbox");
    },
    // 插入卡尺
    showCaliperModal() {
      this.instance.showCaliperModal();
    },
    // 自定义选择框
    insertChoiceBox() {
      const field = this.instance.editor.selection.getFocusField();
      const choicedField = field ? field.parent_box : null;
      if (!choicedField || choicedField.type !== "box") {
        this.instance.showChoiceModal("checkbox");
      } else {
        this.$editor.info("选择框文本域中不允许插入");
      }
    },
    // 插入医学表达式
    insertFormula() {
      this.instance.insertFormula();
    },
    // 医学计算公式
    openMedicalCalcFormula() {
      this.instance.openMedicalCalcFormula();
    },
    // 插入图片
    insertLocalImage() {
      this.instance.insertLocalImage();
    },
    // 分页
    insertPageBreak() {
      this.instance.editor.insertPageBreak();
    },
  },
};
export default insertBtnMixIn;
