import Box from "./Box";
import Button from "./Button";
import Character from "./Character";
import Editor from "./Editor";
import Image from "./Image";
import Widget from "./Widget";
import XField from "./XField";
import Line from "./line";

// 通用的枚举，接口，类型定义
export type alignType = "left" | "center" | "right" | "dispersed" | "docuAlign" | "top" | "middle" | "bottom";
export type insertType = "init" | "input" | "ime_input" | "transit"; // transit为过渡使用，不需要执行update render
export type pageDirection = "horizontal" | "vertical";
export type widgetType = "checkbox" | "radio" | "caliper";
export type pageType = "A4" | "A5" | "B5" | "B5(JIS)" | "16K" | "16K(BIG)" | "custom";
export type DirectionType = "up" | "right" | "down" | "left";

export const ERROR_FLAG = "#";

export type M2WParameter = {
  editor: Editor, 
  header?: boolean, // 是否转换 header
  root?: boolean, // 是否转换 root
  footer?: boolean, // 是否转换 footer
  tableType?: number // 表格类型 是横向还是纵向
}

export enum SourceOfAddRowOrCol {
  INVOKE = 0, // 接口调用
  RIGHT_CLICK = 1, // 右键菜单
  TAB = 2, // 按了 tab 键
  ENTER = 3, // 按下了 enter 键
}

export type CommentConfig = {
  wordUnselectedBgColor: string,
  wordSelectedBgColor: string,
  listWidth: number,
  listBgColor: string,
  listTitleColor: string,
  listTitleNumColor: string,
  listTitleCrossColor: string,
  listTitleSwitchColor: string,
  listItemBgColor: string,
  listItemSelectedBorderColor?: string,
  hideCloseBtn: boolean,
  hideSwitch: boolean,
  hideDeleteBtn: boolean,
  hideReplaceBtn: boolean,
  hideDate: boolean,
  title: string,
  defaultAllOpen: boolean
}


export type ElementInParagraph =
  | Character
  | Widget
  | Image
  | Line
  | Box
  | Button;
export enum RowLineType {
  VOID = 0,
  SOLID = 1,
  NO_MERGE_CELL = 2,
}

export type RowLineTypeExplain = {
  excludesEmpty: boolean, // 不包括没有行的部分 那部分不绘制线
  includesTable: boolean
}

export type CellUpdateChildrenParameter = {
  startRowIndex: number;
  updateRowCount: number;
  updateParagraphIndex: number;
  callTypesetting: boolean;
};

export type TransformDataType = {
  images: Image[];
  fields: XField[];
};

export type HightLightParameter = {
  editor: Editor;
  keywords: { upset: number; offset: number; name: string }[];
  groupId?: string;
  highLightColor?: string;
};

export enum Direction {
  left,
  right,
  up,
  down
}

// 字符上的 mark 标记意思
export enum MarkType {
  NO_ZOOM = "ignore" // pacs 叫 ignore 代表忽略放大缩小
}

// 固定/增加/减少
export enum IncreaseType {
  fixed,
  increase,
  decrease
}

export enum FieldType {
  normal = "normal",
  label = "label",
  select = "select",
  date = "date",
  number = "number",
  box = "box"
}

// pacs 图片排版传参数据类型
export interface PacsLayoutParameter {
  src: string;
  meta?: Object;
  serialNum?: string;
  selectField?: XField;
  defaultCode?:string
}
