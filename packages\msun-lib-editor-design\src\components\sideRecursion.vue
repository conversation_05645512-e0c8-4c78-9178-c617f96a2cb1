<template>
  <div class="side-card-info">
    <div style="display: flex; flex-wrap: wrap">
      <div :class="localData.type === 'fun' ? 'fun-title' : 'side-card-title'">
        {{ localData.name }}：
        <a-tooltip
          v-if="localData.tip"
          placement="top"
          :title="localData.tip"
          :getPopupContainer="
            (triggerNode) => {
              return triggerNode.parentNode || document.body;
            }
          "
        >
          <a-icon type="question-circle" class="card-item-title-icon" />
        </a-tooltip>
      </div>
      <div v-if="localData.type === 'table'">
        <a-button
          type="default"
          size="small"
          style="margin-bottom: 5px; margin-left: 78px"
          @click="handleAdd()"
          >添加</a-button
        >
      </div>
    </div>
    <div v-if="localData.type === 'string'" class="side-card-element">
      <a-input
        v-model="localData.value"
        :title="localData.value"
        :placeholder="
          localData.placeholder ? localData.placeholder : localData.name
        "
      ></a-input>
    </div>
    <div v-else-if="localData.type === 'number'">
      <a-input-number
        :precision="0"
        :min="0"
        :title="String(localData.value)"
        v-model="localData.value"
        :disabled="localData.disabled"
        @change="handleInputChange(localData.value, localData.key)"
      />
    </div>

    <div v-else-if="localData.type === 'select'">
      <a-select
        dropdownClassName="xeditor-input-up"
        class="side-card-element"
        v-model="selectOption"
        @change="selectChange"
      >
        <a-select-option
          v-for="(eleSelect, k) in localData.children"
          :key="k"
          :value="eleSelect.type"
          :title="eleSelect.name"
          >{{ eleSelect.name }}</a-select-option
        >
      </a-select>
    </div>
    <div v-else-if="localData.type === 'fun'">
      <div
        @click="clickButton(localData)"
        style="width: 35px; height: 20px; cursor: pointer"
      >
        &nbsp;&nbsp;
        <icon-common
          icon="icon-bianji"
          style="width: 18px; height: 18px"
        ></icon-common>
      </div>
    </div>
    <div v-else-if="localData.type === 'table'">
      <a-table
        ref="selectTable"
        :columns="localData.columns"
        :data-source="localData.data"
        bordered
        :pagination="false"
        :scroll="{
          scrollToFirstRowOnChange: true,
          y: 108,
        }"
      >
        <template
          v-for="col in ['text', 'name', 'value']"
          :slot="col"
          slot-scope="text, record"
        >
          <template>
            <div :key="col">
              <a-input
                size="small"
                :title="text.value"
                :value="text.value"
                @change="(e) => handleChange(e.target.value, record.key, col)"
              />
            </div>
          </template>
        </template>
        <template slot="operation" slot-scope="text, record">
          <div class="editable-row-operations">
            <a @click="() => deleteRow(record.key)">删除</a>
          </div>
        </template>
      </a-table>
    </div>
  </div>
</template>

<script>
import iconCommon from "./iconCommon.vue";
export default {
  name: "sideRecursion",
  props: {
    sideData: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
      default: "field",
    },
  },

  components: { iconCommon },
  data() {
    return {
      localData: this.sideData,
      selectOption: "",
    };
  },
  watch: {
    sideData: {
      handler(val) {
        this.localData = val;
        this.selectOption = "";
        if (this.localData.type === "select") {
          for (let i = 0; i < this.localData.children.length; i++) {
            const data = this.localData.children[i];
            if (JSON.parse(data.checked)) {
              this.selectOption = data.type;
            }
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {},
  methods: {
    uuid() {
      const uuid = "xxxxxxxx".replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      });
      return uuid;
    },
    handleAdd() {
      const new_source_list = {
        key: this.uuid(),
        text: { type: "string", value: "" },
        name: { type: "string", value: "" },
        value: { type: "string", value: null },
      };
      this.localData.data.push(new_source_list);
    },
    handleChange(val, key, col) {
      const change = this.localData.data.filter((item) => item.key === key);
      change[0][col].value = val;
    },
    deleteRow(key) {
      this.localData.data = this.localData.data.filter(
        (item) => item.key !== key
      );
    },
    clickButton(info) {
      const element =
        this.$parent.$parent.editor.editor.focusElement[this.type];
      info.fun(element);
    },
    handleInputChange(e, key) {
      this.$emit("inputChange", { e, key });
    },
    selectChange(val) {
      for (let i = 0; i < this.localData.children.length; i++) {
        const child = this.localData.children[i];
        child.checked = false;
        if (child.type === val) {
          child.checked = true;
          this.connectMenu(child.connect);
        }
      }
    },
    connectMenu(name) {
      if (name) {
        this.$emit("connectMenu", name);
      }
    },
  },
};
</script>
<style>
.ant-table-body {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d3e3fd;
    border-radius: 6px;
  }
  /* 鼠标悬停时滑块样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #a8c7fa;
  }
  &::-webkit-scrollbar-track {
    background-color: white;
    -webkit-box-shadow: inset 0 0 4px rgba(100, 100, 100, 0.01);
  }
}
</style>
<style scoped lang="less">
.side-card-info {
  display: flex;
  flex-wrap: wrap;
  padding: 8px 5px;
  line-height: 25px;
}

.ant-input {
  height: 25px;
  line-height: 25px;
}

.ant-select {
  font-size: 12px;
}

.ant-input-number {
  height: 25px;
  line-height: 25px;
  width: 112px;
}
.fun-title {
  /*width: 70px;*/
}
.side-card-element {
  width: 112px;
  text-align: justify !important;
}

.side-body /deep/.ant-input-number-input {
  height: 25px;
  line-height: 25px;
}

.side-body/deep/.ant-select-selection--single {
  height: 25px;
}

.side-body/deep/.ant-select-selection__rendered {
  line-height: 25px;
}

.side-body/deep/ .ant-table-tbody > tr > td {
  padding: 5px;
}

.side-body/deep/.ant-table-thead > tr > th {
  padding: 5px;
}

.side-body/deep/.ant-table-hide-scrollbar {
  scrollbar-color: inherit;
}

.side-card-title {
  width: 95px;
  font-size: 13px;
  background-color: white;
  display: flex;
}
.card-item-title-icon {
  color: #f8b600;
  font-size: 14px;
  font-weight: bold;
  background-color: white;
  cursor: pointer;
  margin-top: 6px;
}
</style>
