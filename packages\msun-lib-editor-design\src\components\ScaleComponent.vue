<template>
  <div class="scale-select-component">
    <!-- 拖拽缩放editor的组件 -->
    <div
      class="scale-select-icon scale-select-button"
      @mousedown.prevent.stop="minuteScale"
    >
      <icon-common icon="icon-icon-zoom-out" />
    </div>
    <a-slider
      class="scale-select-range"
      :max="12"
      :min="1"
      v-model="scaleValue"
      @change="changeScale"
      :tip-formatter="formatter"
    >
    </a-slider>
    <div
      class="scale-select-icon scale-select-button"
      @mousedown.prevent.stop="addScale"
    >
      <icon-common icon="icon-icon-zoom-in" />
    </div>
    <div class="scale-select-icon">{{ scaleValue * 25 }}%</div>
  </div>
</template>

<script>
import iconCommon from "./iconCommon.vue";
export default {
  name: "scaleComponent",
  components: { iconCommon },
  props: {
    // 默认的缩放值
    editorScale: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      scaleValue: 4,
    };
  },
  watch: {
    editorScale(val) {
      this.scaleValue = Math.round(val / 0.25);
    },
  },
  methods: {
    formatter(value) {
      return value * 25 + "%";
    },
    changeScale(value) {
      this.$emit("changeScale", (value * 25) / 100);
    },
    addScale() {
      if (this.scaleValue < 12) {
        this.scaleValue++;
        this.changeScale(this.scaleValue);
      }
    },
    minuteScale() {
      if (this.scaleValue > 1) {
        this.scaleValue--;
        this.changeScale(this.scaleValue);
      }
    },
  },
};
</script>

<style lang="less">
.scale-select-component {
  height: 100%;
  padding-right: 10px;
  line-height: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .scale-select-icon {
    flex: 0 0 10px;
    text-align: center;
  }
  .scale-select-button {
    cursor: pointer;
  }
  .scale-select-range {
    flex: 1 1 90%;
    padding-top: 2px;
  }
  .ant-slider-handle {
    height: 15px;
    width: 10px;
    border-radius: 3px;
    top: 1px;
  }
  .ant-slider-step {
    background-color: #91d5ff;
  }
}
</style>
