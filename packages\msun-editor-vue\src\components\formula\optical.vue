<template>
  <div class="optical_otical">
    <div class="col_otical">
      <a-input type="text" class="input_otical" v-model="meta.params[0]" />
      <a-input type="text" class="input_otical" v-model="meta.params[1]" />
      <a-input type="text" class="input_otical" v-model="meta.params[2]" />
    </div>
    <div class="col_otical">
      <a-input type="text" class="input_otical" v-model="meta.params[3]" />
      <a-input type="text" class="input_otical" v-model="meta.params[4]" />
      <a-input type="text" class="input_otical" v-model="meta.params[5]" />
    </div>
    <div class="col_otical">
      <a-input type="text" class="input_otical" v-model="meta.params[6]" />
      <a-input type="text" class="input_otical" v-model="meta.params[7]" />
      <a-input type="text" class="input_otical" v-model="meta.params[8]" />
    </div>
  </div>
</template>

<script>
export default {
  name: "optical",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style scoped>
.optical_otical {
  width: 100%;
  height: 100%;
  display: flex;
}

.col_otical {
  width: 166.5px;
}

.input_otical {
  margin-top: 10px;
  margin-bottom: 10px;
  width: 150px;
}
</style>
