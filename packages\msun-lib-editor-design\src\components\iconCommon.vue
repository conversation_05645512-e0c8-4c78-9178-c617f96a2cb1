<template>
  <svg :style="iconStyle" aria-hidden="true">
    <title>{{ title }}</title>
    <use :xlink:href="`#` + icon"></use>
  </svg>
</template>

<script>
export default {
  props: {
    icon: String,
    iconStyle: {
      type: Object,
      default: () => ({
        width: "14px",
        height: "14px",
        fontSize: "14px",
        verticalAlign: "middle",
        fill: "rgba(0, 0, 0, 0.4)",
        overflow: "hidden",
      }),
    },
    title: {
      type: String,
      default: "",
    },
  },
};
</script>

<style lang="less" scoped></style>
