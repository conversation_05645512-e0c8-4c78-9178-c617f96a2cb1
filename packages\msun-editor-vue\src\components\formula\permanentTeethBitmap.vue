<template>
  <div class="permanentTeethBitmap_permanentTeethBitmap">
    <div class="bitmap-5">上颔</div>
    <div class="bitmap-1">
      <div
        v-for="(item, i) in nameList"
        :key="i"
        :style="{ left: left[i] }"
        class="bitmap2-list"
      >
        {{ item }}
      </div>
    </div>
    <div class="bitmap-2">
      <div
        v-for="(item, i) in meta.params.upBitmapData"
        :key="i"
        :style="{ left: left[i] }"
        :class="i % 2 === 0 ? 'tooth-row' : 'tooth-row-click'"
      >
        <div v-if="i === 0" class="font-side">牙面</div>
        <div v-else-if="i === nameList.length - 1"></div>
        <div v-else>
          <div
            v-for="(ite, index) in item.surface"
            :key="index"
            :class="ite.click ? 'click-tooth-button' : 'tooth-button'"
            @click="choiceButton(ite, i, 1)"
          >
            {{ ite.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="line_permanentTeethBitmap"></div>
    <div class="bitmap-3">
      <div
        v-for="(item, i) in nameList"
        :key="i"
        :style="{ left: left[i] }"
        :class="i % 2 === 0 ? 'tooth-row' : 'tooth-row-click'"
      >
        <div v-if="i === 0" class="bitmap-3-font">右</div>
        <div v-else-if="i === nameList.length - 1" class="bitmap-3-font">
          左
        </div>
        <div v-else>
          <div
            :class="
              meta.params.upToothList[i].click
                ? 'click-tooth-button'
                : 'tooth-button'
            "
            @click="choiceButton(meta.params.upToothList[i], i, 3)"
          >
            {{ bitmapMeta.upToothList[i].name }}
          </div>
          <div
            :class="
              meta.params.downToothList[i].click
                ? 'click-tooth-button'
                : 'tooth-button'
            "
            @click="choiceButton(meta.params.downToothList[i], i, 4)"
          >
            {{ bitmapMeta.downToothList[i].name }}
          </div>
        </div>
      </div>
    </div>
    <div class="line_permanentTeethBitmap"></div>
    <div class="bitmap-2">
      <div
        v-for="(item, i) in meta.params.downBitmapData"
        :key="i"
        :style="{ left: left[i] }"
        :class="i % 2 === 0 ? 'tooth-row' : 'tooth-row-click'"
      >
        <div v-if="i === 0" class="font-side">牙面</div>
        <div v-else-if="i === nameList.length - 1"></div>
        <div v-else>
          <div
            v-for="(ite, index) in item.surface"
            :key="index"
            :class="ite.click ? 'click-tooth-button' : 'tooth-button'"
            @click="choiceButton(ite, i, 2)"
          >
            {{ ite.name }}
          </div>
        </div>
      </div>
    </div>
    <div class="bitmap-4">下颔</div>
  </div>
</template>
<script>
// 这个哪里重复了
export default {
  name: "permanentTeethBitmap",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  // 这个哪里重复了
  data() {
    return {
      left: [
        "20px",
        "50px",
        "75px",
        "100px",
        "125px",
        "150px",
        "175px",
        "200px",
        "225px",
        "250px",
        "275px",
        "300px",
        "325px",
        "350px",
        "375px",
        "400px",
        "425px",
        "450px",
      ],
      nameList: [
        "",
        "第三磨牙",
        "第二磨牙",
        "第一磨牙",
        "第二前磨牙",
        "第一前磨牙",
        "尖牙",
        "侧切牙",
        "中切牙",
        "中切牙",
        "侧切牙",
        "尖牙",
        "第一前磨牙",
        "第二前磨牙",
        "第一磨牙",
        "第二磨牙",
        "第三磨牙",
        "",
      ],

      bitmapMeta: {
        upToothList: [
          null,
          { name: 8, click: false },
          { name: 7, click: false },
          { name: 6, click: false },
          { name: 5, click: false },
          { name: 4, click: false },
          { name: 3, click: false },
          { name: 2, click: false },
          { name: 1, click: false },
          { name: 1, click: false },
          { name: 2, click: false },
          { name: 3, click: false },
          { name: 4, click: false },
          { name: 5, click: false },
          { name: 6, click: false },
          { name: 7, click: false },
          { name: 8, click: false },
          null,
        ],
        downToothList: [
          null,
          { name: 8, click: false },
          { name: 7, click: false },
          { name: 6, click: false },
          { name: 5, click: false },
          { name: 4, click: false },
          { name: 3, click: false },
          { name: 2, click: false },
          { name: 1, click: false },
          { name: 1, click: false },
          { name: 2, click: false },
          { name: 3, click: false },
          { name: 4, click: false },
          { name: 5, click: false },
          { name: 6, click: false },
          { name: 7, click: false },
          { name: 8, click: false },
          null,
        ],
        upBitmapData: [
          "",
          {
            name: "第三磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第二磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第一磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第二前磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第一前磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "尖牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "侧切牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "中切牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "中切牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "侧切牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "尖牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第一前磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第二前磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第一磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第二磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          {
            name: "第三磨牙",
            surface: [
              { name: "P", click: false },
              { name: "L", click: false },
              { name: "B", click: false },
              { name: "D", click: false },
              { name: "O", click: false },
              { name: "M", click: false },
            ],
          },
          "",
        ],
        downBitmapData: [
          "",
          {
            name: "第三磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第二磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第一磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第二前磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第一前磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "尖牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "侧切牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "中切牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "中切牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "侧切牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "尖牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第一前磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第二前磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第一磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第二磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          {
            name: "第三磨牙",
            surface: [
              { name: "M", click: false },
              { name: "O", click: false },
              { name: "D", click: false },
              { name: "B", click: false },
              { name: "L", click: false },
              { name: "P", click: false },
            ],
          },
          "",
        ],
      },
    };
  },
  watch: {
    meta: {
      handler(e) {
        if (e.params.upToothList) {
          this.bitmapMeta = JSON.parse(JSON.stringify(e.params));
        } else {
          e.params = JSON.parse(JSON.stringify(this.bitmapMeta));
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    choiceButton(item, i, num) {
      if (num) {
        item.click = !item.click;
        if (num === 1 && item.click) {
          this.meta.params.upToothList[i].click = true;
        } else if (num === 2 && item.click) {
          this.meta.params.downToothList[i].click = true;
        } else if (num === 3 && !item.click) {
          this.meta.params.upBitmapData[i].surface.forEach((e) => {
            e.click = false;
          });
        } else if (num === 4 && !item.click) {
          this.meta.params.downBitmapData[i].surface.forEach((e) => {
            e.click = false;
          });
        }
      }
    },
  },
};
</script>
<style scoped>
.permanentTeethBitmap_permanentTeethBitmap {
  height: 500px;
  width: 500px;
  color: #000;
}
.bitmap2-list {
  position: absolute;
  width: 25px;
  text-align: center;
  bottom: 0;
}
.bitmap3-list {
  position: absolute;
  width: 25px;
  bottom: 27px;
  text-align: center;
}
.bitmap-1 {
  height: 100px;
  width: 100%;
  display: flex;
  position: relative;
}
.bitmap-2 {
  width: 100%;
  height: 140px;
  display: flex;
  position: relative;
}
.tooth-button {
  border: 1px solid #000;
  color: #000;
  font-weight: 400;
}
.click-tooth-button {
  border: 1px solid #000;
  color: #000;
  font-weight: 400;
  background-color: rgb(0, 120, 215);
}
.tooth-row-click {
  background-color: rgb(215, 228, 242);
  position: absolute;
  width: 25px;
  text-align: center;
  bottom: 0;
}
.tooth-row {
  background-color: white;
  position: absolute;
  width: 25px;
  text-align: center;
  bottom: 0;
}
.font-side {
  position: absolute;
  top: -90px;
}
.line_permanentTeethBitmap {
  height: 1px;
  background-color: #000;
  margin: 5px 20px 5px 20px;
}
.bitmap-3 {
  width: 100%;
  height: 46px;
  display: flex;
  position: relative;
}
.bitmap-3-font {
  position: absolute;
  bottom: 14px;
  left: 5px;
}
.bitmap-4 {
  height: 40px;
  width: 100%;
  text-align: center;
  margin-top: 20px;
  font-size: 16px;
}
.bitmap-5 {
  height: 40px;
  width: 100%;
  position: absolute;
  margin-top: -5px;
  left: 248px;
  font-size: 16px;
}
</style>
