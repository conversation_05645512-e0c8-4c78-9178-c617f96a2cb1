<template>
  <div class="diseasedLowerTooth">
    <div class="editor_row_x">
      <a-input
        type="text"
        class="editor_input_left_x"
        v-model="meta.params[0]"
      />
    </div>
    <div class="editor_row_x">
      <a-input
        type="text"
        class="editor_input_top_x"
        v-model="meta.params[1]"
      />
      <div class="editor_horizontal_x"></div>
      <a-input
        type="text"
        class="editor_input_bottom_x"
        v-model="meta.params[2]"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "diseasedLowerTooth",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style scoped>
.diseasedLowerTooth {
  width: 100%;
  height: 100%;
  display: flex;
}
.editor_row_x {
  text-align: center;
  height: 150px;
  margin-left: 20px;
}
.editor_horizontal_x {
  height: 5px;
  width: 190px;
  margin-left: 10px;
  margin-top: 35px;
  background-color: black;
}
.editor_input_left_x {
  margin-top: 55px;
}
.editor_input_top_x {
  margin-left: 10px;
}
.editor_input_bottom_x {
  margin-top: 30px;
  margin-left: 10px;
}
</style>
