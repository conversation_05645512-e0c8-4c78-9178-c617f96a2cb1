<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="title"
    @submit="submit"
    @cancel="cancel"
    class="image-editing-content"
  >
    <div class="image-editing-header">
      <div
        :class="editingType === 1 ? 'click-image-draw' : 'image-draw'"
        title="初始化鼠标"
        @click="initPointer"
      >
        <icon-common
          icon="icon-guangbiao"
          style="margin-bottom: 3px"
        ></icon-common>
      </div>
      <div class="line"></div>
      <div
        :class="editingType === 2 ? 'click-image-draw' : 'image-draw'"
        title="插入线段"
        @click="insertLine"
      >
        <icon-common
          icon="icon-xianduan-copy"
          style="margin-bottom: 3px"
        ></icon-common>
      </div>

      <div class="line"></div>
      <div
        :class="editingType === 3 ? 'click-image-draw' : 'image-draw'"
        title="插入矩形"
        @click="insertRect"
      >
        <icon-common
          icon="icon-xingzhuang-juxing"
          style="margin-bottom: 3px"
        ></icon-common>
      </div>
      <div class="line"></div>
      <div
        :class="editingType === 4 ? 'click-image-draw' : 'image-draw'"
        title="插入椭圆"
        @click="insertEllipse"
      >
        <icon-common
          icon="icon-ellipse"
          style="margin-bottom: 3px"
        ></icon-common>
      </div>
      <div class="line"></div>
      <div
        :class="editingType === 7 ? 'click-image-draw' : 'image-draw'"
        title="一次旋转90°"
        @click="imgRotate"
      >
        图片旋转
      </div>
      <div class="line"></div>
      <div
        :class="editingType === 5 ? 'click-image-draw' : 'image-draw'"
        title="插入文字"
        @click="insertInput"
      >
        <icon-common
          icon="icon-wenben"
          style="margin-bottom: 3px"
        ></icon-common>
      </div>
      <div class="line"></div>
      <div class="image-draw" title="字体" @mousedown.prevent="changeFontStyle">
        <icon-common
          icon="icon-zitishezhi-shuangse"
          style="margin-bottom: 3px"
        ></icon-common>
      </div>
      <div class="line"></div>
      <div
        :class="editingType === 6 ? 'click-image-draw' : 'image-draw'"
        title="插入自定义线段"
        @click="insertCustomLine"
      >
        <icon-common
          icon="icon-quxianlujing"
          style="margin-bottom: 3px"
        ></icon-common>
      </div>
      <div class="line"></div>
      <div class="withTriangle" title="线条颜色">
        <div class="font-color" @click="showBgColorPicker">
          <icon-common
            icon="icon-beijingyanse1"
            class="line-icon"
          ></icon-common>
        </div>

        <div class="right-part" @click.prevent="showBgColorPicker">
          <div class="font-triangle"></div>
        </div>
        <colorPicker
          ref="bgcolorPicker"
          v-model="color"
          v-on:change="handleChangeColor"
        />
      </div>
      <div class="line"></div>

      <div
        class="withTriangle"
        ref="showLineWidth"
        title="改变线条粗细"
        @mousedown.prevent="showLineWidth"
      >
        <icon-common
          icon="icon-fengexian"
          style="margin: 6px 3px 0 5px"
        ></icon-common>
        <div class="modal" v-if="isChangeLineWidth">
          <div class="alignment">
            <div
              class="hover"
              v-for="(item, i) in lineList"
              :key="i"
              @click="changeLineWidth(item)"
            >
              <img :src="lineWidthList[i]" class="line-img" />
            </div>
          </div>
        </div>
        <div class="triangle"></div>
      </div>

      <div class="line"></div>
      <div
        class="withTriangle"
        ref="showRectBg"
        title="背景"
        @mousedown.prevent="showRectBg"
      >
        <icon-common
          icon="icon-beijing"
          style="margin: 6px 3px 0 5px"
        ></icon-common>
        <div class="text">背景</div>
        <div class="modal" v-if="showRectBackground">
          <div class="alignment">
            <div
              class="hover"
              v-for="(item, i) in bgList"
              :key="i"
              @click="clickBgButton(i)"
            >
              <img :src="rectBgImage[i]" class="img" />
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="triangle"></div>
      </div>
      <div class="line"></div>
      <div class="image-draw" title="删除" @mousedown.prevent="deleteElement">
        <icon-common
          icon="icon-shanchu1"
          style="margin-bottom: 3px"
        ></icon-common>
      </div>
      <div class="line"></div>
      <div class="image-draw" title="图形复位" @click="reset">
        <icon-common icon="icon-fuwei" style="margin-bottom: 3px"></icon-common>
      </div>
    </div>

    <div class="modal-bg" :id="'image-editing' + editorId">
      <customInput
        :id="'customInput' + editorId"
        class="custom-input"
        v-show="editingType === 5"
        :fontStyle="fontStyle"
        :style="clickStyle"
        @changeLocation="changeLocation"
        @blur="inputBlur"
      ></customInput>

      <font-settings
        :show="showFontSetting"
        :fontSizeList="fontSizeList"
        :fontTypeList="fontTypeList"
        :initFontStyle="fontStyle"
        :effect="false"
        @cancel="closeWordModal"
        @submit="confirmWordModal"
      ></font-settings>
      <canvas
        :id="'img-editing-canvas' + editorId"
        ref="canvasDom"
        @pointerdown="pointerDown"
        @pointermove="pointerMove"
        @pointerup="pointerUp"
      ></canvas>
    </div>
    <div slot="editor-modal-footer">
      <a-button type="default" @click="cancel">取消</a-button>
      <a-button type="primary" @click="submit">完成</a-button>
    </div>
  </modal>
</template>

<script>
import { getUUID } from "../assets/js/utils";
import customInput from "./common/input.vue";
import fontSettings from "./font.vue";
import modal from "./common/modal.vue";
// import BUS from "@/assets/js/eventBus";
import iconCommon from "./common/iconCommon.vue";
import rectBgImage from "../assets/js/rectBgImage";
import lineWidthList from "../assets/js/lineWidth";
export default {
  name: "imageEditing",
  components: {
    modal,
    iconCommon,
    fontSettings,
    customInput,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    imageMeta: {
      type: Object,
      default: () => {},
    },
    editorId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      editingType: 1, // 1:初始化鼠标 2.绘制线 3.绘制矩形 4.绘制椭圆 5.绘制文字 6.绘制自定义线
      openColorPicker: false,
      rectBgImage,
      lineWidthList,
      title: "图形编辑",
      modal_width: 798,
      image: null,
      is_editor: false,
      canvas: null,
      lineWidth: 1,
      isChangeLineWidth: false,
      editInput: false,
      hold_mouse: false,
      cacheCustomPoints: [],
      color: "rgb(0,0,0)",
      canvas_height: 375,
      showInput: false,
      showFontSetting: false,
      inputFocusId: "",
      fontSizeList: [],
      fontTypeList: [],
      bgList: [
        { name: "透明" },
        { name: "空白" },
        { name: "触觉减退" },
        { name: "触觉消失" },
        { name: "触觉过敏或异常" },
        { name: "痛觉减退" },
        { name: "痛觉消失" },
        { name: "痛觉过敏或异常" },
        { name: "震动觉减退或异常" },
        { name: "位置觉减退或消失" },
        { name: "浅感觉全部消失" },
        { name: "深浅感觉全部消失" },
        { name: "Ⅰ度" },
        { name: "Ⅱ度" },
        { name: "深Ⅱ度" },
        { name: "Ⅲ度" },
      ],
      lineList: [1, 2, 3, 4, 5],
      rectEdit: undefined,
      customEdit: undefined,
      max_canvas_height: 500,
      line_edit: false,
      bgType: 0,
      drag_line: false,
      dragFigure: false,
      drag_custom_line: false,
      before_move_xy: [0, 0],
      showRectBackground: false,
      clickStyle: {
        left: 0 + "px",
        top: 0 + "px",
      },
      focusElement: {
        type: "line",
        start: [0, 0],
        end: [0, 0],
        isEdit: false,
      },
      fontStyle: {
        family: "宋体",
        height: 14,
        bold: false,
        italic: false,
        underline: false,
        dblUnderLine: false,
        strikethrough: false,
        color: "#000",
        bgColor: "#ffffff",
        script: 3,
      },
      insertList: [],
    };
  },

  watch: {
    show: {
      handler(val) {
        if (val) {
          const curClickInfo = this.editor.editor._curClickInfo;
          if (curClickInfo && curClickInfo.image) {
            this.$nextTick(() => {
              this.initCanvas(curClickInfo.image);
            });
          }
          this.editingType = 1;
          this.fontSizeList = this.editor.builtInVariable.fontSizeList;
          this.editor.builtInVariable.fontTypeList.forEach((e) => {
            this.fontTypeList.push({
              option: e,
              value: e,
            });
          });
        }
      },
    },
    immediate: true,
  },
  mounted() {
    document.addEventListener("keyup", this.imageEditKeyupEvent);
    document.addEventListener("mouseup", this.imageEditMouseUpEvent, false);
  },
  beforeDestroy() {
    document.removeEventListener("keyup", this.imageEditKeyupEvent);
    document.removeEventListener("mouseup", this.imageEditMouseUpEvent);
  },
  methods: {
    imageEditMouseUpEvent(event) {
      const showRectBg = this.$refs.showRectBg;
      if (showRectBg) {
        if (!showRectBg.contains(event.target)) {
          this.showRectBackground = false;
        }
      }
      const showLineWidth = this.$refs.showLineWidth;
      if (showLineWidth) {
        if (!showLineWidth.contains(event.target)) {
          this.isChangeLineWidth = false;
        }
      }
    },
    imageEditKeyupEvent(e) {
      if (
        (e.key === "Backspace" || e.key === "Delete") &&
        this.insertList.length
      ) {
        if (this.focusElement && this.focusElement.type !== "input") {
          this.deleteElement();
        }
      }
    },
    initCanvas(image) {
      this.canvas = document.getElementById(
        "img-editing-canvas" + this.editorId
      );
      if (!this.canvas) {
        return;
      }
      this.image = new Image();
      if (image.meta && image.meta.src) {
        this.image.src = image.meta.src; // 没有线的 src
        this.insertList = image.meta.params || [];
      } else {
        this.image.src = image.src; // 有线的 src
      }
      this.image.width = image.meta.originWidth || image.width;
      this.image.height = image.meta.originHeight || image.height;
      if (image.width <= this.modal_width - 24) {
        this.canvas.width = this.modal_width - 24;
        this.canvas.style.width = this.modal_width - 24 + "px";
      } else {
        this.canvas.width = image.width;
        this.canvas.style.width = image.width + "px";
      }
      const dom = document.getElementById("image-editing" + this.editorId);
      if (image.height <= this.canvas_height) {
        this.canvas.height = this.canvas_height;
        this.canvas.style.height = this.canvas_height + "px";
      } else if (
        image.height > this.canvas_height &&
        image.height <= this.max_canvas_height
      ) {
        this.canvas.height = image.height;
        this.canvas.style.height = image.height + "px";
      } else {
        this.canvas.height = image.height;
        this.canvas.style.height = image.height + "px";
        dom.style.height = this.max_canvas_height + "px";
      }

      const _this = this;
      this.image.onload = function () {
        _this.render();
      };
    },

    handleChangeColor(e) {
      this.openColorPicker = !this.openColorPicker;
      this.color = e;
      if (this.focusElement && this.focusElement.color) {
        this.focusElement.color = e;
        this.render();
      }
    },
    // 打开/关闭背景颜色选择器
    showBgColorPicker() {
      this.openColorPicker = !this.openColorPicker;
      if (this.$refs.bgcolorPicker.openStatus === false) {
        this.openColorPicker = true;
      }
      if (this.openColorPicker) {
        this.$refs.bgcolorPicker.openStatus = true;
      } else {
        this.$refs.bgcolorPicker.closePanel();
      }
    },
    deleteElement() {
      const dom = document.getElementById("customInput" + this.editorId);
      const text = dom.innerText;
      if (text) {
        dom.innerText = "";
        this.showInput = false;
      }
      for (let i = 0; i < this.insertList.length; i++) {
        const element = this.insertList[i];
        if (element.type === "input" && this.inputFocusId === element.id) {
          element.isEdit = true;
        }
        if (element.isEdit) {
          this.insertList.splice(i, 1);

          break;
        }
      }
      this.render();
    },

    reset() {
      this.editingType = 1;
      this.editInput = true;
      this.insertList = [];
      this.render();
    },
    initPointer() {
      this.editingType = 1;
      this.editInput = true;
      this.closeElementEdit();
      this.render();
    },
    //插入线段
    insertLine() {
      this.editingType = 2;
      this.editInput = false;
      this.closeElementEdit();
      this.render();
    },
    //插入矩形
    insertRect() {
      this.editingType = 3;
      this.editInput = false;
      this.closeElementEdit();
      this.render();
    },
    //插入椭圆
    insertEllipse() {
      this.editingType = 4;
      this.editInput = false;
      this.closeElementEdit();
      this.render();
    },
    // 图片旋转
    imgRotate() {
      this.editingType = 7;
      this.editInput = false;

      const image = this.editor.editor._curClickInfo.image;
      image.meta.originRotation =
        (image.meta.originRotation || 0) + Math.PI / 2;

      this.closeElementEdit();
      this.render();
    },
    //插入文字
    insertInput() {
      this.editingType = 5;
      this.editInput = false;

      const dom = document.getElementById("customInput" + this.editorId);
      dom.innerText = "";
      this.closeElementEdit();
      this.render();
    },
    //插入自定义线段
    insertCustomLine() {
      this.editingType = 6;
      this.editInput = false;
      this.closeElementEdit();
      this.render();
    },

    showLineWidth() {
      this.isChangeLineWidth = true;
    },
    changeLineWidth(i) {
      this.isChangeLineWidth = false;
      this.lineWidth = i;
      if (this.focusElement && this.focusElement.lineWidth) {
        this.focusElement.lineWidth = i;
        this.render();
      }
    },
    inputBlur() {
      this.pushData();
      this.closeElementEdit();
      this.render();
    },
    pushData() {
      this.showInput = false;
      const dom = document.getElementById("customInput" + this.editorId);
      const text = dom.innerText;
      dom.innerText = "";
      let style = this.fontStyle;
      for (let i = 0; i < this.insertList.length; i++) {
        const element = this.insertList[i];
        if (element.type === "input" && element.id === dom.inputId) {
          element.value = text;
          this.inputFocusId = element.id;
          style = element.fontStyle;
          break;
        }
      }
      if (!text || dom.inputId) {
        //更新拖动后的input框位置
        for (let i = 0; i < this.insertList.length; i++) {
          const input = this.insertList[i];
          if (input.id === dom.inputId) {
            const left = Number(this.clickStyle.left.split("px")[0]) + 5;
            const top = Number(this.clickStyle.top.split("px")[0]) + 5;
            input.start = [left, top];
          }
        }
        return;
      }
      const ctx = this.canvas.getContext("2d", { alpha: false });
      ctx.save();
      ctx.font = `${style.italic ? "italic " : ""}${style.bold ? 700 : 400} ${
        style.height
      }px ${style.family}`;
      const textList = text.split("\n");
      const numTextList = [];

      for (let i = 0; i < textList.length; i++) {
        numTextList.push(ctx.measureText(textList[i]).width);
      }
      ctx.restore();
      const width = Math.max(...numTextList);
      const height = textList.length * style.height;
      const left = Number(this.clickStyle.left.split("px")[0]) + 5;
      const top = Number(this.clickStyle.top.split("px")[0]) + 5;
      const uuid = getUUID("input");
      this.insertList.push({
        type: "input",
        id: uuid,
        start: [left, top],
        width: width,
        height: height,
        value: text,
        fontStyle: this.fontStyle,
        isEdit: false,
      });
      //初始化input框时，修改字体后字体input框失焦没有focusElement，重新赋值
      if (this.focusElement && this.focusElement.type !== "input") {
        this.focusElement = {
          type: "input",
          id: uuid,
          start: [left, top],
          width: width,
          height: height,
          value: text,
          fontStyle: this.fontStyle,
          isEdit: true,
        };
      }
    },
    pointerDown(e) {
      e.stopPropagation();
      this.hold_mouse = true;
      const x = e.offsetX;
      const y = e.offsetY;

      if (this.canvas) {
        this.closeElementEdit();
        this.render();
        this.canvas.setPointerCapture(e.pointerId);
        const element = this.isInElement(x, y);
        this.drag_custom_line = false;
        if (this.focusElement === element) {
          this.is_editor = true;
        } else {
          this.is_editor = false;
        }
        if (!element) {
          this.focusElement = undefined;
          this.is_editor = false;
        }
        if (element && (this.is_editor || this.editingType === 1)) {
          if (element.type === "line") {
            this.focusElement = element;
            this.focusElement.isEdit = true;
            const result = this.isInLineEdit(x, y);
            if (result) {
              this.line_edit = true;
              if (result === "start") {
                this.flipXY();
              }
            } else {
              this.drag_line = true;
              this.before_move_xy = [x, y];
            }
          } else if (element.type === "rect" || element.type === "ellipse") {
            this.focusElement = element;
            this.focusElement.isEdit = true;
            if (this.focusElement.start[1] > this.focusElement.end[1]) {
              this.flipXY();
            }
            const result = this.isInFigureEdit(x, y);
            if (result !== undefined) {
              this.rectEdit = result;
            } else {
              this.dragFigure = true;
              this.before_move_xy = [x, y];
            }
          } else if (element.type === "input") {
            if (this.editingType === 5 || this.editingType === 1) {
              this.initPointer();
            }
          } else if (element.type === "customLine") {
            const result = this.isInCustomEdit(x, y);
            if (result !== undefined) {
              this.customEdit = result;
            } else {
              this.drag_custom_line = true;
              this.cacheCustomPoints = [...element.points];
            }
            this.focusElement = element;
            this.before_move_xy = [x, y];
            this.focusElement.isEdit = true;
          }
        }
        if (this.is_editor || this.editingType === 1) {
          this.render();
          return;
        }
        if (this.editingType === 2) {
          this.focusElement = {
            type: "line",
            start: [x, y],
            end: [x, y],
            color: this.color,
            lineWidth: this.lineWidth,
            isEdit: false,
          };
        } else if (this.editingType === 3) {
          this.focusElement = {
            type: "rect",
            start: [x, y],
            end: [x, y],
            bgType: this.bgType,
            color: this.color,
            lineWidth: this.lineWidth,
            isEdit: false,
          };
        } else if (this.editingType === 6) {
          this.focusElement = {
            type: "customLine",
            start: [x, y],
            end: [x, y],
            points: [[x, y]],
            color: this.color,
            lineWidth: this.lineWidth,
            isEdit: false,
          };
        } else if (this.editingType === 4) {
          this.focusElement = {
            type: "ellipse",
            start: [x, y],
            end: [x, y],
            bgType: this.bgType,
            color: this.color,
            lineWidth: this.lineWidth,
            isEdit: false,
          };
        }
      }
      this.render();
    },
    pointerMove(e) {
      e.preventDefault();
      let x = e.offsetX;
      let y = e.offsetY;
      requestAnimationFrame(() => {
        this.cursorType(x, y);
        if (this.hold_mouse && this.focusElement) {
          if (this.editingType === 2 || this.line_edit) {
            this.focusElement.end[0] = x;
            this.focusElement.end[1] = y;
            this.render();
            this.drawLineShadow();
          } else if (
            this.drag_line ||
            this.dragFigure ||
            this.drag_custom_line
          ) {
            let type = null;
            if (this.drag_custom_line) {
              type = "customLine";
            }
            this.dragElement(x, y, type);
            this.render();
          } else if (this.editingType === 3) {
            this.setXY(x, y);
            this.render();
            this.drawRectShadow();
          } else if (this.editingType === 6) {
            this.render();
            this.drawCustomLineShadow(x, y);
          } else if (this.editingType === 4) {
            this.setXY(x, y);
            this.render();
            this.drawEllipseShadow();
          } else if (this.customEdit !== undefined) {
            this.changeCustomSize(x, y);
          } else if (this.rectEdit !== undefined) {
            if (x < 3) {
              x = 3;
            }
            if (y < 3) {
              y = 3;
            } else if (y > this.canvas.height - 3) {
              y = this.canvas.height - 3;
            }
            switch (this.rectEdit) {
              case 0:
                this.focusElement.start[0] = x;
                this.focusElement.start[1] = y;
                break;
              case 1:
                this.focusElement.start[1] = y;
                break;
              case 2:
                this.focusElement.start[1] = y;
                this.focusElement.end[0] = x;
                break;
              case 3:
                this.focusElement.start[0] = x;
                break;
              case 4:
                this.focusElement.end[0] = x;
                break;
              case 5:
                this.focusElement.start[0] = x;
                this.focusElement.end[1] = y;
                break;
              case 6:
                this.focusElement.end[1] = y;
                break;
              case 7:
                this.focusElement.end[0] = x;
                this.focusElement.end[1] = y;
                break;
            }
            this.render();
          }
        }
      });
    },
    pointerUp(e) {
      e.preventDefault();
      let x = e.offsetX;
      let y = e.offsetY;
      this.hold_mouse = false;
      this.canvas.setPointerCapture(e.pointerId);

      const element = this.isInElement(x, y);
      if (
        this.editingType === 2 ||
        this.editingType === 3 ||
        this.editingType === 4 ||
        this.editingType === 6
      ) {
        if (!this.focusElement) return;
        if (
          this.focusElement.start[0] === this.focusElement.end[0] &&
          this.focusElement.start[1] === this.focusElement.end[1]
        )
          return;
        this.focusElement.isEdit = true;

        if (this.insertList.indexOf(this.focusElement) === -1) {
          this.insertList.push(this.focusElement);
        }
      } else if (this.editingType === 5) {
        this.showInput = true;
        this.inputOperation(x, y, element);
      } else if (this.editInput) {
        if (element && element.type === "input") {
          this.showInput = true;
          this.inputOperation(x, y, element);
        }
      }
      if (element && element.type === "input") {
        this.focusElement = element;
      }
      this.rectEdit = undefined;
      this.line_edit = false;
      this.drag_line = false;
      this.dragFigure = false;
      this.render();
    },
    setXY(x, y) {
      if (!(this.rectEdit === 1 || this.rectEdit === 6)) {
        if (this.rectEdit === 0 || this.rectEdit === 3 || this.rectEdit === 5) {
          this.focusElement.start[0] = x;
        } else {
          this.focusElement.end[0] = x;
        }
      }
      if (!(this.rectEdit === 3 || this.rectEdit === 4)) {
        if (this.rectEdit === 0 || this.rectEdit === 1 || this.rectEdit === 2) {
          this.focusElement.start[1] = y;
        } else {
          this.focusElement.end[1] = y;
        }
      }
    },
    cursorType(x, y) {
      const element = this.isInElement(x, y);
      if (element && element.isEdit) {
        if (element.type === "line") {
          const result = this.isInLineEdit(x, y);
          if (result) {
            const k =
              (element.end[1] - element.start[1]) /
              (element.end[0] - element.start[0]);
            if (k === 0) {
              this.canvas.style.cursor = "w-resize";
            } else if (k < 0) {
              this.canvas.style.cursor = "sw-resize";
            } else {
              this.canvas.style.cursor = "se-resize";
            }
          } else {
            this.canvas.style.cursor = "default";
          }
        } else if (element.type === "rect" || element.type === "ellipse") {
          const result = this.isInFigureEdit(x, y);
          if (result != undefined) {
            if (result === 0) {
              this.canvas.style.cursor = "se-resize";
            } else if (result === 1) {
              this.canvas.style.cursor = "n-resize";
            } else if (result === 2) {
              this.canvas.style.cursor = "sw-resize";
            } else if (result === 3) {
              this.canvas.style.cursor = "w-resize";
            } else if (result === 4) {
              this.canvas.style.cursor = "w-resize";
            } else if (result === 5) {
              this.canvas.style.cursor = "sw-resize";
            } else if (result === 6) {
              this.canvas.style.cursor = "n-resize";
            } else if (result === 7) {
              this.canvas.style.cursor = "se-resize";
            }
          } else {
            this.canvas.style.cursor = "default";
          }
        } else if (element.type === "customLine") {
          this.canvas.style.cursor = "default";
        }
      } else {
        this.canvas.style.cursor = "default";
        if (this.editingType === 6) {
          this.canvas.style.cursor = "crosshair";
        }
      }
    },
    dragElement(x, y, type) {
      const move_x = x - this.before_move_xy[0];
      const move_y = y - this.before_move_xy[1];
      this.focusElement.start[0] += move_x;
      this.focusElement.start[1] += move_y;
      this.focusElement.end[0] += move_x;
      this.focusElement.end[1] += move_y;
      if (type && type === "customLine") {
        this.focusElement.points.forEach((e) => {
          (e[0] += move_x), (e[1] += move_y);
        });
      }
      this.before_move_xy = [x, y];
    },

    closeElementEdit() {
      this.insertList.forEach((e) => {
        e.isEdit = false;
      });
    },
    getStartAndEnd(start, end) {
      const start_y = start[1];
      const end_y = end[1];

      if (start_y > end_y) {
        return [end, start];
      }
      return [start, end];
    },
    flipXY() {
      const transitionXY = this.focusElement.start;
      this.focusElement.start = this.focusElement.end;
      this.focusElement.end = transitionXY;
    },
    isInElement(x, y) {
      const deviation = 5;
      const p = [x, y];
      for (let i = this.insertList?.length - 1; i >= 0; i--) {
        const element = this.insertList[i];
        if (element.type === "line") {
          const start = this.getStartAndEnd(element.start, element.end)[0];
          const end = this.getStartAndEnd(element.start, element.end)[1];
          const points = [
            [start[0], start[1]],
            [end[0], end[1]],
          ];
          const sentences = this.getPointToLineDistance2(points, p);
          if (sentences < deviation) {
            if (points[0][0] < points[1][0]) {
              if (
                p[0] > points[0][0] - 4 &&
                p[0] < points[1][0] + 4 &&
                p[1] < points[1][1] + 4 &&
                p[1] > points[0][1] - 4
              ) {
                return element;
              }
            } else {
              if (
                p[0] >= points[1][0] - 4 &&
                p[0] <= points[0][0] + 4 &&
                p[1] <= points[1][1] + 4 &&
                p[1] >= points[0][1] - 4
              ) {
                return element;
              }
            }
          }
        } else if (element.type === "rect" || element.type === "ellipse") {
          const start = this.getStartAndEnd(element.start, element.end)[0];
          const end = this.getStartAndEnd(element.start, element.end)[1];
          if (start[0] <= end[0]) {
            if (
              start[0] - 5 < x &&
              x < end[0] + 5 &&
              start[1] - 5 < y &&
              y < end[1] + 5
            ) {
              return element;
            }
          } else {
            if (
              end[0] - 5 < x &&
              x < start[0] + 5 &&
              start[1] - 5 < y &&
              y < end[1] + 5
            ) {
              return element;
            }
          }
        } else if (element.type === "input") {
          if (
            element.start[0] - 5 <= x &&
            x <= element.start[0] + element.width + 5 &&
            element.start[1] - 5 <= y &&
            y <= element.start[1] + element.height + 5
          ) {
            return element;
          }
        } else if (element.type === "customLine") {
          const rangeList = this.getCustomLineRange(element.points);
          const minX = rangeList.start[0] - 5;
          const maxX = rangeList.end[0] + 5;
          const minY = rangeList.start[1] - 5;
          const maxY = rangeList.end[1] + 5;
          if (
            minX - 3 <= x &&
            x <= maxX + 3 &&
            minY - 3 <= y &&
            y <= maxY + 3
          ) {
            return element;
          }
        }
      }
    },
    getPointToLineDistance2(list, point) {
      const [[x1, y1], [x2, y2]] = list;
      const [x, y] = point;

      const b = Math.sqrt((x - x1) * (x - x1) + (y - y1) * (y - y1));
      const c = Math.sqrt((x - x2) * (x - x2) + (y - y2) * (y - y2));
      const a = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));

      if (a === 0) return b; // 如果选取两点为同一个点，则返回已知点和比较点的距离即可

      // 原理：通过半周长和各边长度来计算三角形面积，即海伦公式
      const p = (a + b + c) / 2; // 半周长，halfPerimeter
      // 根据海伦公式求三角形面积
      const areaSize = Math.abs(Math.sqrt(p * (p - a) * (p - b) * (p - c)));
      // 根据三角形面积公式求证点到直线距离
      return (2 * areaSize) / a;
    },

    isInLineEdit(x, y) {
      const pointer = [x, y];
      const in_start_circle = this.pointInsideCircle(
        pointer,
        [this.focusElement.start[0], this.focusElement.start[1]],
        4
      );
      const in_end_circle = this.pointInsideCircle(
        pointer,
        [this.focusElement.end[0], this.focusElement.end[1]],
        4
      );
      if (in_start_circle) {
        return "start";
      } else if (in_end_circle) {
        return "endX";
      }
    },

    isInFigureEdit(x, y) {
      if (!this.focusElement) return;
      const start = this.getStartAndEnd(
        this.focusElement.start,
        this.focusElement.end
      )[0];
      const end = this.getStartAndEnd(
        this.focusElement.start,
        this.focusElement.end
      )[1];

      const rectEditList = [
        { x: [start[0] - 3, start[0] + 3], y: [start[1] - 3, start[1] + 3] },
        {
          x: [
            start[0] + (end[0] - start[0]) / 2 - 3,
            start[0] + (end[0] - start[0]) / 2 + 3,
          ],
          y: [start[1] - 3, start[1] + 3],
        },
        { x: [end[0] - 3, end[0] + 3], y: [start[1] - 3, start[1] + 3] },
        {
          x: [start[0] - 3, start[0] + 3],
          y: [
            start[1] + (end[1] - start[1]) / 2 - 3,
            start[1] + (end[1] - start[1]) / 2 + 3,
          ],
        },
        {
          x: [end[0] - 3, end[0] + 3],
          y: [
            start[1] + (end[1] - start[1]) / 2 - 3,
            start[1] + (end[1] - start[1]) / 2 + 3,
          ],
        },
        { x: [start[0] - 3, start[0] + 3], y: [end[1] - 3, end[1] + 3] },
        {
          x: [
            start[0] + (end[0] - start[0]) / 2 - 3,
            start[0] + (end[0] - start[0]) / 2 + 3,
          ],
          y: [end[1] - 3, end[1] + 3],
        },
        { x: [end[0] - 3, end[0] + 3], y: [end[1] - 3, end[1] + 3] },
      ];
      for (let i = 0; i < rectEditList.length; i++) {
        const limit = rectEditList[i];
        if (
          limit.x[0] <= x &&
          x <= limit.x[1] &&
          limit.y[0] <= y &&
          y <= limit.y[1]
        ) {
          return i;
        }
      }
    },
    isInCustomEdit(x, y) {
      if (!(this.focusElement && this.focusElement.points)) return;
      const rangeList = this.getCustomLineRange(this.focusElement.points);
      const left = rangeList.start[0] - 5;
      const top = rangeList.start[1] - 5;
      const width = rangeList.end[0] - rangeList.start[0] + 10;
      const height = rangeList.end[1] - rangeList.start[1] + 10;
      const customEditList = [
        { x: left, y: top },
        { x: left + width, y: top },
        { x: left, y: top + height },
        { x: left + width, y: top + height },
      ];
      for (let i = 0; i < customEditList.length; i++) {
        const limit = customEditList[i];
        if (
          limit.x - 3 <= x &&
          x <= limit.x + 3 &&
          limit.y - 3 <= y &&
          y <= limit.y + 3
        ) {
          return i;
        }
      }
    },
    pointInsideCircle(point, circle, r) {
      if (r === 0) return false;
      const dx = circle[0] - point[0];
      const dy = circle[1] - point[1];
      return dx * dx + dy * dy <= r * r;
    },
    changeCustomSize(x, y) {
      if (this.customEdit === undefined || !this.focusElement.points.length)
        return;
      const rangeList = this.getCustomLineRange(this.cacheCustomPoints);
      const left = rangeList.start[0] - 5;
      const top = rangeList.start[1] - 5;
      const width = rangeList.end[0] - rangeList.start[0] + 10;
      const height = rangeList.end[1] - rangeList.start[1] + 10;
      //以自定义图形中心点为原点
      let origin = [left + width / 2, top + height / 2];
      //设置缩放比例
      const scale =
        Math.sqrt(Math.pow(x - origin[0], 2) + Math.pow(y - origin[1], 2)) /
        Math.sqrt(
          Math.pow(this.before_move_xy[0] - origin[0], 2) +
            Math.pow(this.before_move_xy[1] - origin[1], 2)
        );
      this.focusElement.points = [];
      // 将点集合以原点为中心进行缩放
      for (let i = 0; i < this.cacheCustomPoints.length; i++) {
        const point = this.cacheCustomPoints[i];
        this.focusElement.points.push([
          origin[0] + (point[0] - origin[0]) * scale,
          origin[1] + (point[1] - origin[1]) * scale,
        ]);
      }
      //需要更新开始位置和结束位置的坐标
      this.focusElement.start = this.focusElement.points[0];
      this.focusElement.end =
        this.focusElement.points[this.focusElement.points.length - 1];
      this.render();
    },
    drawCustomLineShadow(x, y) {
      if (this.focusElement.points) {
        this.focusElement.points.push([x, y]);
        this.focusElement.end = [x, y];
        const ctx = this.canvas.getContext("2d", { alpha: false });
        const color = this.focusElement.color;
        ctx.save();
        ctx.lineWidth = this.focusElement.lineWidth;
        if (color) {
          ctx.strokeStyle = color;
        } else {
          ctx.strokeStyle = this.color;
        }
        ctx.beginPath();
        ctx.moveTo(this.focusElement.start[0], this.focusElement.start[1]);
        for (let i = 0; i < this.focusElement.points.length; i++) {
          const point = this.focusElement.points[i];
          ctx.lineTo(point[0], point[1]);
        }
        ctx.stroke();
        ctx.restore();
      }
    },
    drawLineShadow() {
      const ctx = this.canvas.getContext("2d", { alpha: false });
      const color = this.focusElement.color;
      ctx.save();
      ctx.lineWidth = this.focusElement.lineWidth;
      if (color) {
        ctx.strokeStyle = color;
      } else {
        ctx.strokeStyle = this.color;
      }
      ctx.beginPath();
      ctx.moveTo(this.focusElement.start[0], this.focusElement.start[1]);
      ctx.lineTo(this.focusElement.end[0], this.focusElement.end[1]);
      ctx.stroke();
      ctx.restore();
    },
    drawRectShadow() {
      const ctx = this.canvas.getContext("2d", { alpha: false });
      const color = this.focusElement.color;
      ctx.save();
      ctx.lineWidth = this.focusElement.lineWidth;
      if (color) {
        ctx.strokeStyle = color;
      } else {
        ctx.strokeStyle = this.color;
      }
      ctx.strokeRect(
        this.focusElement.start[0],
        this.focusElement.start[1],
        this.focusElement.end[0] - this.focusElement.start[0],
        this.focusElement.end[1] - this.focusElement.start[1]
      );
      ctx.restore();
    },
    drawEllipseShadow() {
      const ctx = this.canvas.getContext("2d", { alpha: false });
      const color = this.focusElement.color;
      ctx.save();
      ctx.lineWidth = this.focusElement.lineWidth;
      if (color) {
        ctx.strokeStyle = color;
      } else {
        ctx.strokeStyle = this.color;
      }
      const x = this.focusElement.start[0];
      const y = this.focusElement.start[1];
      const w = this.focusElement.end[0] - this.focusElement.start[0];
      const h = this.focusElement.end[1] - this.focusElement.start[1];
      const kappa = 0.5522848;
      const ox = (w / 2) * kappa;
      const oy = (h / 2) * kappa;
      const xe = x + w;
      const ye = y + h;
      const xm = x + w / 2;
      const ym = y + h / 2;

      ctx.beginPath();
      ctx.moveTo(x, ym);
      ctx.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);
      ctx.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);
      ctx.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);
      ctx.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);
      ctx.closePath();
      ctx.stroke();
      ctx.restore();
    },
    inputOperation(x, y, element) {
      const dom = document.getElementById("customInput" + this.editorId);
      dom.inputId = "";
      this.changeInputCss(dom, this.fontStyle);
      this.clickStyle = {
        left: x - 5 + "px",
        top: y - 5 + "px",
      };
      if (element && element.type === "input") {
        dom.innerText = element.value;
        dom.inputId = element.id;
        this.changeInputCss(dom, element.fontStyle);
        this.$nextTick(() => {
          this.clickStyle = {
            left: element.start[0] - 5 + "px",
            top: element.start[1] - 5 + "px",
          };
        });

        const ctx = this.canvas.getContext("2d", { alpha: false });
        ctx.save();
        ctx.font = `${element.fontStyle.italic ? "italic " : ""}${
          element.fontStyle.bold ? 700 : 400
        } ${element.fontStyle.height}px ${element.fontStyle.family}`;

        const textList = element.value.split("\n");
        const numTextList = [];

        for (let i = 0; i < textList.length; i++) {
          numTextList.push(ctx.measureText(textList[i]).width);
        }
        const width = Math.max(...numTextList);
        ctx.restore();
        const height = textList.length * element.fontStyle.height;
        const left = element.start[0];
        const top = element.start[1];
        element.width = width;
        element.height = height;
        element.start = [left, top];
        element.isEdit = true;
      } else if (!element && this.editInput) {
        this.showInput = false;
      } else {
        this.focusElement = {};
      }
      dom.focus();
      this.render();
    },
    changeInputCss(dom, style) {
      dom.style.cssText = `color:${style.color};font-size:${
        style.height
      }px;font-style:${style.italic ? "italic" : "normal"};font-weight:${
        style.bold ? 700 : 400
      };font-family:${style.family}`;
    },
    // 计算旋转后的矩形尺寸 angle 就是弧度
    calculateRotatedRectSize(width, height, angle) {
      const rad = angle;

      // 计算旋转后的四个角点坐标
      const cos = Math.cos(rad);
      const sin = Math.sin(rad);

      // 计算四个角点的坐标
      const points = [
        {
          x: (-width / 2) * cos - (height / 2) * sin,
          y: (-width / 2) * sin + (height / 2) * cos,
        },
        {
          x: (width / 2) * cos - (height / 2) * sin,
          y: (width / 2) * sin + (height / 2) * cos,
        },
        {
          x: (-width / 2) * cos + (height / 2) * sin,
          y: (-width / 2) * sin - (height / 2) * cos,
        },
        {
          x: (width / 2) * cos + (height / 2) * sin,
          y: (width / 2) * sin - (height / 2) * cos,
        },
      ];

      // 找出最小和最大的x、y值
      let minX = Infinity,
        maxX = -Infinity;
      let minY = Infinity,
        maxY = -Infinity;

      points.forEach((point) => {
        minX = Math.min(minX, point.x);
        maxX = Math.max(maxX, point.x);
        minY = Math.min(minY, point.y);
        maxY = Math.max(maxY, point.y);
      });

      // 计算新的宽度和高度
      const newWidth = maxX - minX;
      const newHeight = maxY - minY;

      return { newWidth, newHeight };
    },
    render() {
      const ctx = this.canvas.getContext("2d", { alpha: true });
      this.canvas.style.backgroundColor = "transparent";
      ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      // ctx.fillStyle = "rgb(160,160,160)";
      // ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      // 图片旋转处理 ↓
      const originImage = this.editor.editor._curClickInfo.image;
      // 绘制图片，注意坐标需要相对于旋转中心点计算
      const originWidth = originImage.meta.originWidth || originImage.width;
      const originHeight = originImage.meta.originHeight || originImage.height;
      const { newWidth, newHeight } = this.calculateRotatedRectSize(
        originWidth,
        originHeight,
        originImage.meta.originRotation || 0
      );
      // 计算旋转后的矩形尺寸
      // 计算旋转中心点
      const centerX = newWidth / 2;
      const centerY = newHeight / 2;

      // 保存当前状态
      ctx.save();

      // 移动到旋转中心点
      ctx.translate(centerX, centerY);

      // 旋转
      ctx.rotate(originImage.meta.originRotation || 0);
      // 绘制图片，注意坐标需要相对于旋转中心点计算
      ctx.drawImage(
        this.image,
        -originWidth / 2, // 从中心点向左偏移半个宽度
        -originHeight / 2, // 从中心点向上偏移半个高度
        originWidth,
        originHeight
      );
      // 恢复状态
      ctx.restore();

      // 图片旋转处理 ↑
      // ctx.drawImage(this.image, 0, 0, this.image.width, this.image.height);
      this.insertList?.forEach((e) => {
        if (e.type === "line") {
          this.drawLine(ctx, e);
        } else if (e.type === "input" && !e.isEdit) {
          ctx.save();
          const style = e.fontStyle;
          ctx.font = `${style.italic ? "italic " : ""}${
            style.bold ? 700 : 400
          } ${style.height}px ${style.family}`;
          ctx.fillStyle = style.color;
          const textList = e.value.split("\n");
          for (let i = 0; i < textList.length; i++) {
            const text = textList[i];
            ctx.fillText(
              text,
              e.start[0] - 1,
              e.start[1] + style.height * (i + 1) * 1.5 - 6
            );
          }
          ctx.restore();
        } else if (e.type === "rect" || e.type === "ellipse") {
          this.drawFigure(ctx, e);
        } else if (e.type === "customLine") {
          this.drawCustomLine(ctx, e);
        }
      });
    },
    drawCustomLine(ctx, e) {
      ctx.save();
      ctx.lineWidth = e.lineWidth;
      ctx.strokeStyle = e.color;
      ctx.mozImageSmoothingEnabled = true;
      ctx.beginPath();
      ctx.moveTo(e.start[0], e.start[1]);
      for (let i = 0; i < e.points.length; i++) {
        const point = e.points[i];
        ctx.lineTo(point[0], point[1]);
      }
      ctx.stroke();
      if (e.isEdit) {
        ctx.save();
        ctx.beginPath();
        ctx.globalAlpha = 1;
        ctx.lineWidth = 1;
        ctx.fillStyle = "#fff";
        ctx.strokeStyle = e.color;
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.globalAlpha = 1;
        ctx.fill();
        ctx.stroke();
        const rangeList = this.getCustomLineRange(e.points);
        const width = rangeList.end[0] - rangeList.start[0] + 10;
        const height = rangeList.end[1] - rangeList.start[1] + 10;
        ctx.setLineDash([4, 5]);
        ctx.strokeRect(
          rangeList.start[0] - 5,
          rangeList.start[1] - 5,
          width,
          height
        );
        ctx.restore();
        ctx.save();
        ctx.lineWidth = 1;
        ctx.fillStyle = "#fff";
        ctx.strokeStyle = e.color;
        ctx.strokeRect(rangeList.start[0] - 8, rangeList.start[1] - 8, 6, 6);
        ctx.strokeRect(rangeList.end[0] + 2, rangeList.start[1] - 8, 6, 6);
        ctx.strokeRect(rangeList.start[0] - 8, rangeList.end[1] + 2, 6, 6);
        ctx.strokeRect(rangeList.end[0] + 2, rangeList.end[1] + 2, 6, 6);
        ctx.fillRect(rangeList.start[0] - 8, rangeList.start[1] - 8, 6, 6);
        ctx.fillRect(rangeList.end[0] + 2, rangeList.start[1] - 8, 6, 6);
        ctx.fillRect(rangeList.start[0] - 8, rangeList.end[1] + 2, 6, 6);
        ctx.fillRect(rangeList.end[0] + 2, rangeList.end[1] + 2, 6, 6);
        ctx.restore();
      }
    },
    getCustomLineRange(e) {
      const xList = [];
      const yList = [];
      for (let i = 0; i < e.length; i++) {
        const point = e[i];
        xList.push(point[0]);
        yList.push(point[1]);
      }
      const minX = Math.min(...xList);
      const maxX = Math.max(...xList);
      const minY = Math.min(...yList);
      const maxY = Math.max(...yList);
      return { start: [minX, minY], end: [maxX, maxY] };
    },
    showRectBg() {
      this.showRectBackground = true;
    },
    drawLine(ctx, e) {
      ctx.save();
      ctx.lineWidth = e.lineWidth;
      ctx.strokeStyle = e.color;
      ctx.beginPath();
      ctx.moveTo(e.start[0], e.start[1]);
      ctx.lineTo(e.end[0], e.end[1]);
      ctx.stroke();
      if (e.isEdit) {
        ctx.save();
        ctx.beginPath();
        ctx.globalAlpha = 1;
        ctx.lineWidth = 1;
        ctx.fillStyle = "#fff";
        ctx.strokeStyle = e.color;
        ctx.arc(e.start[0], e.start[1], 4, 0, Math.PI * 2, true);
        ctx.fill();
        ctx.stroke();
        ctx.beginPath();
        ctx.globalAlpha = 1;
        ctx.arc(e.end[0], e.end[1], 4, 0, Math.PI * 2, true);
        ctx.fill();
        ctx.stroke();
        ctx.restore();
      }
      ctx.restore();
    },
    changeLocation(val) {
      this.clickStyle = val;
    },
    drawFigure(ctx, e) {
      ctx.save();
      ctx.lineWidth = e.lineWidth;
      ctx.strokeStyle = e.color;
      const width = e.end[0] - e.start[0];
      const height = e.end[1] - e.start[1];
      if (e.type === "rect") {
        ctx.strokeRect(e.start[0], e.start[1], width, height);
      }
      const canvasHidden = document.createElement("canvas");
      canvasHidden.width = 10;
      canvasHidden.height = 10;
      canvasHidden.style.with = "10px";
      canvasHidden.style.height = "10px";
      const ctxHidden = canvasHidden.getContext("2d");
      ctxHidden.save();
      if (e.bgType !== 0) {
        switch (e.bgType) {
          case 1:
            ctxHidden.fillStyle = "white";
            ctxHidden.fillRect(0, 0, 10, 10);
            break;
          case 2:
            ctxHidden.fillStyle = e.color;
            ctxHidden.fillRect(0, 1, 4, 2);
            break;
          case 3:
            ctxHidden.beginPath();
            ctxHidden.arc(5, 5, 2.5, 0, Math.PI * 2, true);
            ctxHidden.stroke();
            break;
          case 4:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(2, 2);
            ctxHidden.lineTo(8, 8);
            ctxHidden.stroke();
            ctxHidden.beginPath();
            ctxHidden.moveTo(8, 2);
            ctxHidden.lineTo(2, 8);
            ctxHidden.stroke();
            break;
          case 5:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(8, 2);
            ctxHidden.lineTo(2, 8);
            ctxHidden.stroke();
            break;
          case 6:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(0, 5);
            ctxHidden.lineTo(0.5, 5);
            ctxHidden.moveTo(5, 0);
            ctxHidden.lineTo(5, 0.5);
            ctxHidden.moveTo(10, 5);
            ctxHidden.lineTo(9.5, 5);
            ctxHidden.moveTo(5, 9.5);
            ctxHidden.lineTo(5, 10);

            ctxHidden.moveTo(1, 5);
            ctxHidden.lineTo(5, 1);
            ctxHidden.lineTo(9, 5);
            ctxHidden.lineTo(5, 9);
            ctxHidden.lineTo(1, 5);
            ctxHidden.stroke();
            break;
          case 7:
            ctxHidden.lineWidth = 2;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(0, 5);
            ctxHidden.lineTo(10, 5);
            ctxHidden.stroke();
            break;

          case 8:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(2.5, 2);
            ctxHidden.lineTo(5, 9);
            ctxHidden.lineTo(7.5, 2);
            ctxHidden.stroke();
            break;
          case 9:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(1.5, 5);
            ctxHidden.lineTo(1.5, 8.5);
            ctxHidden.lineTo(8.5, 8.5);
            ctxHidden.lineTo(8.5, 5);
            ctxHidden.lineTo(6.5, 5);
            ctxHidden.lineTo(6.5, 1.5);
            ctxHidden.lineTo(3.5, 1.5);
            ctxHidden.lineTo(3.5, 5);
            ctxHidden.lineTo(1.5, 5);
            ctxHidden.stroke();
            break;
          case 10:
            ctxHidden.lineWidth = 2;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(5, 0);
            ctxHidden.lineTo(5, 10);
            ctxHidden.stroke();
            break;
          case 11:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(0, 5.5);
            ctxHidden.lineTo(10, 5.5);
            ctxHidden.moveTo(5.5, 0);
            ctxHidden.lineTo(5.5, 10);
            ctxHidden.stroke();
            break;
          case 12:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(5, 0.5);
            ctxHidden.lineTo(5, 2);
            ctxHidden.moveTo(9, 4.5);
            ctxHidden.lineTo(9, 6);
            ctxHidden.moveTo(9, 8);
            ctxHidden.lineTo(9, 9.5);
            ctxHidden.stroke();
            break;
          case 13:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(10, 0);
            ctxHidden.lineTo(0, 10);
            ctxHidden.stroke();
            break;
          case 14:
            ctxHidden.lineWidth = 1;
            ctxHidden.strokeStyle = e.color;
            ctxHidden.beginPath();
            ctxHidden.moveTo(10, 0);
            ctxHidden.lineTo(0, 10);
            ctxHidden.moveTo(0, 0);
            ctxHidden.lineTo(10, 10);
            ctxHidden.stroke();
            break;
          case 15:
            ctxHidden.fillStyle = e.color;
            ctxHidden.fillRect(0, 0, 10, 10);
            break;
          default:
            break;
        }
        ctx.fillStyle = ctx.createPattern(canvasHidden, "repeat");
      }
      if (e.type === "ellipse") {
        ctx.save();
        ctx.lineWidth = e.lineWidth;
        ctx.strokeStyle = e.color;
        const x = e.start[0];
        const y = e.start[1];
        const w = e.end[0] - e.start[0];
        const h = e.end[1] - e.start[1];
        const kappa = 0.5522848;
        const ox = (w / 2) * kappa;
        const oy = (h / 2) * kappa;
        const xe = x + w;
        const ye = y + h;
        const xm = x + w / 2;
        const ym = y + h / 2;

        ctx.beginPath();
        ctx.moveTo(x, ym);
        ctx.bezierCurveTo(x, ym - oy, xm - ox, y, xm, y);
        ctx.bezierCurveTo(xm + ox, y, xe, ym - oy, xe, ym);
        ctx.bezierCurveTo(xe, ym + oy, xm + ox, ye, xm, ye);
        ctx.bezierCurveTo(xm - ox, ye, x, ym + oy, x, ym);
        if (e.bgType !== 0) {
          ctx.fill();
        }
        ctx.closePath();
        ctx.stroke();
        ctx.restore();
      } else {
        if (e.bgType !== 0) {
          ctx.fillRect(e.start[0], e.start[1], width, height);
        }
      }
      if (e.isEdit) {
        this.drawEditRect(e, ctx, width, height);
      }
      ctx.restore();
    },
    drawEditRect(e, ctx, width, height) {
      ctx.save();
      ctx.lineWidth = 1;
      ctx.fillStyle = "white";
      ctx.strokeStyle = e.color;
      ctx.strokeRect(e.start[0] - 3, e.start[1] - 3, 6, 6);
      ctx.strokeRect(e.start[0] + width / 2 - 3, e.start[1] - 3, 6, 6);
      ctx.strokeRect(e.end[0] - 3, e.start[1] - 3, 6, 6);
      ctx.strokeRect(e.start[0] - 3, e.start[1] + height / 2 - 3, 6, 6);

      ctx.strokeRect(e.end[0] - 3, e.start[1] + height / 2 - 3, 6, 6);
      ctx.strokeRect(e.start[0] - 3, e.end[1] - 3, 6, 6);
      ctx.strokeRect(e.start[0] + width / 2 - 3, e.end[1] - 3, 6, 6);
      ctx.strokeRect(e.end[0] - 3, e.end[1] - 3, 6, 6);

      ctx.fillRect(e.start[0] - 3, e.start[1] - 3, 6, 6);
      ctx.fillRect(e.start[0] + width / 2 - 3, e.start[1] - 3, 6, 6);
      ctx.fillRect(e.end[0] - 3, e.start[1] - 3, 6, 6);
      ctx.fillRect(e.start[0] - 3, e.start[1] + height / 2 - 3, 6, 6);
      ctx.fillRect(e.end[0] - 3, e.start[1] + height / 2 - 3, 6, 6);
      ctx.fillRect(e.start[0] - 3, e.end[1] - 3, 6, 6);
      ctx.fillRect(e.start[0] + width / 2 - 3, e.end[1] - 3, 6, 6);
      ctx.fillRect(e.end[0] - 3, e.end[1] - 3, 6, 6);
      ctx.restore();
    },

    changeFontStyle() {
      this.showFontSetting = true;
    },

    getImageData() {
      if (this.insertList.length) {
        this.closeElementEdit();
        this.render();
      }
      const ctx = this.canvas.getContext("2d", { alpha: false });
      const { newWidth, newHeight } = this.calculateRotatedRectSize(
        this.image.width,
        this.image.height,
        this.editor.editor._curClickInfo.image.meta.originRotation || 0
      );
      const image_data = ctx.getImageData(0, 0, newWidth, newHeight);
      const canvas = document.createElement("canvas");
      const new_ctx = canvas.getContext("2d", { alpha: true });
      canvas.width = newWidth;
      canvas.height = newHeight;
      new_ctx.putImageData(image_data, 0, 0);
      const src = canvas.toDataURL(); // image URL
      return src;
    },

    // 关闭字体模态框
    closeWordModal() {
      this.showFontSetting = false;
      this.showInput = false;
    },
    confirmWordModal(style) {
      this.showFontSetting = false;
      this.fontStyle = style;
      this.showInput = true;
      if (this.focusElement && this.focusElement.type) {
        this.focusElement.fontStyle = style;
        this.inputOperation(
          this.focusElement.start[0],
          this.focusElement.start[1],
          this.focusElement
        );
      }
    },
    clickBgButton(i) {
      this.bgType = i;
      this.showRectBackground = false;
      for (let i = 0; i < this.insertList.length; i++) {
        const element = this.insertList[i];
        if (
          (element.type === "rect" || element.type === "ellipse") &&
          element.isEdit
        ) {
          element.bgType = this.bgType;
        }
      }
      this.render();
    },
    submit() {
      const image = this.getImageData();
      const imgData = {
        img: image,
        meta: {
          meta: {
            src: this.image.src,
            originWidth: this.image.width,
            originHeight: this.image.height,
            originRotation:
              this.editor.editor._curClickInfo.image.meta.originRotation || 0,
            params: this.insertList,
          },
        },
      };
      this.$emit("submit", imgData);
      this.cancel();
      this.initPointer();
    },
    cancel() {
      this.insertList = [];
      this.fontStyle = {
        family: "宋体",
        height: 14,
        bold: false,
        italic: false,
        underline: false,
        dblUnderLine: false,
        strikethrough: false,
        color: "#000",
        bgColor: "#ffffff",
        script: 3,
      };
      this.$emit("cancel");
      this.initPointer();
    },
  },
};
</script>

<style scoped>
.modal-bg {
  background-color: rgb(160, 160, 160);
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
}

.image-editing-header {
  display: flex;
  background-color: rgb(240, 240, 240);
}

.image-draw {
  margin: 2px;
  padding: 0 5px 0 5px;
  cursor: pointer;
  border: 1px solid rgb(240, 240, 240);
}

.image-draw:hover {
  margin: 2px;
  background-color: rgb(179, 215, 243);
  border: 1px solid rgb(0, 120, 215);
}

.click-image-draw {
  color: rgb(0, 120, 215);
  margin: 2px;
  padding: 0 5px 0 5px;
  cursor: pointer;
  border: 1px solid rgb(0, 120, 215);
}

.click-image-draw:hover {
  background-color: rgb(179, 215, 243);
}

.image-editing-content /deep/.ant-modal-body {
  padding: 0 12px 12px 12px;
  overflow: hidden;
}

.image-editing-header /deep/.font-color {
  height: 25px;
  width: 20px;
  font-size: 14px;

  vertical-align: middle;
  overflow: hidden;
}

.image-editing-header /deep/ .m-colorPicker {
  position: absolute;
  top: 19px;
  left: 6px;
  border-radius: 1px;
}

.image-editing-header /deep/ .colorBtn {
  height: 3px;
  width: 12px;
}

.line {
  width: 1px;
  background-color: rgb(150, 150, 150);
  height: 19px;
  margin: 4px 3px 0 3px;
}

.withTriangle {
  display: flex;
  position: relative;
  cursor: pointer;
}

.withTriangle:hover {
  border-radius: 2px;
  -webkit-font-smoothing: antialiased;
  background-color: rgb(220, 220, 220);
}

.line-icon {
  margin-top: -5px;
  margin-left: 5px;
}

.custom-input {
  position: absolute;
}

.right-part {
  width: 13px;
  height: 25px;
}

.right-part:hover {
  background-color: rgb(195, 195, 195);
}

.font-triangle {
  margin-top: 12px;
  margin-right: 4px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgb(124, 124, 124);
}

.triangle {
  margin-top: 12px;
  margin-right: 4px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgb(124, 124, 124);
}

.alignment {
  padding: 5px;
  width: 150px;
  text-align: left;
  cursor: pointer;
}

.line-img {
  width: 138px;
}

.modal {
  position: absolute;
  margin-top: 35px;
  box-shadow: 0 0px 10px rgb(150, 150, 150);
  background-color: rgb(244, 244, 244);
  border-radius: 2px;
  z-index: 999;
}

.text {
  display: flex;
  position: relative;
  cursor: pointer;
  line-height: 28px;
  text-align: center;
}

.hover {
  font-size: 13px;
  background-color: rgb(244, 244, 244);
  border: 1px solid rgb(244, 244, 244);
}

.hover:hover {
  background-color: rgb(188, 211, 250);
  border: 1px solid rgb(41, 138, 218);
}

.img {
  height: 18px;
  width: 18px;
  margin: -3px 5px 0 -1px;
  border: 1px solid rgb(160, 160, 160);
}
</style>
