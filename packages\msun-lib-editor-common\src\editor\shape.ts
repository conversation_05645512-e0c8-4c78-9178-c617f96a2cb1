import Renderer from "./Renderer";
import ShapeEditing from "./ShapeEditing";
import Editor from "./Editor";
import Row from "./Row";
import { ShapeMode, ViewMode } from "./Constant";
import { isNumber,isPointInRectangle } from "./Utils";
export type shapeType = "line" | "circle" | "cross" | "fold_line" | "continue_line" |"close" |"rect";
export interface shapeParams {
  startXY?: { x: number, y: number };

  endXY?: { x: number, y: number };

  lineWidth?: number;

  color?: string;

  para?: { paraId: string, spacingX: number, spacingY: number }// 储存的paraId和shape起始位置距para的Y轴距离

  addition?: { startXY: string | null, endXY: string | null, show: "endXY" | "startXY" };

  foldLine?: any[]
}
export default class Shape {
  startXY: { x: number, y: number };// 开始位置

  endXY: { x: number, y: number };// 结束位置

  lineWidth: number;// 宽度

  type: shapeType;// shape的类型

  color: string;// shape的颜色

  is_editor: boolean;// 是否处于编辑状态

  start_circle: ShapeEditing = new ShapeEditing(0, 0);// 编辑状态下的开始位置的圆圈

  end_circle: ShapeEditing = new ShapeEditing(0, 0);// 编辑状态下的结束位置的圆圈

  para?: { paraId: string, spacingX: number, spacingY: number }; // shape位置绑定的段落

  addition?: { startXY: string | null, endXY: string | null, show: "endXY" | "startXY" | number }; // 绘制直线两端的样式（圆或叉）

  foldLine?: any[]; // 绘制折线储存的数据

  constructor (params: shapeParams = {}, is_edit: boolean = false, type: shapeType = "line") {
    this.type = type;
    this.startXY = params.startXY || { x: 0, y: 0 };
    this.endXY = params.endXY || { x: 0, y: 0 };
    this.lineWidth = params.lineWidth || 0.5;
    this.color = params.color || "000";
    this.is_editor = is_edit;
    this.para = params.para || { paraId: "", spacingX: 0, spacingY: 0 };
    this.addition = params.addition || { startXY: null, endXY: null, show: "endXY" };
    this.foldLine = params.foldLine;
  }

  // 获取Y坐标最小的位置
  get shapeStart () {
    const start_y = this.startXY.y;
    const end_y = this.endXY.y;

    if (start_y > end_y) {
      return this.endXY;
    }
    return this.startXY;
  }

  // 获取Y坐标最大的位置
  get shapeEnd () {
    const start_y = this.startXY.y;
    const end_y = this.endXY.y;

    if (start_y > end_y) {
      return this.startXY;
    }
    return this.endXY;
  }

  // shapeX轴宽度
  get width () {
    return Math.abs(this.shapeEnd.x - this.shapeStart.x);
  }

  // shapeY轴高度
  get height () {
    return (this.shapeEnd.y - this.shapeStart.y);
  }

  // 返回x,y所在矩形的起点和对角线点的坐标
  static getRectStartAndEndXYByXY (x:number, y:number, shape:Shape, editor:Editor) {
    x = x - editor.page_left;
    y = y + editor.scroll_top;
    const list = [[shape.startXY.x, shape.startXY.y], [shape.startXY.x, shape.endXY.y], [shape.endXY.x, shape.startXY.y], [shape.endXY.x, shape.endXY.y]];
    let num = 10;
    let startXY = { x: 0, y: 0 };
    let endXY = { x: 0, y: 0 };
    for (let i = 0; i < list.length; i++) {
      const xy = list[i];
      if ((x - 3.5 <= xy[0] && x + 3.5 >= xy[0]) && (y - 3.5 <= xy[1] && y + 3.5 >= xy[1])) {
        num = i;
        startXY = { x: xy[0], y: xy[1] };
        break;
      }
    }
    switch (num) {
      case 0:
        endXY = { x: list[3][0], y: list[3][1] };
        break;
      case 1:
        endXY = { x: list[2][0], y: list[2][1] };
        break;
      case 2:
        endXY = { x: list[1][0], y: list[1][1] };
        break;
      case 3:
        endXY = { x: list[0][0], y: list[0][1] };
        break;
      default:
        break;
    }
    if (num !== 10) {
      return { startXY: startXY, endXY: endXY };
    } else { return null; }
  }

  /**
   * 鼠标点在编辑点和线上的处理逻辑
   */
  static shapeEditor (editor: Editor, x: number, y: number) {
    const shape = editor.internal.focus_shape;
    const result = shape.isInShapeEditing(x - editor.page_left, y + editor.scroll_top, editor);
    if (result) {
      editor.internal.shape_editor = result;
      if (shape.type === "rect") {
        const xy = Shape.getRectStartAndEndXYByXY(x, y, shape, editor);
        if (xy) {
          shape.startXY = xy.startXY;
          shape.endXY = xy.endXY;
        }
      }
    } else {
      editor.internal.is_drag_shape = true;
      editor.internal.shape_before_move_xy = { x: x, y: y };
    }
  }

  static changeShapeMode (editor: Editor, open: boolean) {
    if (editor.readonly && open) {
      editor.event.emit("message", "当前为只读模式，无法开启图形模式");
      return;
    }
    if (open) {
      editor.is_shape_mode = true;
      editor.setViewMode(ViewMode.VIEW);
      editor.setReadonly(true);
    } else {
      if (editor.is_shape_mode) {
        editor.is_shape_mode = false;
        editor.setViewMode(ViewMode.NORMAL);
        editor.setReadonly(false);
        editor.internal.draw_shape = ShapeMode.NoShape;
        editor.closeShapesEditor();
      }
    }
  }

  static updateShape (editor: Editor) {
    for (let i = 0; i < editor.shapes.length; i++) {
      const shape = editor.shapes[i];
      if (!shape.para.paraId) continue;
      const para = editor.selection.getParagraphById(shape.para.paraId);
      if (para) {
        const height = Math.abs(shape.endXY.y - shape.startXY.y);
        shape.updateShapeStartXYAndEndXY(para.top + shape.para.spacingY, height, shape.type);
      } else {
        const para = Shape.getShapeParaParamsByXY(editor, shape, shape.shapeStart.x + editor.page_left, shape.shapeStart.y);
        shape.para = para;
      }
    }
  }

  // 获取有shape的页面集合
  static getPageShapes (editor: Editor) {
    const pages_shapes: any = {};
    for (let i = 0; i < editor.shapes.length; i++) {
      const shape = editor.shapes[i];
      const shape_start_y = shape.shapeStart.y;
      const shape_end_y = shape.shapeEnd.y;
      const start_page_num = Math.floor(
        (shape_start_y - editor.config.editor_padding_top) / (editor.page_size.height + editor.config.page_margin_bottom)
      );
      const end_page_num = Math.floor(
        (shape_end_y - editor.config.editor_padding_top) / (editor.page_size.height + editor.config.page_margin_bottom)
      );

      for (let j = start_page_num; j <= end_page_num; j++) {
        if (!pages_shapes[j]) {
          pages_shapes[j] = [];
        }
        pages_shapes[j].push(shape);
      }
    }
    return pages_shapes;
  }

  /**
   * 通过原始数据获取shapes
   * @param shapes
   */
  static getShapesByRawData (shapes: any) {
    const shapes_list = [];

    for (let i = 0; i < shapes.length; i++) {
      const shape = shapes[i];

      const params: any = {};

      for (const shape_param in shape) {
        params[shape_param] = shape[shape_param];
      }
      shapes_list.push(new Shape(params, false, shape.type));
    }
    return shapes_list;
  }

  static getDragShapeLocation (editor: Editor, x: number, y: number) {
    const move_x = x - editor.internal.shape_before_move_xy.x;
    const move_y = y - editor.internal.shape_before_move_xy.y;
    const shape = editor.internal.focus_shape;
    shape.startXY.x += move_x;
    shape.startXY.y += move_y;
    shape.endXY.x += move_x;
    shape.endXY.y += move_y;
    if (shape.type === "fold_line") {
      if (shape.foldLine && shape.foldLine.length) {
        for (let i = 0; i < shape.foldLine.length; i++) {
          const point = shape.foldLine[i].point;
          point.x += move_x;
          point.y += move_y;
        }
      }
    }
    editor.internal.shape_before_move_xy = { x: x, y: y };
  }

  /**
   * 绘制shape时的阴影
   */
  static drawShapeShadow (editor: Editor) {
    Renderer.save();
    Renderer.translate(editor.page_left * editor.viewScale * editor.config.devicePixelRatio, 0);
    const offset = editor.internal.view_scale_offset;
    const scale = editor.config.devicePixelRatio * editor.viewScale;
    let startXY = {
      x: (editor.internal.focus_shape.startXY.x + offset) * scale,
      y: (editor.internal.focus_shape.startXY.y - editor.scroll_top) * scale

    };
    let endXY = {
      x: (editor.internal.focus_shape.endXY.x + offset) * scale,
      y: (editor.internal.focus_shape.endXY.y - editor.scroll_top) * scale
    };
    if (editor.internal.draw_shape === ShapeMode.Line) {
      Renderer.drawShapeLine(startXY, endXY, editor.internal.focus_shape.color, editor.internal.focus_shape.lineWidth);
    } else if (editor.internal.draw_shape === ShapeMode.Circle) {
      Renderer.drawEllipse(startXY.x, startXY.y, Math.abs(endXY.x - startXY.x), Math.abs(endXY.y - startXY.y));
    } else if (editor.internal.draw_shape === ShapeMode.Cross) {
      Renderer.drawCross(startXY.x, startXY.y, Math.abs(endXY.x - startXY.x), Math.abs(endXY.y - startXY.y));
    } else if (editor.internal.draw_shape === ShapeMode.Rect) {
      startXY = {
        x: (editor.internal.focus_shape.shapeStart.x + offset) * scale,
        y: (editor.internal.focus_shape.shapeStart.y - editor.scroll_top) * scale
      };
      endXY = {
        x: (editor.internal.focus_shape.shapeEnd.x + offset) * scale,
        y: (editor.internal.focus_shape.shapeEnd.y - editor.scroll_top) * scale
      };
      Renderer.draw_stroke_rect(startXY.x, startXY.y, Math.abs(endXY.x - startXY.x), Math.abs(endXY.y - startXY.y), "black");
    }

    Renderer.restore();
  }

  static getShapeParaParamsByXY (editor: Editor, shape: Shape, x: number, y: number) {
    let param = {
      paraId: "",
      spacingX: 0,
      spacingY: 0
    };
    const element_info = editor.getElementByPoint(x, y - editor.scroll_top);
    if (element_info.isEditHF || !element_info.page) return param;
    if (element_info.row) {
      param = Shape.getShapeParaByRow(shape, element_info.row);
    } else if (element_info.cell) {
      const cell = element_info.cell.getOrigin();
      const row = cell.children[cell.children.length - 1] as Row;
      param = Shape.getShapeParaByRow(shape, row);
    } else if (x >= editor.page_left && x <= editor.page_size.width + editor.page_left) {
      // element_info仅存在page的时候的处理逻辑
      const element = element_info.page.children;
      const row = element[element.length - 1] as Row;
      param = Shape.getShapeParaByRow(shape, row);
    }

    return param;
  }

  static getShapeParaByRow (shape: Shape, row: Row) {
    const param = {
      paraId: "",
      spacingY: 0,
      spacingX: 0
    };
    let para_top = 0;
    let para_left = 0;
    const para = row.paragraph;

    para_top = para.top;
    para_left = para.real_left;
    param.paraId = para.id;

    param.spacingX = shape.shapeStart.x - para_left;
    param.spacingY = shape.shapeStart.y - para_top;

    return param;
  }

  static handleShapes (editor: Editor) {
    const focus_shape = editor.internal.focus_shape;
    if (!editor.internal.is_in_shape) {
      const start_xy = focus_shape.startXY;

      const end_xy = focus_shape.endXY;

      if (!(start_xy.x === end_xy.x && start_xy.y === end_xy.y)) {
        focus_shape.is_editor = true;
        const shapeStart = focus_shape.shapeStart;

        const para = Shape.getShapeParaParamsByXY(editor, focus_shape, shapeStart.x + editor.page_left, shapeStart.y);

        focus_shape.para = para;

        if (editor.internal.shape_editor) return true;

        editor.shapes.push(focus_shape);

        return true;
      }
    } else if (editor.internal.shape_editor) {
      const shapeStart = focus_shape.shapeStart;

      const para = Shape.getShapeParaParamsByXY(editor, focus_shape, shapeStart.x + editor.page_left, shapeStart.y);

      focus_shape.para = para;
    }
    if (editor.internal.is_drag_shape) {
      const para = Shape.getShapeParaParamsByXY(editor, focus_shape, focus_shape.shapeStart.x + editor.page_left, focus_shape.shapeStart.y);

      focus_shape.para = para;

      return true;
    }
  }

  /**
   * 判断shape是否为选中状态
   */
  static shapeIsSelected (editor: Editor) {
    for (let i = 0; i < editor.shapes.length; i++) {
      const shape = editor.shapes[i];
      if (shape.is_editor) {
        return shape;
      }
    }
    return null;
  }

  // 根据距Y轴段落位置设置start和end的xy
  updateShapeStartXYAndEndXY (y: number, height: number, type: shapeType) {
    if (type === "circle" || type === "cross" || type === "rect" || type === "fold_line") {
      this.startXY.y = y;
      this.endXY.y = y + height;
      if (type === "fold_line" && this.foldLine && this.foldLine.length) {
        const start_y = this.foldLine[0].point.y;
        for (let i = 0; i < this.foldLine.length; i++) {
          const point = this.foldLine[i].point;
          point.y = y + point.y - start_y;
        }
      }
    } else {
      this.shapeStart.y = y;
      this.shapeEnd.y = y + height;
    }
  }

  // 是否在线的选择框内或者在编辑球内
  isInLineSign (x: number, y: number, shape: Shape, editor: Editor) {
    if (shape.type === "fold_line") {
      if (shape.foldLine && shape.foldLine.length && shape.addition?.show) {
        if (isNumber(shape.addition?.show)) {
          const foldLine = shape.foldLine[shape.addition.show as number - 1].point;
          const result = this.clickSign(foldLine, shape.addition?.show, x, y);
          return result;
        } else {
          const foldLine = shape.foldLine[shape.foldLine.length - 1].point;
          const result = this.clickSign(foldLine, shape.foldLine.length, x, y);
          return result;
        }
      }
    } else {
      const end = shape.endXY;
      const start = shape.startXY;
      const result: any = {
        location: null,
        type: null
      };
      const list: any = [
        {
          location: "startXY",
          points: [[start.x - 27.5, start.y - 35.5, start.x - 12.5, start.y - 20.5], [start.x - 7.5, start.y - 35.5, start.x + 7.5, start.y - 20.5], [start.x + 12.5, start.y - 35.5, start.x + 27.5, start.y - 20.5]]
        },
        {
          location: "endXY",
          points: [[end.x - 27.5, end.y - 35.5, end.x - 12.5, end.y - 20.5], [end.x - 7.5, end.y - 35.5, end.x + 7.5, end.y - 20.5], [end.x + 12.5, end.y - 35.5, end.x + 27.5, end.y - 20.5]]
        }];

      for (let i = 0; i < list.length; i++) {
        const pointsObj = list[i];
        for (let j = 0; j < pointsObj.points.length; j++) {
          const point = pointsObj.points[j];
          const is_in_rect = isPointInRectangle(point[0], point[1], point[2], point[3], x, y);
          if (is_in_rect) {
            result.location = pointsObj.location;
            if (j === 0) {
              result.type = "circle";
            } else if (j === 1) {
              result.type = "cross";
            } else {
              result.type = "delete";
            }
            return result;
          }
        }
      }
      const isInShapeEditing = shape.isInShapeEditing(x, y, editor);
      if (isInShapeEditing) {
        result.location = isInShapeEditing;
      }
      return result;
    }
  }

  // 点击在shape编辑球上的方法
  clickSign (foldLine: any, location: any, x: number, y: number) {
    const result: any = {
      location: null,
      type: null
    };
    result.location = location;
    const points = [[foldLine.x - 27.5, foldLine.y - 35.5, foldLine.x - 12.5, foldLine.y - 20.5], [foldLine.x - 7.5, foldLine.y - 35.5, foldLine.x + 7.5, foldLine.y - 20.5], [foldLine.x + 12.5, foldLine.y - 35.5, foldLine.x + 27.5, foldLine.y - 20.5]];
    for (let j = 0; j < points.length; j++) {
      const point = points[j];
      const is_in_rect = isPointInRectangle(point[0], point[1], point[2], point[3], x, y);
      if (is_in_rect) {
        if (j === 0) {
          result.type = "circle";
        } else if (j === 1) {
          result.type = "cross";
        } else {
          result.type = "delete";
        }
        return result;
      }
    }
    return result;
  }

  // 删除shape
  static deleteShape (editor: Editor) {
    if (editor.shapes && editor.shapes.length && editor.internal.focus_shape.is_editor) {
      const index = editor.shapes.indexOf(editor.internal.focus_shape);
      if (index > -1) {
        editor.shapes.splice(index, 1);
        editor.update();
        editor.render();
        return true;
      }
    }
    return false;
  }

  // x,y是否在shape内
  static isInShape (x: number, y: number, editor: Editor) {
    const deviation = 5;
    const shapes = [];
    // 将所有符合条件的shape放在shapes集合内
    for (let i = editor.shapes.length - 1; i >= 0; i--) {
      const shape: Shape = editor.shapes[i];
      if (shape.type === "line") {
        const line_result = Shape.isInLine(shape.shapeStart, shape.shapeEnd, x, y, deviation);
        if (line_result) {
          shapes.push(shape);
        }
      } else if (shape.type === "circle" || shape.type === "cross") {
        const width = Math.abs(shape.endXY.x - shape.startXY.x);
        const height = Math.abs(shape.endXY.y - shape.startXY.y);
        const rectWidth = 3.5;
        const result = isPointInRectangle(shape.startXY.x - width - rectWidth, shape.startXY.y - height - rectWidth, shape.startXY.x + width + rectWidth, shape.startXY.y + height + rectWidth, x, y);
        if (result) {
          shapes.push(shape);
        }
      } else if (shape.type === "rect") {
        const rectWidth = 3.5;
        let startX = shape.startXY.x - rectWidth;
        let startY = shape.startXY.y - rectWidth;
        let endX = shape.endXY.x + rectWidth;
        let endY = shape.endXY.y + rectWidth;

        if (endX < startX) {
          startX = shape.startXY.x + rectWidth;
          endX = shape.endXY.x - rectWidth;
        }
        if (endY < startY) {
          startY = shape.startXY.y + rectWidth;
          endY = shape.endXY.y - rectWidth;
        }
        const result = isPointInRectangle(startX, startY, endX, endY, x, y);

        if (result) {
          shapes.push(shape);
        }
      } else if (shape.type === "fold_line") {
        if (shape.foldLine && shape.foldLine.length) {
          const deviation = 5;
          for (let i = 0; i < shape.foldLine.length; i++) {
            const xy = shape.foldLine[i];
            const next_xy = shape.foldLine[i + 1];
            if (next_xy) {
              const result = Shape.isInLine(xy.point, next_xy.point, x, y, deviation);
              if (result) {
                shapes.push(shape);
              }
            }
          }
        }
      }
    }
    // 循环shapes集合，首先返回线和折线，其次返回在所有点击到的shape中最高点最小的shape,保证在多层shape嵌套时选中的是最内层的shape
    if (shapes.length) {
      for (let i = 0; i < shapes.length; i++) {
        const shape = shapes[i];
        if (shape.type === "line" || shape.type === "fold_line") {
          return shape;
        }
      }
      // 获取Y轴最高点最小的shape
      const shape = shapes.reduce((min, shape) => {
        const shape_y = shape.shapeStart.y - shape.height;
        const min_y = min.shapeStart.y - min.height;
        if (shape.type === "circle" || shape.type === "cross" || shape.type === "rect") {
          if (shape_y < min_y) {
            return min;
          } else {
            return shape;
          }
        } else {
          return shape;
        }
      });
      return shape;
    }
    return false;
  }

  // 是否点在了shape的线上
  static isInLine (startXY:any, endXY:any, x:number, y:number, deviation:number) {
    let points = [[startXY.x, startXY.y], [endXY.x, endXY.y]];
    if (startXY.y >= endXY.y) {
      points = [[endXY.x, endXY.y], [startXY.x, startXY.y]];
    }
    const p = [x, y];
    const sentences = Shape.getPointToLineDistance2(points, p);

    if (sentences < deviation) {
      // startXY的Y永远在上方
      if (points[0][0] < points[1][0]) {
        if (p[0] > points[0][0] - 4 && p[0] < points[1][0] + 4 && p[1] < points[1][1] + 4 && p[1] > points[0][1] - 4) {
          return true;
        }
      } else {
        if (p[0] >= points[1][0] - 4 && p[0] <= points[0][0] + 4 && p[1] <= points[1][1] + 4 && p[1] >= points[0][1] - 4) {
          return true;
        }
      }
    }
  }

  // 计算是否在线上
  static getPointToLineDistance2 (list: any, point: any) {
    const [[x1, y1], [x2, y2]] = list;
    const [x, y] = point;

    const b = Math.sqrt((x - x1) * (x - x1) + (y - y1) * (y - y1));
    const c = Math.sqrt((x - x2) * (x - x2) + (y - y2) * (y - y2));
    const a = Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));

    if (a === 0) return b; // 如果选取两点为同一个点，则返回已知点和比较点的距离即可

    // 原理：通过半周长和各边长度来计算三角形面积，即海伦公式
    const p = (a + b + c) / 2; // 半周长，halfPerimeter
    // 根据海伦公式求三角形面积
    const areaSize = Math.abs(Math.sqrt(p * (p - a) * (p - b) * (p - c)));
    // 根据三角形面积公式求证点到直线距离
    return (2 * areaSize) / a;
  }

  static shapeDeal (editor:Editor, params:any, type:shapeType, x:number, y:number) {
    // 若是折线或者继续绘制折线，且是绘制模式则往折线数据里push点
    if (editor.internal.focus_shape.type === "fold_line" && editor.internal.focus_shape.foldLine && (editor.internal.draw_shape === ShapeMode.FoldLine || editor.internal.draw_shape === ShapeMode.ContinueLine)) {
      editor.internal.focus_shape.foldLine && (editor.internal.focus_shape.foldLine.push({ point: { x: x - editor.page_left, y: y + editor.scroll_top }, type: null }));
    } else if (editor.internal.draw_shape) {
      // 若是绘制图形模式且没点击到shape上则new新的shape
      editor.internal.focus_shape = new Shape(params, false, type);
      if (editor.internal.focus_shape.type === "fold_line") {
        if (editor.internal.focus_shape.foldLine && editor.internal.focus_shape.foldLine.length) {
          const point = editor.internal.focus_shape.foldLine[0].point;
          const para = Shape.getShapeParaParamsByXY(editor, editor.internal.focus_shape, point.x + editor.page_left, point.y + editor.scroll_top);
          editor.internal.focus_shape.para = para;
        }
        editor.shapes.push(editor.internal.focus_shape);
      }
    }
    return true;
  }

  draw () {
    if (this.type === "line") {
      Renderer.drawShapeLine(this.startXY, this.endXY, this.color, this.lineWidth);
      if (this.addition) {
        if (this.addition.startXY) {
          if (this.addition.startXY === "circle") {
            Renderer.drawArc(this.startXY.x, this.startXY.y, 6, 0, 360, "black");
          } else if (this.addition.startXY === "cross") {
            Renderer.drawCross(this.startXY.x, this.startXY.y, 6, 6);
          }
        }
        if (this.addition.endXY) {
          if (this.addition.endXY === "circle") {
            Renderer.drawArc(this.endXY.x, this.endXY.y, 6, 0, 360, "black");
          } else if (this.addition.endXY === "cross") {
            Renderer.drawCross(this.endXY.x, this.endXY.y, 6, 6);
          }
        }
      }
    } else if (this.type === "circle") {
      Renderer.drawEllipse(this.startXY.x, this.startXY.y, Math.abs(this.endXY.x - this.startXY.x - 0.5), Math.abs(this.endXY.y - this.startXY.y - 0.5));
    } else if (this.type === "cross") {
      Renderer.drawCross(this.startXY.x, this.startXY.y, Math.abs(this.endXY.x - this.startXY.x), Math.abs(this.endXY.y - this.startXY.y));
    } else if (this.type === "rect") {
      const startY = this.shapeStart.y;
      let startX = this.shapeStart.x;
      if (this.shapeEnd.x < this.shapeStart.x) {
        startX = this.shapeEnd.x;
      }
      Renderer.draw_stroke_rect(startX + 0.5, startY + 0.5, this.width, this.height, "black");
    } else if (this.type === "fold_line") {
      if (this.foldLine && this.foldLine.length) {
        for (let i = 0; i < this.foldLine.length; i++) {
          const point = this.foldLine[i];
          if (this.foldLine[i + 1]) {
            Renderer.drawShapeLine(point.point, this.foldLine[i + 1].point, this.color, this.lineWidth);
          }
          if (point.type === "circle") {
            Renderer.drawArc(point.point.x, point.point.y, 6, 0, 360, "black");
          } else if (point.type === "cross") {
            Renderer.drawCross(point.point.x, point.point.y, 6, 6);
          }
        }
      }
    }

    if (this.is_editor) {
      // 要绘制的编辑圆圈点的集合

      let pointList = [[this.startXY.x, this.startXY.y], [this.endXY.x, this.endXY.y]];
      let editType = "line";
      if (this.type === "line") {
        if (this.addition) {
          if (this.addition.show === "startXY") {
            Renderer.drawShapeSign(this.startXY.x, this.startXY.y);
          } else {
            Renderer.drawShapeSign(this.endXY.x, this.endXY.y);
          }
        } else {
          Renderer.drawShapeSign(this.endXY.x, this.endXY.y);
        }
      }
      if (this.type === "circle" || this.type === "cross") {
        editType = "circle";
        const width = Math.abs(this.endXY.x - this.startXY.x);
        const height = Math.abs(this.endXY.y - this.startXY.y);
        pointList = [[this.startXY.x - width, this.startXY.y - height], [this.startXY.x + width, this.startXY.y - height], [this.startXY.x - width, this.startXY.y + height], [this.startXY.x + width, this.startXY.y + height]];
        const border = 6.5;
        Renderer.save();
        Renderer.draw_line_dash(this.startXY.y - height - 0.5, this.startXY.x - width + border, this.startXY.x + width - border, "rgb(150,150,150)");
        Renderer.draw_line_dash(this.startXY.y + height + 0.5, this.startXY.x - width + border, this.startXY.x + width - border, "rgb(150,150,150)");
        Renderer.draw_line_dash(this.startXY.x - width - 0.5, this.startXY.y - height + border, this.startXY.y + height - border, "rgb(150,150,150)", "vertical");
        Renderer.draw_line_dash(this.startXY.x + width + 0.5, this.startXY.y - height + border, this.startXY.y + height - border, "rgb(150,150,150)", "vertical");
        Renderer.restore();
      } else if (this.type === "rect") {
        editType = "circle";
        pointList = [[this.startXY.x, this.startXY.y], [this.startXY.x, this.endXY.y], [this.endXY.x, this.endXY.y], [this.endXY.x, this.startXY.y]];
      }
      if (this.type === "fold_line" && this.foldLine && this.foldLine.length) {
        pointList = [];
        for (let i = 0; i < this.foldLine.length; i++) {
          const foldPoint = this.foldLine[i];
          const circle = new ShapeEditing(foldPoint.point.x, foldPoint.point.y, editType);
          circle.draw();
          if ((this.addition && isNumber(this.addition.show) && i === this.addition.show - 1) || (this.addition && this.addition.show === "endXY" && i === this.foldLine.length - 1)) {
            Renderer.drawShapeSign(foldPoint.point.x, foldPoint.point.y);
          }
        }
        return;
      }

      for (let i = 0; i < pointList.length; i++) {
        const point = pointList[i];
        const circle = new ShapeEditing(point[0], point[1], editType);
        circle.draw();
      }
    }
  }

  /**
* 判断是否点击在shape编辑的圆圈内
* @param x
* @param y
* @returns
*/
  isInShapeEditing (x: number, y: number, editor: Editor) {
    const fold_shape = editor.internal.focus_shape;
    if (fold_shape.type === "line") {
      const in_start_circle = this.pointInsideCircle([x, y], [this.startXY.x, this.startXY.y], 4);
      const in_end_circle = this.pointInsideCircle([x, y], [this.endXY.x, this.endXY.y], 4);
      if (in_start_circle) {
        return "startXY";
      } else if (in_end_circle) {
        return "endXY";
      }
    } else if (fold_shape.type === "circle" || fold_shape.type === "cross") {
      const width = Math.abs(this.endXY.x - this.startXY.x);
      const height = Math.abs(this.endXY.y - this.startXY.y);
      const pointList = [[this.startXY.x - width, this.startXY.y - height], [this.startXY.x + width, this.startXY.y - height], [this.startXY.x - width, this.startXY.y + height], [this.startXY.x + width, this.startXY.y + height]];
      for (let i = 0; i < pointList.length; i++) {
        const point = pointList[i];
        const result = isPointInRectangle(point[0] - 3.5, point[1] - 3.5, point[0] + 3.5, point[1] + 3.5, x, y);
        if (result) {
          return i + 1;
        }
      }
      // i+1是避免判断i=0返回false
    } else if (fold_shape.type === "rect") {
      const pointList = [[this.startXY.x, this.startXY.y], [this.startXY.x, this.endXY.y], [this.endXY.x, this.startXY.y], [this.endXY.x, this.endXY.y]];
      for (let i = 0; i < pointList.length; i++) {
        const point = pointList[i];
        const result = isPointInRectangle(point[0] - 3.5, point[1] - 3.5, point[0] + 3.5, point[1] + 3.5, x, y);
        if (result) {
          return i + 1;
        }
      }
    } else if (editor.internal.focus_shape.type === "fold_line") {
      if (fold_shape.foldLine && fold_shape.foldLine.length) {
        for (let i = 0; i < fold_shape.foldLine.length; i++) {
          const point = fold_shape.foldLine[i].point;
          const result = isPointInRectangle(point.x - 3.5, point.y - 3.5, point.x + 3.5, point.y + 3.5, x, y);
          if (result) {
            return i + 1;
          }
        }
      }
    }

    return false;
  }



  /**
*  判断一个点是否在圆内
* @param point 点
* @param circle 圆心位置
* @param r 圆半径
* @returns
*/
  pointInsideCircle (point: any, circle: any, r: number) {
    if (r === 0) return false;
    const dx = circle[0] - point[0];
    const dy = circle[1] - point[1];
    return dx * dx + dy * dy <= r * r;
  }
}
