<template>
  <modal
    title="直接打印配置"
    class="table-modal"
    :show="show"
    :width="width"
    :freePoint="true"
    :sessionMove="true"
    pointer-events:none
    @cancel="cancel"
  >
    <div class="editor-ruler-editor">
      <div>
        <div class="print-order">
          <span class="print-title">打印份数：</span>
          <a-input-number
            class="editor-inputNumber"
            v-model="printMultiple"
            :min="1"
            :max="100"
          />
        </div>
        <div class="print-order">
          <span class="print-title">右偏移：</span>
          <a-input-number
            class="editor-inputNumber"
            style="margin-left: 15px"
            v-model="marginLeft"
          />
          <span class="print-title" style="margin-left: 15px">下偏移：</span>
          <a-input-number class="editor-inputNumber" v-model="marginTop" />
        </div>
        <div class="print-order">
          <span class="print-title">打印顺序：</span>
          <a-radio-group
            style="width: 100%"
            v-model="sortValue"
            :default-value="1"
          >
            <a-radio :value="1">正序</a-radio>
            <a-radio :value="2">逆序</a-radio>
          </a-radio-group>
        </div>

        <div class="print-order">
          <span class="print-title">双面打印</span>
          <a-radio-group
            style="width: 100%"
            v-model="doublePrint"
            :default-value="0"
          >
            <div class="editor-radio-row">
              <a-radio :value="2">默认</a-radio>
              <a-radio :value="0">单面</a-radio>
              <a-radio :value="1">双面</a-radio>
              <a-radio :value="3">双面(兼容)</a-radio>
            </div>
          </a-radio-group>
        </div>

        <div v-if="doublePrint === 1" class="print-order">
          <span class="print-title">翻转方式：</span>
          <a-radio-group
            style="width: 100%"
            v-model="duplexFlip"
            :default-value="1"
          >
            <a-radio :value="1">长边</a-radio>
            <a-radio :value="2">短边</a-radio>
          </a-radio-group>
        </div>
        <div class="print-order">
          <span class="print-title">彩色打印：</span>
          <a-radio-group style="width: 100%" v-model="color" :default-value="1">
            <a-radio :value="1">彩色</a-radio>
            <a-radio :value="0">黑白</a-radio>
          </a-radio-group>
        </div>
        <div class="print-order">
          <span class="print-title">纸张摆放（仅A5生效）：</span>
          <a-radio-group
            style="width: 100%"
            v-model="paperOrientation"
            :default-value="0"
          >
            <a-radio :value="0">纵向</a-radio>
            <a-radio :value="1">横向</a-radio>
          </a-radio-group>
        </div>
      </div>
    </div>
    <div
      slot="editor-modal-footer"
      class="footer"
      style="padding-top: 0px; margin-left: 60%"
    >
      <div>
        <a-button type="default" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "./common/modal.vue";

export default {
  name: "rulerEditor",
  components: { modal },
  data() {
    return {
      width: 430,
      printMultiple: 1,
      sortValue: 1,
      doublePrint: 2,
      duplexFlip: 3, //双面打印翻转方式
      color: 1,
      marginLeft: 0,
      marginTop: 0,
      paperOrientation: 0,
    };
  },

  props: {
    show: {
      type: Boolean,
      default: false,
    },
    field: {
      type: Object,
      default: () => {},
    },
    replaceRule: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    formattedText() {
      return this.afterHandledExample.replace(/\\n/g, "\n");
    },
  },
  watch: {
    show(val) {
      if (val) {
        const editor = this.editor.editor;
        if (
          editor.document_meta &&
          editor.document_meta.immediatePrintSetting
        ) {
          const {
            sortValue,
            doublePrint,
            duplexFlip,
            color,
            marginLeft,
            marginTop,
            paperOrientation,
          } = editor.document_meta.immediatePrintSetting;
          this.sortValue = sortValue;
          this.doublePrint = doublePrint;
          this.duplexFlip = duplexFlip;
          this.color = color;
          this.marginLeft = marginLeft;
          this.marginTop = marginTop;
          this.paperOrientation = paperOrientation;
        }
      }
    },

    immediate: true,
    deep: true,
  },
  methods: {
    cancel() {
      this.$emit("cancel");
    },
    submit() {
      const immediatePrintSetting = {
        printMultiple: this.printMultiple,
        sortValue: this.sortValue,
        doublePrint: this.doublePrint,
        duplexFlip: this.duplexFlip,
        color: this.color,
        marginLeft: this.marginLeft,
        marginTop: this.marginTop,
        paperOrientation: this.paperOrientation,
      };
      this.$emit("submit", immediatePrintSetting);
    },
  },
};
</script>

<style scoped>
.footer {
  display: flex;
  justify-content: space-between;
  padding-top: 35px;
}
.editor-ruler-editor {
  margin-bottom: 10px;
}
.print-order {
  margin-top: 5px;
}
.print-title {
  font-size: 16px;
  width: 100%;
}
.print-list {
  border: 1px solid rgb(50, 144, 252);
  width: 100%;
  padding-left: 10px;
  margin-top: 10px;
  height: calc(100% - 130px);
  border-radius: 10px;
  overflow-x: hidden;
  overflow-y: auto;
}
</style>
