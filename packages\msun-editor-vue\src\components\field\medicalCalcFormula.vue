<template>
  <modal
    id="medicalCalcFormula"
    :show="show"
    :width="640"
    title="医学公式"
    @cancel="cancel"
  >
    <a-row type="flex" justify="space-around" align="middle">
      <a-col :span="7">
        <a-menu
          mode="vertical"
          @click="handleClick"
          v-model="selectedKeys"
          style="height: 432.13px; overflow: auto"
        >
          <a-sub-menu key="bnjs">
            <template #title>补钠计算</template>
            <a-sub-menu key="bnjs-male" title="男性可用公式">
              <a-menu-item key="bnjs-male-1">应补钠总量</a-menu-item>
              <a-menu-item key="bnjs-male-2">应补氯化钠总量</a-menu-item>
              <a-menu-item key="bnjs-male-3">应补生理盐水</a-menu-item>
              <a-menu-item key="bnjs-male-4">应补3%氯化钠</a-menu-item>
              <a-menu-item key="bnjs-male-5">应补5%氯化钠</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="bnjs-female" title="女性可用公式">
              <a-menu-item key="bnjs-female-1">应补钠总量</a-menu-item>
              <a-menu-item key="bnjs-female-2">应补氯化钠总量</a-menu-item>
              <a-menu-item key="bnjs-female-3">应补生理盐水</a-menu-item>
              <a-menu-item key="bnjs-female-4">应补3%氯化钠</a-menu-item>
              <a-menu-item key="bnjs-female-5">应补5%氯化钠</a-menu-item>
            </a-sub-menu>
          </a-sub-menu>
          <a-sub-menu key="byjs">
            <template #title>补液计算</template>
            <a-menu-item key="byjs-1">输液量</a-menu-item>
            <a-menu-item key="byjs-2">补液量</a-menu-item>
          </a-sub-menu>
          <a-sub-menu key="btjs">
            <template #title>补铁计算</template>
            <a-menu-item key="btjs-1">总缺铁量</a-menu-item>
            <a-menu-item key="btjs-2">需补充铁量</a-menu-item>
          </a-sub-menu>
          <a-sub-menu key="djzbcjs">
            <template #title>电解质补充计算</template>
            <a-menu-item key="djzbcjs-1">电解质缺少总量</a-menu-item>
            <a-menu-item key="djzbcjs-2">克数</a-menu-item>
          </a-sub-menu>
          <a-sub-menu key="jxnlxhjs">
            <template #title>静息能量消耗计算</template>
            <a-sub-menu key="jxnlxhjs-male" title="男性可用公式">
              <a-menu-item key="jxnlxhjs-male-1"
                >Harris-Benedict计算公式</a-menu-item
              >
            </a-sub-menu>
            <a-sub-menu key="jxnlxhjs-female" title="女性可用公式">
              <a-menu-item key="jxnlxhjs-female-1"
                >Harris-Benedict计算公式</a-menu-item
              >
            </a-sub-menu>
          </a-sub-menu>
          <a-sub-menu key="Ccr">
            <template #title>肌酐清除率计算</template>
            <a-sub-menu key="Ccr-male" title="男性可用公式">
              <a-menu-item key="Ccr-male-1">Ccr计算公式</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="Ccr-female" title="女性可用公式">
              <a-menu-item key="Ccr-female-1">Ccr计算公式</a-menu-item>
            </a-sub-menu>
          </a-sub-menu>
          <a-sub-menu key="ywsysdjs">
            <template #title>药物输液速度计算</template>
            <a-sub-menu key="ywsysdjs-sysdpd" title="输液速度判定">
              <a-menu-item key="ywsysdjs-sysdpd-1"
                >每小时输入量(ml)</a-menu-item
              >
              <a-menu-item key="ywsysdjs-sysdpd-2"
                >每分钟滴数(gtt/min)</a-menu-item
              >
              <a-menu-item key="ywsysdjs-sysdpd-3">输液所需时间(h)</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="ywsysdjs-jmsydjsjsf" title="静脉输液滴进数计算法">
              <a-menu-item key="ywsysdjs-jmsydjsjsf-1"
                >每分钟滴数(gtt/min)</a-menu-item
              >
              <a-menu-item key="ywsysdjs-jmsydjsjsf-2"
                >每小时输入量(ml)</a-menu-item
              >
            </a-sub-menu>
          </a-sub-menu>
          <a-sub-menu key="tbmj">
            <template #title>体表面积</template>
            <a-sub-menu key="tbmj-male" title="成年男性">
              <a-menu-item key="tbmj-male-1">体表面积</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="tbmj-female" title="成年女性">
              <a-menu-item key="tbmj-female-1">体表面积</a-menu-item>
            </a-sub-menu>
            <a-sub-menu key="tbmj-kids" title="小儿">
              <a-menu-item key="tbmj-kids-1">体表面积</a-menu-item>
            </a-sub-menu>
          </a-sub-menu>
          <a-sub-menu key="ck">
            <template #title>产科</template>
            <a-menu-item key="ck-1">孕周</a-menu-item>
          </a-sub-menu>
          <a-menu-item key="bmi"> BMI </a-menu-item>
        </a-menu>
      </a-col>
      <a-col :span="17">
        <a-card style="height: 432.13px">
          <div class="breadcrumb">{{ formData.titlePath }}</div>
          <a-form
            layout="horizontal"
            :label-col="{ span: 10 }"
            :wrapper-col="{ span: 12 }"
            v-show="formData.placeholders?.[0] !== `末次月经第一天：`"
          >
            <a-form-item
              v-for="(placeholder, index) in formData.placeholders"
              :label="placeholder + formData.unit[index]"
              :key="placeholder + index"
            >
              <a-input
                v-model="inputValueObj[placeholder]"
                :placeholder="placeholder"
              />
            </a-form-item>
            <b> {{ formData.formulaText }} </b>
          </a-form>
          <div v-show="formData.placeholders?.[0] === `末次月经第一天：`">
            <a-date-picker
              dropdownClassName="xeditor-input-up set-date"
              @change="onChange"
            />
            <div>
              <b>
                {{ gestationalAgeCalculation }}
              </b>
            </div>
          </div>
          <br />
          <div class="calculationResult">
            <b
              >计算结果:
              {{
                formData.placeholders?.[0] === `末次月经第一天：`
                  ? formData.formulaFn?.(inputValueObj)
                  : keepDecimal(formData.formulaFn?.(inputValueObj), 2)
              }}</b
            >
          </div>
          <div class="introduce">
            <span>说明: 将文本域的背景文本设置为:</span>
            <span style="color: #000">{{ formData.dblclickPlaceholder }}</span>
            <span>可通过双击这个文本域,弹出该界面</span>;
            <br />
            <span>自动获取"背景文本"值分别为</span>
            <span style="color: gray" v-show="true">|</span>
            <span
              style="color: #000"
              v-for="placeholder in formData.placeholders"
              :key="placeholder"
              >{{ placeholder
              }}<span style="color: gray" v-show="true">|</span> </span
            >的文本域的值,填充上方的输入框,做计算
          </div>
        </a-card>
      </a-col>
    </a-row>

    <div slot="editor-modal-footer" class="parent-footer">
      <div>
        <a-button type="defalut" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../common/modal.vue";
import { keepDecimal } from "../../assets/js/utils";
function isNumber(value) {
  return !isNaN(parseFloat(value)) && isFinite(value);
}
const map = new Map([
  [
    "bnjs-male-1",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×0.6`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            0.6;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>男性可用公式=>应补钠总量",
        dblclickPlaceholder: "应补钠总量",
      };
    },
  ],
  [
    "bnjs-male-2",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×0.035`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            0.035;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>男性可用公式=>应补氯化钠总量",
        dblclickPlaceholder: "应补氯化钠总量",
      };
    },
  ],
  [
    "bnjs-male-3",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×3.888`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            3.888;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>男性可用公式=>应补生理盐水",
        dblclickPlaceholder: "应补生理盐水",
      };
    },
  ],
  [
    "bnjs-male-4",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×1.1666`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            1.1666;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>男性可用公式=>应补3%氯化钠",
        dblclickPlaceholder: "应补3%氯化钠",
      };
    },
  ],
  [
    "bnjs-male-5",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×0.7`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            0.7;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>男性可用公式=>应补5%氯化钠",
        dblclickPlaceholder: "应补5%氯化钠",
      };
    },
  ],
  [
    "bnjs-female-1",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×0.5`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            0.5;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>女性可用公式=>应补钠总量",
        dblclickPlaceholder: "应补钠总量",
      };
    },
  ],
  [
    "bnjs-female-2",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×0.03`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            0.03;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>女性可用公式=>应补氯化钠总量",
        dblclickPlaceholder: "应补氯化钠总量",
      };
    },
  ],
  [
    "bnjs-female-3",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×3.311`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            3.311;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>女性可用公式=>应补生理盐水",
        dblclickPlaceholder: "应补生理盐水",
      };
    },
  ],
  [
    "bnjs-female-4",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×3.311`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            3.311;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>女性可用公式=>应补3%氯化钠",
        dblclickPlaceholder: "应补3%氯化钠",
      };
    },
  ],
  [
    "bnjs-female-5",
    () => {
      return {
        formulaText: `应补钠总量(mmol/L)= [142-病人血Na﹢(mmol/L)]×体重(kg)×0.596`,
        formulaFn(obj) {
          const res =
            (142 - parseFloat(obj["病人血Na﹢"])) *
            parseFloat(obj["体重"]) *
            0.596;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["病人血Na﹢", "体重"],
        unit: ["(mmol/L)", "(kg)"],
        titlePath: "补钠计算=>女性可用公式=>应补5%氯化钠",
        dblclickPlaceholder: "应补5%氯化钠",
      };
    },
  ],
  [
    "byjs-1",
    () => {
      return {
        formulaText: `输液量=正常血容量×(正常红细胞比积/患者红细胞比积)`,
        formulaFn(obj) {
          const res =
            parseFloat(obj["正常血容量"]) *
            (parseFloat(obj["正常红细胞比积"]) /
              parseFloat(obj["患者红细胞比积"]));
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["正常血容量", "正常红细胞比积", "患者红细胞比积"],
        unit: ["", "", ""],
        titlePath: "补液计算=>输液量",
        dblclickPlaceholder: "输液量",
      };
    },
  ],
  [
    "byjs-2",
    () => {
      return {
        formulaText: `补液量=1/2累计损失量+当天额外损失量+每天正常需要量`,
        formulaFn(obj) {
          const res =
            0.5 * parseFloat(obj["累计损失量"]) +
            Number(parseFloat(obj["当天额外损失量"])) +
            Number(parseFloat(obj["每天正常需要量"]));
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["累计损失量", "当天额外损失量", "每天正常需要量"],
        unit: ["", "", ""],
        titlePath: "补液计算=>补液量",
        dblclickPlaceholder: "补液量",
      };
    },
  ],
  [
    "btjs-1",
    () => {
      return {
        formulaText: `总缺铁量[mg]=体重[kg]×(Hb目标值-Hb实际值)[g/1]×0.238+储存铁量(mg) <br>储存铁量=10mg/kg×体重`,
        formulaFn(obj) {
          const cctl = 10 * obj["体重"];
          const res =
            parseFloat(obj["体重"]) *
              (parseFloat(obj["Hb目标值"]) - parseFloat(obj["Hb实际值"])) *
              0.238 +
            cctl;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "Hb目标值", "Hb实际值"],
        unit: ["(kg)", "", ""],
        titlePath: "补铁计算=>总缺铁量",
        dblclickPlaceholder: "总缺铁量",
      };
    },
  ],
  [
    "btjs-2",
    () => {
      return {
        formulaText: `需补充铁量[mg]=失血单位量×200`,
        formulaFn(obj) {
          const res = parseFloat(obj["失血单位量"]) * 200;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["失血单位量"],
        unit: [""],
        titlePath: "补铁计算=>需补充铁量",
        dblclickPlaceholder: "需补充铁量",
      };
    },
  ],
  [
    "djzbcjs-1",
    () => {
      return {
        formulaText: `缺少总量=(正常mmol/L-测得mmol/L)×体重(kg)×0.6`,
        formulaFn(obj) {
          const res =
            (parseFloat(obj["正常量"]) - parseFloat(obj["测得量"])) *
            parseFloat(obj["体重"]) *
            0.6;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "正常量", "测得量"],
        unit: ["(kg)", "", ""],
        titlePath: "电解质补充计算=>电解质缺少总量",
        dblclickPlaceholder: "电解质缺少总量",
      };
    },
  ],
  [
    "djzbcjs-2",
    () => {
      return {
        formulaText: `克数 = (正常mmol/L - 测得mmol/L) × 体重(kg) × 0.6克数 / 1g电解质所含mmol数`,
        formulaFn(obj) {
          const res =
            ((parseFloat(obj["正常量"]) - parseFloat(obj["测得量"])) *
              parseFloat(obj["体重"]) *
              0.6) /
            parseFloat(obj["1g电解质所含mmol数"]);
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "正常量", "测得量", "1g电解质所含mmol数"],
        unit: ["(kg)", "", "", ""],
        titlePath: "电解质补充计算=>克数",
        dblclickPlaceholder: "克数",
      };
    },
  ],
  [
    "jxnlxhjs-male-1",
    () => {
      return {
        formulaText: `Harris-Benedict计算公式:REE(Kcal/d)=66+13.7W+5.0H-6.8A`,
        formulaFn(obj) {
          const res =
            66 +
            13.7 * parseFloat(obj["体重"]) +
            5.0 * parseFloat(obj["身高"]) -
            6.8 * parseFloat(obj["年龄"]);
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "身高", "年龄"],
        unit: ["(kg)", "(cm)", "(岁)"],
        titlePath: "静息能量消耗计算=>男性可用公式=>Harris-Benedict计算公式",
        dblclickPlaceholder: "Harris-Benedict",
      };
    },
  ],
  [
    "jxnlxhjs-female-1",
    () => {
      return {
        formulaText: `Harris-Benedict计算公式:REE(Kcal/d)=655+9.6W+1.7H-4.7A`,
        formulaFn(obj) {
          const res =
            655 +
            9.6 * parseFloat(obj["体重"]) +
            1.7 * parseFloat(obj["身高"]) -
            4.7 * parseFloat(obj["年龄"]);
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "身高", "年龄"],
        unit: ["(kg)", "(cm)", "(岁)"],
        titlePath: "静息能量消耗计算=>女性可用公式=>Harris-Benedict计算公式",
        dblclickPlaceholder: "Harris-Benedict",
      };
    },
  ],
  [
    "Ccr-male-1",
    () => {
      return {
        formulaText: `Ccr计算公式:[(140-年龄)x体重(kg)]/[0.818xScr(umol/L)]`,
        formulaFn(obj) {
          const res =
            ((140 - parseFloat(obj["年龄"])) * parseFloat(obj["体重"])) /
            (0.818 * parseFloat(obj["Scr"]));
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "Scr", "年龄"],
        unit: ["(kg)", "(umol/L)", "(岁)"],
        titlePath: "Ccr计算=>男性可用公式=>Ccr计算公式",
        dblclickPlaceholder: "Ccr",
      };
    },
  ],
  [
    "Ccr-female-1",
    () => {
      return {
        formulaText: `Ccr计算公式:[(140-年龄)x体重(kg)]/[0.818xScr(umol/L)]*0.85`,
        formulaFn(obj) {
          const res =
            (((140 - parseFloat(obj["年龄"])) * parseFloat(obj["体重"])) /
              (0.818 * parseFloat(obj["Scr"]))) *
            0.85;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "Scr", "年龄"],
        unit: ["(kg)", "(umol/L)", "(岁)"],
        titlePath: "Ccr计算=>女性可用公式=>Ccr计算公式",
        dblclickPlaceholder: "Ccr",
      };
    },
  ],
  [
    "ywsysdjs-sysdpd-1",
    () => {
      return {
        formulaText: `每小时输入量(ml)=每分钟滴数×4`,
        formulaFn(obj) {
          const res = parseFloat(obj["每分钟滴数"]) * 4;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["每分钟滴数"],
        unit: [""],
        titlePath: "药物输液速度计算=>输液速度判定=>每小时输入量(ml)",
        dblclickPlaceholder: "每小时输入量",
      };
    },
  ],
  [
    "ywsysdjs-sysdpd-2",
    () => {
      return {
        formulaText: `每分钟滴数(gtt/min)=输入液体总ml数÷[输液总时间(h)×4]`,
        formulaFn(obj) {
          const res =
            parseFloat(obj["输入液体总ml数"]) /
            (parseFloat(obj["输液总时间"]) * 4);
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["输入液体总ml数", "输液总时间"],
        unit: ["", "(h)"],
        titlePath: "药物输液速度计算=>输液速度判定=>每分钟滴数(gtt/min)",
        dblclickPlaceholder: "每分钟滴数",
      };
    },
  ],
  [
    "ywsysdjs-sysdpd-3",
    () => {
      return {
        formulaText: `输液所需时间(h)=输入液体总ml数÷(每分钟滴数×4)`,
        formulaFn(obj) {
          const res =
            parseFloat(obj["输入液体总ml数"]) /
            (parseFloat(obj["每分钟滴数"]) * 4);
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["每分钟滴数", "输入液体总ml数"],
        unit: ["", ""],
        titlePath: "药物输液速度计算=>输液速度判定=>输液所需时间(h)",
        dblclickPlaceholder: "输液所需时间",
      };
    },
  ],
  [
    "ywsysdjs-jmsydjsjsf-1",
    () => {
      return {
        formulaText: `每分钟滴数 = 每h输入量 × 每ml滴数(15gtt) / 60(min)`,
        formulaFn(obj) {
          const res = (parseFloat(obj["每小时输入量"]) * 15) / 60;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["每小时输入量"],
        unit: [""],
        titlePath:
          "药物输液速度计算=>静脉输液滴进数计算法=>每分钟滴数(gtt/min)",
        dblclickPlaceholder: "每分钟滴数1",
      };
    },
  ],
  [
    "ywsysdjs-jmsydjsjsf-2",
    () => {
      return {
        formulaText: `每h输入量 = 每min滴数 × 60(min) / 每min相当滴数(15gtt)`,
        formulaFn(obj) {
          const res = (parseFloat(obj["每分钟滴数"]) * 60) / 15;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["每分钟滴数"],
        unit: [""],
        titlePath: "药物输液速度计算=>静脉输液滴进数计算法=>每小时输入量(ml)",
        dblclickPlaceholder: "每小时输入量1",
      };
    },
  ],
  [
    "tbmj-male-1",
    () => {
      return {
        formulaText: `成年男性 BSA=0.00607H+0.0127W-0.0698`,
        formulaFn(obj) {
          const res =
            0.00607 * parseFloat(obj["身高"]) +
            0.0127 * parseFloat(obj["体重"]) -
            0.0698;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "身高"],
        unit: ["(kg)", "(cm)"],
        titlePath: "体表面积=>成年男性=>体表面积",
        dblclickPlaceholder: "体表面积",
      };
    },
  ],
  [
    "tbmj-female-1",
    () => {
      return {
        formulaText: `成年女性 BSA=0.00586H+0.0126W-0.0461`,
        formulaFn(obj) {
          const res =
            0.00586 * parseFloat(obj["身高"]) +
            0.0126 * parseFloat(obj["体重"]) -
            0.0461;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "身高"],
        unit: ["(kg)", "(cm)"],
        titlePath: "体表面积=>成年女性=>体表面积",
        dblclickPlaceholder: "体表面积",
      };
    },
  ],
  [
    "tbmj-kids-1",
    () => {
      return {
        formulaText: `小儿 BSA=0.0061H+0.0128W-0.1529`,
        formulaFn(obj) {
          const res =
            0.0061 * parseFloat(obj["身高"]) +
            0.0128 * parseFloat(obj["体重"]) -
            0.1529;
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "身高"],
        unit: ["(kg)", "(cm)"],
        titlePath: "体表面积=>小儿=>体表面积",
        dblclickPlaceholder: "体表面积",
      };
    },
  ],
  [
    "ck-1",
    () => {
      return {
        formulaText: `孕周计算：（当前日期-末次月经的第一天）除以7得到孕周，孕期按照280天计算`,
        formulaFn(obj) {
          return obj["末次月经第一天："];
        },
        placeholders: ["末次月经第一天："],
        unit: [""],
        titlePath: "产科=>孕周",
        dblclickPlaceholder: "孕周",
      };
    },
  ],
  [
    "bmi",
    () => {
      return {
        formulaText: `BMI = 体重(kg) / 身高²(m²)`,
        formulaFn(obj) {
          const res =
            parseFloat(obj["体重"]) /
            Math.pow(parseFloat(obj["身高"]) / 100, 2);
          return isNumber(res) && !isNaN(res) ? res : "";
        },
        placeholders: ["体重", "身高"],
        unit: ["(kg)", "(cm)"],
        titlePath: "BMI",
        dblclickPlaceholder: "BMI",
      };
    },
  ],
]);

export default {
  name: "medicalCalcFormula",
  data() {
    return {
      date: "",
      formData: {},
      inputValueObj: {},
      gestationalAgeCalculation: "",
      gestationalAgeCalculationRes: "",
      selectedKeys: [], // 医学计算公式选中项
    };
  },
  components: {
    modal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    open(selectedKey) {
      selectedKey = selectedKey || "bnjs-male-1";
      this.$set(this.selectedKeys, 0, selectedKey);
      this.$set(this.inputValueObj, {});
      const res = map.get(selectedKey)();
      if (!res) return;
      this.handleFormData(res);
    },
    cancel() {
      this.$emit("cancel");
    },
    submit() {
      this.$emit("submit", this.formData, this.inputValueObj);
    },
    handleFormData(res) {
      const { editor } = this.editor;
      const fieldMap = editor.getFieldsByPlaceholder(res.placeholders);
      res.placeholders.forEach((placeholder) => {
        this.$set(
          this.inputValueObj,
          placeholder,
          fieldMap.has(placeholder) ? fieldMap.get(placeholder)[0].text : ""
        );
      });
      this.formData = res;
    },
    handleClick({ key }) {
      this.$set(this.inputValueObj, {});
      const res = map.get(key)();
      this.handleFormData(res);
    },
    onChange(_, date) {
      if (!date) {
        this.gestationalAgeCalculation = "";
        this.gestationalAgeCalculationRes = "";
        return;
      }
      const today = new Date();
      const dueDate = new Date(date);
      dueDate.setDate(dueDate.getDate() + 280); // 根据妊娠期计算预产期
      const diffTime = Math.abs(dueDate - today);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // 距离预产期的天数

      const gestationDays = Math.floor(
        (today - new Date(date)) / (1000 * 60 * 60 * 24)
      );
      const weeks = Math.floor(gestationDays / 7);
      const days = gestationDays % 7;

      const result = `今天是${today.getFullYear()}-${
        today.getMonth() + 1
      }-${today.getDate()}，预产期是${dueDate.getFullYear()}-${
        dueDate.getMonth() + 1
      }-${dueDate.getDate()}，您已经怀孕${gestationDays}天（第${weeks}周+${days}天），距离预产期还有${diffDays}天。`;
      this.gestationalAgeCalculation =
        "孕周计算：（当前日期-末次月经的第一天）除以7得到孕周，孕期按照280天计算" +
        "  " +
        result;
      this.gestationalAgeCalculationRes = `${weeks}周+${days}天`;
      this.$set(
        this.inputValueObj,
        "末次月经第一天：",
        this.gestationalAgeCalculationRes
      );
    },
    keepDecimal: keepDecimal,
  },
};
</script>
<style scoped>
#medicalCalcFormula .breadcrumb {
  color: gray;
  line-height: 24px;
  font-size: 12px;
}
#medicalCalcFormula .calculationResult {
  height: 20px;
  color: red;
}

#medicalCalcFormula .introduce {
  margin-top: 8px;
  color: gray;
  line-height: 18px;
  font-size: 12px;
}

#medicalCalcFormula >>> .ant-form-item {
  margin-bottom: 10px;
}
</style>
