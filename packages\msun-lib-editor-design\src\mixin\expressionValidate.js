const expressionValidate = {
  data() {
    return {
      fieldMap: null,
    };
  },
  methods: {
    // 提取中括号内容 包括中括号的边框
    extractUnquotedBracketContentsToSet(expressionStr) {
      const inputFieldSet = new Set();
      let startStr = "",
        startIndex = 0;
      for (let i = 0; i < expressionStr.length; i++) {
        if (!startStr) {
          if (['"', "'", "["].includes(expressionStr[i])) {
            // 循环字符串的每个字符 如果当前的字符 是 双引号 单引号 或者 中括号的一半 就记录下来 并且标记位置
            startStr = expressionStr[i];
            startIndex = i;
          }
        } else {
          if (startStr === "[" && expressionStr[i] === "]") {
            inputFieldSet.add(expressionStr.substring(startIndex, i + 1));
            startStr = "";
          } else if (startStr === expressionStr[i]) {
            startStr = "";
          }
        }
      }
      return inputFieldSet;
    },

    // 给 this.fieldMap 赋值
    updateFieldMapByAllDataSetTree(force) {
      if (this.fieldMap && !force) return this.fieldMap;
      for (const key in this.allDataSetTree) {
        for (const field of this.allDataSetTree[key]) {
          if (!this.fieldMap) {
            this.fieldMap = new Map();
          }
          if (!this.fieldMap.has("[" + field.code + "]")) {
            this.fieldMap.set("[" + field.code + "]", true);
          }
        }
      }
    },

    replaceRule(expression) {
      let expressionStr = expression.formula;
      expressionStr = expressionStr
        .replace(/[，]/g, ",")
        .replace(/[‘’]/g, "'")
        .replace(/[\f\n\v\r]/g, "")
        .replace(/===/g, "=")
        .replace(/==/g, "=")
        .trim();
      this.updateFieldMapByAllDataSetTree();

      const bracketsContentSet =
        this.extractUnquotedBracketContentsToSet(expressionStr);

      const externalBrackedContent = []; // 放数据集里边没有的 表达式里边有的中括号的内容

      for (let str of bracketsContentSet) {
        if (!this.fieldMap.has(str)) {
          externalBrackedContent.push(str);
        }
      }
      return { expressionStr, externalBrackedContent };
    },
    parenthesisIsPaired(expressionStr) {
      // 检查小括号是否合法
      const stack = [];
      for (let i = 0; i < expressionStr.length; i++) {
        if (expressionStr[i] === "(") {
          stack.push("(");
        } else if (expressionStr[i] === ")") {
          if (stack.length === 0) {
            return false;
          }
          stack.pop();
        }
      }
      return !stack.length;
    },

    parseExpressionStr(expressionStr) {
      expressionStr = expressionStr.startsWith("=")
        ? expressionStr.slice(1)
        : expressionStr;
    },
  },
};

export default expressionValidate;

// // 入参是需要校验的字符串 替换掉中文符号为英文 拿到数据集里边没有的字段返回
// replaceRule(rule) {
//     rule = rule
//       .replace(/[，]/g, ',')
//       .replace(/[‘’]/g, '\'')
//       .replace(/[\f\n\v\r]/g, '')
//       .replace(/===/g, '=')
//       .replace(/==/g, '=')
//       .trim();
//     // 【确定出哪些字段字符没有找到字段，提示出来；最新：在引号内的[xxx]不解析】
//     const inputFieldSet = new Set();

//     let startStr = '', startIndex = 0;
//     for (let i =0; i< rule.length;i++) {
//       if (!startStr) {
//         if (['"','\'', '['].includes(rule[i])) {
//           startStr = rule[i];
//           startIndex = i;
//         }
//       } else {
//         if(startStr === '[' && rule[i] === ']') {
//           inputFieldSet.add(rule.substring(startIndex, i + 1));
//           startStr = '';
//         } else if(startStr === rule[i]) {
//           startStr = '';
//         }
//       }
//     }
//     const designFields = this.designFields.filter(item => item.value !== this.editValue);
//     const systemFields = this.systemFields.map(it => ({
//       value: `系统:${it.name}`,
//       name: it.value,
//       isSystem: true
//     }));
//     designFields.push(...this.externalParamList.map(it => ({
//       value: `参数:${it.key}`,
//       name: it.value
//     })),...systemFields);
//     let treeRule = rule;
//     designFields.forEach(item => {
//       if (rule.includes(`[${item.value}]`)) {
//         inputFieldSet.delete(`[${item.value}]`);
//         if (item.isSystem) {
//           treeRule = treeRule.replaceAll(`[${item.value}]`,`[${item.value}<<>>${item.name}]`);
//         }
//       } else if (rule.includes(`[${item.name}_${item.value}]`)) {
//         inputFieldSet.delete(`[${item.name}_${item.value}]`);
//         rule = rule.replace(new RegExp(`\\[${item.name}_${item.value}\\]`, 'g'), `[${item.value}]`);
//       }
//     });
//     return { rule, inputFieldSet };
//   },

//   // 入参 是 inputFieldSet 没找到的字段集合 确认没找到的是不是字符串 是字符串就改成字符串 不确认就自己改
//   confirmField(sets) { // 存在没找到的字段，需要确认是否继续操作
//     const h = this.$createElement;
//     const fields = Array.from(sets);
//     return new Promise((resolve, _reject) => {
//       this.$confirm({
//         title: '温馨提示',
//         content: h('div', {}, [
//           h('p', {}, `您的公式中【${fields.join('，')}】字段信息未识别，请您检查！`),
//           h('p', { style: 'font-size:85%;opacity:0.7;font-weight:bold;' }, '注意，如果手动输入字段，请使用当前页面显示的字段名称，否则无法识别~'),
//           h('p', {}, '如果您确定这些是误报，您可以点 【直接执行】继续。继续执行的时候这些字符将被放到引号内（\'[xxx]\'）')
//         ]),
//         okText: '直接执行',
//         cancelText: '取消',
//         onOk() {
//           resolve(true);
//         },
//         onCancel() {
//           resolve(false);
//         }
//       });
//     });
//   },

//   // 入参是写的公式的字符串 先执行 checkRule 没有通过再走这个 通过了就不走了 提示是否有没闭合的小括号
//   confirmRule(composeRule) {
//     const h = this.$createElement;

//     const ruleDoms = [];
//     let j = 0;
//     for (var i = 0; i < composeRule.length; i++) {
//       if (['（', '）'].includes(composeRule[i])) {
//         if (j < i) {
//           ruleDoms.push(h('span', {}, composeRule.substring(j, i)));
//           ruleDoms.push(h('b', { style: 'background:yellow' }, composeRule.substring(i, i + 1)));
//         }
//         j = ++i;
//       }
//     }
//     j < i && ruleDoms.push(h('span', {}, composeRule.substring(j, i)));
//     return new Promise((resolve, _reject) => {
//       this.$confirm({
//         title: '温馨提示',
//         content: h('div', {}, [
//           h('p', {}, '检测到您的公式中存在不闭合的小括号，请您检查是否存在问题（中文括号已标黄）'),
//           h('p', { style: 'font-family:\'宋体\';' }, ruleDoms),
//           h('p', {}, '如果您确定是误报，您可以点 【直接执行】继续'),
//         ]),
//         okText: '直接执行',
//         cancelText: '取消',
//         onOk() {
//           resolve(true);
//         },
//         onCancel() {
//           resolve(false);
//         }
//       });
//     });
//   },

//   // 入参是：解析后的字段表达式解析成了  tree 这个能用来计算的数据结构 判断这个数据结构是否合法
//   checkTree(tree) { // 检查字段规则树有没有报错; 同时检查传入的参数类型是否符合函数约定
//     let nodes = [tree];
//     while (nodes.length) {
//       const currentNode = nodes.shift();
//       if (currentNode.type === 'ERROR') {
//         return currentNode.msg;
//       }
//       if (currentNode.children) {
//         if (currentNode.node.type === 'FUNCTION') {
//           const handler = this.handlers.find(item => item.key?.startsWith(currentNode.node.value + '('));
//           if (handler && handler.paramType?.length) {
//             const paramType = handler.paramType;
//             for (let i = 0; i < currentNode.children.length; i++) {
//               const type = paramType[i] || paramType[0];
//               const child = currentNode.children[i];
//               if (type === 'VARIABLE') {
//                 // childType 得存在，不存在的说明是自建字段
//                 if (child.type && child.type !== 'VARIABLE') { return `函数 ${currentNode.node.value} 的参数${i + 1}只能传入字段变量，请您重试~`; } else if (child.value === this.editValue) {
//                   return '自建字段不能直接或间接引用自己，请您重试~';
//                 }
//               } else {
//                 if (child.type === 'VARIABLE') {
//                   if (child.value === this.editValue) {
//                     return '自建字段不能直接或间接引用自己，请您重试~';
//                   }
//                   const field = this.designFields.find(item => item.value === child.value);
//                   if (field && !type.includes(field.transformType || field.type)) return `函数 ${currentNode.node.value} 的参数${i + 1}只能传入 ${type} 类型，请您重试~`;
//                 } else if (child.type === 'OPERATOR') { // 运算符
//                   if ((['>', '<', '>=', '<>', '<=', '==', '==='].includes(child.value) && !type.includes('boolean')) ||
//                     (child.value === '&' && !type.includes('string')) ||
//                     !type.includes('number')
//                   ) {
//                     return `函数 ${currentNode.node.value} 的参数${i + 1}只能传入 ${type} 类型，请您重试~`;
//                   }
//                 } else if (child.type && !type.includes(child.type.toLowerCase())) {
//                   return `函数 ${currentNode.node.value} 的参数${i + 1}只能传入 ${type} 类型，请您重试~`;
//                 }
//               }
//             }
//           }
//         }
//         nodes = nodes.concat(currentNode.children);
//       }
//     }

//     return '';
//   },
