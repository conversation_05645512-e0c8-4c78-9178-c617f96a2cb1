<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="title"
    :centered="true"
    @submit="submit"
    @cancel="cancel"
  >
    <div class="formula-list">
      <div
        class="formula-type"
        v-for="(item, index) in formula"
        :key="index"
        :class="formulaType === index ? 'change-style' : 'formula-type'"
        @click="changeDivStyle(index)"
      >
        <div class="formula-flex">
          <div class="formula-left">
            <img
              :src="src[index]"
              width="120px"
              height="40px"
              style="margin-top: 5px"
            />
          </div>
          <div class="formula-right">{{ formulaName[index] }}</div>
        </div>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "./common/modal.vue";
// import BUS from "@/assets/js/eventBus";
export default {
  name: "medicalFormulaList",
  components: { modal },
  data() {
    return {
      masks: true,
      modal_width: 548,
      formulaType: 0,
      formula: [
        "normal",
        "menstruation1",
        "menstruation2",
        "menstruation3",
        "menstruation4",
        "pupil",
        "optical",
        "fetalHeart",
        "PDTooth",
        "diseasedLowerTooth",
        "diseasedUpperTooth",
        "toothBitmap",
        "permanentTeethBitmap",
        "primaryTeethBitmap",
      ],
      formulaName: [
        "通用公式",
        "月经史公式1",
        "月经史公式2",
        "月经史公式3",
        "月经史公式4",
        "瞳孔图",
        "光定位图",
        "胎心图",
        "PD牙位图",
        "病变下牙牙位图",
        "病变上牙牙位图",
        "十字牙位图",
        "恒牙牙位图",
        "乳牙牙位图",
      ],
      title: "医学表达式",
      formulaTitle: null,
      // src: [],
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
    src: {
      type: Array,
      default: () => [],
    },
  },

  mounted() {
    // BUS.$on("editor_" + this.editorId, ({ instance }) => {
    //   if (instance) this.src = instance.config.getConfig().formulaImage;
    // });
  },
  methods: {
    submit() {
      this.$emit("submit");
    },
    cancel() {
      this.formulaType = 0;
      this.$emit("cancel");
    },
    changeDivStyle(i) {
      this.formulaType = i;
    },
  },
};
</script>
<style scoped>
.formula-list {
  width: 100%;
  background-color: white;
  border: 1px solid rgb(217, 217, 217);
}
.formula-type {
  height: 50px;
  margin: 5px;
  width: 250px;
  display: inline-block;
  cursor: pointer;
}
.formula-flex {
  display: flex;
}
.formula-left {
  height: 50px;
  text-align: center;
  border: 1px solid rgb(217, 217, 217);
}
.formula-right {
  margin-left: 20px;
  line-height: 50px;
  color: black;
  text-align: right;
}
.change-style {
  height: 50px;
  margin: 5px;
  cursor: pointer;
  background-color: rgb(240, 240, 240);
  color: aliceblue;
}
</style>
