import baseStartBtnMixin from "./baseButton";
import insertBtnMixIn from "./insertButton";
import viewBtnMixIn from "./viewButton";
import tableBtnMixIn from "./tableButton";
import editBtnMixIn from "./editBtn";
import fileBtnMixIn from "./fileBtn";
import settingBtnMixIn from "./settingButton";
const headerBtnMixIn = {
  mixins: [
    baseStartBtnMixin,
    insertBtnMixIn,
    viewBtnMixIn,
    tableBtnMixIn,
    fileBtnMixIn,
    editBtnMixIn,
    settingBtnMixIn,
  ],
  data() {
    return {
      selectedSymbolIcon: "icon-line-bracketszhongkuohao",
      selectedFieldAlignIcon: "icon-field-zuoduiqi",
      selectedIcon: "icon-suoyoukuangxian",
      showIconSelect: "",
      selectHeaderBtn: 0, // 当前选中的一级按钮
      contextStateFont: {},
      customSelected: {},
      paraStyle: { align: "left", vertical_align: "top" },
      editorConfig: {},
      dynamicSelectMenu: {},
      initData: {
        fontSize: {
          option: "小四",
          value: 16,
        },
        fontType: {
          option: "宋体",
          value: "宋体",
        },
      },
      selectData: {
        fontSize: [],
        fontType: [],
      },
    };
  },
  created() {},
  methods: {
    initContextState() {
      const { editor } = this.instance;
      // 实现绑定 ， 控制按钮选中状态
      this.contextStateFont = editor.contextState.font;
      this.editorConfig = editor.config;
    },
    setHeaderBtnStatus() {
      this.brush = false;
      this.setFontStyle();
      this.closeSelectedMenu();
      this.closeColorPicker();
      this.setParaStyleStatus();
      this.wordStatisticsFun();
    },
    setParaStyleStatus() {
      const { focusElement } = this.instance.editor;
      this.paraStyle = {
        align: focusElement.paragraph.align,
        vertical_align: focusElement.paragraph.vertical_align,
      };
      if (focusElement.cell) {
        this.paraStyle = {
          align: focusElement.paragraph.align,
          vertical_align: focusElement.cell.vertical_align,
        };
      }
    },
    closeSelectedMenu() {
      for (const key in this.initData) {
        const compVue = this.$refs["selectedMenu_" + key][0];
        if (compVue) {
          compVue.show = false;
        }
      }
      this.showIconSelect = "";
      this.showTableChoice = false;
    },
    setFontStyle() {
      this.contextStateFont = this.instance.editor.contextState.font;
      const curFont = this.contextStateFont;
      this.initData.fontType = this.selectData.fontType.find(
        (item) => item.option === curFont.family
      );
      this.initData.fontSize = this.selectData.fontSize.find(
        (item) => item.value === curFont.height
      );
    },
    showIconTextMenu(type) {
      if (this.showIconSelect === type) {
        this.showIconSelect = "";
      } else {
        this.showIconSelect = type;
      }
    },
    fieldSymbolIconClickHandler(event, cItem) {
      event.stopPropagation(event);
      const children = cItem.children;
      children.forEach((child) => {
        if (child.icon === this.selectedSymbolIcon) {
          child.func();
        }
      });
    },
    fieldAlignIconClickHandler(event, cItem) {
      event.stopPropagation(event);
      const children = cItem.children;
      children.forEach((child) => {
        if (child.icon === this.selectedFieldAlignIcon) {
          child.func();
        }
      });
    },
    iconClickHandler(event, cItem) {
      event.stopPropagation(event);
      const children = cItem.children;
      children.forEach((child) => {
        if (child.icon === this.selectedIcon) {
          child.func();
        }
      });
    },

    headerBtnClickDecorator(func, item) {
      const { editor } = this.instance;
      const res = editor.event.emit("headerBtnClick", item);
      // 返回true则阻止默认行为
      if (res !== "origin" || res === true) return;
      func();
      this.contextStateFont = editor.contextState.font; //重新设置contextStateFont，解决初次点击不联动问题
    },
    // 设置二级下拉菜单选中状态
    selectMenuSelected(pItem, cItem) {
      if (this.judgeSelectedClass(cItem)) {
        return "selected";
      } else {
        return "unselected";
      }
    },
    // 设置二级下拉菜单图标选中状态
    selectMenuIconSelected(cItem) {
      if (!cItem.key) {
        return "icon";
      }
      if (
        (cItem.selected && cItem.selected.value) ||
        this.judgeSelectedClass(cItem)
      ) {
        return "icon-click";
      }
      return "icon";
    },
    judgeSelectedClass(cItem) {
      if (
        (this.paraStyle[cItem.key] !== undefined &&
          this.paraStyle[cItem.key] === cItem.val) ||
        (this.editorConfig[cItem.key] !== undefined &&
          this.editorConfig[cItem.key] === cItem.val) ||
        this.customSelected[cItem.key] ||
        (this.instance &&
          this.instance.editor[cItem.key] !== undefined &&
          this.instance.editor[cItem.key] === cItem.val) ||
        (this.contextStateFont[cItem.key] !== undefined &&
          this.contextStateFont[cItem.key] === cItem.val)
      ) {
        return true;
      } else {
        return false;
      }
    },
  },
};
export default headerBtnMixIn;
