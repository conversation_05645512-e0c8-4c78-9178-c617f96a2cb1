function handleFieldCharacterSize(editor, focus_field) {
  const res = editor.event.emit("beforeSetFieldCharacterSize");
  if (res === "prevent") {
    // 如果没有绑定事件的话，返回值为 origin
    return;
  }
  if (!focus_field) return;
  // 处理字体缩放 ↓
  const maxHeight = focus_field.maxHeight;

  if (!maxHeight) return;

  let count1 = 0;
  let count2 = 0;
  // uniformFontHeight 统一字体高度
  res === "uniformFontHeight" && editor.setCharacterSize(focus_field, "base");

  // focus_field.height 不能缓存 需要每次都重新计算
  // 字体放大
  while (
    focus_field.height < maxHeight &&
    focus_field.children[0]?.height < focus_field.style.height &&
    count2 < 10
  ) {
    editor.setCharacterSize(focus_field, "bigger");
    count2++;
  }

  // 字体缩小
  while (focus_field.height > maxHeight && count1 < 12) {
    // 加个次数限制 避免字体已经缩小到最小了还是超高进入死循环
    editor.setCharacterSize(focus_field, "smaller");
    count1++;
  }
  // 处理字体缩放 ↑

  editor.event.emit(
    "afterSetFieldCharacterSize",
    focus_field.children[0]?.height
  );
}

const editorEventMixIn = {
  methods: {
    /**
     * 编辑器抛出事件处理
     * @param Instance
     */
    bindInstanceEvent(Instance) {
      const editor = Instance.editor;
      this.curClickInfo = {};
      Instance.loadComSentencesData = (data) => {
        this.commonSentences = data ? data : [];
      };

      editor.event.on("boxChecked", ({ field }) => {
        if (!field || field.type !== "box") return;
        const parent_box = field.parent_box;
        if (parent_box.cascade_list && parent_box.cascade_list.length) {
          const checkedItems = field.box_checked_items;
          if (checkedItems.length) {
            const txtArr = checkedItems.map((item) => item.text);
            editor.showOrHideField(parent_box, txtArr);
          } else {
            editor.showOrHideField(parent_box, []);
          }
        }

        if (field.name) {
          editor.updateFieldAdvancedFunctions(field);
        } else if (parent_box) {
          editor.updateFieldAdvancedFunctions(parent_box);
        }
      });
      // 编辑器内容改变
      editor.event.on("contentChanged", () => {
        const focus_field = editor.selection.getFocusField();
        if (focus_field) {
          if (editor.field_valid) {
            this.editorContentChangedValid(focus_field);
          }

          // 因为人家调用接口也会频繁的弹出提示 不合理 所以先暂时去掉 后边再想怎么办
          // if (!editor.getInputDOM() && editor.isMobileTerminal()) {
          //   this.$editor.info("请注意：数据有改动", 0.5);
          // }
          handleFieldCharacterSize(editor, focus_field);
        }
        if (
          editor.config.source === "design" ||
          (this.instance && this.instance.localTest.useLocal)
        ) {
          this.quickInputSelect();
        }
      });
      // 编辑器点击事件
      editor.event.on("click", (event, { x, y }) => {
        this.showInputSelect = false;
        this.hideFloatBtn();
        if (!editor.formula_mode) {
          this.curClickInfo = editor.getElementByPoint(x, y);
          this.buttonInfo = this.curClickInfo.element;
          this.fieldInfo = this.curClickInfo.field;
          //解决直接右键打开文本域属性窗口不能正常赋值问题

          if (this.isDebugMode) {
            this.logDebugInfo(this.curClickInfo);
          }
        }
      });
      // 编辑器获得焦点事件
      editor.event.on("editorFocus", () => {
        this.browserAfterPrint && this.browserAfterPrint();
        this.browserAfterPrint = null;
      });
      editor.event.on("pointerUp", (event) => {
        const res = editor.event.emit(
          "beforePointerUp",
          event,
          this.curClickInfo
        );
        if (res === false) {
          return;
        }
        //处理文本域点击事件  只有左键点击生效
        // (当点击任意功能按钮调用了编辑器接口时会使编辑器获得焦点，此时curClickInfo还未赋值就触发了pointerUp事件，所以需增加curClickInfo判断)
        if (
          event.button === 0 &&
          this.curClickInfo &&
          !editor.isMobileTerminal()
        ) {
          this.handleFieldClick();
        }
      });
      editor.event.on("exeCommand", (event) => {
        if (event.command === "paste") {
          handleFieldCharacterSize(editor, editor.selection.getFocusField());
        }
      });
      editor.event.on("assembleText", (text, rawField, extraInfo) => {
        if (!this.instance || !this.instance.localTest.useNew) {
          return;
        }
        if (extraInfo.isGetRawData) {
          this.handleAssembleText(text, rawField);
        }
      });
    },
  },
};
export default editorEventMixIn;
