
import Editor from "./Editor";
import { initPrintEditor, getImgBase64List } from "./Helper";
import Renderer from "./Renderer";
import { isTable } from "./Utils";

export default class Print {
  // 为PDF提供base64
  static getBase64List (editor: Editor) {
    const imagesBase64 = systemPrint(editor);
    Renderer.init(editor, editor.init_canvas);
    return imagesBase64;
  }

  // 执行打印的方法
  static print (editor: Editor, printParameter?: any, afterPrint?: (e?: any) => {}) {
    const cursor_position = editor.selection.anchor;
    editor.print_mode = true;
    let print_src: any[] = [];
    if (printParameter && printParameter.printRatio) {
      editor.config.printRatio = printParameter.printRatio;
    }

    print_src = systemPrint(editor, printParameter);
    afterPrint && afterPrint();

    editor.print_mode = false;
    Renderer.init(editor, editor.init_canvas);
    editor.selection.setCursorPosition([...cursor_position]);
    editor.update();
    editor.focus();
    editor.render();

    return print_src;
  }

  // 系统打印打印奇数页和偶数页的方法
  static printOddOrEvent (editor: Editor, mode: string) {
    const cursor_position = editor.selection.anchor;
    editor.print_mode = true;
    let print_src: any[] = [];
    // 创建新的editor
    const { editor2, temporaryCanvas } = initPrintEditor(editor);
    // 根据页码获取的base64集合
    print_src = getImgBase64List(editor, editor2, temporaryCanvas, null, mode);
    editor.print_mode = false;
    Renderer.init(editor, editor.init_canvas);
    editor.selection.setCursorPosition([...cursor_position]);
    editor.update();
    editor.focus();
    editor.render();
    return print_src;
  }

  static rawDataa2Base64 (editor: Editor, rawDataList: any[]) {
    let printList: any[] = [];
    for (let i = 0; i < rawDataList.length; i++) {
      const rawData = rawDataList[i];
      // 创建新的editor
      const { editor2, temporaryCanvas } = initPrintEditor(editor, rawData);
      // 根据页码获取的base64集合
      const base64_list = getImgBase64List(editor, editor2, temporaryCanvas);
      printList = printList.concat(base64_list);
    }
    return printList;
  }

  // 绘制区域打印的遮罩
  static drawMaskOfAreaPrint (editor: Editor, x: number, y: number) {
    y = y + editor.scroll_top;
    editor.internal.area_location.end_absolute_x = x;
    editor.internal.area_location.end_absolute_y = y;

    if (editor.internal.area_location.transition_x > x) {
      editor.internal.area_location.start_absolute_x = editor.internal.area_location.transition_x;
      editor.internal.area_location.end_absolute_x = x;
    }
    if (editor.internal.area_location.transition_y > y) {
      editor.internal.area_location.end_absolute_y = editor.internal.area_location.transition_y;
      editor.internal.area_location.start_absolute_y = y;
    }
    if (x < editor.config.page_padding_left + editor.page_left) {
      editor.internal.area_location.end_absolute_x = editor.page_left + editor.page_size.width;
    } else if (x > editor.page_left + editor.page_size.width - editor.config.page_padding_right) {
      editor.internal.area_location.end_absolute_x = editor.page_left + editor.page_size.width;
    }
  }

  // 区域打印
  static areaPrint (editor: Editor, flag: boolean) {
    editor.print_continue = false;
    editor.area_print = flag;
    editor.internal.area_location = {
      start_absolute_x: 0,
      end_absolute_x: 0,
      start_absolute_y: 0,
      end_absolute_y: 0,
      transition_x: 0,
      transition_y: 0
    };
    const keepSymbolWidthWhenPrint = !!editor.document_meta.keepSymbolWidthWhenPrint;
    // 如果展示了需要隐藏边框
    if (!keepSymbolWidthWhenPrint) {
      editor.showFieldSymbol(!flag);
    }
    editor.closeShapesEditor();
    editor.render();
  }

  // 设置续打遮罩高度
  static setMaskHeightOfContinuePrint (editor: Editor, x: number, y: number) {
    y += editor.scroll_top;
    const container_info = editor.getContainerInfoByPointHelper(x, y);
    const coverPageNum = Math.round(y / (editor.page_size.height + editor.config.page_margin_bottom));
    const currentPageNum = Math.floor(y / (editor.page_size.height + editor.config.page_margin_bottom));
    if (container_info) {
      const { row, view_path } = container_info;
      // 判断鼠标是否在表格下一行

      editor.internal.print_absolute_y = editor.getElementAbsolutePositionByViewPath(
        view_path
      ).y;
      // 鼠标所在row的前一个row或table

      // 判断鼠标当前所在row不在table内且上一行在table内
      if (!row.parent.parent) {
        const previous = row.parent.children[row.cell_index - 1];
        if (previous && isTable(previous)) {
          editor.internal.print_absolute_y = editor.internal.print_absolute_y + 1;
        }
      } else {
        const table = row.parent.parent;
        let previous: any;
        if (table.cell_index > 0) {
          previous = table.children[table.cell_index - 1];
        } else {
          previous = table.children[table.cell_index];
        }
        if (previous) {
          editor.internal.print_absolute_y = editor.internal.print_absolute_y - 1;
        }
      }

      const currentPage = editor.pages[currentPageNum];
      if (row === currentPage.children[currentPage.children.length - 1] && y > (editor.internal.print_absolute_y + row.height)) {
        editor.internal.print_absolute_y = coverPageNum * (editor.page_size.height + editor.config.page_margin_bottom) + editor.config.editor_padding_top;
      }
      editor.render();
    } else {
      if (y - coverPageNum * (editor.page_size.height + editor.config.page_margin_bottom) <= editor.pages[coverPageNum].header.header_outer_bottom) {
        editor.internal.print_absolute_y = coverPageNum * (editor.page_size.height + editor.config.page_margin_bottom) + editor.config.editor_padding_top;
      }
    }
  }

  // 续打
  static continueToPrint (editor: Editor, flag: boolean) {
    editor.area_print = false;
    const keepSymbolWidthWhenPrint = !!editor.document_meta.keepSymbolWidthWhenPrint;
    // 如果展示了需要隐藏边框
    if (!keepSymbolWidthWhenPrint) {
      editor.showFieldSymbol(!flag);
    }
    editor.print_continue = flag;
    // 续打遮罩高度设置为0
    editor.internal.print_absolute_y = 0;
    editor.closeShapesEditor();
    editor.render();
  }

  static continueToPrintImmediately (editor: Editor) {
    editor.showFieldSymbol(false);
    editor.print_continue = true;
    // 续打遮罩高度设置为0
    const printInfo = editor.document_meta.printInfo;
    if (printInfo && printInfo.height) {
      editor.internal.print_absolute_y = printInfo.height;
    }
    const src = editor.print();
    editor.print_continue = false;
    editor.internal.print_absolute_y = 0;
    return src;
  }

  /**
 * 打印当前页
 */
  static printCurrentPage (editor: Editor) {
    const pageHeight = editor.page_size.height + editor.config.page_margin_bottom;
    // 上方页面可视区域剩余高度
    const restHeight = pageHeight - editor.scroll_top % (editor.page_size.height + editor.config.page_margin_bottom) + editor.config.page_margin_bottom / 2;
    // 当前显示在上方的页面
    let page = Math.floor(editor.scroll_top / pageHeight);
    // 可视区域一半的高度
    const half = editor.init_canvas.height / 2;
    // 如果上方页面剩余的高度小于一半的高度，则打印下方的页面，当前页页码加一
    if (restHeight <= half) {
      page += 1;
    }
    return page;
  }

  static getFieldIDClosestToCaret (editor: Editor, container_info: any) {
    if (!container_info) return;
    const para_path = editor.modelPath2ParaPath(container_info.model_path);
    const field = editor.selection.getFieldByPath(para_path);
    if (field) {
      return field.id;
    }
    const char_index = para_path[para_path.length - 1];
    const current_para = container_info.row.paragraph;
    // 先获取当前段落中当前字符前面有无文本域，有的话则定位到其结尾边框之前，如果没有则往后找寻
    const pre_chars = current_para.characters.slice(0, char_index);
    pre_chars.reverse();
    for (let i = 0; i < pre_chars.length; i++) {
      if (pre_chars[i].field_id) {
        para_path[para_path.length - 1] = para_path[para_path.length - 1] - i - 1;
        container_info.model_path = editor.paraPath2ModelPath(para_path);
        return pre_chars[i].field_id;
      }
    }
    const rear_chars = current_para.characters.slice(char_index);
    for (let i = 0; i < rear_chars.length; i++) {
      if (rear_chars[i].field_id) {
        para_path[para_path.length - 1] = para_path[para_path.length - 1] + i + 1;
        container_info.model_path = editor.paraPath2ModelPath(para_path);
        return rear_chars[i].field_id;
      }
    }
  }
}

/**
 * 系统打印方法,也可为打印提供返回base64参数
 * @param page_nums 页码集合
 * @param editor
 * @returns 返回的是图片的 base64
 */
export function systemPrint (editor: Editor, printParameter?: any) {
  // 创建新的editor
  const { editor2, temporaryCanvas } = initPrintEditor(editor); // c++ 打印预览的时候也会走到这里 出来的 temporayCanvas 就已经是绘制好的 canvas 了 可以直接 .toDataURL() 看效果
  // 根据页码获取的base64集合
  const base64_list = getImgBase64List(editor, editor2, temporaryCanvas, printParameter) as string[];
  return base64_list;
}
