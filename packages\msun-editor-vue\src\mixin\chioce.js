// import BUS from "@/assets/js/eventBus";
const choiceMixIn = {
  data() {
    return {
      isShowChoiceModal: false,
    };
  },
  methods: {
    showChoiceModal() {
      this.isShowChoiceModal = true;
      this.instance.editor.formulaMode(true);
    },
    closeChioceModal() {
      this.isShowChoiceModal = false;
      this.instance.editor.formulaMode(false);
      this.editor.focus();
    },
    insertChioceModal(data) {
      let field = null;
      const editor = this.editor;
      //   插入操作
      const parameter = {};
      parameter.isGroup = data.isToBeGroup ? true : false;
      parameter.groupName = data.groupName;
      parameter.isMulti = data.type === "plural" ? true : false;
      parameter.required = data.required;
      parameter.widgetType =
        data.choiceBoxType === "checkbox_rimless" ||
        data.choiceBoxType === "checkbox_black"
          ? "checkbox"
          : data.choiceBoxType;
      parameter.border =
        data.choiceBoxType === "checkbox_rimless"
          ? "dotted"
          : data.choiceBoxType === "checkbox_black"
          ? "black"
          : "solid";
      parameter.deletable = data.isDeleteAble;
      parameter.hideBorder = data.isHideSymbol;
      parameter.position = data.position;
      parameter.items = data.datasource;
      parameter.automation_list = data.automation_list;
      parameter.checkBoxSeparator = data.checkBoxSeparator;
      parameter.dynamic =
        data.dynamic !== undefined
          ? data.dynamic
          : data.choicedField?.parent_box?.meta.dynamic;
      parameter.selectType =
        data.selectType !== undefined
          ? data.selectType
          : data.choicedField?.parent_box?.meta.selectType;
      if (data.edit) {
        // 设置复选框属性的时候，将选区定位到当前文本域的开头，解决选区状态下修改属性的报错
        editor.selection.setCursorPosition(editor.selection.focus);
        field = editor.updateWidget(data.choicedField, parameter);
      } else {
        field = editor.insertWidget(parameter);
      }

      this.isShowChoiceModal = false;
      editor.formulaMode(false);
      editor.focus();
      return field;
    },
  },
};
export default choiceMixIn;
