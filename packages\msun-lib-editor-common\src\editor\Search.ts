import Paragraph from "./Paragraph";
import Cell from "./Cell";
import Table from "./Table";
import PathUtils from "./Path";
import Editor from "./Editor";
import Group from "./Groups";
import XField from "./XField";
import { isCharacter, isParagraph, isTable, isNotNullAndUndefined } from "./Utils";
import { isCell, isGroup } from "./Helper";

let searched_total_num: number = 0; // 查找处全部字符的数量

let recordLocation: any = [];

let case_sensitive: boolean = false; // 区分大小写

export default class Search {
  // 查找
  static searchAll(
    editor: Editor,
    text: string | string[],
    isHighLight: boolean = true,
    group?: Group,
    color?: string,
    para?: Paragraph | Table,
    index?: number
  ) {
    searched_total_num = 0;
    let type = "noHeighLight";
    if (isHighLight) {
      type = "select";
    }
    let paras = editor.current_cell.paragraph;
    if (group) {
      paras = group.paragraph;
    }
    if (para) {
      paras = [para];
    }
    let style = {};
    if (color) {
      style = {
        highLight: color,
      };
    }
    if (typeof text === "string") {
      Search.searchGlobalText(text, type, paras, style, undefined, index);
      return searched_total_num;
    }
    const resultInfo: any = {};
    for (let i = 0; i < text.length; i++) {
      const t = text[i];
      searched_total_num = 0;
      Search.searchGlobalText(t, type, paras, style);
      resultInfo[t] = searched_total_num;
    }
    return resultInfo;
  }

  static cancelFind(editor: Editor) {
    Search.cancelSearch(editor);
    editor.selection.setCursorPosition(editor.selection.anchor); // 调用该方法之后要更新 不能只 render 否则绘制的位置就不对了
    editor.updateCaret();
    editor.render();
  }

  // 查找下一个
  static findNext(editor: Editor, text: string, group?: Group) {
    let result = {};
    const focus = editor.selection.focus;
    const is_searched = Search.searchNext(text, editor, group);
    if (is_searched === false) {
      editor.selection.setCursorByRootCell("start");
      const is_reSearched = Search.searchNext(text, editor, group);
      if (is_reSearched === false) {
        editor.selection.setCursorPosition(focus);
        result = {
          data: "无法找到您所查找的内容",
        };
        return result;
      }
    }
    editor.scroll_by_focus();
    editor.render();
    return result;
  }

  static highLightMark(
    editor: Editor,
    text: string,
    color?: string,
    data?: Group | XField[]
  ) {
    editor.selection.setCursorPosition(editor.selection.anchor);
    let colorParam = {};
    if (color) {
      colorParam = { highLight: color };
    } else {
      colorParam = { highLight: null };
    }
    if (isGroup(data)) {
      const paras = data.paragraph;
      Search.searchGlobalText(text, "highLight", paras, colorParam);
    } else if (data && data instanceof Array) {
      Search.searchFieldText(text, data, color);
    } else {
      const paras = editor.current_cell.paragraph;
      Search.searchGlobalText(text, "highLight", paras, colorParam);
    }

    editor.render();
  }

  // 替换单个
  static replaceOne(
    editor: Editor,
    select_text: string,
    replace_text: string,
    group?: Group
  ) {
    const result = Search.replaceFont(select_text, replace_text, editor, group);
    editor.scroll_by_focus();
    editor.render();
    return result;
  }

  // 替换全部
  static replaceAll(
    editor: Editor,
    select_text: string,
    replace_text: string,
    group?: Group
  ) {
    let result = {};
    const search_num = Search.replaceAllFont(
      select_text,
      replace_text,
      editor,
      group
    );
    result = {
      data: "全部完成。已完成" + search_num + "处替换。",
    };
    return result;
  }

  /**
   * 区分大小写
   */
  static caseSensitive() {
    case_sensitive = !case_sensitive;
  }

  // 高亮
  static highLight(text: string, editor: Editor, color: string) {
    searched_total_num = 0;
    // Search.cancelSearch();
    Search.searchGlobalText(text, "highLight", editor.current_cell.paragraph, {
      highLight: color,
    });
    return searched_total_num;
  }

  // 获取匹配字符下标
  static getTextLocation(
    text: string,
    container: Paragraph
  ): { start_index: number; end_index: number; text: string }[] {
    // 传入的查找字符在段落中的位置集合
    const text_location: {
      start_index: number;
      end_index: number;
      text: string;
    }[] = [];
    if (!text) {
      return text_location;
    }
    // 判断数组转换为的字符串中是否包含传入的查找字符
    const params = container.getStr(true, true);
    let para_str = params.str; // 段落返回字符
    if (!case_sensitive) {
      para_str = para_str.toLowerCase();
      text = text.toLowerCase();
    }
    let index = para_str.indexOf(text);
    if (index !== -1) {
      while (index > -1) {
        text_location.push({
          start_index: params.indexArray[index],
          end_index: params.indexArray[index + text.length],
          text: text,
        }); // 段落返回字符坐标 params.indexArray
        index = para_str.indexOf(text, index + text.length);
      }
    }
    return text_location;
  }

  // 根据传入内容查找文本域内字符并高亮
  static searchFieldText(texts: string, fields: XField[], style?: string) {
    for (let i = 0; i < fields.length; i++) {
      const field = fields[i];
      let textIndex = field.text.indexOf(texts);
      // 循环所有可能匹配的元素
      if (textIndex !== -1) {
        while (textIndex > -1) {
          for (let j = textIndex; j < textIndex + texts.length; j++) {
            const fieldInfo = field.children[j];
            if (isCharacter(fieldInfo)) {
              const font = field.cell.editor.fontMap.add(
                Object.assign({}, fieldInfo.font, {
                  highLight: style || "yellow",
                })
              );
              fieldInfo.font = font;
            }
          }
          textIndex = field.text.indexOf(texts, textIndex + texts.length);
        }
      }
    }
  }

  static replaceParagraphTextByIndex(
    para: Paragraph,
    targetText: string,
    replaceText: string,
    index: number,
    editor: Editor
  ) {
    const text_location = this.searchGlobalText(
      targetText,
      "replace",
      [para],
      undefined,
      undefined,
      index
    );
    if (recordLocation.length && recordLocation[0]) {
      const paraStartPath = para.start_para_path;
      let startPath = paraStartPath.slice();
      startPath[startPath.length - 1] = recordLocation[0].start_index;
      const endPath = paraStartPath.slice();
      endPath[startPath.length - 1] = recordLocation[0].end_index;
      editor.selection.setSelectionByPath(startPath, endPath);
      editor.delete_backward();
      editor.insertText(replaceText);
    }
  }

  // 根据传入内容全文查找并高亮处理
  static searchGlobalText(
    texts: string,
    type: string,
    containers: (Cell | Table | Paragraph)[],
    style?: { bgColor?: string; color?: string; highLight?: string },
    match_texts?: string[],
    index?: number
  ) {
    if (texts === "") {
      return;
    }
    for (let i = 0; i < containers.length; i++) {
      const container = containers[i];
      if (isTable(container)) {
        Search.searchGlobalText(
          texts,
          type,
          container.children,
          style,
          match_texts
        );
      } else if (isCell(container)) {
        Search.searchGlobalText(
          texts,
          type,
          container.paragraph,
          style,
          match_texts
        );
      } else {
        // container类型是段落时的处理方式
        recordLocation = [];
        let text_location: any[] = [];
        // 获取匹配文本位置,包含字符下标与字符长度
        text_location = Search.getTextLocation(texts, container);
        if (index) {
          text_location = [text_location[index - 1]];
          recordLocation = text_location;
          searched_total_num = 1;
        } else {
          searched_total_num += text_location.length;
        }
        text_location.forEach((e) => {
          if (e && isNotNullAndUndefined(e.start_index) && isNotNullAndUndefined(e.end_index)) {
            for (let k = e.start_index; k < e.end_index; k++) {
              const select_character = (container as Paragraph).characters[k];
              if (type === "highLight") {
                // 高亮
                // 对查找的字符进行样式改变
                let highColor = "yellow";
                if (style && style.highLight) {
                  highColor = style.highLight;
                }
                const font = container.cell.editor.fontMap.add(
                  Object.assign({}, select_character.font, {
                    highLight: highColor,
                  })
                );
                select_character.font = font;

                match_texts && match_texts.push(e.text);
              } else if (type === "select") {
                // 对查找的字符进行样式改变
                let highColor = "yellow";
                if (style && style.highLight) {
                  highColor = style.highLight;
                }
                const font = container.cell.editor.fontMap.add(
                  Object.assign({}, select_character.font, {
                    temp_word_bgColor: highColor,
                  })
                );
                select_character.font = font;
              } else if (type === "mark") {
                // 标记敏感词
                // 对查找的字符进行样式改变
                const character_font_style = container.cell.editor.fontMap.add(
                  Object.assign({}, select_character.font, style)
                );
                select_character.font = character_font_style;
                match_texts &&
                  k === e.end_index - 1 &&
                  match_texts.push(e.text);
              }
            }
          }
        });
      }
    }
  }

  // 取消查找
  static cancelSearch(editor: Editor) {
    for (const [, fontStyle] of editor.fontMap.get().entries()) {
      if (fontStyle.highLight) {
        fontStyle.highLight = null;
      }
      if (fontStyle.temp_word_bgColor) {
        fontStyle.temp_word_bgColor = undefined;
      }
    }
  }

  // 查找下一个
  static searchNext(
    text: string,
    editor: Editor,
    group?: Group,
    para?: Paragraph | Table,
    index?: number
  ) {
    if (text === "") {
      return false;
    }
    const focus_para_path = editor.modelPath2ParaPath(editor.selection.focus);
    let path = [...focus_para_path];
    let paras = editor.current_cell.paragraph;
    if (group) {
      paras = group.paragraph;
    }
    if (para) {
      paras = [para];
      if (isParagraph(para)) {
        path = [...para.start_para_path];
      }
    }
    // 判断是否在表格内
    if (PathUtils.isTablePath(path)) {
      const path_shift = path.shift();
      if (path_shift !== undefined) {
        // 从光标所在处开始向下循环
        for (let i = path_shift; i < paras.length; i++) {
          const container = paras[i];

          // 表格内的处理
          if (i === path_shift) {
            const path_shift_t = path.shift();
            if (path_shift_t !== undefined) {
              // 光标在第几个表格内
              for (let j = path_shift_t; j < container.children.length; j++) {
                const cell = container.children[j] as Cell;

                // 判断在表格第几个段落
                if (j === path_shift_t) {
                  const path_shift_p = path[path.length - 2];
                  for (let k = path_shift_p; k < cell.paragraph.length; k++) {
                    const para = cell.paragraph[k];
                    let is_focus_p = false;
                    if (k === path_shift_p) {
                      is_focus_p = true;
                    }

                    const isInParagraph = Search.textIsInParagraph(
                      is_focus_p,
                      text,
                      focus_para_path,
                      para as Paragraph,
                      editor
                    );
                    if (isInParagraph) {
                      return true;
                    }
                  }
                } else {
                  for (let k = 0; k < cell.paragraph.length; k++) {
                    const para = cell.paragraph[k];
                    const isInParagraph = Search.textIsInParagraph(
                      false,
                      text,
                      focus_para_path,
                      para as Paragraph,
                      editor
                    );
                    if (isInParagraph) {
                      return true;
                    }
                  }
                }
              }
            }
          } else {
            // 剩余循环的处理
            if (isTable(container)) {
              const isInParagraph = Search.searchRestArea(
                text,
                focus_para_path,
                container,
                editor
              );
              if (isInParagraph) {
                return true;
              }
            } else {
              const isInParagraph = Search.textIsInParagraph(
                false,
                text,
                focus_para_path,
                container as Paragraph,
                editor
              );
              if (isInParagraph) {
                return true;
              }
            }
          }
        }

        return false;
      }
    } else {
      // 光标不在表格内
      const path_shift = path.shift();
      if (path_shift !== undefined) {
        for (
          let i = path_shift;
          i < editor.current_cell.paragraph.length;
          i++
        ) {
          const container = editor.current_cell.paragraph[i];
          if (isTable(container)) {
            const isInParagraph = Search.searchRestArea(
              text,
              focus_para_path,
              container,
              editor
            );
            if (isInParagraph) {
              return true;
            }
          } else {
            let is_focus_p = false;
            if (i === path_shift) {
              is_focus_p = true;
            }
            const isInParagraph = Search.textIsInParagraph(
              is_focus_p,
              text,
              focus_para_path,
              container as Paragraph,
              editor
            );
            if (isInParagraph) {
              return true;
            }
          }
        }
        return false;
      }
    }
  }

  // 获取Table中所有的段落
  static searchRestArea(
    text: string,
    focus_para_path: number[],
    containers: Table,
    editor: Editor
  ) {
    for (let i = 0; i < containers.children.length; i++) {
      const cell = containers.children[i];
      for (let j = 0; j < cell.paragraph.length; j++) {
        const para = cell.paragraph[j];

        const isInParagraph = Search.textIsInParagraph(
          false,
          text,
          focus_para_path,
          para as Paragraph,
          editor
        );
        if (isInParagraph) {
          return true;
        }
      }
    }
  }

  // 查找的text是否在某段落中
  /**
   *
   * @param i 光标后第几个段落
   * @param para 段落对象
   */
  static textIsInParagraph(
    is_focus_p: boolean,
    text: string,
    path: number[],
    paragraph: Paragraph,
    editor: Editor
  ) {
    // 光标所在段落
    let start_p = 0;
    if (is_focus_p) {
      start_p = path[path.length - 1];
    }
    const para = paragraph;
    if (isTable(para)) {
      return false;
    }
    const para_value_list: any = [];
    // 剩余段落
    let fields: XField[] = [];
    for (let j = start_p; j < para.characters.length; j++) {
      const character = para.characters[j];
      // 因为这是在某一个段里边 而文本域 内容是可以跨段的 所以不能通过判断是否是边框来获取文本域
      if (character.field_id && character.field_position !== "placeholder") {
        if (!fields.some(f => f.id === character.field_id)) {
          fields.push(editor.getFieldById(character.field_id)!)
        }
        if (character.field_position === "end") {
          fields.pop();
        }
      }
      let readonly = false;
      for (let i = fields.length - 1; i >= 0; i--) { // 往前都是父级 所以只要有一个祖先级是只读的都不行
        if (fields[i].readonly) {
          readonly = true;
        }
      }
      if (
        isCharacter(character) &&
        character.field_position !== "placeholder" && !readonly
      ) {
        para_value_list.push(character.value ? character.value : " ");
      } else {
        para_value_list.push(" ");
      }
    }

    let font_string_list = para_value_list.join("");

    if (!case_sensitive) {
      font_string_list = font_string_list.toLowerCase();
      text = text.toLowerCase();
    }
    // 判断数组转换为的字符串中是否包含传入的查找字符
    if (font_string_list.indexOf(text) !== -1) {
      let index_of = font_string_list.indexOf(text);
      if (is_focus_p) {
        // 光标所在段落查找的字符偏移量为光标所在位置加上出现的indexof
        index_of += path[path.length - 1];
      }

      const start = (para as Paragraph).start_para_path;
      const end = [...start];
      start[start.length - 1] = index_of;
      end[end.length - 1] = index_of + text.length;
      // 选区
      editor.selection.setSelectionByPath(start, end);
      return true;
    }
  }

  // 替换单个内容
  static replaceFont(
    select_text: string,
    replace_text: string,
    editor: Editor,
    group?: Group
  ) {
    // 查找内容为空直接返回
    if (select_text === "") {
      return;
    }
    let result = {};
    // 选区情况
    if (!editor.selection.isCollapsed) {
      // 拿到选区所有elment
      const { all_chars } = editor.selection.selected_fields_chars;
      const list: any[] = [];
      // 取到element的value并放入集合中
      if (all_chars) {
        all_chars.forEach((e: any) => {
          list.push(e.value);
        });
      }
      const text = list.join("");
      // 拼成字符串判断是否和查询字符串相等
      if (text === select_text) {
        const focus_field = editor.selection.getFocusField()
        if (!(focus_field && focus_field.type === "label")) {
          // 删除选区，插入替换字符
          editor.delete_backward();
          editor.insertText(replace_text);
        }else{
          editor.event.emit("message","标签文本域不可替换")
        }
      }
    }
    const focus = editor.selection.focus;
    // 查询下一个并置为选区，查询不到返回失败结果
    const is_searched = Search.searchNext(select_text, editor, group);
    if (is_searched === false) {
      if (group) {
        editor.selection.setCursorByGroup(group);
      } else {
        editor.selection.setCursorByRootCell("start");
      }

      const is_reSearched = Search.searchNext(select_text, editor, group);
      if (is_reSearched === false) {
        editor.selection.setCursorPosition(focus);
        result = {
          data: "无法找到您所查找的内容",
        };
        return result;
      }
    }
    return result;
  }

  // 全部替换
  static replaceAllFont(
    select_text: string,
    replace_text: string,
    editor: Editor,
    group?: Group
  ) {
    if (select_text === "") {
      return;
    }
    let paras = editor.current_cell.paragraph;
    if (group) {
      paras = group.paragraph;
    }

    searched_total_num = 0;
    if (group) {
      editor.selection.setCursorByGroup(group);
    } else {
      editor.selection.setCursorByRootCell("start");
    }

    Search.searchGlobalText(select_text, "replace", paras);
    let i = 0;
    let k = 0;
    while (i < searched_total_num) {
      // 不应该等于啊 那不永远都多一个吗 总共找到了1个 从零开始 还到1 不就俩了吗
      Search.searchNext(select_text, editor);
      if (!editor.selection.isCollapsed) {
        const focus_field = editor.selection.getFocusField()
        if (!(focus_field && focus_field.type === "label")) {
          const is_delete = editor.delete_backward();
          if (is_delete) {
            editor.insertText(replace_text);
            k += 1;
          }
        }
      }      
      i++;
    }

    return k;
  }
}
