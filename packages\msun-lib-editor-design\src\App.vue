<template>
  <div id="editor-design">
    <a-layout style="height: 100%">
      <a-drawer
        title="版本历史记录"
        placement="right"
        width="296"
        wrap-class-name="version-history-drawer"
        :closable="true"
        :visible="isVersionHistoryVisible"
        @close="isVersionHistoryVisible = false"
      >
        <version-history-record
          ref="editorRecord"
          :page.sync="pageNumber"
          :total="pageTotal"
          :cur-version-id="curVersionId"
          :version-history-data="versionHistoryData"
          :on-switch-version-item="onSwitchVersionItem"
          :on-restore-selected-version="onRestoreSelectedVersion"
          @onPageChange="openVersionHistoryPanel"
        />
      </a-drawer>
      <a-layout-header>
        <div class="header" id="headerDom">
          <div
            style="
              display: flex;
              height: 64px;
              border-bottom: 1px solid rgb(224, 224, 224);
            "
          >
            <img
              src="./assets/editor.png"
              style="height: 40px; margin-top: 12px"
              alt=""
            />
            <div style="flex: 1">
              <div style="min-width: 180px; line-height: 30px">
                <input
                  ref="editorNameInput"
                  type="text"
                  :value="editorName"
                  class="editor-design-input"
                />
              </div>
              <div
                style="display: flex; line-height: 26px; align-items: center"
              >
                <headerList
                  ref="headerList"
                  :headerBtn="headerBtn"
                  @onMenuClick="onClickMenu"
                ></headerList>
                <div
                  v-if="dataSetInfo.dataSets"
                  class="editor-history"
                  @click="openVersionHistoryPanel"
                >
                  历史版本记录
                </div>
              </div>
            </div>
            <div
              style="height: 64px; margin: 0 auto; padding-right: 10px"
              v-if="customButtons"
            >
              <a-button
                v-for="item in [...customButtons.buttons]"
                :key="item.key"
                :type="item.type"
                :disabled="item.disable"
                style="margin: 17px 10px 0px 0px; text-align: right"
                @click="operationButtonClick(item)"
              >
                {{ item.showName }}
              </a-button>
            </div>
          </div>

          <div class="header-top">
            <div class="header-title msun-editor-forms-scrollbar">
              <template v-for="(cItem, cIndex) in baseStartBtn">
                <template v-if="cItem.type === 'icon'">
                  <my-icon
                    :key="cItem.type + cIndex"
                    :type="cItem.icon"
                    :title="cItem.title"
                    :class="selectMenuIconSelected(cItem)"
                    @mousedown.prevent="
                      () => {
                        headerBtnClickDecorator(() => {
                          cItem.func($event);
                        }, cItem);
                      }
                    "
                  />
                </template>
                <template v-else-if="cItem.type === 'textIcon'">
                  <div
                    :key="cItem.type + cIndex"
                    :class="'icon-font ' + iconTextSelected(cItem)"
                    @mousedown.prevent="
                      () => {
                        headerBtnClickDecorator(() => {
                          cItem.func($event);
                        }, cItem);
                      }
                    "
                    :title="cItem.title"
                  >
                    <my-icon :type="cItem.icon" class="icon" />
                    <div class="text">{{ cItem.title }}</div>
                  </div>
                </template>
                <template v-else-if="cItem.type === 'line'">
                  <div :key="cItem.type + cIndex" class="line"></div>
                </template>
                <template v-else-if="cItem.type === 'select'">
                  <selected
                    :key="cItem.type + cIndex"
                    :ref="'selectedMenu_' + cItem.dataType"
                    :data="selectData[cItem.dataType]"
                    :width="cItem.width"
                    :initData="initData[cItem.dataType]"
                    @selected="
                      (val, option) => {
                        headerBtnClickDecorator(() => {
                          cItem.func(val, option, cItem);
                        }, cItem);
                      }
                    "
                  ></selected>
                </template>
                <template v-else-if="cItem.type === 'color'">
                  <div :key="cItem.type + cIndex" class="withTriangle">
                    <my-icon
                      :type="cItem.icon"
                      :title="cItem.title"
                      class="font-color"
                      @mousedown.prevent="
                        () => {
                          headerBtnClickDecorator(() => {
                            cItem.func(cItem.isBg);
                          }, cItem);
                        }
                      "
                    />
                    <div
                      class="right-part"
                      @click.prevent="
                        () => {
                          headerBtnClickDecorator(() => {
                            showColorPicker(cItem.isBg);
                          }, cItem);
                        }
                      "
                    >
                      <div class="font-triangle"></div>
                    </div>
                    <colorPicker
                      :ref="cItem.isBg ? 'bgcolorPicker' : 'colorPicker'"
                      v-model="colorData[cItem.isBg ? 'bgColor' : 'fontColor']"
                      v-on:change="
                        (e) => {
                          changeColor(e, cItem.isBg);
                        }
                      "
                    />
                  </div>
                </template>
                <template v-else-if="cItem.type === 'fieldSymbolMenu'">
                  <div
                    :key="cItem.type + cIndex"
                    class="withTriangle"
                    :title="cItem.title"
                    @mousedown.prevent="
                      () => {
                        headerBtnClickDecorator(() => {
                          showIconTextMenu(cItem.showType);
                        }, cItem);
                      }
                    "
                  >
                    <my-icon
                      :type="selectedSymbolIcon"
                      class="icon-group"
                      @mousedown="fieldSymbolIconClickHandler($event, cItem)"
                    />
                    <div class="triangle"></div>
                    <div class="modal" v-if="showIconSelect === cItem.showType">
                      <div class="alignment">
                        <template v-for="(ccItem, ccIndex) in cItem.children">
                          <div
                            :key="ccItem.type + ccIndex"
                            :title="ccItem.title"
                            @mousedown.prevent="
                              () => {
                                headerBtnClickDecorator(
                                  () => {
                                    ccItem.key !== undefined
                                      ? ccItem.func(ccItem.key)
                                      : ccItem.func($event);
                                  },
                                  ccItem,
                                  cItem
                                );
                              }
                            "
                            :class="
                              'hover ' + selectMenuSelected(cItem, ccItem)
                            "
                          >
                            <my-icon
                              :type="ccItem.icon"
                              :class="selectMenuIconSelected(ccItem)"
                              class="icon"
                            />
                            {{ ccItem.title }}
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else-if="cItem.type === 'fieldAlign'">
                  <div
                    :key="cItem.type + cIndex"
                    class="withTriangle"
                    :title="cItem.title"
                    @mousedown.prevent="
                      () => {
                        headerBtnClickDecorator(() => {
                          showIconTextMenu(cItem.showType);
                        }, cItem);
                      }
                    "
                  >
                    <my-icon
                      :type="selectedFieldAlignIcon"
                      class="icon-group"
                      @mousedown="fieldAlignIconClickHandler($event, cItem)"
                    />
                    <div class="triangle"></div>
                    <div class="modal" v-if="showIconSelect === cItem.showType">
                      <div class="alignment">
                        <template v-for="(ccItem, ccIndex) in cItem.children">
                          <div
                            :key="ccItem.type + ccIndex"
                            :title="ccItem.title"
                            @mousedown.prevent="
                              () => {
                                headerBtnClickDecorator(
                                  () => {
                                    ccItem.key !== undefined
                                      ? ccItem.func(ccItem.key)
                                      : ccItem.func($event);
                                  },
                                  ccItem,
                                  cItem
                                );
                              }
                            "
                            :class="
                              'hover ' + selectMenuSelected(cItem, ccItem)
                            "
                          >
                            <my-icon
                              :type="ccItem.icon"
                              :class="selectMenuIconSelected(ccItem)"
                              class="icon"
                            />
                            {{ ccItem.title }}
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else-if="cItem.type === 'groupMenu'">
                  <div
                    :key="cItem.type + cIndex"
                    class="withTriangle"
                    :title="cItem.title"
                    @mousedown.prevent="
                      () => {
                        headerBtnClickDecorator(() => {
                          showIconTextMenu(cItem.showType);
                        }, cItem);
                      }
                    "
                  >
                    <my-icon
                      :type="selectedIcon"
                      class="icon-group"
                      @mousedown="iconClickHandler($event, cItem)"
                    />
                    <div class="triangle"></div>
                    <div class="modal" v-if="showIconSelect === cItem.showType">
                      <div class="alignment">
                        <template v-for="(ccItem, ccIndex) in cItem.children">
                          <div
                            :key="ccItem.type + ccIndex"
                            :title="ccItem.title"
                            @mousedown.prevent="
                              () => {
                                headerBtnClickDecorator(
                                  () => {
                                    ccItem.key !== undefined
                                      ? ccItem.func(ccItem.key)
                                      : ccItem.func($event);
                                  },
                                  ccItem,
                                  cItem
                                );
                              }
                            "
                            :class="
                              'hover ' + selectMenuSelected(cItem, ccItem)
                            "
                          >
                            <my-icon
                              :type="ccItem.icon"
                              :class="selectMenuIconSelected(ccItem)"
                              class="icon"
                            />
                            {{ ccItem.title }}
                          </div>
                        </template>
                      </div>
                    </div>
                  </div>
                </template>
                <template v-else-if="cItem.type === 'orderList'">
                  <div :key="cItem.type + cIndex" class="ignoreClickMenu">
                    <my-icon
                      :type="cItem.icon"
                      @mousedown.prevent="
                        () => {
                          headerBtnClickDecorator(() => {
                            cItem.func(cItem.showType);
                          }, cItem);
                        }
                      "
                      :title="cItem.title"
                      class="icon"
                    />
                    <div
                      class="list_div"
                      v-show="showIconSelect === cItem.showType"
                    >
                      <orderListStyle @orderType="toOrderList"></orderListStyle>
                    </div>
                  </div>
                </template>
                <template v-else-if="cItem.type === 'dragBox'">
                  <drag-box
                    :key="cItem.type + cIndex"
                    @dragstart.native="
                      () => {
                        headerBtnClickDecorator(() => {
                          cItem.func($event);
                        }, cItem);
                      }
                    "
                  ></drag-box>
                </template>
              </template>
            </div>
          </div>
          <div class="menu-bar">
            <div
              @mousedown.prevent="confirmInsertTable"
              class="container"
              :style="position"
              v-show="showTableChoice"
              @mousemove="mouseMove($event)"
            >
              <div class="insert-table">
                <span v-if="colNum === 0 || rowNum === 0">插入</span>
                <span v-else>{{ rowNum }} X {{ colNum }}</span>
                表格
              </div>
              <div
                :class="{ box_bgc: isShowBgcClass(i) }"
                v-for="i in 100"
                :key="i"
                class="box"
              ></div>
            </div>
          </div>
        </div>
      </a-layout-header>
      <a-layout>
        <a-layout-sider v-if="showLeftSider()">
          <navigation-menu
            v-if="leftMenuConfig && leftMenuConfig.menuData"
            :menu-data="leftMenuConfig.menuData"
            :defaultSelectedKey="leftMenuConfig.defaultSelectedKey"
            :default-open-keys="leftMenuConfig.defaultOpenKeys"
            @clickMenu="clickLeftMenu"
          ></navigation-menu>
          <tree-menu
            v-else
            :tree-data="leftPanelConfig.treeData"
            :draggable="leftPanelConfig.draggable"
            :dataSetInfo="dataSetInfo"
            @clickTree="clickTreeMenu"
            @dragstart="treeMenuDragstart"
            @handleDataSetChange="handleDataSetSelect"
          ></tree-menu>
          <createFields
            :show="showCreateFields"
            :tree-data="leftPanelConfig.treeData"
            :draggable="leftPanelConfig.draggable"
            :dataSetInfo="dataSetInfo"
            :customFieldList="customFieldList"
            :allDataSetTree="allDataSetTree"
            @handleDataSetChange="handleDataSetSelect"
            @submit="createFieldsSubmit"
            @cancel="createFieldsCancel"
            @deleteCustomFiledItem="deleteCustomFiledItem"
            @saveChangedCustomField="saveChangedCustomField"
            ref="createFields"
          ></createFields>
        </a-layout-sider>
        <a-layout-content>
          <div id="editorDom" style="width: 100%; height: 100%">
            <msun-editor-vue
              style="width: 100%; height: 100%"
              @init="init"
              :upConfig="config"
            ></msun-editor-vue>
          </div>
        </a-layout-content>
        <a-layout-sider v-if="showRightAttrPanel()" :width="320">
          <div style="display: flex; height: 100%">
            <div style="background-color: rgb(255, 255, 255); width: 48px">
              <div
                v-for="(item, i) in showRightList"
                :key="i"
                :class="item.show ? 'side-menu' : 'side-menu-forbid'"
              >
                <div
                  :class="
                    i === clickNum
                      ? !item.show
                        ? 'editor-button-active'
                        : 'editor-button-click'
                      : 'editor-button-show'
                  "
                  :title="item.name"
                  @click="clickIcon(item.key, i)"
                >
                  <icon-common
                    v-if="item.icon.indexOf('icon') > -1"
                    :icon-style="customIconStyle"
                    :title="item.name"
                    :icon="item.icon"
                  />
                </div>
              </div>
            </div>
            <div
              style="
                background-color: rgb(191, 191, 191);
                width: 1px;
                height: 100%;
                position: relative;
              "
            ></div>
            <div
              v-if="
                rightMenuConfig.treeData &&
                rightMenuConfig.treeData.length &&
                clickListName === 'dictionary'
              "
              style="width: 272px"
            >
              <tree-menu
                :tree-data="rightMenuConfig.treeData"
                :draggable="rightMenuConfig.draggable"
                :dataSetInfo="dictSetInfo"
                @clickTree="clickTreeMenu"
                @dragstart="treeMenuDragstart"
                @handleDataSetChange="handleDictSetSelect"
              >
              </tree-menu>
            </div>
            <div
              v-else-if="clickListName !== 'dictionary'"
              style="
                width: 272px;
                position: relative;
                height: calc(100% - 40px);
              "
            >
              <div v-if="mask" class="side-mask"></div>
              <editorElement
                ref="editorElement"
                class="msun-editor-forms-scrollbar"
                :type="clickListName"
                :dataSource="dataSource"
                @submit="sideSubmit()"
              ></editorElement>
            </div>
            <div class="submit-button">
              <a-button
                type="primary"
                v-if="clickListName !== 'dictionary'"
                @click="sideSubmit()"
                class="button"
                >提交(Ctrl+Enter)</a-button
              >
            </div>
          </div>
        </a-layout-sider>
      </a-layout>
      <a-layout-footer>
        <div class="statistic_div">
          <span class="editor-span">{{ statistic_message }}</span>
          <div class="editor-control-content">
            <div class="editor-control-space"></div>
            <div class="editor-control-scale">
              <ScaleComponent
                ref="scaleComponent"
                :editorScale="editorScale"
                @changeScale="changeEditorScale"
              />
            </div>
          </div>
          <span class="editor-span">{{ location_message }}</span>
        </div>
      </a-layout-footer>
    </a-layout>
    <dataSetModal
      :show="showDataSetModal"
      :oriDataSet="oriDataSet"
      @submit="updateDataSet"
      @cancel="closeDataSetModal"
    >
    </dataSetModal>
    <editorModal :show="showEditorModal" @cancel="closeEditorModal">
    </editorModal>
    <div v-show="isElectron" id="msun-editor-gpt" class="app" tabindex="-1">
      <GptMsg
        ref="gptMsgVueComponent"
        @onGetDataset="onGetDataset"
        @initGrfData="onInitGrfData"
        @clearData="clearData"
      ></GptMsg>
    </div>
  </div>
</template>

<script>
import headerBtnMixIn from "./mixin/headerBtn/headerBtn";
import editorEventMixIn from "./mixin/editorEvent";
import sideBarMixIn from "./mixin/sideBar";
import createFieldsMixIn from "./mixin/createFields";
import config from "./js/config";
import TreeMenu from "./components/treeMenu.vue";
import editorElement from "./components/editorElement.vue";
import ScaleComponent from "./components/ScaleComponent.vue";
import NavigationMenu from "./components/navigationMenu.vue";
import dataSetMixIn from "./mixin/treeDataSet";
import editorModalMixIn from "./mixin/editorModal";
import VersionHistoryRecord from "./components/VersionHistoryRecord.vue";
import dataSetModal from "./components/dataSetModal.vue";
import editorModal from "./components/editorModal.vue";
// import histories from "./history.js";
import selected from "./components/selected.vue";
import orderListStyle from "./components/orderListStyle.vue";
import headerList from "./components/headerList.vue";
import msunEditorVue from "msun-editor-vue";
import iconCommon from "./components/iconCommon.vue";
import createFields from "./components/createFields.vue";
import "ant-design-vue/dist/antd";
import "./plugins/use";
import expressionValidate from "./mixin/expressionValidate";
import GptMsg from "./components/gpt/GptMsg.vue";
export default {
  name: "msunLibEditorDesign",
  components: {
    NavigationMenu,
    TreeMenu,
    selected,
    orderListStyle,
    createFields,
    // dragBox,
    VersionHistoryRecord,
    headerList,
    ScaleComponent,
    msunEditorVue,
    dataSetModal,
    iconCommon,
    editorElement,
    editorModal,
    GptMsg,
  },
  mixins: [
    headerBtnMixIn,
    editorEventMixIn,
    dataSetMixIn,
    sideBarMixIn,
    editorModalMixIn,
    createFieldsMixIn,
    expressionValidate,
  ],
  props: {
    upConfig: {
      type: Object,
      default: () => null,
    },
    leftMenuConfig: {
      type: Object,
      default: () => null,
    },
    rightMenuConfig: {
      type: Object,
      default: () => {
        return {};
      },
    },
    leftPanelConfig: {
      type: Object,
      default: () => {
        return {};
      },
    },
    otherConfig: {
      type: Object,
      default: () => null,
    },
    customButtons: {
      type: Object,
      default: () => {
        [];
      },
    },
    designMode: {
      type: String,
      default: "",
    },
    editorName: {
      type: String,
      default: "众阳文档模板设计器",
    },
  },
  data() {
    return {
      isElectron: false,
      config: config,
      clickNum: 0,
      // editorName: "编辑器一",
      editorScale: 1,
      fullscreen: false, // 当前是否全屏
      wordStatistics: { page_num: 0, page_all_num: 0, word_nums: 0 },
      statistic_message: "",
      location_message: "",
      headerBtn: [],
      showTableChoice: false,
      dragInfo: {},
      versionHistoryData: null,
      pageNumber: 1, // 当前页码
      pageTotal: 1, //总共页码
      fontFamily: [], //字体种类列表
      fontSize: [], //字体大小列表
      historyRecord: [], // 历史操作记录
      isVersionHistoryVisible: false, //打开历史记录面板
      curVersionId: "1", //* 当前所使用的历史版本的ID
      customFieldList: [], //自定义创建字段列表
      showRightList: [
        {
          key: "field",
          name: "文本域",
          icon: "icon-icon-test-copy-copy",
          show: false,
        },
        {
          key: "checkBox",
          name: "多选框",
          icon: "icon-duoxuankuangyixuan-copy-copy-copy",
          show: false,
        },
        {
          key: "group",
          name: "分组",
          icon: "icon-fenzushezhi-copy-copy",
          show: false,
        },
        {
          key: "cell",
          name: "单元格",
          icon: "icon-danyuangeshezhi-copy-copy",
          show: false,
        },
        {
          key: "table",
          name: "表格",
          icon: "icon-biaogeshezhi-copy-copy",
          show: false,
        },
      ],
      clickListName: "field",
      customIconStyle: {
        width: "25px",
        height: "25px",
        fill: "rgba(0, 133, 255, 0.8)",
        marginTop: "7.5px",
        marginLeft: "7.5px",
        cursor: "pointer",
      },
      // customButtons: {
      //   buttons: [
      //     {
      //       showName: "预览",
      //       disable: false,
      //       key: "previewReport",
      //       type: "default",
      //     },
      //     {
      //       showName: "保存模板",
      //       disable: false,
      //       key: "saveTemplate",
      //       type: "default",
      //     },
      //     {
      //       showName: "保存并发布",
      //       disable: false,
      //       key: "saveAndPublishReport",
      //       type: "primary",
      //     },
      //   ],
      // },
    };
  },
  created() {
    // 初始化赋值
    if (this.upConfig) {
      this.config = this.upConfig;
    }
  },
  mounted() {
    this.judgeIsElectron();
    const headerBtn = [
      { name: "文件", data: [...this.fileBtn] },
      { name: "编辑", data: [...this.editBtn] },
      { name: "插入", data: [...this.insertBtn] },
      { name: "表格", data: [...this.tableBtn] },
      { name: "视图", data: [...this.viewBtn] },
      { name: "设置", data: [...this.settingBtn] },
    ];
    this.$emit("initHeaderBtn", headerBtn, this.baseStartBtn);
    if (this.rightMenuConfig.treeData && this.rightMenuConfig.treeData.length) {
      this.showRightList.unshift({
        key: "dictionary",
        name: "字典集",
        icon: "icon-dict-copy-copy",
        show: true,
      });
      this.clickListName = "dictionary";
    }

    this.headerBtn = headerBtn;
    this.updateSideData();
    this.readRouter(location.search.replace("?", ""));
    this.customFieldList = this.instance.editor.document_meta.customField
      ? this.instance.editor.document_meta.customField
      : [];
    this.$refs.gptMsgVueComponent.editor = this.instance;
    this.$refs.gptMsgVueComponent.$refs.chat.instance = this.instance;
  },
  methods: {
    judgeIsElectron() {
      const userAgent = navigator.userAgent.toLowerCase();
      if (
        userAgent.indexOf("electron/") > -1 ||
        (this.$workEnv &&
          this.$workEnv.isElectronEnv &&
          this.$workEnv.isElectronEnv())
      ) {
        this.isElectron = true;
      } else {
        this.isElectron = false;
      }
    },
    // AI 聊天机器人 ↓
    onGetDataset(res) {
      // 设置数据集
      console.log("执行了 onGetDataset, 设置数据集", res);
      // this.$refs.msunReport.setDataSets([res]);
      // this.$refs.msunReport.initReportData(null);
    },
    clearData() {
      // 清空数据集
      console.log("执行了 clearData 清空数据集");
      // this.$refs.msunReport.setDataSets([]);
      // const InsStore = this.getInstance();
      // InsStore.sheet.clear();
    },
    onInitGrfData(res) {
      // 恢复table GRF数据
      console.log("执行了 oInitGrfData 恢复 table GRF 数据", res);
      // this.$refs.msunReport.initReportData(res);
    },
    // AI 聊天机器人 ↑
    deleteCustomFiledItem(item) {
      const editor = this.instance.editor;
      const customFields = editor.document_meta.customField;
      if (customFields && Array.isArray(customFields)) {
        for (let i = 0; i < customFields.length; i++) {
          if (customFields[i].key === item.key) {
            customFields.splice(i, 1);
            this.updateCustomFieldDataSet();
            this.$refs.createFields.updateList();
            this.$forceUpdate();
            editor.event.emit("message", "删除成功");
            break;
          }
        }
      }
    },
    saveChangedCustomField(expression, obj) {
      const { type } = obj;
      const editor = this.instance.editor;
      const { expressionStr, externalBrackedContent } =
        this.replaceRule(expression);
      if (!expressionStr) {
        editor.event.emit("message", {
          type: "error",
          msg: "字段表达式无效",
        });
        return;
      }
      if (externalBrackedContent.length) {
        console.log("有数据集里不存在的字段", externalBrackedContent.join(","));
        editor.event.emit("message", {
          type: "error",
          msg: "有数据集里不存在的字段",
        });
        return;
      }

      if (!this.parenthesisIsPaired(expressionStr)) {
        console.log("校验没有通过， 括号不成对");
        editor.event.emit("message", {
          type: "error",
          msg: "括号不成对",
        });
        return;
      }
      if (!editor.document_meta.customField) {
        editor.document_meta.customField = [];
      }
      if (type === "create") {
        editor.document_meta.customField.push(expression);
      } else {
        for (let i = 0; i < editor.document_meta.customField.length; i++) {
          const c = editor.document_meta.customField[i];
          if (c.key === this.$refs.createFields.checkedCustomField.key) {
            editor.document_meta.customField.splice(i, 1, expression);
            break;
          }
        }
      }
      this.$refs.createFields.updateList();
      this.updateCustomFieldDataSet();

      this.customFieldList = editor.document_meta.customField
        ? editor.document_meta.customField
        : [];
      this.updateFieldMapByAllDataSetTree(true);
      this.$forceUpdate();
      editor.event.emit("message", {
        type: "success",
        msg: "保存成功",
      });
    },
    readRouter(path) {
      if (!path || !path.startsWith("doc_")) return;
      return fetch("../editorData/" + path)
        .then((response) => response.text())
        .then((data) => {
          if (!data) return;
          data = JSON.parse(data);
          data.dataSource &&
            data.dataSource.data &&
            this.instance.initDataSet(data.dataSource);
          data.rawData && this.instance.editor.reInitRawByConfig(data.rawData);
          this.instance.editor.refreshDocument();
          this.$nextTick(() => {
            this.instance.editor.updateCanvasSize();
          });
        });
    },
    init(instance, err) {
      if (err) {
        this.$editor.error(err.message);
        console.log(err);
      }
      // 注意 ：此处this.instance是没有在当前组件data中定义的，因为当编辑器中用到的实例在data中定义后属性会被绑定get、set方法用于监听属性变化，严重影响性能造成卡顿。
      // 所以此处建议如下在不定义的情况下进行赋值或定义为全局变量
      this.instance = instance;
      // 绑定编辑器事件，用于各种业务功能扩展
      this.bindInstanceEvent(instance);
      this.initContextState();
      this.initFontStyleAndType();
      this.initOtherSetAfter();
      instance.initDataSet = this.initDataSet;
      this.$emit("init", instance);
    },
    showLeftSider() {
      if (
        (this.leftMenuConfig && this.leftMenuConfig.menuData) ||
        (this.dataSetInfo.dataSets && this.dataSetInfo.dataSets.length)
      ) {
        return true;
      }
      return false;
    },
    showRightAttrPanel() {
      if (this.otherConfig && this.otherConfig.hideAttrPanel) {
        return false;
      } else {
        return true;
      }
    },
    getInstance() {
      return this.instance;
    },
    onClickMenu(type, e) {
      let dataList = [];
      switch (type) {
        case "文件":
          dataList = this.fileBtn;
          break;
        case "编辑":
          dataList = this.editBtn;
          break;
        case "插入":
          dataList = this.insertBtn;
          break;
        case "表格":
          dataList = this.tableBtn;
          break;
        case "视图":
          dataList = this.viewBtn;
          break;
        case "设置":
          dataList = this.settingBtn;
      }
      for (let i = 0; i < dataList.length; i++) {
        const info = dataList[i];
        if (info.title === e.title) {
          e.func();
          break;
        } else if (info.children && info.children.length) {
          for (let j = 0; j < info.children.length; j++) {
            const element = info.children[j];
            if (element.title === e.title) {
              element.func();
              break;
            }
          }
        }
      }
    },

    operationButtonClick(item) {
      const { editor } = this.instance;
      if (this.isShowRealValue) {
        this.showRealValue();
        this.$editor.warning("已自动退出真实值展示模式！");
      }
      const rawData = editor.getRawData();
      const param = {
        customButtonsKey: item.key,
        customButtonName: item.showName,
        reportData: JSON.stringify(rawData),
      };
      this.$emit("onCustomButtonsClick", param);
    },
    openVersionHistoryPanel() {
      this.isVersionHistoryVisible = true;
      // this.fetchVersionHistoryData(this.pageNumber, (data) => {
      //   // TODO 设置记录列表和记录总数
      //   this.initHistoryRecord(data.list);
      //   this.pageTotal = data.total;
      // });
      this.$emit("fetchVersionHistoryData", this.pageNumber, (data) => {
        // TODO 设置记录列表和记录总数
        this.initHistoryRecord(data.list);
        this.pageTotal = data.total;
      });
    },

    // 获取历史版本数据
    // fetchVersionHistoryData(page, callback) {
    //   console.info("请求历史版本数据...");
    //   window.setTimeout(() => {
    //     const data = {
    //       list: histories,
    //       total: 100,
    //     };
    //     callback(data);
    //     this.versionHistoryData = data; // [];
    //   }, 600);
    // },
    // 请求获取历史版本记录的数据
    // emitFetchVersionHistoryData(page, callback) {
    //   this.$emit("fetchVersionHistoryData", page, callback);
    // },
    changeEditorScale(scale) {
      // 缩放改变的时候
      this.editorScale = scale;
      this.instance.editor.setViewScale(scale);
    },
    clickIcon(key, i) {
      this.clickListName = key;
      this.clickNum = i;
      if (i) {
        this.$nextTick(() => {
          this.$refs.editorElement.editor = this.instance;
        });
      }
      if (key === "group") {
        this.$nextTick(() => {
          const editorElement = this.$refs.editorElement;
          if (editorElement) {
            const recursion = editorElement.$refs.sideCard[0];
            recursion.selectOption =
              this.focusElement["group"] && this.focusElement["group"].name
                ? this.focusElement["group"].name
                : "";
          }
        });
      }
      const result = this.showRightList.find(
        (e) => e.show && e.key === this.clickListName
      );
      if (!result) {
        this.mask = true;
      } else {
        this.mask = false;
      }
    },
    getDateDesc(date) {
      const dateKey = date.split(" ")[0];
      let dateAliasName = dateKey;
      const sourceDate = new Date(dateKey.replace(/-/g, "/"));
      const sourceDay = [
        sourceDate.getFullYear(),
        sourceDate.getMonth() + 1,
        sourceDate.getDate(),
      ];
      const now = new Date();
      const nowDay = [now.getFullYear(), now.getMonth() + 1, now.getDate()];
      if (nowDay[0] === sourceDay[0] && nowDay[1] === sourceDay[1]) {
        const deltaDay = nowDay[2] - sourceDay[2];
        if (deltaDay < 3) {
          dateAliasName = ["今天", "昨天", "前天"][deltaDay];
        }
      }
      return { dateKey, dateAliasName };
    },
    initHistoryRecord(data) {
      if (data.length) {
        // 需要处理成versionHistoryRecord需要的列表格式
        const obj = {};
        data.forEach((item, index) => {
          const dateDesc = this.getDateDesc(item.logTime); //getDateDesc(item.hisUpdateTime);
          if (!obj[dateDesc.dateKey]) {
            obj[dateDesc.dateKey] = {
              ...dateDesc,
              verRecords: [],
            };
          }
          obj[dateDesc.dateKey].verRecords.push({
            reportFileLogId: item.reportFileLogId,
            reportFileName: item.reportFileName,
            restoreDesc: null, // 依赖哪个版本还原的。目前都不要了
            reportGrf: item.reportGrf,
            logTime: item.logTime,
            collaborators: [
              {
                id: "x-" + index,
                name: item.createUserName,
              },
            ],
          });
        });
        this.historyRecord = Object.values(obj);
        this.$refs.editorRecord.recordList = this.historyRecord;
      }
    },
    /**
     * 从历史记录面板恢复某一版本的存储记录时的处理逻辑
     * @test 待完善
     */
    onRestoreSelectedVersion(selectedVersionId) {
      // 获取某条数据的详情
      let data;
      this.historyRecord.some((item) => {
        return item.verRecords.some((it) => {
          if (it.reportFileLogId === selectedVersionId) {
            data = it.reportGrf ? it.reportGrf : "";
            return true;
          }
        });
      });
      this.isVersionHistoryVisible = false;
      if (!data) return;
      this.curVersionId = selectedVersionId;
      this.instance.editor.reInitRawByConfig(data, false);
      this.instance.editor.refreshDocument();
    },
    initOtherSetAfter() {
      // 窗口改变编辑器自适应大小
      this.$nextTick(() => {
        this.instance.editor.updateCanvasSize();
      });
      this.wordStatisticsFun();
      this.addEventListener();
      this.instance.editor.show_tips = true;
      this.$refs.editorElement.editor = this.instance;
    },
    addEventListener() {
      // 窗口改变编辑器自适应大小
      window.onresize = () => {
        this.$nextTick(() => {
          this.instance.editor.updateCanvasSize();
        });
      };
      let myObserver = new ResizeObserver(() => {
        this.$nextTick(() => {
          this.instance && this.instance.editor.updateCanvasSize(); // 解决热更新问题，比如修改了 systemconfig this.instance 为 null
        });
      });
      myObserver.observe(document.getElementById("headerDom")); // dom
      document.addEventListener("mouseup", this.mouseUpEvent, false);
      document.addEventListener("fullscreenchange", this.fullScreenChangeEvent);
    },

    // 页面下方数据展示
    wordStatisticsFun() {
      this.wordStatistics = this.instance.editor.wordStatistics();
      this.statistic_message = `第${this.wordStatistics.page_num}页\xa0\xa0共${this.wordStatistics.page_all_num}页\xa0\xa0\xa0\xa0\xa0\xa0${this.wordStatistics.word_nums}个字`;
      this.location_message = `第${this.wordStatistics.row_num}行\xa0\xa0第${this.wordStatistics.col_num}列`;
    },
    // 局部全屏
    divFullScreen() {
      this.instance.editor.focus();
      const element2 = document.getElementById("editor-design");
      const element = document.documentElement;
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
        element2.style = {
          display: "flex",
          height: "100%",
          flexDirection: "column",
          justifyContent: "flex-start",
          alignItems: "flex-start",
        };
      } else {
        const top = element2.getBoundingClientRect().top;
        const left = element2.getBoundingClientRect().left;
        this.top = top;
        this.left = left;
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          element.msRequestFullscreen();
        }
        element2.style.position = "absolute";
        element2.style.top = "0";
        element2.style.right = "0";
        element2.style.bottom = "0";
        element2.style.left = "0";
      }
    },
    clickLeftMenu(data, key, dataArray) {
      this.$emit("clickLeftMenu", data, key, dataArray);
    },

    fullScreenChangeEvent() {
      const isFullScreen =
        document.fullScreen ||
        document.mozFullScreen ||
        document.webkitIsFullScreen;
      this.fullscreen = isFullScreen;
      if (!this.fullscreen) {
        const element2 = document.getElementById("editor-design");
        element2.style = {
          display: "flex",
          height: "100%",
          flexDirection: "column",
          justifyContent: "flex-start",
          alignItems: "flex-start",
        };
      }
      // 此处改为 this.$nexttick 不生效
      setTimeout(() => {
        this.instance.editor.updateCanvasSize();
      });
    },
    /**
     * 在版本历史记录面板切换当前所预览的版本
     * @test 待完善
     */
    onSwitchVersionItem(versionId) {
      console.info("version id: ", versionId);
    },
    mouseUpEvent(event) {
      const fontDoms = document.getElementsByClassName("select-bottom");
      for (let i = 0; i < fontDoms.length; i++) {
        const pDom = fontDoms[i];
        if (pDom.contains(event.target)) {
          return;
        }
      }

      const groupMenuDoms = document.getElementsByClassName("withTriangle");
      for (let i = 0; i < groupMenuDoms.length; i++) {
        const pDom = groupMenuDoms[i];
        // console.log(pDom);
        if (pDom.contains(event.target)) {
          return;
        }
      }
      const needIgnoreMenuDoms =
        document.getElementsByClassName("ignoreClickMenu");
      for (let i = 0; i < needIgnoreMenuDoms.length; i++) {
        const pDom = needIgnoreMenuDoms[i];
        if (pDom.contains(event.target)) {
          return;
        }
      }
      this.closeSelectedMenu();
      this.closeColorPicker();
    },
  },
  beforeDestroy() {
    this.instance = null;
    document.removeEventListener("mouseup", this.mouseUpEvent);
    document.removeEventListener(
      "fullscreenchange",
      this.fullScreenChangeEvent
    );
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="less" scoped>
.view-selected {
  font-size: 14px;
  vertical-align: middle;
  overflow: hidden;
  background-color: rgb(200, 200, 200) !important;
  border-radius: 3px;
}

.header {
  vertical-align: middle;
  background-color: rgb(255, 255, 255);
  // margin-bottom: 10px;
  box-shadow: 0 0px 10px rgb(150, 150, 150);
}

.header-top {
  color: #666;
  display: flex;
  line-height: 36px;
  height: 36px;
  align-items: center;
  white-space: nowrap;
}

.header-title {
  display: flex;
  font-size: 14px;
  align-items: center;
  height: 34px;
  padding-left: 20px;
  overflow-x: scroll;
  overflow-y: hidden;
}

/* 全局自定义的滚动条样式（基于webkit-scrollbar，注意兼容性 / 不会像原生的一样，可以自定隐藏） */
.msun-editor-forms-scrollbar {
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #d3e3fd;
    border-radius: 6px;
  }
  /* 鼠标悬停时滑块样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #a8c7fa;
  }
  &::-webkit-scrollbar-track {
    background-color: white;
    -webkit-box-shadow: inset 0 0 4px rgba(100, 100, 100, 0.01);
  }
}

.header-type {
  display: flex;
  height: 32px;
  padding: 5px 0px 5px 0px;
  border-bottom: 1px solid rgb(224, 224, 224);
  cursor: default;
  position: relative;
}

.header-click {
  font-size: 14px;
  color: #fff;
  padding: 1px 10px 1px 10px;
  background-color: #5b9cf3;
  border-radius: 3px;
  cursor: default;
}

.header-center {
  display: flex;
  text-align: center;
  margin: 0 auto;
}

.header-content {
  cursor: pointer;
}

.header-content:hover {
  cursor: pointer;
  background-color: rgb(238, 238, 238);
}

#editor-design {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  position: relative;

  & > * {
    flex-shrink: 0;
    width: 100%;
  }
}

.line {
  width: 1px;
  background-color: rgb(150, 150, 150);
  height: 20px;
  margin: 2px 5px 0 5px;
}

.menu-bar {
  .container {
    width: 260px; // 计算该宽度的时候 记得加上边框的宽度 (box的宽度 + margin*2)*每行显示的数 + 2px + padding*2
    padding: 40px 20px 20px 20px;
    border: none;
    flex-wrap: wrap;
    display: flex;
    position: fixed;
    box-shadow: 2px 2px 5px;
    background-color: #fff;
    z-index: 99;

    .insert-table {
      width: 212px; // 260(.container的width) - 24(margin-left) * 2
      height: 30px;
      line-height: 30px;
      margin-left: 24px; // padding-left + margin 20 + 4
      border-bottom: 1px solid #ccc;
      position: absolute;
      top: 5px;
      left: 0;

      span {
        display: inline-block;
        width: 50px;
        text-align: left;
      }
    }

    .box {
      width: 14px;
      height: 14px;
      border: 1px solid rgb(226, 230, 237);
      box-sizing: border-box;
      margin: 4px;
    }

    .box_bgc {
      background: rgb(228, 239, 253);
    }
  }
}

.header/deep/.icon {
  width: 25px;
  height: 25px;
  font-size: 14px;
  padding: 5px 5px 2px 5px;
  vertical-align: middle;
  border-radius: 3px;
  margin: 0px 5px 0px 5px;
  // overflow: hidden;
  z-index: 99;
}

.header/deep/.icon:hover {
  border-radius: 2px;
  -webkit-font-smoothing: antialiased;
  background-color: rgb(220, 225, 224);
}

.hover:hover {
  background-color: rgb(220, 225, 224);
}

.icon-click {
  width: 25px;
  height: 25px;
  font-size: 14px;
  padding: 5px 5px 2px 5px;
  vertical-align: middle;
  margin: 0px 5px 0px 5px;
  // overflow: hidden;
  background-color: rgb(200, 200, 200);
  border-radius: 1px;
}

.header/deep/.icon-group {
  width: 25px;
  height: 25px;
  font-size: 14px;
  padding: 5px 5px 2px 5px;
  vertical-align: middle;
  margin: 0px 5px 0px 5px;
  overflow: hidden;
}

.header/deep/.font-color {
  height: 25px;
  font-size: 14px;
  padding: 5px 0px 2px 5px;
  vertical-align: middle;
  margin: 0px 0px 0px 5px;
  overflow: hidden;
}

.header/deep/.font-color:hover {
  background-color: rgb(195, 195, 195);
}

.header/deep/.icon-font {
  display: flex;
  border-radius: 2px;
  -webkit-font-smoothing: antialiased;
  background-color: rgb(244, 244, 244);
}

.header/deep/.icon-font:hover {
  display: flex;
  border-radius: 2px;
  -webkit-font-smoothing: antialiased;
  background-color: rgb(220, 225, 224);
  cursor: default;
}

.header .header-title/deep/ .m-colorPicker {
  position: absolute;
  top: 19px;
  left: 11px;
  border-radius: 1px;
}

.header .header-title/deep/ .colorBtn {
  height: 3px;
  width: 12px;
}

.change-left-bar {
  position: absolute;
  margin-top: 5px;
  left: 20px;
  font-size: 16px;
}

.text {
  margin-right: 5px;
  line-height: 25px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.fullscreen-icon {
  position: absolute;
  right: 20px;
  margin-top: 5px;
  font-size: 16px;
}

.withTriangle ::v-deep .m-colorPicker {
  position: absolute;
  bottom: 8px;
  left: 8px;
}

.withTriangle ::v-deep .m-colorPicker .colorBtn {
  position: absolute;
  height: 3px;
}

.withTriangle ::v-deep .m-colorPicker .box.open {
  z-index: 9;
  position: fixed;
}

.withTriangle {
  position: relative;
  display: flex;
  border-radius: 4px;
  flex-shrink: 0;
  cursor: pointer;

  &:hover {
    background: #eee;
  }
}

.alignment {
  padding: 5px;
  width: 200px;
  text-align: left;
  cursor: pointer;
}

.loading {
  position: absolute;
  height: 100%;
  text-align: center;
  background: rgba(248, 253, 255, 0.8);
  z-index: 999;
}

.loading-icon {
  position: absolute;
  left: 50%;
  top: 30%;

  transform: translate(-50%, -30%);
}

/deep/.ant-spin-dot-item {
  background-color: rgb(24, 144, 255);
  opacity: 0.3;
}

/deep/.ant-spin-text {
  color: rgb(24, 144, 255);
}

.triangle {
  margin-top: 10px;
  margin-right: 4px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgb(124, 124, 124);
}

.font-triangle {
  margin-top: 12px;
  margin-right: 4px;
  width: 0;
  height: 0;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgb(124, 124, 124);
}

.right-part {
  width: 13px;
  height: 25px;
}

.right-part:hover {
  background-color: rgb(195, 195, 195);
}

.alignment {
  position: fixed;
  box-shadow: 0 0px 10px rgb(150, 150, 150);
  padding: 0;
}

.modal {
  position: absolute;
  margin-top: 28px;
  box-shadow: 0 0px 10px rgb(150, 150, 150);
  background-color: rgb(244, 244, 244);
  border-radius: 2px;
  z-index: 999;
}

.second-list {
  cursor: pointer;
}

.second-list:hover {
  background-color: rgb(211, 215, 214);
  cursor: pointer;
}

.modal-next {
  position: absolute;
  margin-left: 175px;
  margin-top: -5px;
  box-shadow: 0 0px 10px rgb(150, 150, 150);
  background-color: rgb(244, 244, 244);
  border-radius: 2px;
}

.list_div {
  position: absolute;
  margin: 2px 5px;
  box-shadow: 0 0px 10px rgb(150, 150, 150);
  background-color: rgb(244, 244, 244);
  border-radius: 2px;
  z-index: 1;
}

.selected {
  background-color: rgb(224, 224, 224);
  padding: 3px 0px 3px 0px;
  cursor: pointer;
}

.unselected {
  padding: 3px 0px 3px 0px;
  background-color: rgb(255, 255, 255);
  cursor: pointer;
}

.side-menu-forbid {
  height: 60px;
  width: 40px;
  margin: 0 auto;
  display: table;
  pointer-events: auto;
  opacity: 0.2;
}

.side-menu {
  height: 60px;
  width: 40px;
  margin: 0 auto;
  display: table;
}

.tableLine {
  padding: 5px;
  width: 160px;
  display: flex;
  cursor: pointer;
}

.groupCss {
  padding: 5px;
  width: 180px;
  display: flex;
  cursor: pointer;
}

.tableLine:hover,
.template_css:hover,
.groupCss:hover {
  background-color: rgb(211, 215, 214);
}

.template_css {
  padding: 5px;
  min-width: 100px;
  display: flex;
  justify-content: flex-start;
  cursor: pointer;
}

.statistic_div {
  display: flex;

  .editor-span {
    margin-left: 25px;
    margin-right: 25px;
  }
}

.editor-control-content {
  display: flex;
  flex: 1;

  .editor-control-space {
    flex: 1;
  }

  .editor-control-scale {
    width: 200px;
    height: 20px;
  }
}

.editor-button-show {
  height: 40px;
  width: 40px;
  margin-top: 10px;
}

.editor-button-click {
  height: 40px;
  width: 40px;
  margin-top: 10px;
  border-radius: 5px;
  background-color: rgb(231, 231, 231);
}

.editor-button-active {
  height: 40px;
  width: 40px;
  margin-top: 10px;
  border-radius: 5px;
  background-color: rgb(200, 200, 200);
}
.editor-button-show:hover {
  height: 40px;
  width: 40px;
  margin-top: 10px;
  border-radius: 5px;
  background-color: rgb(241, 241, 241);
}

.editor-design-input {
  outline: none;
  border: 1px solid transparent;
  font-size: 16px;
  vertical-align: bottom;
  line-height: 20px;
  color: #333;

  &:focus {
    border: 1px solid #1a73e8 !important;
    box-shadow: none;
    border-radius: 4px !important;
  }
}

// 右键菜单
#selfRightMenu {
  width: 200px;
  height: 300px;
  position: fixed;
  top: 0;
  left: 0;
  border: 1px solid #000;
  background-color: #ccc;
}

.modal /deep/ .ant-btn-defalut {
  color: #000;
}

.modal /deep/ .ant-modal-header {
  padding: 8px 10px;
}

.modal /deep/ .ant-modal-close-x {
  height: 40px;
  line-height: 40px;
}

.editor-history {
  color: #7c7c7c;
  font-size: 13px;
  text-decoration: underline;
  cursor: pointer;
  user-select: none;
  margin-left: 10px;
}

.modal /deep/ .ant-modal-body {
  background-color: rgb(240, 240, 240);
  padding: 12px;
}

.modal /deep/.ant-modal-content {
  box-shadow: 0 0px 12px rgb(0, 0, 0, 0.4);
}

.modal /deep/ .ant-modal-footer {
  background-color: rgb(240, 240, 240);
  border-top: none;
  padding: 0 16px 10px 16px;
}

::v-deep .ant-modal-wrap {
  z-index: 9999;
}
.submit-button {
  position: absolute;
  top: calc(100% - 40px);
  left: 49px;
  width: 270px;
  background-color: white;
  height: 40px;
  border-top: 1px solid rgb(225, 225, 225);
}
.button {
  position: absolute;
  right: 20px;
  top: 3px;
}
.side-mask {
  position: absolute;
  height: calc(100% + 40px);
  width: 100%;
  padding: 0 10px 10px 10px;
  background-color: rgba(245, 245, 245, 0.5);
  z-index: 999;
}
.title {
  height: 23px;
  display: flex;
}

.modal /deep/.arrow_box {
  animation: glow 100ms ease-out 3 backwards;
}

@keyframes glow {
  0% {
    box-shadow: 0 0 5px rgba(100, 100, 100, 0.2),
      inset 0 0 5px rgba(100, 100, 100, 0.1);
  }

  100% {
    box-shadow: 0 0 20px rgba(100, 100, 100, 0.6),
      inset 0 0 10px rgba(100, 100, 100, 0.4);
  }
}

#editor-design .ant-layout-content {
  color: #fff;
  line-height: normal;
  padding: 0px 0px 0px 0px;
  min-height: 500px;
  height: 100%;
}

#editor-design .ant-layout-header {
  height: auto;
  padding: 0px;
  line-height: normal;
  background-color: #f4f4f4;
}

#editor-design .ant-layout-footer {
  height: 20px;
  padding: 0px;
  line-height: 20px;
}

#editor-design .ant-layout-sider {
  background-color: #ececec;
}
</style>
