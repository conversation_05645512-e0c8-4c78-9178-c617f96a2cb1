<template>
  <div
    ref="rightClickMenu"
    id="rightClickMenuContainer"
    class="editorRightMenu"
    :style="style"
    @contextmenu.prevent
  >
    <a-menu
      style="width: 100%; text-align: left"
      mode="vertical"
      @click="handleClick"
      class="right-menu-a-menu"
    >
      <template v-for="item in data">
        <!-- 如果没有children 没有子菜单 -->
        <a-menu-item
          v-if="!item.children"
          :key="item.key"
          :disabled="item.isDisabled"
          class="right-menu-a-menu-item"
        >
          <div class="right-menu-content">
            <div>
              <icon-common :icon="item.icon"></icon-common>
              {{ item.value }}
            </div>
            <div class="short-cut-key">{{ item.shortCutKey }}</div>
          </div>
        </a-menu-item>
        <!-- 有children 就是有子菜单 -->
        <a-sub-menu
          v-if="item.children"
          class="xeditor_menu_index"
          :key="item.key + `x`"
          ref="subMenuContainer"
        >
          <span slot="title">
            <icon-common :icon="item.icon"></icon-common>
            {{ item.value }}
          </span>
          <a-menu-item
            class="menu-editor-item"
            v-for="c in item.children"
            :key="c.key"
            :disabled="c.isDisabled"
          >
            <icon-common :icon="c.icon"></icon-common> {{ c.value }}
          </a-menu-item>
        </a-sub-menu>
        <a-divider v-if="item.line" :key="item.key + `line`"></a-divider>
      </template>
    </a-menu>
  </div>
</template>

<script>
import "../assets/icon/iconfont";
import iconCommon from "./common/iconCommon.vue";
export default {
  props: ["data", "rightMenuEvent", "insertFormula", "config", "editorId"],
  components: {
    iconCommon,
  },
  data() {
    return {
      isShowModal: false,
      isShowSplitCellModal: false,
      isShowLineModal: false,
      isShowSecondaryMenu: false,
      testShowValue: "",
      secondaryStyle: {
        position: "absolute",
        top: 0,
        left: "100%",
        width: "100px",
        height: "100px",
        backgroundColor: "#eee",
      },
      style: {
        position: "fixed",
        left: 0,
        top: 0,
      },
    };
  },
  methods: {
    handleClick(e) {
      const key = e.key;
      const { editor } = this.editor;
      if (key.startsWith("custom")) {
        // 如果点中的是用户自定义事件
        for (const item of this.data) {
          if (item.key === key) {
            this.$emit("cancelRightMenu");
            if (item.handler) {
              item.handler(editor);
              editor.focus();
            }
            return;
          }
        }
      }

      this.$emit("handleRightClickMenuSelected", key);
    },
    showSecondaryMenu(e, item) {
      this.secondaryStyle.top = e.target.offsetTop + "px"; // 使用layer 鼠标从上往下进入和从下往上进入位置不同
      this.testShowValue = item.value;
      this.isShowSecondaryMenu = true;
    },
    closeSecondaryMenu() {
      this.isShowSecondaryMenu = false;
    },
  },
};
</script>

<style>
.ant-menu-item-selected.menu-editor-item {
  color: red;
}
.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected.menu-editor-item {
  background: none;
  color: rgba(0, 0, 0, 0.65);
}
.ant-menu:not(.ant-menu-horizontal)
  .ant-menu-item-selected.menu-editor-item:hover {
  background: #eee;
}
.ant-menu-vertical .ant-menu-item:not(:last-child).menu-editor-item,
.ant-menu-vertical-left .ant-menu-item:not(:last-child).menu-editor-item,
.ant-menu-vertical-right .ant-menu-item:not(:last-child).menu-editor-item,
.ant-menu-inline .ant-menu-item:not(:last-child).menu-editor-item {
  margin: 0;
}
.ant-menu-submenu-popup {
  z-index: 99999;
}
/*二级菜单层级调整*/
.ant-menu-submenu {
  z-index: 99999 !important;
}
</style>

<style lang="less" scoped>
.editorRightMenu {
  border: 1px solid #ccc;
  background-color: #ccc;
  z-index: 99999;
  overflow-y: auto;
  /deep/ .right-menu-a-menu.ant-menu {
    background-color: #fff;
    color: rgba(0, 0, 0, 0.65);
  }
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 3px;
  }
  /deep/ .ant-menu-item-selected {
    color: rgba(0, 0, 0, 0.65);
    background: none;
  }
  /deep/ .right-menu-a-menu-item.ant-menu-item {
    padding: 0 12px;
  }
  /deep/
    .ant-menu-vertical
    .right-menu-a-menu-item.ant-menu-item:not(:last-child) {
    margin-bottom: 0;
  }
  /deep/ .ant-menu-vertical > .right-menu-a-menu-item.ant-menu-item {
    height: 30px;
    line-height: 30px;
    margin: 0;
    padding: 0 12px;
    color: rgba(0, 0, 0, 0.65);
  }
  /deep/ .right-menu-a-menu-item.ant-menu-item-active {
    background-color: #eee;
    color: #000;
  }
  /deep/ .ant-divider-horizontal {
    display: block;
    clear: both;
    width: 100%;
    min-width: 100%;
    height: 1px;
    margin: 0;
  }
  /deep/ .ant-menu-submenu-title {
    padding: 0 12px;
    height: 30px;
    line-height: 30px;
    margin: 0 0 4px 0;
  }
  /deep/ .ant-menu-submenu-selected {
    color: rgba(0, 0, 0, 0.65);
    background: none;
  }
  /deep/ .right-menu-a-menu-item.ant-menu-item:hover,
  /deep/ .right-menu-a-menu-item.ant-menu-item-active,
  /deep/ .ant-menu:not(.ant-menu-inline) .ant-menu-submenu-open,
  /deep/ .ant-menu-submenu-active,
  /deep/ .ant-menu-submenu-title:hover {
    color: rgba(0, 0, 0, 0.65);
    background-color: #eee;
  }
  /deep/
    .ant-menu-submenu-vertical
    > .ant-menu-submenu-title:hover
    .ant-menu-submenu-arrow::after,
  /deep/
    .ant-menu-submenu-vertical-left
    > .ant-menu-submenu-title:hover
    .ant-menu-submenu-arrow::after,
  /deep/
    .ant-menu-submenu-vertical-right
    > .ant-menu-submenu-title:hover
    .ant-menu-submenu-arrow::after,
  /deep/
    .ant-menu-submenu-inline
    > .ant-menu-submenu-title:hover
    .ant-menu-submenu-arrow::after,
  /deep/
    .ant-menu-submenu-vertical
    > .ant-menu-submenu-title:hover
    .ant-menu-submenu-arrow::before,
  /deep/
    .ant-menu-submenu-vertical-left
    > .ant-menu-submenu-title:hover
    .ant-menu-submenu-arrow::before,
  /deep/
    .ant-menu-submenu-vertical-right
    > .ant-menu-submenu-title:hover
    .ant-menu-submenu-arrow::before,
  /deep/
    .ant-menu-submenu-inline
    > .ant-menu-submenu-title:hover
    .ant-menu-submenu-arrow::before {
    background: rgba(0, 0, 0, 0.65);
  }
  .short-cut-key {
    color: #ccc;
    padding-left: 12px;
  }
  .click-button {
    display: block;
    width: 100%;
  }
  .right-menu-content {
    display: flex;
    justify-content: space-between;
    flex: auto;
  }

  section {
    padding: 0 10px 0 2px;
    text-align: left;
    line-height: 30px;
    color: #000;
    margin: 1px;
    i {
      display: inline-block;
      padding: 0 6px;
    }
  }
}

.menu-editor-item {
  width: 100%;
  height: 30px;
  line-height: 30px;
  padding: 0 0 0 12px;
}

.menu-editor-item:hover {
  background-color: #eee;
  color: #000;
}
/deep/ .ant-menu-submenu-popup {
  position: fixed !important;
  top: 0;
  left: 0;
}
</style>
