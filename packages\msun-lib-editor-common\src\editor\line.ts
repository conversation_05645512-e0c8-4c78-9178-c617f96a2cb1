import { keepDecimal } from "./Utils";
import Cell from "./Cell";
import Editor from "./Editor";
import Paragraph from "./Paragraph";
import Font from "./Font";
export default class Line {
  width: number = 0;
  cell: Cell;
  left: number = 0;
  top: number = 0;
  height: number;
  line_height: number = 1;
  value: string = "";
  type: string = "line"; // 表格该元素是水平线
  form: string = "solid";
  color: string = "#000";
  field_id: string | null = null;
  field_position: string = "normal";
  font: Font = new Font({ family: "", height: 0 });
  // transparent:number=1;
  // draw_width:number=608
  constructor (cell: Cell, height: number, lineHeight?: number, color?: string, form?: string) {
    this.cell = cell;
    this.form = form || "solid";
    if (this.cell.parent) {
      this.width = this.cell.width - cell.editor.config.table_padding_horizontal * 2;
    } else {
      this.width = this.cell.width - cell.editor.config.page_padding_left - cell.editor.config.page_padding_right;
    }

    this.height = height;

    if (lineHeight) {
      this.line_height = lineHeight;
    }
    if (color) {
      this.color = color;
    }
  }

  static insert (editor: Editor, line_hight: number, color: string, focus_para?: Paragraph, form?: string) {
    // 分组如果锁定 则不能编辑
    if (!editor.operableOrNot(["cell", "group"])) return false;
    if (!editor.selection.isCollapsed) {
      editor.delete_backward();
    }
    focus_para = focus_para || editor.selection.getFocusParagraph();
    const focus_row = editor.selection.getFocusRow();
    const focus_field = editor.selection.getFocusField();
    // 1、如果是文本域且文本域为只读时停止或者不是文本域中且为表单模式时停止
    if ((focus_field && focus_field.isReadonly) ||
      (!focus_field && (editor.view_mode === "form" && !editor.adminMode))) {
      return false;
    }
    const para_path = editor.selection.para_focus;
    form = form || "solid";
    focus_para.insertLine(editor, line_hight, color, para_path[para_path.length - 1], focus_field, form);
    editor.selection.stepForward(1, focus_row);
    editor.update();
    editor.render();
    return true;
  }

  static attrJudgeUndefinedAssign (newModel: Line, raw: any) {
    if (raw.field_id !== undefined) newModel.field_id = raw.field_id;
    if (raw.line_height !== undefined) newModel.line_height = raw.line_height;
    if (raw.color !== undefined) newModel.color = raw.color;
  }

  get right (): number {
    return keepDecimal(this.left + this.width);
  }

  get bottom (): number {
    return keepDecimal(this.top + this.height);
  }

  center (): number {
    return this.left + this.width * 0.5;
  }

  contain_vertical (y: number) {
    return this.top <= y && y <= this.bottom;
  }

  contain_horizontal (x: number) {
    return this.left <= x && x <= this.right;
  }

  contain (x: number, y: number) {
    return this.contain_horizontal(x) && this.contain_vertical(y);
  }

  copy (): Line {
    const line = new Line(this.cell, this.height, this.line_height, this.color);
    line.field_id = this.field_id;
    line.field_position = this.field_position;
    return line;
  }
}
