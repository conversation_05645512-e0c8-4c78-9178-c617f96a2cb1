import Editor from "../../msun-lib-editor-common/src/editor/Editor";
import { versionDiff } from "./Util";

export function recordVersionInfo (editor: Editor) {
  const version = editor.config.version;
  if (!editor.document_meta.versionList) {
    editor.document_meta.versionList = [];
    editor.document_meta.versionList.push({
      version,
      time: Date.now()
    });
  } else {
    const lastVersion = editor.document_meta.versionList.pop();
    if (!lastVersion) {
      editor.document_meta.versionList.push({
        version,
        time: Date.now()
      });
    } else {
      if (lastVersion.version === version) {
        editor.document_meta.versionList.push(lastVersion);
      } else {
        editor.document_meta.versionList.push(lastVersion, {
          version,
          time: Date.now()
        });
      }
    }
  }
}

// 根据编辑器版本号处理一些功能 在版本回滚后的兼容问题
export function handleCompatibilityProblemByVersion (editor: Editor) {
  // TODO 整合到相应的方法中，此处的作用是兼容性配置处理，结合recordVersionInfo，以后兼容性配置都尽量与版本关联判断
  // 拿到大版本号和中间的版本号，以现在（2.17）的版本号为判断标准，之后的版本都不保留1像素
  const version = editor.document_meta.versionList[0].version;
  if (version) {
    const transitionVersion = versionDiff(version, "2.0.0") < 0 && versionDiff(version, "1.48.0") >= 0;
    if (versionDiff(version, "2.19.14") >= 0 || transitionVersion) {
      // 打印时是否保留文本域一像素宽度
      editor.document_meta.fieldSymbolWidth = 0;
    }
    if (editor.config.keepSymbolWidthWhenPrint && (versionDiff(version, "2.21.0") >= 0 || transitionVersion)) {
      // 如果开启打印时保留文本域边框空白区域配置并且文档首个版本号大于 2.21.0时设置文档扩展属性
      editor.document_meta.keepSymbolWidthWhenPrint = true;
    }
    if (versionDiff(version, "2.21.0") >= 0 || transitionVersion) {
      if (editor.document_meta.keepSymbolWidthWhenPrint === undefined) {
        if (editor.config.keepSymbolWidthWhenPrint) {
          editor.document_meta.keepSymbolWidthWhenPrint = true;
        }
        if (!editor.config.keepSymbolWidthWhenPrint) {
          editor.document_meta.keepSymbolWidthWhenPrint = false;
        }
      }
    }
    // if (editor.config.keepSymbolWidthWhenPrint && (versionDiff(version, "2.21.0") >= 0 || versionis48Or49)) {
    //   // 如果开启打印时保留文本域边框空白区域配置并且文档首个版本号大于 2.21.0时设置文档扩展属性
    //   editor.document_meta.keepSymbolWidthWhenPrint = true;
    // }
    if (versionDiff(version, "2.21.0") >= 0 || transitionVersion) {
      // 从 36 迭代 21 版本开始默认启用新版字符对齐逻辑
      editor.document_meta.newDocuAlign = true;
    }
    if (versionDiff(version, "2.27.0") >= 0 || transitionVersion) {
      // 从 36 迭代 21 版本开始默认启用新版字符对齐逻辑
      editor.document_meta.useNewToggleSymbol = true;
    }
    if (versionDiff(version, "10.0.0") >= 0) {
      // 从 48 迭代开始修改 表格 单元格 居中对齐方式 row_size 计算 包含 padding_top 和 padding_bottom
      editor.document_meta.handleTableRowSize = true;
    }
    if (version.startsWith("10.")) {
      if (versionDiff(version, "10.4.0") > 0) {
        // 从 52 迭代开始，文本域边框字符不进行字符排版逻辑
        editor.document_meta.fieldSymbolEndNoNewLine = true;
      }
      if (versionDiff(version, "10.4.15") <= 0) {
        editor.config.contentBorder = false;
      }
    } else {
      if (versionDiff(version, "1.51.0") > 0) {
        // 从 52 迭代开始，文本域边框字符不进行字符排版逻辑
        editor.document_meta.fieldSymbolEndNoNewLine = true;
      }
      if (versionDiff(version, "1.52.18") <= 0) {
        editor.config.contentBorder = false;
      }
    }
  }
}

export const BASE_CELL_NODE = {
  pos: [0, 0],
  colspan: 1,
  rowspan: 1,
  children: [],
  is_show_slash_up: false,
  is_show_slash_down: false
};
