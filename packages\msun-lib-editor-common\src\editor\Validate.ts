const valid = {
  /**
     * 校验规则
     * @param {校验参数} para
     */
  required: (para: any) => {
    // 非空
    return para.value !== "";
  },
  date_valid: (para: any) => {
    // 日期
    const date_rex =
      /^((([0-9]{2})(0[48]|[2468][048]|[13579][26]))|((0[48]|[2468][048]|[13579][26])00)-02-29)|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0?[13578]|1[02])[-/](0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)[-/](0?[1-9]|[12][0-9]|30))|(0?2[-/](0?[1-9]|[1][0-9]|2[0-9])))$/;
    const date_time_rex = /^((([0-9]{2})(0[48]|[2468][048]|[13579][26]))|((0[48]|[2468][048]|[13579][26])00)-02-29)|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0?[13578]|1[02])[-/](0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)[-/](0?[1-9]|[12][0-9]|30))|(0?2[-/](0?[1-9]|[1][0-9]|2[0-9])))\s+([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
    const date_time_nosecond_rex =
      /^((([0-9]{2})(0[48]|[2468][048]|[13579][26]))|((0[48]|[2468][048]|[13579][26])00)-02-29)|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})-(((0?[13578]|1[02])[-/](0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)[-/](0?[1-9]|[12][0-9]|30))|(0?2[-/](0?[1-9]|[1][0-9]|2[0-9])))\s+([01][0-9]|2[0-3]):[0-5][0-9]$/;
    const date_zh_rex = /^((([0-9]{2})(0[48]|[2468][048]|[13579][26]))|((0[48]|[2468][048]|[13579][26])00)-02-29)|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})年(((0?[13578]|1[02])月(0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)月(0?[1-9]|[12][0-9]|30))|(0?2月(0?[1-9]|[1][0-9]|2[0-8])))日$/;
    const date_HM_zh_rex = /^((([0-9]{2})(0[48]|[2468][048]|[13579][26]))|((0[48]|[2468][048]|[13579][26])00)-02-29)|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})年(((0?[13578]|1[02])月(0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)月(0?[1-9]|[12][0-9]|30))|(0?2月(0?[1-9]|[1][0-9]|2[0-8])))日\s+([01][0-9]|2[0-3])时$/;
    const date_time_H_zh_rex =
      /^((([0-9]{2})(0[48]|[2468][048]|[13579][26]))|((0[48]|[2468][048]|[13579][26])00)-02-29)|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})年(((0?[13578]|1[02])月(0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)月(0?[1-9]|[12][0-9]|30))|(0?2月(0?[1-9]|[1][0-9]|2[0-8])))日\s+([01][0-9]|2[0-3])时+([0-5][0-9])分$/;
    const date_time_MD_rex = /^(((0?[13578]|1[02])[-/](0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)[-/](0?[1-9]|[12][0-9]|30))|(0?2[-/](0?[1-9]|[1][0-9]|2[0-9])))$/;
    const date_time_MD_zh_rex = /^(((0?[13578]|1[02])月(0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)月(0?[1-9]|[12][0-9]|30))|(0?2月(0?[1-9]|[1][0-9]|2[0-8])))日$/;
    const date_HM_zh = /^([01][0-9]|2[0-3])时+([0-5][0-9])分$/;
    const date_HM = /^([01][0-9]|2[0-3]):[0-5][0-9]$/;
    const date_time_H_M_S_rex =
      /^((([0-9]{2})(0[48]|[2468][048]|[13579][26]))|((0[48]|[2468][048]|[13579][26])00)-02-29)|([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})年(((0?[13578]|1[02])月(0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)月(0?[1-9]|[12][0-9]|30))|(0?2月(0?[1-9]|[1][0-9]|2[0-8])))日\s+([01][0-9]|2[0-3])时+([0-5][0-9])分+([0-5][0-9])秒$/;
    const date_M_D_H_M =
      /^(((0?[13578]|1[02])[-/](0?[1-9]|[12][0-9]|3[01]))|((0?[469]|11)[-/](0?[1-9]|[12][0-9]|30))|(0?2[-/](0?[1-9]|[1][0-9]|2[0-9])))\s+([01][0-9]|2[0-3]):[0-5][0-9]$/;
    return (
      // 只有日期 YYYY-MM-DD
      date_rex.test(para.value) ||
      // 日期加时间 YYYY-MM-DD HH:mm:ss
      date_time_rex.test(para.value) ||
      // 日期加时间没有秒 YYYY-MM-DD HH:mm
      date_time_nosecond_rex.test(para.value) ||
      // 年月日版日期 YYYY年MM月DD日
      date_zh_rex.test(para.value) ||
      // 年月日版时间日期 YYYY年MM月DD日 HH时
      date_HM_zh_rex.test(para.value) ||
      // 年月日版时间日期时间 YYYY年MM月DD日 HH时mm分
      date_time_H_zh_rex.test(para.value) ||
      // MM-DD
      date_time_MD_rex.test(para.value) ||
      // MM月DD日
      date_time_MD_zh_rex.test(para.value) ||
      // HH：mm
      date_HM.test(para.value) ||
      // HH时mm分
      date_HM_zh.test(para.value) ||
      // 年月日版时间日期时间 YYYY年MM月DD日 HH时mm分ss秒
      date_time_H_M_S_rex.test(para.value) ||
      // MM-DD HH:mm
      date_M_D_H_M.test(para.value)
    );
  },
  number_valid: (para: any) => {
    if (para.value === "") {
      return true;
    }
    const valueArr = para.value.split("");
    const rule = /^0|1|2|3|4|5|6|7|8|9|\.|-$/;
    for (const num of valueArr) {
      if (!rule.test(num)) {
        return false;
      }
    }
    const value = parseFloat(para.value);
    // 数字
    return (
      ((para.min_num === null || para.min_num === undefined) || para.min_num <= value) &&
      ((para.max_num === null || para.max_num === undefined) || value <= para.max_num)
    );
  },
  string_valid: (para: any) => {
    // 字符串
    return (para.value.length <= para.max_length || !para.max_length) && (para.value.length >= para.min_length || !para.min_length);
  },
  idcard_valid: (para: any) => {
    // 获取输入的身份证
    const card = para.value;
    // 加权因子
    const weight_factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    // 校验码
    const check_code = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
    const code = card + "";
    let last: string = "";
    // 开始进行验证
    try {
      // 判空
      if (card !== null) {
        // 最后一个
        last = card[17];
      }
    } catch (error) {
      console.log("error :>> ", error);
      return false;
    }
    const seventeen = code.substring(0, 17);
    // 判断最后一位校验码是否正确
    const arr: any[] = seventeen.split("");
    // 长度
    const len = arr.length;
    let num = 0;
    // 遍历获取
    for (let i = 0; i < len; i++) {
      num = num + arr[i] * weight_factor[i];
    }

    // 获取余数
    const resisue = num % 11;
    const last_no = check_code[resisue];
    // 身份证验证
    const idcard_patter =
      /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/;

    // 判断格式是否正确
    const format = idcard_patter.test(card);
    // 返回数据
    return last === last_no && format;
  },
  phone_valid: (para: any) => {
    // 电话号码
    const fixed_phone = /^(\d{3,4}-)?\d{7,8}$/;
    const mobile_phone =
      /^((\+86)|(86))?(13[0-9]|14[5|7]|15[0|1|2|3|4|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\d{8}$/;
    if (para.type === "mobile_phone" || !para.type) {
      // 手机号码
      return mobile_phone.test(para.value);
    } else {
      //   固定电话
      return fixed_phone.test(para.value);
    }
  },
  regex_valid: (para: any) => {
    const regex = new RegExp(para.regex);
    return regex.test(para.value);
  }
};
export default valid;
