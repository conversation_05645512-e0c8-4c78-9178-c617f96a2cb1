<!-- eslint-disable standard/no-callback-literal -->
<template>
  <div v-show="show" id="chatDik" class="chat-dik">
    <div class="chat-did">
      <div class="title-did-1" />
      <div class="title-did-2">
        <div class="title-did2-back" />
        <div class="close-icon" @click="show = false">
          <my-icon type="icon-icon-cuowu" />
        </div>
      </div>
      <div class="title-content">
        <div class="title-left-button" title="新建对话" @click="reStart">
          <my-icon type="icon-icon_tianjia" />
        </div>
        <robot :size="28" /> MsunGPT对话
      </div>

      <!-- 对话列表 -->
      <div class="chat-content">
        <div ref="msgContent" class="chat-content-did">
          <div
            v-for="(mess, i) in showHistory"
            :key="i"
            class="chat-content-item"
          >
            <div v-if="mess.type === 'time'" class="chat-content-time">
              <span>{{ mess.data }}</span>
            </div>
            <div
              v-else-if="mess.type === 'answer'"
              class="right-content chat-item my-chat-item"
            >
              <span v-html="mess.data" />
            </div>
            <!-- 应该是只有gpt问的问题才有可能有这些选项 -->
            <div
              v-else-if="typeof mess.data === 'string'"
              class="left-content chat-item my-chat-item"
              v-html="mess.data"
            />
            <div v-else class="left-content chat-item my-chat-item">
              <template v-for="(msData, ind) in mess.data">
                <div
                  v-if="msData.contentType === 'text'"
                  :key="ind + 500"
                  v-html="msData.data"
                ></div>
                <div
                  v-if="mess.hasProgram"
                  class="chat-action-buttons"
                  :key="ind + 501"
                >
                  <button
                    class="action-button edit-button"
                    @click="restoreExecuteAfter(mess)"
                  >
                    恢复到本次执行之后
                  </button>
                  <button
                    class="action-button edit-button"
                    @click="restoreExecuteBefore(mess)"
                  >
                    恢复到本次执行之前
                  </button>
                </div>
              </template>
            </div>
            <div class="clear" />
          </div>
        </div>
        <div class="chat-input-dik">
          <textarea
            ref="chatInput"
            v-model="msg"
            class="chat-input"
            :disabled="pending || isRecording"
            :placeholder="
              pending
                ? '思考中...'
                : isRecording
                ? '请说出您的需求'
                : '输入内容，Enter发送'
            "
            @keydown.enter="sendMsg(null)"
          />
          <div class="chat-button" @click="sendMsg(null)">
            <img src="./assets/img/send.png" class="chat-img" />
            <div v-show="pending" class="chat-loading">
              <my-icon type="icon-icon_redo" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Robot from "./robot.vue";
import Record from "./utils/ASR/main";

export default {
  name: "GptChat",
  components: {
    Robot,
  },
  props: {
    rawDataList: {
      type: Array,
      default() {
        return [];
      },
    },
    visible: {
      type: Boolean,
      default: false,
    },
    historyMsg: {
      type: Array,
      default() {
        return [];
      },
    },
    pending: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      show: false,
      msg: "",
      showHistory: [],
      isRecording: false, // 录音模式
    };
  },
  computed: {},
  watch: {
    visible(val) {
      this.show = val;
    },
    show(val) {
      this.$emit("update:visible", val);
      setTimeout(() => {
        this.scrollToEnd();
      }, 50);
    },
    historyMsg: {
      handler(val) {
        const tmp = [];
        let tmpTime = "";
        val.forEach((item, index) => {
          if (!tmpTime) {
            tmpTime = item.dataTime;
            tmp.push({
              type: "time",
              data: tmpTime,
            });
          } else {
            const start = new Date(tmpTime);
            const end = new Date(item.dataTime);
            if (end - start > 1000 * 60 * 10) {
              tmp.push({
                type: "time",
                data: item.dataTime,
              });
              tmpTime = item.dataTime;
            }
          }
          item.question &&
            tmp.push({
              type: "question",
              data: item.question,
              hasProgram: item.hasProgram,
              indexInRawDataList: item.indexInRawDataList,
              isEnd: index === val.length - 1, // 最后一个问题可以编辑
            });
          item.answer &&
            tmp.push({
              type: "answer",
              data: item.answer,
              hasProgram: item.hasProgram,
              indexInRawDataList: item.indexInRawDataList,
              isEnd: index === val.length - 2,
            });
        });
        this.showHistory = tmp;
        this.scrollToEnd();
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {
    Record.init(
      () => {
        this.msg = "";
      },
      (res) => {
        this.msg = res;
      }
    );
    this.initKeyEvent();
  },
  methods: {
    restoreExecuteAfter(data) {
      const index = data.indexInRawDataList;
      const editor = this.instance.editor;
      editor.history.clear(); // 清除历史记录 避免造成混乱
      const rawData = this.rawDataList[index === -1 ? index + 2 : index + 1];
      if (rawData) {
        editor.reInitRawByConfig(rawData);
        editor.refreshDocument();
      }
    },
    restoreExecuteBefore(data) {
      const editor = this.instance.editor;
      editor.history.clear();
      const index = data.indexInRawDataList;
      const rawData = this.rawDataList[index === -1 ? index + 1 : index];
      if (rawData) {
        editor.reInitRawByConfig(rawData);
        editor.refreshDocument();
      }
    },
    initKeyEvent() {
      const appDom = document.getElementById("msun-editor-gpt");
      appDom.addEventListener("keydown", (ev) => {
        // esc 关闭对话框
        switch (ev.key) {
          case "Escape": {
            this.show = false;
            break;
          }
          case "F2": {
            ev.preventDefault();
            this.show = true;
            break;
          }
          case "F3": {
            ev.preventDefault();
            if (!this.show || this.isRecording) return;
            this.isRecording = true;
            // 先连接服务器
            Record.connect();
            Record.record();
            break;
          }
        }
      });
      appDom.addEventListener("keyup", (ev) => {
        // esc 关闭对话框
        switch (ev.key) {
          case "F3": {
            ev.preventDefault();
            if (!this.show || !this.isRecording) return;
            this.sendMsg();
            this.isRecording = false;
            Record.stop();
            break;
          }
        }
      });
    },
    reStart() {
      // 重新开始对话
      this.$emit("onRestart");
    },
    sendMsg(data = null) {
      const sendMsg = { msg: this.msg };
      const param = data
        ? Object.assign(sendMsg, data)
        : Object.assign(sendMsg, this.getCurrentData());
      this.$emit("sendMsg", param);
      this.msg = "";
      this.scrollToEnd();
      if (this.isRecording) {
        this.isRecording = false;
        Record.stop();
      }
    },
    dateChangeMsg(date, name) {},
    resendMsg(index) {
      // 重新发送
      this.$emit("resendMsg");
    },
    scrollToEnd() {
      // 对话框滚动到底部
      const el = this.$refs.msgContent;
      el &&
        setTimeout(() => {
          el.scrollTo({
            top: el.scrollHeight, // behavior: "smooth", 去掉这个 否则给长文的时候 就不能实时滚动了
          });
        }, 300);
    },
    startRecord() {
      // 开始录音
      if (!this.isRecording) {
        this.isRecording = true;
        // 先连接服务器
        Record.connect();
        Record.record();
      } else {
        this.sendMsg();
        this.isRecording = false;
        Record.stop();
      }
    },
    fieldChangeMsg(index, [newData]) {
      // this.showHistory[index] = newData
      this.msg = "是"; //msg.join('，')
      this.sendMsg(newData);
    },
    conditionChangeMsg(index, [newContidon]) {
      // this.showHistory[index] = newContidon
      this.msg = "是"; //msg.join('，')
      this.sendMsg(newContidon);
    },
    getCurrentData() {
      // 没有确认直接发送消息， 获取数据
      const index = this.showHistory.length - 1;
      const editCom = this.$refs["editData" + index];
      if (editCom && editCom[0]) {
        return editCom[0].getCurrentData();
      } else {
        return null;
      }
    },
  },
};
</script>

<style lang="less" scoped>
.chat-dik {
  position: fixed;
  right: 15px;
  bottom: 15px;
  z-index: 999;
  width: 360px;
  height: calc(100% - 120px);
  border-radius: 20px;
  box-shadow: 0 0 15px 10px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  background: #584cd6;
  padding: 60px 0 0;
  box-sizing: border-box;
  user-select: text;
  .chat-did {
    width: 100%;
    height: 100%;
    position: relative;
    border-top-right-radius: 14px;
    background: #fff;
    .title-did-1 {
      position: absolute;
      top: -45px;
      left: 0;
      width: calc(100% - 40px);
      height: 30px;
      border-top-right-radius: 30px;
      border-top-left-radius: 30px;
      background: #fff;
    }
    .title-did-2 {
      position: absolute;
      top: -15px;
      left: 0;
      width: 100%;
      height: 15px;
      background: #fff;
      .title-did2-back {
        width: 40px;
        height: 100%;
        float: right;
        background: #584cd6;
        border-bottom-left-radius: 15px;
      }
      .close-icon {
        width: 40px;
        height: 40px;
        text-align: center;
        line-height: 40px;
        color: #fff;
        cursor: pointer;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
    .title-content {
      position: absolute;
      top: -40px;
      left: 0;
      width: calc(100% - 40px);
      line-height: 34px;
      text-align: center;
      .title-left-button {
        width: 34px;
        height: 34px;
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        margin: auto;
        font-size: 16px;
        color: #584cd6;
        cursor: pointer;
        z-index: 1000;
      }
    }
    .chat-content {
      // 聊天对话
      width: 100%;
      height: 100%;
      position: relative;
      z-index: 999;
      box-sizing: border-box;
      padding: 15px 20px 70px 20px;
      .chat-content-did {
        width: 100%;
        height: 100%;
        overflow: hidden;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 4px;
          background: #f1f2f9;
        }
        &::-webkit-scrollbar-thumb {
          width: 4px;
          background: #ddd;
        }
        // 对话
        .chat-content-item {
          width: 100%;
          padding: 6px 0;
          .clear {
            clear: both;
          }
          .chat-item {
            position: relative;
            display: inline-block;
            max-width: 92%;
            border-radius: 16px;
            word-break: break-all;
          }
          .left-content {
            // background: #584cd6;
            color: #000;
            padding: 15px;
            border-top-left-radius: 0;
          }
          .right-content {
            margin-right: 5px;
            background: rgb(239, 246, 255);
            color: #000;
            padding: 14px;
            float: right;
            border: 1px solid rgb(239, 246, 255);
          }
          .chat-resend-button {
            position: absolute;
            width: 24px;
            height: 24px;
            font-size: 14px;
            cursor: pointer;
            opacity: 0.2;
            left: -26px;
            top: 0;
            bottom: 0;
            margin: auto;
            line-height: 24px;
            text-align: center;
            color: #333;
            &:hover {
              color: #584cd6;
              opacity: 1;
            }
          }
          .chat-content-time {
            text-align: center;
            font-size: 12px;
            color: #666;
            span {
              display: inline-block;
              padding: 0 5px;
              border-radius: 5px;
              background: #f1f2f9;
            }
          }
          .chat-action-buttons {
            display: flex;
            justify-content: flex-end;
            margin-top: 8px;

            .action-button {
              margin-left: 4px;
              padding: 4px 8px;
              border-radius: 12px;
              font-size: 12px;
              cursor: pointer;
              border: 1px solid #ddd;
              background: #fff;
              transition: all 0.2s ease;

              &:hover {
                background: #f5f5f5;
              }

              &.accept-button {
                background: #584cd6;
                color: #fff;
                border-color: #584cd6;

                &:hover {
                  background: #4a3fc2;
                }
              }

              &.reject-button {
                color: #666;

                &:hover {
                  color: #333;
                }
              }

              &.edit-button {
                background: #f1f2f9;

                &:hover {
                  background: #e5e7f5;
                }
              }

              &.resend-button {
                color: #584cd6;
                border-color: #584cd6;

                &:hover {
                  background: #f5f5ff;
                }
              }
            }
          }
        }
      }
      .chat-input-dik {
        width: calc(100% - 40px);
        height: auto;
        min-height: 40px;
        max-height: 200px;
        display: flex;
        position: absolute;
        bottom: 24px;
        left: 20px;
        .chat-input {
          flex: 1 1 60%;
          appearance: none;
          min-height: 40px;
          height: auto;
          max-height: 200px;
          border-radius: 20px;
          padding-left: 15px;
          background: #fff;
          border: 1px solid #ddd;
          color: #000;
          &:focus {
            outline: none;
          }
        }
        .record-button {
          width: 40px;
          height: 40px;
          cursor: pointer;
          line-height: 36px;
          margin-right: 5px;
          border-radius: 50%;
          border: 1px solid #ddd;
          font-size: 22px;
          text-align: center;
        }
        .chat-button {
          width: 40px;
          height: 40px;
          line-height: 34px;
          margin-left: 5px;
          border-radius: 50%;
          text-align: center;
          color: #666;
          background: #584cd6;
          cursor: pointer;
          position: relative;
          .chat-img {
            width: 80%;
            margin: auto;
          }
          .chat-loading {
            line-height: 38px;
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: #f1f2f9;
            border-radius: 50%;
            overflow: hidden;
            color: #584cd6;
            animation: rollAndRoll 2s linear infinite;
          }
        }
      }
    }
  }
  .button {
    width: 50px;
    height: 50px;
    position: absolute;
    right: 0;
    bottom: 0;
    text-align: center;
    line-height: 40px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fff;
    z-index: 99;
  }
  .close {
    position: absolute;
    right: 10px;
    top: 10px;
    width: 20px;
    height: 20px;
    line-height: 18px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 50;
    cursor: pointer;
    color: #fff;
    text-align: center;
    font-size: 12px;
  }
}
@keyframes rollAndRoll {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
<style lang="less">
.my-chat-item {
  table,
  img,
  li,
  pre {
    display: block;
    max-width: 100%;
    margin: 10px 0;
  }
}
</style>
