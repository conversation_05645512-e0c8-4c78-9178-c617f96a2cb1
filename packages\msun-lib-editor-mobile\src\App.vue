<!-- eslint-disable vue/valid-v-model -->
<template>
  <div ref="editor_vue" id="editor-vue-x" :style="{ position: 'static' }">
    <div :id="editorId" ref="content" class="editor-content-x"></div>
    <van-popup
      v-if="isMobile1"
      v-model="mobileDatePickerShow"
      position="bottom"
      :style="{ height: '40%' }"
    >
      <van-datetime-picker
        v-model="currentDate"
        type="datetime"
        @confirm="mobileDatePickerConfirm_"
        @cancel="mobileDatePickerCancel_"
      />
    </van-popup>

    <!-- 不能干掉 要不移动端的下垃就没了 -->
    <field-select
      ref="field_select"
      :editorId="editorId"
      :show="showFieldSelectPanel"
      @select="replaceFieldText"
      :position="fieldPanelOffset"
      :separatorGroups="separatorGroups"
    />
    <float-button
      ref="editorFloatButton"
      :show="floatBtnShow"
      :btnData="floatBtnData"
      :floatBtnPosition_="floatBtnPosition_"
      @floatBtnClick="floatBtnClick"
    ></float-button>
    <mobile-button
      :show="VL.is_mobile_button"
      :btnData="mobileBtnData"
      :mobilePosition="mobilePosition"
      @mobileBtnClick="mobileBtnClick"
    ></mobile-button>
    <!-- 移动端的悬浮球 -->
    <mobileFloatBall
      @toggleInputDOM_="toggleInputDOM_"
      :text="mobileFloatBallText"
      ref="mobileFloatBall"
    >
    </mobileFloatBall>
    <mobilePrompt
      :show="mobilePromptShow"
      :options="options"
      ref="mobilePromptRef"
    ></mobilePrompt>
  </div>
</template>
<script>
import "./global-component";
import initEditor from "msun-editor";
import fieldMixIn from "./mixin/fieldMixIn";
import editorEventMixIn from "./mixin/editorEvent";
import extMixIn from "./mixin/ext";
import config from "../package.json";
import medicalConfig from "./config";
import localConfigSet from "./mixin/localConfigSet";
import mobileFloatBall from "./components/mobile/mobileFloatBall.vue";
import mobilePrompt from "./components/mobile/mobilePrompt.vue";
import floatBtnMixIn from "./mixin/floatBtnMixIn";
import mobilePromptMixin from "./mixin/mobile/mobilePromptMixin";
import mobileEventMixin from "./mixin/mobile/mobileEventMixin";
import mobileButton from "./components/mobile/mobileButton.vue";
export default {
  name: "msunEditorMobile",
  mixins: [
    fieldMixIn,
    editorEventMixIn,
    extMixIn,
    localConfigSet,
    floatBtnMixIn,
    mobilePromptMixin,
    mobileEventMixin,
  ],
  props: ["upConfig", "parent"],
  components: {
    mobileFloatBall,
    mobilePrompt,
    mobileButton,
  },
  provide() {
    return {
      parentInstance: this,
    };
  },
  data() {
    return {
      curFieldID: "",
      showWaterMark: false,
      showCustomWaterMark: false,
      url: "",
      savedRange: null, // 保留当前选择的范围
      openFileType: "load",
      pdfLoading: false,
      insertMarkImage: false,
      focusOffset: 0,
      versionInfo: config,
      editorId: "",
      testRight: false,
      triggerFlag: false,
      showInputSelect: false,
      isShapeMode: false,
      showFieldFormula: false,
      formula: "",
      hideReplaceBtn: false,
      is_touch: true, // 节流用
      timer: null, // 节流用
      is_move: false, // 手指滑动
      start_y: 0, // 手指滑动
      distance_y: 0, // 手指滑动
      frameId: 1, // 手指滑动
      waterMark: false,
      isShowNumberSelect: false,
      inputLocation: {
        left: 0,
        top: 0,
      },
      separatorGroups: [
        { index: 0, separator: "，" },
        { index: 1, separator: "；" },
        { index: 2, separator: "、" },
        { index: 3, separator: "%" },
      ],
      modal_width: 524,
      // editor: null,
      // instance: null,
      // curClickInfo: {}, //当前点击能够获取到的信息
      print_parameter: {
        type: 2,
        application: "RawPrintPlugin",
        keyword: "initPlugin",
        content: {
          pluginName: "RawPrintPlugin",
          pluginVersion: "1.0.0.0",
          pluginRemark: "原始打印插件",
          dllFile: "plugins/RawPrintPlugin/RawPrintPlugin.dll",
        },
      },
      print_pic: {
        type: 0,
        application: "RawPrintPlugin",
        keyword: "printImage",
        content: {
          printerName: "Microsoft Print to PDF",
          pageSizeName: "A4",
          pageSizeWidth: 827,
          pageSizeHeight: 1169,
          marginLeft: 30,
          marginTop: 1,
          landscape: false, // true 横向 \ false 纵向
          imageBase64: "",
        },
      },
      editorMonitor: {},
      isMobile1: false,
      VL: {},
      reg: [],
    };
  },
  mounted() {
    // const canvasDom = document.getElementById(this.editorId);
    document.addEventListener(
      "selectionchange",
      this.HandleSelectionChange,
      false
    );
    document.addEventListener("mousedown", this.mouseDownEvent, true);
    // 随意增加个注释
    const realConfig1 = this.handleConfig();
    const testRawData = localStorage.getItem("testRawData")
      ? JSON.parse(localStorage.getItem("testRawData"))
      : this.upConfig?.rawData;
    // 合并本地系统配置后再初始化编辑器
    this.initEditorAfterHandleConfig(testRawData, realConfig1); // 该方法之后才有 editor
    this.boundTouchEvent_();
    if (this.instance && this.instance.editor.isMobileTerminal()) {
      setTimeout(() => {
        this.isMobile1 = true;
      }, 500);
    }
  },
  created() {
    const uuid = "xxxxxxxx".replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
    this.editorId = "editor_" + uuid;
  },
  beforeDestroy() {
    this.$refs.editor_vue.innerHTML = "";
    this.editor.init_canvas.parentNode.innerHTML = "";
    document.removeEventListener("mousedown", this.mouseDownEvent);
    this.initChildInstance(null);
    this.editor.destroy();
    this.editor.history.clear();
    this.editor = null;
    this.instance = null;
  },
  methods: {
    handleConfig() {
      //设置了不显示批注替换按钮就隐藏
      if (this.upConfig?.hideReplaceBtn) {
        this.hideReplaceBtn = true;
      }
      if (this.upConfig?.medicalFont) {
        medicalConfig.formula_font =
          Number(this.upConfig?.medicalFont) *
          medicalConfig.img_devicePixelRatio;
      }
      // 如果遇到正常的打印机，设置localStorage
      if (this.upConfig?.printMarginTop) {
        this.print_pic.content.marginTop = this.upConfig?.printMarginTop;
      }
      if (this.upConfig?.printMarginLeft) {
        this.print_pic.content.marginLeft = this.upConfig?.printMarginLeft;
      }
      const systemConfig = this.upConfig?.systemConfig || {
        page_size_type: "A4",
        secret: "",
      };
      // 合并本地系统配置
      this.initReadConfigInfo(systemConfig);
      const realConfig = this.deleteEmptyConfig(systemConfig);
      realConfig.version = this.versionInfo.version; // 这是 package.json 里边的 version
      if (
        !realConfig.version.startsWith("10.") &&
        this.versionInfo.realVersion
      ) {
        realConfig.version = this.versionInfo.realVersion; // 这是执行的 node build before 的版本
      }
      return realConfig;
    },
    initEditorAfterHandleConfig(rawData, config) {
      try {
        this.instance = initEditor(`#${this.editorId}`, config);
        this.editorUseLocalTest();
        if (rawData) {
          this.instance.editor.reInitRaw(rawData);
          this.instance.editor.refreshDocument();
        }
      } catch (error) {
        this.sendEditorToParent(error);
        return;
      }
      this.editor = this.instance.editor;
      if (this.editor) {
        this.VL = this.editor.internal.VL;
      }

      this.initChildInstance(this.instance);

      this.bindInstanceEvent(this.instance);

      // 监听 ctrl+v键盘事件
      // document.addEventListener("paste", (event) => {
      //   handleClipBoardData(event, this.instance);
      // });
      // 校验secret是否合法,此处secret需要先经过base64解码
      /*const decryptKey = decrypt(systemConfig.secret);
      if (decryptKey && decryptKey.startsWith("msun-editor-")) {
        const expirationTime = decryptKey
          .split("msun-editor-")[1]
          .replace(/\//g, "-");
        // 校验secret是否过期
        if (new Date().valueOf() > new Date(expirationTime).valueOf()) {
          this.sendEditorToParent({ legalSecret: true, expired: true });
          return;
        }
      } else {
        this.sendEditorToParent({ legalSecret: false, expired: true });
        return;
      }*/

      this.sendEditorToParent(); // 往父组件传递editor
    },
    HandleSelectionChange() {
      let sel = window.getSelection && window.getSelection();
      if (sel && sel.rangeCount) {
        this.savedRange = sel.getRangeAt(0);
      }
    },
    deleteEmptyConfig(config) {
      for (const key in config) {
        if (
          config[key] === null ||
          config[key] === undefined ||
          config[key] === ""
        ) {
          delete config[key];
        }
      }
      return config;
    },
    editorUseLocalTest() {
      if (!this.instance) {
        return;
      }
      if (!this.instance.localTest) {
        this.instance.localTest = {};
      }
      this.instance.localTest.useLocal =
        !!localStorage.getItem("EditorLocalTest");
      this.instance.localTest.useNew = !!localStorage.getItem("EditorUseNew");
      if (localStorage.getItem("EditorTransUse") === "0") {
        this.instance.localTest.transUse = false;
      }
    },
    //初始化子组件用到的实例对象
    initChildInstance(instance) {
      this.$refs.field_select.editor = instance;
      this.$refs.mobileFloatBall.editor = instance;
    },

    /**
     * 判断是否在dom内
     *
     * @param {*} dom
     */
    outDom(dom, currentDom) {
      return [].every.call(dom, (item) => !item.contains(currentDom));
    },
    getVersionInfo() {
      return this.versionInfo.version;
    },
    //传入改变instance属性的方法
    mergeAttributeToInstance(data) {
      for (const key in data) {
        this.instance[key] = data[key];
      }
    },
    sendEditorToParent(errMsg) {
      if (errMsg) {
        this.$emit("init", null, errMsg);
      } else {
        const editor = {
          ...this.instance,
          id: this.editorId,
          insertField: this.insertField,
          searchAndReplace: this.searchAndReplace,
          insertFormula: this.insertFormula,
          insertTable: this.showInsertTblModal,
          showTableAttr: this.showTableAttr,
          showSplitCellModal: this.showSplitCellModal,
          convertPDF: this.convertPDF,
          insertLocalImage: this.insertLocalImage,
          showChoiceModal: this.showChoiceModal, // 插入自定义选择框
          showParaAttrModal: this.showParaAttrModal,
          getPrinterName: this.getPrinterName,
          mergeAttributeToInstance: this.mergeAttributeToInstance,
          getVersionInfo: this.getVersionInfo,
          saveTraceInfo: this.saveTraceInfo, // 保存修改痕迹
          userLogin: this.userLogin, // 用户登录
          compareTraces: this.compareTraces, //返回对比的rowData
          newVersionCompareTraces: this.newVersionCompareTraces, //返回对比的rowData
          getChangeData: this.getChangeData, // 给刘通用的获取两个数据对比的痕迹
          compareGroupTraces: this.compareGroupTraces, //返回对比的分组rawdata
          addComment: this.openCommentEditModal, //添加批注
          openCommentList: this.openCommentList, //打开批注查看模式
          closeCommentList: this.closeCommentList, //关闭批注哈看模式
          getLastModificationRecord: this.getLastModificationRecord, // 获取跟上一版本对比 修改的段落和内容
          openImagePopUpFrame: this.openImagePopUpFrame,
          printCurrentPage: this.printCurrentPage, //打印当前页
          openLineModal: this.openLineModal, //打开插入线窗口
          openPageConfigModal: this.openPageConfigModal, //页面设置弹窗
          openMedicalCalcFormula: this.openMedicalCalcFormula, // 打开医学计算公式弹窗
          jsPDFLoad: this.judgeJsPDFLoad, // 字体文件主动加载
          refreshCusCommentList: this.refreshCusCommentList,
          // openCusCommentEditModal: this.openCusCommentEditModal,
          deleteCusComment: this.deleteCusComment,
          replaceCusContent: this.replaceCusContent,
          selectCusComment: this.selectCusComment,
          submitCusComment: this.submitCusComment,
          openCusCommentEditModal: this.openCusCommentEditModal,
          showCaliperModal: this.showCaliperModal,
          showFloatBtn: this.showFloatBtn_,
          referenceCusComment: this.referenceCusComment,
          quickInputSelect: this.quickInputSelect,
          openFieldAdvanced: this.openFieldAdvanced,
          insertChioceModal: this.insertChioceModal,
          plugin: {
            diff: this.dmp,
          },
        };
        if (this.instance.localTest.useLocal) {
          // 目前用于api打印,是否有其他副作用位置，暂时先本地测试用
          this.instance = editor;
        }
        this.$emit("init", editor);
      }
    },
  },
};
</script>
<style scoped lang="less">
#editor-vue-x {
  position: relative;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .editor-content-x {
    // margin-top: 200px;
    position: absolute;
    height: 100%;
    width: 100%;
    // height: 100px;
    // width: 100px;
    // margin-top: 200px;
    overflow-y: hidden;
    overflow-x: auto;
  }
}
</style>
