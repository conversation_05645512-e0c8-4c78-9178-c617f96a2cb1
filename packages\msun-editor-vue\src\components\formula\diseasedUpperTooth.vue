<template>
  <div class="diseasedUpperTooth">
    <div class="editor_cross_sign_left"></div>
    <div class="editor_cross_sign_right"></div>
    <div class="editor_cross_sign_bottom"></div>
    <div class="editor_col_x">
      <a-input type="text" class="editor_input_top" v-model="meta.params[0]" />
    </div>
    <div class="editor_col_x">
      <div class="editor_bottom_x">
        <a-input type="text" class="editor_input_x" v-model="meta.params[1]" />
        <a-input type="text" class="editor_input_x" v-model="meta.params[2]" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "diseasedUpperTooth",
  components: {},
  data() {
    return {};
  },
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },

  methods: {},
};
</script>
<style scoped>
.diseasedUpperTooth {
  width: 100%;
  height: 100%;
  margin: 0;
}

.editor_col_x {
  text-align: center;
  width: 100%;
  height: 100px;
}
.editor_bottom_x {
  display: flex;
}
.editor_cross_sign_left {
  position: absolute;
  pointer-events: none;
  width: 245px;
  height: 100px;
  box-sizing: border-box;
  background: linear-gradient(
    22deg,
    transparent 48.5%,
    #000 49.5%,
    #000 50.5%,
    transparent 51.5%
  );
}
.editor_cross_sign_right {
  position: absolute;
  pointer-events: none;
  width: 245px;
  height: 100px;
  left: 260px;
  box-sizing: border-box;
  background: linear-gradient(
    158deg,
    transparent 48.5%,
    #000 48.5%,
    #000 50.5%,
    transparent 51.5%
  );
}
.editor_cross_sign_bottom {
  position: absolute;
  pointer-events: none;
  width: 490px;
  height: 100px;
  top: 155px;
  box-sizing: border-box;
  background: linear-gradient(
    90deg,
    transparent 49.4%,
    #000 49.4%,
    #000 50.2%,
    transparent 50.2%
  );
}
.editor_input_top {
  margin-top: 15px;
  width: 160px;
}
.editor_input_x {
  margin-top: 2px;
  margin: 20px 42.5px 0 42.5px;
}
</style>
