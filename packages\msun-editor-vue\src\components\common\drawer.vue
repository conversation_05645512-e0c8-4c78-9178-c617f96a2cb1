<template>
  <div>
    <transition name="fade">
      <div
        class="drawer-backdrop"
        v-if="visible"
        @click="
          () => {
            closeDrawer(false);
          }
        "
      ></div>
    </transition>

    <transition name="slide">
      <div class="drawer" v-if="visible">
        <header class="drawer-header">
          <h3 class="drawer-title">{{ title }}</h3>
          <button
            class="drawer-close-button"
            @click="
              () => {
                closeDrawer(false);
              }
            "
          >
            <span class="drawer-close-icon">&times;</span>
          </button>
        </header>
        <div class="drawer-content">
          <slot></slot>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: "Drawer",
  props: {
    visible: Boolean,
    title: String,
    triggerText: {
      type: String,
      default: "列表",
    },
  },
  methods: {
    closeDrawer() {
      this.$emit("closeDrawer", true);
    },
  },
};
</script>

<style scoped>
/* 抽屉按钮样式 */

/* 抽屉背景遮罩样式 */
.drawer-backdrop {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

/* 抽屉容器样式 */
.drawer {
  position: absolute;
  top: 0;
  right: 0px;
  bottom: 0;
  width: 300px;
  z-index: 1;
  background-color: #fff;
  transition: right 0.3s ease;
  overflow: auto;
}

/* 抽屉标题样式 */
.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #fff;
  border-bottom: #cccccc solid 1px;
  color: #fff;
}

/* 抽屉标题文字样式 */
.drawer-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}

/* 抽屉关闭按钮样式 */
.drawer-close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  font-size: 24px;
  color: #000;
  z-index: 1;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

/* 抽屉关闭图标样式 */
.drawer-close-icon {
  display: block;
  width: 20px;
  height: 20px;
  line-height: 20px;
  text-align: center;
}

/* 抽屉内容样式 */
.drawer-content {
  padding: 16px;
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  visibility: hidden;
}

/* 滑动动画 */
.slide-enter-active,
.slide-leave-active {
  transition: right 0.3s ease;
}

.slide-enter,
.slide-leave-to {
  right: -300px;
}
</style>
