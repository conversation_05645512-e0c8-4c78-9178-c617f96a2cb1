import { formatDateTime } from "../assets/js/utils";
const contrast_tip = {
  data() {
    return {
      contrast_content: {},
      show_contrast_tip: false,
      fieldTipStyle: {
        left: 0,
        top: 0,
      },
      contrastTimeout: {},
    };
  },
  methods: {
    /**
     * 在同一个位置停留两秒后展示修改痕迹信息
     * @param {number} x 横坐标
     * @param {number} y 纵坐标
     */
    showContrastTip(x, y) {
      clearTimeout(this.contrastTimeout);
      // 移开之后半秒让信息框消失
      setTimeout(() => {
        this.contrast_content = {};
        this.show_contrast_tip = false;
      }, 200);
      // 停留一秒后搜索信息并展示
      this.contrastTimeout = setTimeout(() => {
        // 当前位置出的元素信息
        if (!this.instance || !this.instance.editor) return;
        const curInfo = this.instance.editor.getElementByPoint(x, y);
        // 判断当前位置元素是否存在
        if (curInfo.element) {
          // 段落id
          const para_id = curInfo.paragraph.id;
          // 在表格内的时候获取单元格和表格id
          const cell_id = curInfo.cell?.id;
          const table_id = curInfo.table?.id;
          // 根据位置获取相对位置
          const { x: view_x, y: view_y } =
            this.instance.editor.getAbsoluteXYByPointer(x, y);
          // 获取当前元素在段落中的偏移
          const location = curInfo.paragraph.characters.indexOf(
            curInfo.element
          );
          let offset = location > -1 ? location : null;
          // 查询修改信息
          const traceInfo = this.getTraceInfoByPath(
            para_id,
            offset,
            cell_id,
            table_id
          );
          // 如果没有查到相应的修改痕迹，清除定时器，并且中断展示程序
          if (!traceInfo) {
            clearTimeout(this.contrastTimeout);
            return;
          }
          // 展示内容
          this.contrast_content = this.contrastResult(traceInfo, curInfo);
          // 展示信息
          this.show_contrast_tip = true;
          this.$nextTick(() => {
            if (!this.instance || !this.instance.editor) return;
            // 调整位置，让其在内容上方展示
            this.fieldTipStyle = {
              left:
                view_x -
                this.instance.editor.scrollX +
                this.$refs.content.offsetLeft,
              top:
                view_y -
                this.instance.editor.scroll_top -
                55 +
                this.$refs.content.offsetTop,
              // left: view_x + this.$refs.content.offsetLeft,
              // top: view_y + this.$refs.content.offsetTop,
            };
          });
        }
      }, 300);
    },
    /**
     * 查找修改记录
     * @param {string} para_id 当前段落id
     * @param {number} offset 当前字符偏移
     * @param {string} cell_id 当前单元格id
     * @param {string} table_id 当前表格id
     * @returns 对应的修改记录
     */
    getTraceInfoByPath(para_id, offset, cell_id, table_id) {
      if (!this.instance.editor.document_meta.traceInfo) return null;
      let traceInfo = this.instance.editor.document_meta.traceInfo;
      // console.log(traceInfo);
      // 修改痕迹
      for (let i = 0; i < traceInfo.length; i++) {
        // console.log(traceInfo);
        const trace = traceInfo[i];
        // 传递了表格id，那就代表在表格内，直接去找表格的id，其余的跳过
        if (table_id) {
          if (table_id === trace.id) {
            traceInfo = trace.cells;
            table_id = null;
            i = -1;
          }
          continue;
        }
        // 传递了cell，那就代表在单元格内，直接去找单元格的id，其余的跳过
        if (cell_id) {
          if (cell_id === trace.id) {
            traceInfo = trace.children;
            cell_id = null;
            i = -1;
          }
          continue;
        }
        // 当前字符偏移需要比对应记录的偏移大并且小于记录的偏移加上内容的长度
        if (para_id === trace.id) {
          for (let j = 0; j < trace.change.length; j++) {
            // 每个变动
            const change = trace.change[j];
            // 当次改动最大偏移量
            const max_offset =
              change[1] + this.handleImageOffset(change[2]).length;
            // 当次改动最小偏移量
            const min_offset = change[1];
            if (min_offset <= offset && offset < max_offset) {
              if (change[0] === 0) continue;
              return change;
            }
          }
        }
      }
      return null;
    },
    /**
     * 拼接信息
     * @param {object} change_info 找到的对比结果
     * @param {object} curInfo 当前点击的问题
     * @returns 返回信息
     */
    contrastResult(change_info, curInfo) {
      // 时间处理
      const date = formatDateTime(
        new Date(change_info.meta.date),
        "YYYY-MM-DD HH:mm:ss"
      );
      // 修改人
      let author = "";
      const userInfo = this.instance.editor.document_meta.userInfo;
      for (let i = 0; i < userInfo.length; i++) {
        const user = userInfo[i];
        if (user.id === change_info.meta.userId) {
          author = user.name;
        }
      }
      let bgColor;
      const ele = curInfo.element;
      if (ele) {
        const pCds = curInfo.paragraph.characters;
        if (ele.font?.temp_word_bgColor) {
          bgColor = ele.font?.temp_word_bgColor;
        } else if (ele.src) {
          bgColor = pCds[pCds.indexOf(ele) - 1]?.font?.temp_word_bgColor;
        }
        this.contrast_content.color = bgColor ? bgColor : "white";
      }
      const result = {
        option: change_info[0] === 1 ? "新增" : "删除",
        userName: author,
        date: date,
        color: bgColor,
      };
      return result;
    },
  },
};
export default contrast_tip;
