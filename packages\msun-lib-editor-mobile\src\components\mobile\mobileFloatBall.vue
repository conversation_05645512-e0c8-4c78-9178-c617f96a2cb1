<!-- eslint-disable vue/valid-v-model -->
<template>
  <div
    v-show="showModal"
    id="editor-mobile-float-ball"
    class="editor-mobile-float-ball"
  >
    {{ text }}
  </div>
</template>
<script>
export default {
  name: "mobileFloatBall",
  data() {
    return {
      distanceY: 0,
      distanceX: 0,
      touchstartX: 0,
      touchstartY: 0,
      touchstartTime: 0,
      showModal: false,
    };
  },
  props: {
    text: {
      type: String,
      default: "编辑",
    },
  },
  mounted() {
    const dom = this.$el;
    dom.addEventListener("touchstart", (ev) => {
      ev.preventDefault();
      this.distanceX = ev.touches[0].clientX - dom.offsetLeft; // 手指距离悬浮球边界的距离
      this.distanceY = ev.touches[0].clientY - dom.offsetTop;
      this.touchstartX = ev.touches[0].clientX;
      this.touchstartY = ev.touches[0].clientY;
      this.touchstartTime = Date.now();
    });
    dom.addEventListener("touchmove", (ev) => {
      dom.style.left = ev.targetTouches[0].clientX - this.distanceX + "px";
      dom.style.top = ev.targetTouches[0].clientY - this.distanceY + "px";
    });
    dom.addEventListener("touchend", (ev) => {
      const x = (ev.targetTouches[0] || ev.changedTouches[0]).clientX;
      const y = (ev.targetTouches[0] || ev.changedTouches[0]).clientY;
      if (
        Math.abs(x - this.touchstartX) < 3 &&
        Math.abs(y - this.touchstartY) < 3
      ) {
        this.$emit("toggleInputDOM_");
      }
    });
    this.$nextTick(() => {
      // 不加 nextTick 这儿会在赋值之前执行
      const editor = this.editor.editor;
      if (editor.isMobileTerminal()) {
        const canvasDom = editor.init_canvas;
        const canvasDomRect = canvasDom.getBoundingClientRect();
        dom.style.left =
          canvasDomRect.right -
          editor.config.page_padding_right * editor.viewScale -
          36 +
          "px";
        dom.style.top =
          canvasDomRect.bottom - editor.config.page_padding_bottom - 36 + "px";
        this.showModal = true;
      } else {
        this.showModal = false;
      }
    });
  },
};
</script>
<style scoped lang="less">
.editor-mobile-float-ball {
  width: 36px;
  height: 36px;
  position: fixed;
  right: 40px;
  bottom: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: rgb(65, 116, 234);
  color: #fff;
  font-size: 12px;
  -webkit-user-select: none;
  user-select: none;
}
</style>
