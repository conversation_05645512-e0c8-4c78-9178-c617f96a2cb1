import Editor from "./Editor";
import { Config } from "./Config";
export default class ServerEditor extends Editor {
  constructor (config: any = {}) {
    super(config);
    this.config.history_limit = 0;
    this.config.source = "server";
  }

  // 初始化画布宽度，防止报错
  init_canvas:any = {
    style: {
      width: 1920,
      height: 885
    }
  };

  /**
   * 重置状态，清空缓存的数据
   */
  resetStatus () {
    this.imageMap.clear();
    this.fontMap.clear();
  }

  /**
     * 重写focus
     */
  focus () {

  }

  /**
     * 重写render
     */
  render () {

  }

  updateCaret () {

  }

  /**
   * 初始化字体宽度字典
   * @param dict
   */
  loadConfigFontDict (dict:any) {
    Config.configFontJson = dict;
  }

  /**
   * 测试重新刷新数据
   */
  testReInitData () {
    const rawData = localStorage.getItem("rawData");
    if (!rawData) {
      return alert("需使用localStorage中的rawData");
    }
    this.clearDocument(true);
    setTimeout(() => {
      this.reInitRaw(JSON.parse(rawData));
      this.refreshDocument();
    }, 1000);
  }
}
