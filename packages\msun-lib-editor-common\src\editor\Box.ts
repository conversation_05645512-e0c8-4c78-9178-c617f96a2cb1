import Element from "./Element";
import EditorConfig from "./EditorConfig";
import Font from "./Font";
export default class Box extends Element {
  type: string = "box";
  height: number;
  width: number = 0.01;
  value: string = "";
  level: number = 0;
  content: any;
  name: string = "";
  field_id: string | null = null;
  field_position: string = "normal";
  config: EditorConfig = new EditorConfig();
  font: Font = new Font({ family: "", height: 0 });
  meta: any = {
    save_width: 0.00001
  }
  static attrJudgeUndefinedAssign(newModel: Box, raw: any) {
    if (raw.field_id !== undefined) newModel.field_id = raw.field_id;
  }

  constructor(content: any, height: number, name: string) {
    super(height, height);
    // TODO 47迭代移除该判断 直接使用0.01
    const width = typeof localStorage !== "undefined" && parseInt(String(localStorage.getItem("cascadeBox")));
    if (width) {
      this.width = width;
    } else {
      this.width = 0.01;
    }
    this.meta.save_width = this.width
    this.height = height;
    this.content = content;
    this.name = name;
  }

  contain_vertical(y: number) {
    return this.top <= y && y <= this.bottom;
  }

  contain_horizontal(x: number) {
    return this.left <= x && x <= this.right;
  }

  contain(x: number, y: number) {
    return this.contain_horizontal(x) && this.contain_vertical(y);
  }

  copy() {
    const box = new Box(this.content, this.height, this.name);
    box.field_id = this.field_id;
    box.field_position = this.field_position;
    return box;
  }
}
