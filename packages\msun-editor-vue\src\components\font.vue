<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="title"
    @submit="submit"
    @cancel="cancel"
  >
    <div class="font-settings_font">
      <div class="setting-top">
        <div>
          <div class="top-title">字体：</div>
          <selected
            ref="getFontType"
            :data="fontTypeList"
            :width="fontTypeWidth"
            :initData="fontfamily"
          ></selected>
        </div>
        <div>
          <div class="top-title">字形：</div>
          <selected
            ref="getFontGlyph"
            :data="fontGlyphList"
            :width="fontGlyphWidth"
            :initData="fontGlyph"
          ></selected>
        </div>
        <div>
          <div class="top-title">字号：</div>
          <selected
            ref="getFontSize"
            :data="fontSizeList"
            :width="fontSizeWidth"
            :initData="fontSize"
          ></selected>
        </div>
      </div>
      <div class="setting-middle">
        <div class="top-title">所有文字</div>
        <div class="line-top"></div>
      </div>
      <div class="setting-top">
        <div class="setting-color">
          <div class="left-title">字体颜色：</div>
          <div class="border_font">
            <div @click="handleClickNoTextColorSymbol">
              <div
                v-show="noTextColor"
                :style="{
                  width: '17px',
                  height: '17px',
                  background: 'transparent',
                  border: '1px solid rgb(236,236,236)',
                }"
              ></div>
              <colorPicker
                v-model="color"
                v-on:change="handleChangeColor"
                v-show="!noTextColor"
                ref="textColorPicker"
              />
            </div>
          </div>
        </div>
        <div class="setting-color" v-show="effect">
          <div class="left-title">字体背景颜色：</div>
          <div class="border_font" @click="handleColorPick">
            <icon-common
              icon="icon-masaike1"
              class="masaike"
              v-show="noColor"
            ></icon-common>
            <colorPicker
              v-model="bgColor"
              v-on:change="handleChangeBgColor"
              v-show="!noColor"
              ref="colorPicker"
            />
          </div>
        </div>
        <div class="setting-color cancelBgColorBox" v-show="effect">
          <div class="left-title"></div>
          <a-button @click="cancelBgColor" class="cancelBgColor"
            >无背景</a-button
          >
        </div>
      </div>
      <div class="setting-middle">
        <div class="top-title">排版</div>
        <div class="line-top"></div>
      </div>
      <div class="left-title">
        <div v-show="effect">
          <label>字符间距: </label>
          <a-input-number
            size="small"
            class="prop-input_num"
            v-model="characterSpacing"
          >
          </a-input-number>
        </div>
        <div style="margin-left: 30px">对齐方式：</div>
        <div
          :class="align === 'top' ? 'defaultAlign' : 'align'"
          title="上对齐"
          @click="changeAlign(1)"
        >
          <icon-common
            icon="icon-shangduiqi"
            style="margin-bottom: 3px"
          ></icon-common>
        </div>
        <div
          :class="align === 'middle' ? 'defaultAlign' : 'align'"
          title="居中对齐"
          @click="changeAlign(2)"
        >
          <icon-common
            icon="icon-shangxiajuzhong"
            style="margin-bottom: 3px"
          ></icon-common>
        </div>
        <div
          :class="align === 'bottom' ? 'defaultAlign' : 'align'"
          title="下对齐"
          @click="changeAlign(3)"
        >
          <icon-common
            icon="icon-Q-xiaduiqi"
            style="margin-bottom: 3px"
          ></icon-common>
        </div>
      </div>
      <div v-show="effect">
        <div class="setting-middle">
          <div class="top-title">效果</div>
          <div class="line-bottom"></div>
        </div>
        <div class="setting-bottom">
          <div class="setting-bottom-content">
            <div
              class="click-checkbox"
              @click="changeDeleteLine"
              onselectstart="return false"
            >
              <input
                v-model="isDeleteLine"
                type="checkbox"
                class="checkbox_font"
              />
              删除线
            </div>
            <div
              class="click-checkbox"
              @click="changeUnderline"
              onselectstart="return false"
            >
              <input
                v-model="isUnderline"
                type="checkbox"
                class="checkbox_font"
              />
              下划线
            </div>
            <div
              class="click-checkbox"
              @click="changeDblUnderLine"
              onselectstart="return false"
            >
              <input
                v-model="isDblUnderLine"
                type="checkbox"
                class="checkbox_font"
              />
              双下划线
            </div>
          </div>
          <div class="setting-bottom-content">
            <div
              class="click-checkbox"
              @click="changeSuperScript"
              onselectstart="return false"
            >
              <input
                v-model="isSuperScript"
                type="checkbox"
                class="checkbox_font"
              />
              上标
            </div>
            <div
              class="click-checkbox"
              @click="changeSubScript"
              onselectstart="return false"
            >
              <input
                v-model="isSubScript"
                type="checkbox"
                class="checkbox_font"
              />
              下标
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="setting-middle">
        <div class="top-title">预览</div>
        <div class="line-bottom"></div>
      </div> -->
    </div>
  </modal>
</template>
<script>
import selected from "./common/select.vue";
import modal from "./common/modal.vue";
import iconCommon from "./common/iconCommon.vue";
export default {
  name: "fontSettings",
  components: {
    modal,
    selected,
    iconCommon,
  },
  props: {
    noColorFlag: { type: Boolean, default: false },
    show: {
      type: Boolean,
      default: false,
    },
    fontSizeList: {
      type: Array,
      default: () => [],
    },
    fontTypeList: {
      type: Array,
      default: () => [],
    },
    initFontStyle: {
      type: Object,
      default: () => {},
    },
    effect: {
      type: Boolean,
      default: true,
    },
  },
  watch: {
    initFontStyle: {
      handler(e) {
        if (e.color) {
          this.color = e.color;
        } else {
          this.color = "";
          this.noTextColor = true;
        }
        this.bgColor = "rgba(255,255,255,0)";
        this.noColor = true;
        this.characterSpacing = e.characterSpacing;
        if (e.bgColor) {
          if (e.bgColor === "rgba(255,255,255,0)") {
            this.noColor = true;
          } else {
            this.bgColor = e.bgColor;
            this.noColor = false;
          }
        } else {
          this.bgColor = "";
        }
        if (e.align) {
          this.align = e.align;
        } else {
          this.align = "bottom";
        }
        this.isUnderline = e.underline;
        this.isDeleteLine = e.strikethrough;
        this.isDblUnderLine = e.dblUnderLine;

        this.fontfamily = {
          option: e.family,
          value: e.family,
        };
        this.fontSize = {
          option: e.height,
          value: e.height,
        };
        for (let i = 0; i < this.fontSizeList.length; i++) {
          const el = this.fontSizeList[i];
          if (el.value === e.height) {
            this.fontSize.option = el.option;
            break;
          }
        }

        if (e.script === 1) {
          this.isSuperScript = true;
          this.isSubScript = false;
        } else if (e.script === 2) {
          this.isSuperScript = false;
          this.isSubScript = true;
        } else if (e.script === 3) {
          this.isSuperScript = false;
          this.isSubScript = false;
        }

        if (e.bold && e.italic) {
          this.fontGlyph.option = "加粗 倾斜";
          this.fontGlyph.value = { bold: true, italic: true };
        } else if (!e.bold && e.italic) {
          this.fontGlyph.option = "倾斜";
          this.fontGlyph.value = { bold: false, italic: true };
        } else if (e.bold && !e.italic) {
          this.fontGlyph.option = "加粗";
          this.fontGlyph.value = { bold: true, italic: false };
        } else if (!e.bold && !e.italic) {
          if (e.bold === undefined || e.italic === undefined) {
            this.fontGlyph.option = "";
          } else {
            this.fontGlyph.option = "常规";
            this.fontGlyph.value = { bold: false, italic: false };
          }
        }
      },
      deep: true,
      immediate: true,
    },
    noColorFlag() {
      if (
        this.$refs.colorPicker &&
        this.$refs.colorPicker.showPanelColor === "rgba(255,255,255,0)"
      ) {
        this.noColor = true;
      }
    },
  },
  data() {
    return {
      fontfamily: {},
      fontGlyph: {},
      fontSize: {},
      title: "字体设置",
      masks: true,
      align: "bottom",
      modal_width: 524,
      fontTypeWidth: 240,
      fontGlyphWidth: 120,
      fontSizeWidth: 80,
      fontSelectWidth: 120,
      isUnderline: false, // 是否有下划线
      isDeleteLine: false, // 是否有删除线
      isDblUnderLine: false, // 是否有双下划线
      isSuperScript: false,
      isSubScript: false,
      color: "#000000",
      bgColor: "#FFFFFF",
      noColor: true,
      noTextColor: false,
      characterSpacing: 0,
      fontGlyphList: [
        {
          option: "常规",
          value: { bold: false, italic: false },
        },
        {
          option: "倾斜",
          value: { bold: false, italic: true },
        },
        {
          option: "加粗",
          value: { bold: true, italic: false },
        },
        {
          option: "加粗 倾斜",
          value: { bold: true, italic: true },
        },
      ],
    };
  },

  methods: {
    submit() {
      const getFontType = { family: this.$refs.getFontType.inputValue }; //family
      const getFontGlyph = this.$refs.getFontGlyph.inputValue; //加粗斜体
      const getFontSize = { height: this.$refs.getFontSize.inputValue }; //height
      const getUnderline = { underline: false }; //下划线
      const getStrikeThrough = { strikethrough: false }; //删除线
      const getDblUnderLine = { dblUnderLine: false }; //双下划线
      const getScript = { script: 3 }; //上标
      let color;
      if (this.color) {
        color = { color: this.color }; //文字颜色
      }
      let bgColor;
      if (this.bgColor) {
        bgColor = { bgColor: this.bgColor };
      }
      const align = { align: this.align };
      const characterSpacing = { characterSpacing: this.characterSpacing };

      if (this.isSuperScript) {
        getScript.script = 1;
      } else if (this.isSubScript) {
        getScript.script = 2;
      }

      if (this.isUnderline) {
        getUnderline.underline = true;
      }

      if (this.isDblUnderLine) {
        getDblUnderLine.dblUnderLine = true;
      }

      if (this.isDeleteLine) {
        getStrikeThrough.strikethrough = true;
      }

      const style = Object.assign(
        {},
        getFontType,
        getFontGlyph,
        getFontSize,
        getUnderline,
        getDblUnderLine,
        getStrikeThrough,
        getScript,
        color ?? { color: undefined },
        bgColor ?? { bgColor: undefined },
        align,
        characterSpacing
      );

      // 过滤掉值为 undefined 的属性
      const filteredStyle = Object.keys(style).reduce((acc, key) => {
        if (style[key] !== undefined) {
          acc[key] = style[key];
        }
        return acc;
      }, {});

      this.$emit("submit", filteredStyle);
      this.cancel();
    },
    cancel() {
      this.$emit("cancel");
    },
    //改变字体对齐方式
    changeAlign(num) {
      switch (num) {
        case 1:
          this.align = "top";

          break;
        case 2:
          this.align = "middle";
          break;
        case 3:
          this.align = "bottom";
          break;
        default:
          this.align = "bottom";
          break;
      }
    },
    //改变删除线
    changeDeleteLine() {
      this.isDeleteLine = !this.isDeleteLine;
    },
    //改变下划线
    changeUnderline() {
      this.isUnderline = !this.isUnderline;
    },
    //改变双下划线
    changeDblUnderLine() {
      this.isDblUnderLine = !this.isDblUnderLine;
    },
    //改变上标
    changeSuperScript() {
      this.isSuperScript = !this.isSuperScript;
      if (this.isSuperScript) {
        this.isSubScript = false;
      }
    },
    //改变下标
    changeSubScript() {
      this.isSubScript = !this.isSubScript;
      if (this.isSubScript) {
        this.isSuperScript = false;
      }
    },
    //改变字体颜色
    handleChangeColor(e) {
      this.color = e;
    },
    handleClickNoTextColorSymbol() {
      this.noTextColor = false;
      this.$refs.textColorPicker.openPanel();
    },
    //改变背景颜色
    handleChangeBgColor(e) {
      this.bgColor = e;
    },
    cancelBgColor() {
      this.bgColor = "rgba(255,255,255,0)";
      this.noColor = true;
    },
    handleColorPick() {
      this.noColor = false;
      this.$refs.colorPicker.openPanel();
    },
  },
};
</script>
<style scoped>
.font-settings_font {
  width: 500px;
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(217, 217, 217);
  padding: 10px;
  color: #000;
}
.setting-top {
  display: flex;
  justify-content: space-around;
}
.setting-middle {
  padding-top: 10px;
  display: flex;
  padding-bottom: 5px;
}
.setting-bottom {
  display: flex;
}
.setting-bottom-content {
  width: 240px;
  padding-left: 20px;
}
.setting-color {
  display: flex;
  width: 234px;
}
.top-title {
  padding: 5px;
  padding-left: 5px;
}
.left-title {
  padding: 5px;
  padding-left: 20px;
  display: flex;
}
.line-top {
  margin-top: 16px;
  margin-left: 5px;
  background-color: rgb(217, 217, 217);
  height: 1px;
  width: 400px;
}
.line-bottom {
  margin-top: 16px;
  margin-left: 5px;
  background-color: rgb(217, 217, 217);
  height: 1px;
  width: 428px;
}
.border_font {
  border: 1px solid rgb(217, 217, 217);
  height: 18px;
  /* width: 56px; */
  margin-top: 6px;
  background-color: rgb(225, 225, 225);
  position: relative;
  cursor: pointer;
}
.font-settings_font /deep/.colorBtn [data-v-29accc04] {
  width: 50px;
  height: 14px;
  position: absolute;
  margin-top: -14px;
  margin-left: 2px;
  border: 1px rgb(217, 217, 217);
}
.click-checkbox {
  cursor: pointer;
}
.checkbox_font {
  margin-top: 4px;
  margin-right: 5px;
  height: 13px;
  width: 13px;
}
.cancelBgColor {
  height: 22px;
  margin-top: 5px;
  color: "#000000";
}
.cancelBgColorBox {
  margin-left: 5px;
}
.masaike {
  margin-top: -10px;
}
.align {
  margin-left: 10px;
  cursor: pointer;
  border: 1px solid white;
}
.align:hover {
  background-color: rgb(241, 241, 241);
  border-radius: 2px;
  border: 1px solid rgb(241, 241, 241);
}
.defaultAlign {
  margin-left: 10px;
  cursor: pointer;
  border-radius: 2px;
  border: 1px solid #0078d7;
  background-color: #0078d7;
}
</style>
