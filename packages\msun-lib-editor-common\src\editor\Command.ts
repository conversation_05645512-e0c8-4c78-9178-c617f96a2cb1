import Editor from "./Editor";

export enum commands {
  init,
  input, // 文本输入 可成组撤销
  insert,
  cut, // 剪切
  paste, // 粘贴
  ime_input, // 输入法输入
  add_list, // 有序无序列表添加
  backspace_delete, // 回退键删除
  enter_linebreak, // 可成组撤销
  style_change, // 样式修改 （各种样式的修改
  handle_cell, // 单元格处理
  handle_table, // 表格处理
  insert_image, // 插入图片
  uncertainty, // 不确定的
  handle_shapes, // 处理图形
  watermark // 水印
}

let previousCommand = commands.init;

/**
 * 光标移动后还原记录的上一次命令
 */
export function restorePreviousCommand (editor: Editor) {
  previousCommand = commands.init;
  recordEndSelection(editor);
}

/**
 * 记录光标移动前，其他插入等命令改后的光标位置，用于撤销后重做时设置光标
 * @param editor
 */
function recordEndSelection (editor: Editor) {
  const undoStack = editor.history.getUndo();
  if (undoStack.length < 1) return;
  // 光标移动前重新设置堆栈中选区，增加end_selection用于记录重做时应该将光标设置的位置
  const contentState = undoStack[undoStack.length - 1];
  if (!contentState.selection.end_selection) {
    contentState.end_selection = editor.selection.getSelection();
  }
}

/**
 * 命令改变前获取当前编辑器的原始数据内容以及选区状态放置到撤销堆栈中
 * @param command
 * @param editor
 */
export function addUndoStack (command: commands, editor: Editor) {
  // 当操作命令改变时，或者不是输入、回车操作，记录到撤销堆栈中
  if (
    (command !== commands.input && command !== commands.enter_linebreak && command !== commands.backspace_delete) ||
    command !== previousCommand ||
    !editor.history.canBeUndo()
  ) {
    const raw = editor.getContentState();
    editor.history.add(raw, editor);
    previousCommand = command;
    return raw;
  }
}

/**
 * 移除撤销堆栈中的最新一条数据
 */
export function popUndoStack (editor: Editor) {
  editor.history.getUndo().pop();
}

/**
 * 清空重做堆栈内容
 */
export function clearRedoStack (editor: Editor) {
  editor.history.clearRedo();
}
/**
 * 清空历史堆栈内容
 */
export function clearStack (editor: Editor) {
  editor.history.clear();
}

/**
 * 撤销操作 ctrl+z
 * @param editor
 */
export function undo (editor: Editor) {
  // 续打或区域打印模式下不允许撤销重做
  if (editor.print_continue || editor.area_print) {
    return;
  }
  // 如果是图形模式则撤销后取消绘制状态
  if (editor.is_shape_mode) {
    editor.drawShape("close");
  }
  const contentState = editor.history.undo(editor);
  editor.setContentState(contentState);
  previousCommand = commands.init;
  editor.event.emit("exeCommand", { command: "undo" });
}

/**
 * 重做操作 ctrl+y
 * @param editor
 */
export function redo (editor: Editor) {
  // 续打或区域打印模式下不允许撤销重做
  if (editor.print_continue || editor.area_print) {
    return;
  }
  const contentState = editor.history.redo();
  editor.setContentState(contentState, "redo");
  previousCommand = commands.init;
  editor.event.emit("exeCommand", { command: "redo" });
}
