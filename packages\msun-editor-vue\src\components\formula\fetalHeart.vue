<template>
  <div class="editor_fetalHeart">
    <div class="editor_col_fetal_heart">
      <a-input
        type="text"
        class="editor_input_fetal_heart"
        v-model="meta.params[0]"
      />
      <div class="editor_center_fetal_heart">
        <div class="editor_center_line_fetal_heart"></div>
        <a-input
          type="text"
          class="editor_input_fetal_heart"
          v-model="meta.params[1]"
        />
        <div class="editor_center_line_fetal_heart"></div>
      </div>
      <a-input
        type="text"
        class="editor_input_fetal_heart"
        v-model="meta.params[2]"
      />
    </div>
    <div class="editor_vertical_line_fetal_heart"></div>
    <div class="editor_col_fetal_heart">
      <a-input
        type="text"
        class="editor_input_fetal_heart"
        v-model="meta.params[3]"
      />
      <div class="editor_center_fetal_heart">
        <div class="editor_center_line_fetal_heart"></div>
        <a-input
          type="text"
          class="editor_input_fetal_heart"
          v-model="meta.params[4]"
        />
        <div class="editor_center_line_fetal_heart"></div>
      </div>
      <a-input
        type="text"
        class="editor_input_fetal_heart"
        v-model="meta.params[5]"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "fetalHeart",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style scoped>
.editor_fetalHeart {
  width: 100%;
  height: 100%;
  display: flex;
}

.editor_col_fetal_heart {
  text-align: center;
  width: 244px;
}
.editor_vertical_line_fetal_heart {
  height: 156px;
  width: 5px;
  background-color: #000;
}
.editor_input_fetal_heart {
  margin-top: 10px;
  margin-bottom: 10px;
  width: 170px;
}
.editor_center_fetal_heart {
  display: flex;
  align-items: center;
}
.editor_center_line_fetal_heart {
  width: 46.5px;
  height: 5px;
  background-color: #000;
}
</style>
