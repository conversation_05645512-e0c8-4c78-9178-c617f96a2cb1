
export function versionDiff (version1:string, version2:string) {
  // // 定义两个版本号
  // const version1 = '1.2.3';
  // const version2 = '1.4.0';
  // 将版本号转换为数字数组，方便比较大小
  const v1 = version1.split(".").map(Number);
  const v2 = version2.split(".").map(Number);
  // 比较版本号大小
  for (let i = 0; i < v1.length || i < v2.length; i++) {
    const diff = (v1[i] || 0) - (v2[i] || 0);
    if (diff !== 0) {
      return diff > 0 ? 1 : -1;
    }
  }
  // 如果没有差异，则版本号相等
  return 0;
}
