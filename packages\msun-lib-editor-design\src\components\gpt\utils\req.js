/*
  请求的方法封装
  【postData】(url, data, param = {}) POST 方法请求； url 是请求链接; data 请求参数 ;param 可选 可设置loading:true 请求回来之前显示loading,后续可能支持其他配置参数
  【getData】(url, data, param = {}) GET 方法请求； url 是请求链接; data 请求参数 ;param 可选 可设置loading:true 请求回来之前显示loading,后续可能支持其他配置参数
*/
import axios from "axios";

// axios可以通过create 绑定一些默认选项
var server = axios.create({
  timeout: 600000,
  baseURL: "http://msunllmtest.msunsoft.com", // 使用相对路径，让 Vite 代理处理
  headers: {
    // 全局默认请求头
    "Content-Type": "application/json",
    Authorization: "app-qvM1OicEA2tQF1FPhklFTxMD",
    appId: "msun-editor-chat-app",
    nlpToken:
      "SUiIN+ZRWDlJ6Cy7bR/rf04zawAiDk2NbfJwsDCP/vjdPbIUMtU0Gxu8gZJ5+xuOZS9A3f4DInpfzTY7tjydlI4xeZ4gTal3c1UD8x0O1YL4HjPH48KEJ+CrLybodArGFO0E2ZDkIGQd1lvKkaml87NDl3oW2KMdT/yxleu+6YA=",
  },
});
axios.defaults.headers.get["Content-Type"] =
  "application/x-www-form-urlencoded"; // 设置get 的content-Type

export function postData(url, data = {}) {
  return new Promise((resolve, reject) => {
    server
      .post(url, data)
      .then((res) => {
        resolve(res.data);
      })
      .catch((err) => {
        reject(err);
      });
  });
}
export function getData(url, data = {}, param = {}) {
  let token = sessionStorage.getItem("token") || "";
  return new Promise((resolve, reject) => {
    server
      .get(url, {
        headers: {
          token,
        },
        params: data,
        data,
      })
      .then((res) => {
        if (!param.message && res.data.code != "200") {
          reject(res.data.message);
        }
        resolve(res.data);
      });
  });
}
