import EditorLocalTest from "../../localtest";
import Cell from "./Cell";
import Character from "./Character";
import { COMMENT_SWITCH_WIDTH, COMMENT_SWITCH_DISTANCE_TO_CROSS, COMMENT_TITLE_CROSS_HEIGHT, COMMENT_TITLE_CROSS_TOP, COMMENT_TITLE_CROSS_WIDTH, COMMENT_TITLE_PADDING_LEFT, COMMENT_TITLE_PADDING_RIGHT, COMMENT_WIDTH, COMMENT_LIST_MIN_WIDTH } from "./Constant";
import Editor from "./Editor";
import EditorHelper from "./EditorHelper";
import Font from "./Font";
import Group from "./Groups";
import { getCellIndex } from "./Helper";
import Paragraph from "./Paragraph";
import { Path } from "./Path";
import Renderer from "./Renderer";
import Table from "./Table";
import { isCharacter, isParagraph, isTable, uuid } from "./Utils";
import XField from "./XField";

export default class Comment {
  static addComment(
    editor: Editor,
    addContent: any,
    isCus: boolean = false,
    cusId?: string
  ) {
    if(addContent.name===undefined){
      addContent.name = editor.user ? editor.user.name : "未登录";
    }
    if(addContent.date===undefined){
      addContent.date = Date.now()
    }
    if (editor.selection.isCollapsed) return; // 不是选区 不能添加批注
    const allChars = editor.selection.selected_fields_chars.all_chars;
    const selectCell = editor.selection.selected_cells;

    let haveChar = false;
    allChars.forEach((char: any) => {
      if (isCharacter(char) && char.value !== "\n") {
        haveChar = true;
      }
    });
    if (!haveChar || !allChars.length || selectCell.length) return;
    const selected_paragraph_info = editor.selection.selected_para_info;
    const selected_paragraphs_spread_table_paragraph =
      selected_paragraph_info.paragraphs; // 选中的所有完整段落 将表格内的段落 平铺放入了该数组
    // group_id 只在段落和表格上才有 单元格里边的段落是没有的
    const compare_group_id =
      selected_paragraphs_spread_table_paragraph[0].cell?.parent?.group_id ||
      selected_paragraphs_spread_table_paragraph[0].group_id;
    if (
      selected_paragraphs_spread_table_paragraph.some(
        (paragraph) =>
          (paragraph.cell?.parent?.group_id || paragraph.group_id) !==
          compare_group_id
      )
    ) {
      editor.event.emit("message", {
        type: "warning",
        msg: `跨分组不能同时添加批注`,
      });
      return;
    } // 如果跨分组了 不能添加批注

    let group_id;
    let id;

    if (isCus) {
      editor.document_meta.cusCommentsIDSet ||
        (editor.document_meta.cusCommentsIDSet = {});
      group_id = compare_group_id || "document";
      id = cusId
        ? cusId + "$$" + group_id
        : uuid("CusComment") + "$$" + group_id;
    } else {
      editor.document_meta.commentsIDSet ||
        (editor.document_meta.commentsIDSet = {}); // TODO 多个方法里边都有这行判断 可能不是个好的解决方案 应该在源头上避免它是 undefined 或者 Null
      group_id = compare_group_id || "document";
      id = uuid("comment") + "$$" + group_id;
    }
    // 给选区的每个字符都添加上 comment Id
    this.assignIdToCharacters(allChars, id, isCus);

    // 将批注的信息 全部都存在 document_meta 中
    if (isCus) {
      if (editor.document_meta.cusCommentsIDSet[group_id]) {
        editor.document_meta.cusCommentsIDSet[group_id].push({
          id,
          ...addContent,
        });
      } else {
        editor.document_meta.cusCommentsIDSet[group_id] = [
          { id, ...addContent },
        ];
      }
    } else {
      if (editor.document_meta.commentsIDSet[group_id]) {
        editor.document_meta.commentsIDSet[group_id].push({
          id,
          ...addContent,
        });
      } else {
        editor.document_meta.commentsIDSet[group_id] = [{ id, ...addContent }];
      }
    }
    editor.update();
    if (editor.useNewVersionCommentList) {
      editor.pages.forEach(p => {
        p.commentBox.forEach((c: any) => {
          if (c.comment.id === id) {
            editor.newVersionOpenCommentMap.set(id, true);
            p.setCommentBox();
          } else {
            c.comment.selected = false;
          }
        })
      })
    } else {
      editor.render();
      editor.commentBox.forEach((box: any) => {
        if (box.comment.id === id) {
          editor.internal.currentComment = box.comment;
        }
      })
    }
    editor.render();
    if (EditorLocalTest.transUse && editor.is_comment_mode && !isCus && !editor.useNewVersionCommentList) {
      Comment.commentScrollToViewRange(editor, id);
    }
    editor.event.emit("addComment")

    return id;
  }

  static addCommentByField(editor: Editor, addContent: any, field: XField) {
    //设置选区
    const startPath = field.start_para_path;
    const endPath = field.end_para_path;
    editor.selection.setSelectionByPath(startPath, endPath);
    editor.addComment(addContent);
  }

  static commentScrollToViewRange(editor: Editor, id: string) {
    const res = editor.internal.getInfoByCommentID(id);
    const commentId = res.characters[0].comment_id;
    const commentArr = editor.internal.getCommentInfo();
    commentArr.forEach((comment: any) => {
      if (comment.id === commentId) {
        comment.selected = true;
        const commentBox = editor.commentBox;
        const curComment = commentBox.find(
          (comment: any) => comment.commentId === commentId
        );
        const originTop = editor.scroll_top
          ? editor.scroll_top
          : editor.pages[0].top;
        const commentTop = curComment.top;
        const commentBottom = commentTop + curComment.height;
        const canvasHeight =
          (editor.init_canvas.height / editor.config.devicePixelRatio - 50)/editor.viewScale
        let totalHeight = commentBox.length * 30;
        commentBox.forEach((comment: any) => {
          totalHeight += comment.height;
        });
        const ratio = canvasHeight / totalHeight;
        // 从下往上弹的逻辑
        if (commentBottom > originTop + canvasHeight) {
          const shouldBeBottomPosition =
            originTop + canvasHeight - 30 - curComment.height;
          const bottomDifferenceValue =
            commentBottom - shouldBeBottomPosition - curComment.height;
          const bottomScrollBarDifferenceValue = bottomDifferenceValue * ratio;
          editor.internal.currentCommentPosition += bottomDifferenceValue;
          editor.internal.scrollBarTop += bottomScrollBarDifferenceValue;
        }
        if (commentTop < originTop + 50) {
          const shouldBeTopPosition = originTop + 50;
          const topDifferenceValue = shouldBeTopPosition - commentTop;
          const topScrollBarDifferenceValue = topDifferenceValue * ratio;
          editor.internal.currentCommentPosition -= topDifferenceValue;
          editor.internal.scrollBarTop -= topScrollBarDifferenceValue;
        }
      } else {
        comment.selected = false;
      }
    });

    editor.internal.current_has_temp_bg_color_characters = res.characters;
    editor.internal.addHighLighter(res.characters);
    editor.update();
    editor.render();
  }

  static unComments(
    editor: Editor,
    ID: string | undefined,
    groupID: string | undefined,
    isCus: boolean = false
  ) {
    if (isCus) {
      editor.document_meta.cusCommentsIDSet ||
        (editor.document_meta.cusCommentsIDSet = {});
    } else {
      editor.document_meta.commentsIDSet ||
        (editor.document_meta.commentsIDSet = {});
    }
    if (groupID) {
      if (ID) {
        let group_comment;
        isCus
          ? (group_comment = editor.document_meta.cusCommentsIDSet[groupID])
          : (group_comment = editor.document_meta.commentsIDSet[groupID]);
        for (let i = 0; i < group_comment?.length; i++) {
          if (group_comment[i].id === ID) {
            group_comment.splice(i, 1);
            return true;
          }
        }
      } else {
        if (isCus) {
          delete editor.document_meta.cusCommentsIDSet[groupID];
        } else {
          delete editor.document_meta.commentsIDSet[groupID];
        }
        return true;
      }
    } else {
      if (ID) {
        let keys;
        isCus
          ? (keys = Object.keys(editor.document_meta.cusCommentsIDSet))
          : (keys = Object.keys(editor.document_meta.commentsIDSet));
        for (let i = 0; i < keys.length; i++) {
          let group_comments;
          isCus
            ? (group_comments = editor.document_meta.cusCommentsIDSet[keys[i]])
            : (group_comments = editor.document_meta.commentsIDSet[keys[i]]);
          for (let j = 0; j < group_comments?.length; j++) {
            if (group_comments[j].id === ID) {
              group_comments.splice(j, 1);
              return true;
            }
          }
        }
      } else {
        isCus
          ? (editor.document_meta.cusCommentsIDSet = {})
          : (editor.document_meta.commentsIDSet = {});
        return true;
      }
    }
    return false;
  }

  /**
   * 根据批注 id 获取批注
   * @param id 批注 id
   * @returns
   */
  static getCommentByID(editor: Editor, id: string, isCus: boolean = false) {
    if (isCus) {
      editor.document_meta.cusCommentsIDSet ||
        (editor.document_meta.cusCommentsIDSet = {});
    } else {
      editor.document_meta.commentsIDSet ||
        (editor.document_meta.commentsIDSet = {});
    }
    let keys;
    isCus
      ? (keys = Object.keys(editor.document_meta.cusCommentsIDSet))
      : (keys = Object.keys(editor.document_meta.commentsIDSet));
    for (let i = 0; i < keys.length; i++) {
      let group_comments;
      isCus
        ? (group_comments = editor.document_meta.cusCommentsIDSet[keys[i]])
        : (group_comments = editor.document_meta.commentsIDSet[keys[i]]);
      for (let j = 0; j < group_comments?.length; j++) {
        if (group_comments[j].id === id) {
          return group_comments[j];
        }
      }
    }
  }

  static getInfoByID(
    editor: Editor,
    ID: string,
    isCus: boolean = false,
    cell?: Cell
  ) {
    cell = cell || editor.current_cell;
    const res: any[] = [];

    let para_path: Path = [];

    let end_path: Path = [];

    let first_time = true;

    cell.paragraph.forEach((para) => {
      if (isParagraph(para)) {
        for (let i = 0; i < para.characters.length; i++) {
          const char = para.characters[i];
          let id;
          isCus ? (id = char.cusCommentId) : (id = char.comment_id);
          if (id === ID) {
            if (first_time) {
              if (para.cell.parent) {
                // 说明在表格里
                para_path.push(
                  para.cell.parent.para_index,
                  getCellIndex(para.cell),
                  para.para_index,
                  i
                );
                end_path = [
                  para.cell.parent.para_index,
                  getCellIndex(para.cell),
                  para.para_index,
                  i + 1,
                ];
              } else {
                para_path.push(para.para_index, i);
                end_path = [para.para_index, i + 1];
              }
              first_time = false;
            } else {
              if (para.cell.parent) {
                end_path = [
                  para.cell.parent.para_index,
                  getCellIndex(para.cell),
                  para.para_index,
                  i + 1,
                ];
              } else {
                end_path = [para.para_index, i + 1];
              }
            }
            res.push(char);
          }
        }
      } else if (isTable(para)) {
        para.children.forEach((cell) => {
          let result;
          isCus
            ? (result = this.getInfoByID(editor, ID, true, cell))
            : (result = this.getInfoByID(editor, ID, false, cell));
          result.para_path.length && (para_path = result.para_path);
          result.end_path.length && (end_path = result.end_path);
          res.push(...result.characters);
        });
      }
    });
    return { characters: res, para_path, end_path };
  }

  /**
   * 更新批注
   * @param id 批注 id
   * @param updateValue 批注内容
   * @returns 是否成功
   */
  static updateComments(
    editor: Editor,
    id: string,
    updateValue: string,
    isCus: boolean = false
  ) {
    if (isCus) {
      editor.document_meta.cusCommentsIDSet ||
        (editor.document_meta.cusCommentsIDSet = {});
    } else {
      editor.document_meta.commentsIDSet ||
        (editor.document_meta.commentsIDSet = {});
    }
    let keys;
    isCus
      ? (keys = Object.keys(editor.document_meta.cusCommentsIDSet))
      : (keys = Object.keys(editor.document_meta.commentsIDSet));
    for (let i = 0; i < keys.length; i++) {
      let group_comments;
      isCus
        ? (group_comments = editor.document_meta.cusCommentsIDSet[keys[i]])
        : (group_comments = editor.document_meta.commentsIDSet[keys[i]]);
      for (let j = 0; j < group_comments?.length; j++) {
        if (group_comments[j].id === id) {
          group_comments[j].value = updateValue;
          return;
        }
      }
    }
  }

  /**
   * 给字符赋值批注 id
   * @param characters 要赋值 id 的字符
   * @param comment_id 批注 id
   */
  static assignIdToCharacters(
    characters: any[],
    id: string,
    isCus: boolean = false
  ) {
    if (isCus) {
      characters.forEach((char) => {
        if (char.field_position === "normal" && isCharacter(char)) {
          char.cusCommentId = id;
        }
      });
    } else {
      characters.forEach((char) => {
        if (char.field_position === "normal" && isCharacter(char)) {
          char.comment_id = id;
        }
      });
    }
  }

  /**
   * 根据批注 id 设置高亮
   * @param id 批注 id
   * @returns 是否成功
   */
  static addHightLighterByID(
    editor: Editor,
    id: string,
    isCus: boolean = false
  ): Boolean {
    if (isCus) {
      editor.document_meta.cusCommentsIDSet ||
        (editor.document_meta.cusCommentsIDSet = {});
    } else {
      editor.document_meta.commentsIDSet ||
        (editor.document_meta.commentsIDSet = {});
    }
    let res;
    isCus
      ? (res = editor.internal.getInfoByCusCommentId(id))
      : (res = editor.internal.getInfoByCommentID(id));
    if (res.para_path?.length) {
      Comment.unHightLighter(
        editor,
        editor.internal.current_has_temp_bg_color_characters
      );
      editor.selection.setCursorPosition(
        editor.paraPath2ModelPath(res.para_path)
      );
      editor.updateCaret();
      editor.scroll_by_focus();
      Comment.addHighLighter(editor, res.characters);
      return true;
    } else {
      let idArr: any;
      isCus
        ? (idArr = (Object as any).values(
          editor.document_meta.cusCommentsIDSet
        ))
        : (idArr = (Object as any).values(editor.document_meta.commentsIDSet));
      for (const arr of idArr) {
        for (let i = 0; i < arr.length; i++) {
          if (arr[i] === id) {
            arr.splice(i, 1);
            return false;
          }
        }
      }
    }
    return false;
  }

  /**
   * 设置高亮
   * @param characters 添加高亮的字符
   */
  static addHighLighter(editor: Editor, characters: Character[]) {
    editor.internal.current_has_temp_bg_color_characters = characters;
    characters.forEach((char) => {
      char.temp_bgColor = editor.config.comment.wordSelectedBgColor;
    });
  }

  /**
   * 取消高亮
   * @param characters 取消高亮的字符
   */
  static unHightLighter(editor: Editor, characters: Character[]) {
    characters.forEach((char) => {
      char.temp_bgColor = undefined;
    });
    editor.internal.current_has_temp_bg_color_characters = [];
  }

  static handleCommentIDSetByParagraphsTables(
    editor: Editor,
    paragraphs_tables: (Paragraph | Table)[],
    group_id: string,
    isCus: boolean = false
  ) {
    let comment_arr: { id: string; [propName: string]: string }[];
    isCus
      ? (comment_arr = editor.document_meta?.cusCommentsIDSet?.[group_id] || [])
      : (comment_arr = editor.document_meta?.commentsIDSet?.[group_id] || []);

    const should_exist_id_obj: { [propName: string]: boolean } = {};
    for (const paragraphOrTable of paragraphs_tables) {
      if (isParagraph(paragraphOrTable)) {
        for (const character of paragraphOrTable.characters) {
          let id;
          isCus ? (id = character.cusCommentId) : (id = character.comment_id);
          if (id && !should_exist_id_obj[id]) {
            should_exist_id_obj[id] = true;
          }
        }
      } else {
        for (const cell of paragraphOrTable.children) {
          for (const paragraph of cell.paragraph) {
            for (const character of (paragraph as Paragraph).characters) {
              let id;
              isCus
                ? (id = character.cusCommentId)
                : (id = character.comment_id);
              if (id && !should_exist_id_obj[id]) {
                should_exist_id_obj[id] = true;
              }
            }
          }
        }
      }
    }

    for (let i = 0; i < comment_arr.length; ) {
      const comment_id = comment_arr[i].id;
      if (!should_exist_id_obj[comment_id]) {
        comment_arr.splice(i, 1);
      } else {
        i++;
      }
    }

    if (comment_arr.length === 0) {
      if (isCus) {
        delete editor.document_meta?.cusCommentsIDSet?.[group_id];
      } else {
        delete editor.document_meta?.commentsIDSet?.[group_id];
      }
    }
  }

  static handleCommentIDSet(
    editor: Editor,
    current_group: Group | "document" | "all",
    isCus: boolean = false
  ) {
    if (current_group === "document") {
      if (isCus) {
        editor.internal.handleCusCommentIDSet_document();
      } else {
        editor.internal.handleCommentIDSet_document();
      }
    } else if (current_group === "all") {
      if (isCus) {
        editor.internal.handleCusCommentIDSet_document();
        editor.current_cell.groups.forEach((group) => {
          editor.internal.handleCusCommentIDSet_group(group);
        });
      } else {
        editor.internal.handleCommentIDSet_document();
        editor.current_cell.groups.forEach((group) => {
          editor.internal.handleCommentIDSet_group(group);
        });
      }
    } else if (current_group) {
      if (isCus) {
        editor.internal.handleCusCommentIDSet_group(current_group);
      } else {
        editor.internal.handleCommentIDSet_group(current_group);
      }
    }
  }

  static toggleMode(editor: Editor, mode: boolean, isCus: boolean = false) {
    isCus ? (editor.isCusCommentMode = mode) : (editor.is_comment_mode = mode);
    if (editor.is_comment_mode) {
      editor.useNewVersionCommentList = true;
      if (editor.config.comment.defaultAllOpen) {
        const commentArr = editor.getCommentInfo();
        if (commentArr && commentArr.length) {
          for (const comment of commentArr) {
            editor.newVersionOpenCommentMap.set(comment.id, true);
          }
        }
      } else {
        editor.newVersionOpenCommentMap.clear();
      }

    }
    editor.pages = [];
    editor.update();
    editor.render();
    if (!mode) return;
    isCus
      ? editor.internal.handleCusCommentIDSet("all")
      : editor.internal.handleCommentIDSet("all");
  }

  static getCommentInfo(editor: Editor) {
    const commentInfo = editor.document_meta.commentsIDSet;
    const abolishCommentInfo = editor.document_meta.abolishCommentIDSet;
    // 储存所有批注信息
    const commentArr: any = [];
    
    const map = new Map();
    
    if (abolishCommentInfo && abolishCommentInfo.length) {
      abolishCommentInfo.forEach((abolishComment: any) => {
        if (!map.has(abolishComment.id)) {
          abolishComment.abolished = true;
          commentArr.push(abolishComment);
          map.set(abolishComment.id, true);
        }
      });
    }
    
    for (const key in commentInfo) {
      commentInfo[key].forEach((comment: any) => {
        if (comment.value && !map.has(comment.id)) {
          commentArr.push(comment);
          map.set(comment.id, true);
        }
      });
    }
    
    return commentArr;
  }

  static getCommentByXY(editor: Editor, x: number, y: number): {
    comment: any, 
    isReplace: boolean, 
    isDelete: boolean, 
    isOpen: boolean
  } | undefined {
    const firstComment = editor.commentBox[0];
    let isReplace = false;
    let isDelete = false;
    let isOpen = false;
    if (firstComment) {
      const commentBox = editor.commentBox;
      const comment = commentBox.find(
        (item: any) =>
          x > item.left &&
          x < item.left + item.width &&
          y > item.top &&
          y < item.top + item.height
      );
      if (comment) {
        if (y > comment.top + 10 && y < comment.top + 35) {
          if (x > comment.left + 180 && x < comment.left + 204) {
            isReplace = true;
          } else if (x > comment.left + 210 && x < comment.left + 234) {
            isDelete = true;
          } else if (x > comment.left + 240 && x < comment.left + 265) {
            isOpen = true;
          }
        }
      }
      return { comment, isReplace, isDelete, isOpen };
    }
  }

  // TODO tang 1.demo跨行文本域替换bug 2.修改锚点文本域插入方式和fontMap添加时机 3.批注替换的时候判断被替换对象的类型
  // 4.批注替换判断只读锁定 5.水印模式下可以拖动表格线 6.图片层级不在依赖于单独或者重复模式 7.图片层级的提升或者降低不了的时候给个提示
  // 8.把defaultFont属性干掉
  static replaceContent(editor: Editor, id: string, content: string) {
    const elements = editor.internal.getInfoByCommentID(id);
    const paraPath = elements.para_path;
    const endPath = elements.end_path;
    if (paraPath.length && endPath.length) {
      editor.selection.setSelectionByPath(paraPath, endPath, "para_path");
      EditorHelper.deleteBackward(editor);
      const modelPath = editor.paraPath2ModelPath(paraPath);
      editor.selection.setCursorPosition(modelPath);
      let mark = false;
      if (editor.config.useLetterPlaceholder) {
        editor.config.useLetterPlaceholder = false;
        mark = true;
      }
      EditorHelper.insertText(editor, content);
      if (mark) {
        editor.config.useLetterPlaceholder = true;
      }
      editor.update();
      editor.render();
      return true;
    }
  }

  static deleteComment(editor: Editor, id: string, groupId: string) {
    let readonlyMark;
    if (editor.readonly) {
      editor.setReadonly(false);
      readonlyMark = true;
    }
    editor.uncomments(id, groupId);
    if (readonlyMark) {
      editor.setReadonly(true);
    }
    editor.update();
    editor.render();
  }

  // 作废掉一条批注，但是保留痕迹
  static abolishComment(editor: Editor, commentId: string, type: string, obj?: any) {
    let paraId = obj?.paraId;
    const document_meta = editor.document_meta;
    const comment = Comment.getCommentByID(editor, commentId);
    const commentCopy = JSON.parse(JSON.stringify(comment)); // 深复制
    // commentCopy.id += "-abolish"; // 修改复制品的id后缀
    document_meta.abolishCommentIDSet = document_meta.abolishCommentIDSet
      ? document_meta.abolishCommentIDSet
      : [];
    if (type === "replace") {
      commentCopy.processMode = 1;
    }
    if (type === "delete") {
      commentCopy.processMode = 0;
    }
    const index = document_meta.abolishCommentIDSet.findIndex(
      (item: any) => item.id === commentCopy.id
    );
    if (index !== -1) {
      document_meta.abolishCommentIDSet.splice(index, 1);
    }
    commentCopy.paraId = paraId;
    document_meta.abolishCommentIDSet.push(commentCopy);
  }

  static drawTitle(editor: Editor, left: number, top: number) {
    // 绘制 关闭批注列表的 叉号
    const COMMENT_LIST_WIDTH = Math.max(editor.config.comment.listWidth, COMMENT_LIST_MIN_WIDTH)
    const marginWidth = (COMMENT_LIST_WIDTH - COMMENT_WIDTH) / 2;
    const l = left + marginWidth + (COMMENT_WIDTH - COMMENT_TITLE_PADDING_RIGHT) - COMMENT_TITLE_CROSS_WIDTH;
    if(!editor.config.comment.hideCloseBtn){
      Renderer.draw_line(
        [l, top + COMMENT_TITLE_CROSS_TOP],
        [l + COMMENT_TITLE_CROSS_WIDTH, top + COMMENT_TITLE_CROSS_TOP + COMMENT_TITLE_CROSS_HEIGHT],
        editor.config.comment.listTitleCrossColor,
        1,
        1.5,
        "solid"
      );
      const r = 
      Renderer.draw_line(
        [l + COMMENT_TITLE_CROSS_WIDTH, top + COMMENT_TITLE_CROSS_TOP],
        [l, top + COMMENT_TITLE_CROSS_TOP + COMMENT_TITLE_CROSS_HEIGHT],
        editor.config.comment.listTitleCrossColor,
        1,
        1.5,
        "solid"
      );
      // // 再绘制一个切换模式的图标
      // // 跟叉号差距 10px 吧 也就是结束点是 l - 10
      // const switchWidth = 10;
      // const start = left + marginWidth + (COMMENT_WIDTH - COMMENT_TITLE_PADDING_RIGHT)
      // Renderer.draw_line(
      //   []
      // )

    }
    if (!editor.config.comment.hideSwitch) {
      Renderer.drawSwitch({ start: [l - COMMENT_SWITCH_DISTANCE_TO_CROSS - COMMENT_SWITCH_WIDTH, top + COMMENT_TITLE_CROSS_TOP], height: COMMENT_TITLE_CROSS_HEIGHT, width: COMMENT_SWITCH_WIDTH, color: editor.config.comment.listTitleSwitchColor });
    }


    // 绘制批注列表的标题
    const titleFontStyle = {
      family: "微软雅黑",
      height: 20,
      bold: true,
      italic: false,
      underline: false,
      strikethrough: false,
      dblUnderLine: false,
      script: 3,
      characterSpacing: 0,
      color: editor.config.comment.listTitleColor,
      bgColor: null,
      highLight: null
    };
    const titleFont = new Font(titleFontStyle);
    const text = editor.config.comment.title ?? "批注列表";
    const { width } = Renderer.measure(titleFont, text);
    const titleLeft = left + marginWidth + COMMENT_TITLE_PADDING_LEFT;
    Renderer.draw_text(titleFont, text, titleLeft, top + 33, editor);
    const numFontStyle = {
      family: "微软雅黑",
      height: 12,
      bold: false,
      italic: false,
      underline: false,
      strikethrough: false,
      dblUnderLine: false,
      script: 3,
      characterSpacing: 0,
      color: editor.config.comment.listTitleNumColor,
      bgColor: null,
      highLight: null
    }
    const numFont = new Font(numFontStyle);
    Renderer.draw_text(numFont, `(${editor.commentTotal})`, titleLeft + width + 5, top + 31, editor);
    
    // 批注列表标题下边的线
    // Renderer.draw_line(
    //   [left, top + 45],
    //   [left + 300, top + 45],
    //   "rgb(150,150,150)",
    //   1,
    //   0.5,
    //   "solid"
    // );
  }
}
