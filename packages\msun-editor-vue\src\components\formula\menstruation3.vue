<template>
  <div class="menstruation3">
    <div class="cross_sign_left_menstrual3"></div>
    <div class="cross_sign_right_menstrual3"></div>
    <div class="editor_col_menstrual3">
      <div class="editor_left_top_menstrual3">初潮年龄</div>
      <a-input type="text" class="input_menstrual3" v-model="meta.params[0]" />
    </div>
    <div class="editor_col_menstrual3">
      <div class="editor_center_top_menstrual3">经期（天）</div>
      <a-input type="text" class="input_menstrual3" v-model="meta.params[1]" />
      <div class="editor_center_menstrual3">末次月经/绝经年龄</div>
      <a-input type="text" class="input_menstrual3" v-model="meta.params[2]" />
    </div>
    <div class="editor_col_menstrual3">
      <div class="editor_right_top_menstrual3">周期（天）</div>
      <a-input type="text" class="input_menstrual3" v-model="meta.params[3]" />
    </div>
  </div>
</template>

<script>
export default {
  name: "menstruation3",
  components: {},
  data() {
    return {};
  },
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },

  methods: {},
};
</script>
<style scoped>
.menstruation3 {
  width: 100%;
  height: 100%;
  display: flex;
}

.editor_col_menstrual3 {
  text-align: center;
  width: 166.5px;
  height: 240px;
}
.editor_left_top_menstrual3 {
  margin-top: 80px;
  align-items: center;
}
.editor_center_top_menstrual3 {
  margin-bottom: 5px;
}
.editor_center_menstrual3 {
  margin-top: 100px;
  margin-bottom: 5px;
}
.editor_right_top_menstrual3 {
  margin-top: 70px;
  margin-bottom: 10px;
}
.cross_sign_left_menstrual3 {
  position: absolute;
  pointer-events: none;
  width: 490px;
  height: 240px;
  box-sizing: border-box;
  background: linear-gradient(
    26deg,
    transparent 49.5%,
    #000 49.5%,
    #000 50.5%,
    transparent 50.5%
  );
}
.cross_sign_right_menstrual3 {
  position: absolute;
  pointer-events: none;
  width: 490px;
  height: 240px;
  box-sizing: border-box;
  background: linear-gradient(
    154deg,
    transparent 49.5%,
    #000 49.5%,
    #000 50.5%,
    transparent 50.5%
  );
}
.input_menstrual3 {
  margin-top: 2px;
  width: 170px;
}
</style>
