import Cell from "./Cell";
import Editor from "./Editor";
import Paragraph from "./Paragraph";
import Row from "./Row";
import Table from "./Table";
import { isParagraph, isRow, isTable } from "./Utils";

export type Path = number[];

export interface PathInterface {
  compareFrontOrBack: (path: Path, another: Path) => -1 | 0 | 1; // 判断两个坐标前后关系
  equals: (path: Path, another: Path) => boolean; // 判断两个坐标是否相等
  isAfter: (path: Path, another: Path) => boolean; // 判断path是否在another之后
  isBefore: (path: Path, another: Path) => boolean; // 判断path是否在another之前
  isPath: (value: any) => value is Path; // 判断是否是一个path
  isTablePath: (path: Path) => boolean; // 判断是否是表格中的坐标
  isCellFirstRow: (path: Path) => boolean;
  isCellFirstPara: (para_path: Path) => boolean;
  isEndPathInCell: (path: Path, cell: Cell) => boolean;
  isStartPathInCell: (path: Path) => boolean;
  isEndPathInRow: (path: Path, row: Row) => boolean;
  isStartPathInRow: (path: Path) => boolean;
  isMiddlePathInRow: (path: Path, row: Row) => boolean;
  isSameRow: (start_path: Path, end_path: Path) => boolean;
  isSameCell: (start_path: Path, end_path: Path) => boolean;
  isStartPathInPara: (para_path: Path) => boolean;
  isEndPathInPara:(para_path: Path, para:Paragraph) => boolean;
  isMiddlePathInPara:(para_path: Path, para:Paragraph) => boolean;
  isSameParagraph: (start_para_path: Path, end_para_path: Path) => boolean;
  isSameTable: (start_path: Path, end_path: Path) => boolean;
  isTwoDiffTable: (start_path: Path, end_path: Path) => boolean;
  isValid: (editor: Editor, path: Path, type: "modelPath" | "paraPath") => boolean;
  paraPathisValid: (editor: Editor, path: Path) => boolean;
  modelPathIsValid: (editor: Editor, path: Path) => boolean

  getEndPathByRow:(row:Row)=>Path;
  getStartPathByRow:(row:Row)=>Path;
  getRowStartPathByPath:(model_path:Path)=>Path;

  getEndParaPathByPara:(para:Paragraph)=>Path;
  getStartParaPathByPara:(para:Paragraph)=>Path;
  getParagraphStartParaPathByPath:(para_path:Path)=>Path;

  getEndPathByCell:(cell:Cell)=>Path;
  getStartPathByCell:(cell:Cell)=>Path;
  getCellStartPathByPath:(model_path:Path)=>Path;

  getEndPathByTable:(table:Table)=>Path;
  getStartPathByTable:(table:Table)=>Path;
  getTableStartPathByPath:(model_path:Path)=>Path;

  isStartInDocument:(path:Path) => boolean;
  isEndInDocument:(model_path:Path, root_cell:Cell) => boolean;

  getDocumentStart:(root_cell:Cell) => Path;
  getDocumentEnd:(root_cell:Cell) => Path;
  movePathCharNum:(path:Path, x?:number)=>void;
  confirmExistence:(id:string, path:Path, editor:Editor)=>boolean
  checkGroupId:(editor:Editor, groupId:string, path:Path)=>boolean

}

const PathUtils: PathInterface = {

  /**
   * 对比 path, another 的前后关系，path 在 another 之前 -1，一致：0， 之后：1
   * @param path
   * @param another
   */

  compareFrontOrBack (path: Path, another: Path): -1 | 0 | 1 {
    const min = Math.min(path.length, another.length);
    for (let i = 0; i < min; i++) {
      if (path[i] < another[i]) return -1;
      if (path[i] > another[i]) return 1;
    }
    return 0;
  },

  /**
   * 两个path是否等值
   * @param path
   * @param another
   */

  equals (path: Path, another: Path): boolean {
    return path.toString() === another.toString();
  },

  /**
   * path 是否在 another 之后
   * @param path
   * @param another
   */

  isAfter (path: Path, another: Path): boolean {
    return this.compareFrontOrBack(path, another) === 1;
  },

  isValid (editor: Editor, path: Path, type: "modelPath" | "paraPath"): boolean {
    if (type === "modelPath") {
      return this.modelPathIsValid(editor, path);
    } else {
      return this.paraPathisValid(editor, path);
    }
  },

  paraPathisValid (editor: Editor, path: Path): boolean {
    const container = editor.current_cell.paragraph[path[0]];
    if (isTable(container) && path.length === 4) {
      const cell = container.children?.[path[1]];
      const cellParagraph = cell?.paragraph[path[2]] as Paragraph;
      return !!cellParagraph?.characters[path[3]] || cellParagraph?.characters.length === path[3];
    } else if (isParagraph(container) && path.length === 2) {
      return !!container.characters[path[1]] || (container.characters.length === path[1]);
    }
    return false;
  },

  modelPathIsValid (editor: Editor, path: Path): boolean {
    const container = editor.current_cell.children[path[0]];
    if (isTable(container) && path.length === 4) {
      const cell = container.children?.[path[1]];
      const cellRow = cell?.children[path[2]] as Row;
      return !!cellRow?.children[path[3]] || (cellRow?.children.length === path[3]);
    } else if (isRow(container) && path.length === 2) {
      return !!container.children[path[1]] || (container.children.length === path[1]);
    }
    return false;
  },

  confirmExistence (groupId:string, path:Path, editor:Editor):boolean {
    // 先根据id拿到group
    const root_cell = editor.root_cell;
    if (path.length === 2) {
      if (isTable(root_cell.children[path[0]])) return false;
      if (path[path.length - 1] === 0) {
        return this.checkGroupId(editor, groupId, path);
      }
      const content = root_cell.children[path[0]]?.children[path[1] - 1];
      if (content) {
        return this.checkGroupId(editor, groupId, path);
      } else { return false; }
    } else if (path.length === 4) {
      const table = root_cell.children[path[0]];
      if (isTable(table)) {
        if (path[path.length - 1] === 0) {
          return this.checkGroupId(editor, groupId, path);
        }
        const content = (table.children[path[1]] as Cell).children[path[2]]?.children[path[3] - 1];
        if (content) {
          return this.checkGroupId(editor, groupId, path);
        } else { return false; }
      } else {
        return false;
      }
    } else {
      return false;
    }
  },
  checkGroupId (editor:Editor, groupId:string, path:Path):boolean {
    let paraPath:Path = [];
    if (this.modelPathIsValid(editor, path)) {
      paraPath = editor.modelPath2ParaPath(path);
    }
    const paragraph = editor.current_cell.paragraph[paraPath[0]];
    if (paragraph) {
      return paragraph.group_id === groupId;
    } else {
      return false;
    }
  },
  /**
   * path 是否在 another 之前
   * @param path
   * @param another
   */

  isBefore (path: Path, another: Path): boolean {
    return this.compareFrontOrBack(path, another) === -1;
  },
  /**
   * value是否是path
   * @param value
   */

  isPath (value: any): value is Path {
    return (
      Array.isArray(value) &&
      (value.length === 0 || typeof value[0] === "number")
    );
  },

  /**
   *判断传入的path是否为一个表格中单元格路径
   * @param path view_path ,model_path,para_path都可用
   */

  isTablePath (path: Path): boolean {
    return path.length > 3;
  },

  /**
   * 判断坐标是否为行首
   * @param path 为model_path
   */

  isStartPathInRow (path: Path) {
    return path[path.length - 1] === 0;
  },
  /**
   * 判断是否为Cell中的第一行
   * @param path 为model_path
   */

  isCellFirstRow (path: Path) {
    return path[path.length - 2] === 0;
  },

  /**
   * 判断是否为Cell中的第一段
   * @param para_path
   */

  isCellFirstPara (para_path: Path) {
    return para_path[para_path.length - 2] === 0;
  },

  /**
   * 是否在行中间位置
   * @param model_path
   * @param row
   */

  isMiddlePathInRow (model_path: Path, row: Row) {
    return (
      model_path[model_path.length - 1] > 0 &&
      model_path[model_path.length - 1] < row.children.length
    );
  },

  /**
   * 判断坐标是否为行结尾（光标在行的末尾处）
   * @param path
   *  @param row
   */

  isEndPathInRow (path: Path, row: Row) {
    return row.children.length === path[path.length - 1];
  },

  /**
   * 判断某个path是否在某个cell的起始位置,此处的path必须到字符的偏移
   * @param path
   */

  isStartPathInCell (path: Path): boolean {
    return path[path.length - 2] === 0 && path[path.length - 1] === 0;
  },

  /**
   * 判断某个path是否在某个cell的起始位置,此处的path必须到字符的偏移
   * @param path
   * @param cell
   */

  isEndPathInCell (path: Path, cell: Cell): boolean {
    const char_index = path[path.length - 1];
    const row_index = path[path.length - 2];
    const last_row_index = cell.children.length - 1;
    const last_row = cell.children[last_row_index];
    // cell最后一个row肯定存在换行符
    return (
      row_index === last_row_index && char_index === last_row.children.length
    );
  },

  /**
   * 判断是否在一个单元格内 参数为model_path或者para_path
   * @param start_path 开始坐标
   * @param end_path 结束坐标
   */

  isSameCell (start_path: Path, end_path: Path): boolean {
    return (
      this.isTablePath(start_path) &&
     this.isTablePath(end_path) &&
      start_path[0] === end_path[0] &&
      start_path[1] === end_path[1]
    );
  },

  /**
   * 判断是否在一行内 参数为model_path
   * @param start_path 开始坐标
   * @param end_path 结束坐标
   */

  isSameRow (start_path: Path, end_path: Path): boolean {
    return (
      start_path.length === end_path.length &&
      start_path[0] === end_path[0] &&
      start_path[start_path.length - 2] === end_path[end_path.length - 2]
    );
  },

  /**
   * 判断是否为段落开始坐标
   * @param para_path
   */

  isStartPathInPara (para_path: Path) {
    return para_path[para_path.length - 1] === 0;
  },

  /**
   * 判断是否为段落末尾
   * @param para_path
   * @param para
   */

  isEndPathInPara (para_path: Path, para:Paragraph) {
    return para_path[para_path.length - 1] === para.characters.length - 1;
  },

  /**
   * 判断是否在段落中间位置
   * @param para_path
   * @param para
   */

  isMiddlePathInPara (para_path: Path, para:Paragraph) {
    return !this.isStartPathInPara(para_path) && !this.isEndPathInPara(para_path, para);
  },

  /**
   * 判断是否在同一个段落中
   * @param start_para_path
   * @param end_para_path
   */

  isSameParagraph (start_para_path: Path, end_para_path: Path) {
    return (
      start_para_path.length === end_para_path.length && start_para_path[start_para_path.length - 2] ===
      end_para_path[end_para_path.length - 2] &&
      (this.isSameCell(start_para_path, end_para_path) ||
          !PathUtils.isTablePath(start_para_path))
    );
  },

  /**
   * 判断是否为同一个表格
   * @param start_path
   * @param end_path
   */

  isSameTable (start_path: Path, end_path: Path) {
    return start_path[0] === end_path[0];
  },

  /**
   * 开始位置与结束位置都是表格（并且不是同一表格）
   * @param start_path
   * @param end_path
   */

  isTwoDiffTable (start_path: Path, end_path: Path):boolean {
    return (
      start_path[0] !== end_path[0] &&
      this.isTablePath(start_path) &&
      start_path.length === end_path.length
    );
  },

  /**
   * 判断是否为文档的头部
   * @param path model_path或者para_path
   */

  isStartInDocument (path:Path) {
    return path.every(item => item === 0);
  },

  /**
   * 判断是否为文档的尾部
   * @param model_path
   * @param cell
   */

  isEndInDocument (model_path:Path, cell:Cell) {
    const end_container = cell.children[cell.children.length - 1];
    if (model_path[0] !== cell.children.length - 1) {
      return false;
    }
    if (isTable(end_container)) {
      const path = this.getEndPathByTable(end_container);
      return this.equals(path, model_path);
    } else {
      return this.isEndPathInRow(model_path, end_container);
    }
  },

  /**
   * 向前或向后移动相应的字符位数 ，x为正则向前移动，负数为向后移动
   * @param path 任意路径
   * @param x 移动位数 ,默认向后移动一位
   */

  movePathCharNum (path:Path, x:number = 1) {
    path[path.length - 1] += x;
  },
  /**
   * 获取文档的开始坐标
   * @param root_cell
   */

  getDocumentStart (root_cell: Cell) {
    if (isRow(root_cell.children[0])) {
      return [0, 0];
    } else {
      return [0, 0, 0, 0];
    }
  },

  /**
   * 获取文档的结束坐标
   * @param root_cell
   */

  getDocumentEnd (root_cell:any) {
    const end_container:Row|Table = root_cell.children[root_cell.children.length - 1];
    if (isRow(end_container)) {
      return this.getEndPathByRow(end_container);
    } else {
      return this.getEndPathByTable(end_container);
    }
  },

  /**
   * 根据row获取这个行尾坐标
   * @param row
   */
  getEndPathByRow (row:Row) {
    return row.end_path;
  },
  /**
   * 根据row获取这个行首坐标
   * @param row
   */
  getStartPathByRow (row:Row) {
    return row.start_path;
  },
  /**
   * 根据一个modelPath获取行首坐标
   * @param row
   */
  getRowStartPathByPath (model_path:Path) {
    const new_path = [...model_path];
    new_path[new_path.length - 1] = 0;
    return new_path;
  },

  getEndParaPathByPara (para:Paragraph) {
    return para.end_para_path;
  },
  getStartParaPathByPara (para:Paragraph) {
    return para.start_para_path;
  },
  getParagraphStartParaPathByPath (para_path:Path) {
    const new_path = [...para_path];
    new_path[new_path.length - 1] = 0;
    return new_path;
  },
  getEndPathByCell (cell:Cell) {
    return cell.end_path;
  },
  getStartPathByCell (cell:Cell) {
    return cell.start_path;
  },
  getCellStartPathByPath (model_path:Path) {
    const new_path = [...model_path];
    new_path[new_path.length - 1] = 0;
    new_path[new_path.length - 2] = 0;
    return new_path;
  },
  getEndPathByTable (table:Table) {
    return table.end_path;
  },
  getStartPathByTable (table:Table) {
    return table.start_path;
  },
  getTableStartPathByPath (model_path:Path) {
    const new_path = [...model_path];
    new_path.slice(0, 2).concat([0, 0]);
    return new_path;
  }

};

export default PathUtils;
