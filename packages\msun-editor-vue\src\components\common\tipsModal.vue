<template>
  <a-modal
    class="editor-searchModal-x"
    v-model="visible"
    :width="width"
    :mask="masks"
    :footer="null"
    :maskClosable="false"
    @cancel="cancel"
    destroyOnClose
  >
    <div slot="title" class="editor-title-tipsmodal">
      <div style="font-size: 14px">{{ title }}</div>
    </div>
    <div class="editor-modal-tipsmodal">
      <div class="editor-modal-content-tipsmodal">
        <icon-common icon="icon-jingshi"></icon-common>{{ datas }}
      </div>
      <a-button type="primary" @click="cancel">确定</a-button>
    </div>
  </a-modal>
</template>

<script>
import "../../assets/icon/iconfont";
import iconCommon from "./iconCommon.vue";
export default {
  name: "modal",
  components: { iconCommon },
  data() {
    return {
      title: "查找替换",
      width: 424,
      visible: false,
      masks: false,
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    datas: {
      type: String,
      default: "",
    },
  },
  watch: {
    show(e) {
      this.visible = e;
    },
    immediate: true,
  },
  mounted() {},
  methods: {
    cancel() {
      this.$emit("tipCancel");
    },
  },
};
</script>

<style scoped>
.editor-searchModal-x /deep/ .ant-modal-header {
  padding: 8px 10px;
}
.editor-searchModal-x /deep/ .ant-modal-close-x {
  height: 40px;
  line-height: 40px;
}
.editor-searchModal-x /deep/ .ant-modal-body {
  text-align: center;
  padding: 12px;
}
.editor-searchModal-x /deep/.ant-modal-content {
  box-shadow: 0 0px 12px rgb(0 0 0 / 40%);
}
.editor-searchModal-x /deep/ .ant-modal-footer {
  border-top: none;
  padding: 0 16px 10px 16px;
}

.editor-searchModal-x /deep/ .ant-modal-wrap {
  z-index: 10000;
}

.editor-title-tipsmodal {
  height: 23px;
  display: flex;
}
.editor-modal-tipsmodal {
  width: 400px;
  background-color: rgb(255, 255, 255);
  border: 1px solid rgb(255, 255, 255);
  padding: 10px;
  color: #000;
}
.editor-modal-content-tipsmodal {
  height: 50px;
  line-height: 50px;
  font-size: 16px;
  margin-bottom: 10px;
}
</style>
