import Editor from "./Editor";

// TODO 目前先假设永远都会只有一个 FocusAround 实例
export default class FocusAround {
  editor: Editor;

  origin: [number, number] = [0, 0]; // 原点位置 永远相对于 canvas 画布原点
  width: number;
  height: number;

  constructor (editor: Editor, origin: [number, number], width: number, height: number) {
    this.editor = editor;
    this.origin = [...origin];
    this.width = width;
    this.height = height;
  }

  draw () {
    console.log("绘制");
  }
}
