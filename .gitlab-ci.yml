stages:
  - build
  - deploy-dev
  - tag
  - deploy-prod


default:
  tags:
    - msun-sheet
  # cache: &cache
  #   key:
  #     files:
  #       - pnpm-lock.yaml
  #   paths:
  #     - .pnpm-store
  #   policy: pull

  before_script:
    - git fetch origin main
    - git checkout main
    - git pull
    - pnpm install
    # - npm i -g pnpm
    # - pnpm config set store-dir .pnpm-store

# push到main上后，先build
build_job:
  stage: build
  only:
    refs:
      - main
  # cache:
  #   <<: *cache
  #   policy: push
  artifacts:
    paths:    # 把build出来的dist存起来给后面用
      - packages/msun-lib-editor-common/dist/
      - packages/msun-lib-editor-transform/dist/
      - packages/msun-editor-base/dist/
      - packages/msun-editor-vue/dist/
      - packages/msun-lib-editor-design/dist/

      # - .pnpm-store
    expire_in: 1 hr

  script:
    - git checkout main
    - git pull --tag -f
    - pnpm install
    - pnpm run build

# build成功后发布到dev环境

publish-dev:
  stage: deploy-dev
  only:
    refs:
      - main
  dependencies:
    - build_job

  script:
    - pnpm publish -r --registry=$REGISTRY_DEV --no-git-checks

# 如果main的package.json没有变更，则到这里结束

# 如果有变更，则继续更新tag

put-tag:
  tags:
    - msun-sheet
  stage: tag
  only:
    refs:
      - main
    changes:
      - package.json

  script:
    - git remote set-url origin https://gitlab-ci-token:$MY_CI_TOKEN@$CI_SERVER_HOST/$CI_PROJECT_PATH.git
    - git checkout main
    - git pull --tag -f

    - VERSION=$(node -p "require('./package.json').version")
    - git tag "tag-release-V${VERSION}"
    - git push --tag

# tag成功更新后三个环境一起发版

# re-publish-dev:
#   stage: deploy-prod
#   only:
#     refs:
#       - main
#     changes:
#       - package.json
#   dependencies:
#     - build_job

#   script:
#     - pnpm publish -r --registry=$REGISTRY_DEV

# publish-test:
#   stage: deploy-prod
#   only:
#     refs:
#       - main
#     changes:
#       - package.json
#   dependencies:
#     - build_job

#   script:
#     - pnpm publish -r --registry=$REGISTRY_TEST --no-git-checks

# publish-prod:
#   stage: deploy-prod
#   only:
#     refs:
#       - main
#     changes:
#       - package.json
#   dependencies:
#     - build_job

#   script:
#     - pnpm publish -r --registry=$REGISTRY_PROD --no-git-checks
