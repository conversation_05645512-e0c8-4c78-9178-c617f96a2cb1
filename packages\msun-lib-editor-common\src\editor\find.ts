// 
import { is<PERSON><PERSON><PERSON> } from "./Utils";

class AhoCorasick {
    goto: any;
    output: any;
    fail: any;
    constructor(keywords: any) {
      this.goto = {};
      this.output = {};
      this.fail = {};
      this.buildGoto(keywords);
      this.buildFailure();
    }

    buildGoto(keywords: any) {
      let newState = 0; // 状态编号 从 0 开始

      for (let keyword of keywords) {
        let currentState = 0;

        for (let char of keyword) {
          if (!this.goto[currentState]) {
          // 如果当前状态没有东西就初始化一个空对象
            this.goto[currentState] = {};
          }
          if (!this.goto[currentState][char]) {
          // 如果当前状态下的对象里边 没有当前字符的话 就用下一个状态编号
            this.goto[currentState][char] = ++newState;
          }
          currentState = this.goto[currentState][char]; // 更新当前状态的值
        }

        if (!this.output[currentState]) {
          this.output[currentState] = [];
        }
        this.output[currentState].push(keyword);
      }
    }

    buildFailure() {
      let queue = [];
      for (let key in this.goto[0]) {
        let state = this.goto[0][key];
        this.fail[state] = 0;
        queue.push(state);
      }

      while (queue.length > 0) {
        let state: any = queue.shift();

        for (let key in this.goto[state]) {
          let failState = this.fail[state];
          let transitionState = this.goto[state][key];

          while (failState !== 0 && !this.goto[failState]?.[key]) {
            failState = this.fail[failState];
          }
          if (this.goto[failState] && this.goto[failState][key]) {
            this.fail[transitionState] = this.goto[failState][key];
          } else {
            this.fail[transitionState] = 0;
          }

          if (!this.output[transitionState]) {
            this.output[transitionState] = [];
          }
          this.output[transitionState] = this.output[transitionState].concat(
            this.output[this.fail[transitionState]] || []
          );

          queue.push(transitionState);
        }
      }
    }

    search(paragraph: any) {
      let currentState = 0;
      let results = [];
      let charIndices = []; // 记录字符的真实索引

      for (let i = 0; i < paragraph.length; i++) {
        if (isCharacter(paragraph[i]) &&  paragraph[i].value && paragraph[i].field_position === "normal") {
          charIndices.push(i);
          let char = paragraph[i].value;

          while (currentState !== 0 && (!this.goto[currentState] || !this.goto[currentState][char])) {
            currentState = this.fail[currentState];
          }

          if (this.goto[currentState] && this.goto[currentState][char]) {
            currentState = this.goto[currentState][char];
          }

          if (this.output[currentState]) {
            for (let keyword of this.output[currentState]) {
              results.push({
                keyword: keyword,
                start: charIndices[charIndices.length - keyword.length],
                end: charIndices[charIndices.length - 1]
              });
            }
          }
        }
      }

      return results;
    }
}

export default function findKeywordsInParagraphs(keywords: any, paragraphs: any) {
  let ac = new AhoCorasick(keywords);
  let results = [];

  for (let i = 0; i < paragraphs.length; i++) {
    let matches = ac.search(paragraphs[i]);
    for (let match of matches) {
      results.push({
        paragraphIndex: i,
        keyword: match.keyword,
        start: match.start,
        end: match.end
      });
    }
  }

  return results;
}