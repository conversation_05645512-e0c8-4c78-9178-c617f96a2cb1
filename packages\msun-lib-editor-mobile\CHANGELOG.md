## 2.30.15

## 10.26.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.26.1

## 10.26.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.26.0

## 10.25.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.25.1

## 10.25.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.25.0

## 10.24.6

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.24.6

## 10.24.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.24.5

## 10.24.4

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.24.4

## 10.24.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.24.3

## 10.24.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.24.2

## 10.24.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.24.1

## 10.24.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.24.0

## 10.23.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.23.2

## 10.23.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.23.1

## 10.23.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.23.0

## 10.22.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.22.2

## 10.22.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.22.1

## 10.22.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.22.0

## 10.21.11

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.11

## 10.21.10

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.10

## 10.21.9

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.9

## 10.21.8

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.8

## 10.21.7

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.7

## 10.21.6

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.6

## 10.21.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.5

## 10.21.4

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.4

## 10.21.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.3

## 10.21.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.2

## 10.21.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.21.1

## 10.21.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.21.0

## 10.20.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.20.1

## 10.20.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.20.0

## 10.19.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.19.2

## 10.19.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.19.1

## 10.19.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.19.0

## 10.18.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.18.0

## 10.17.6

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.17.6

## 10.17.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.17.5

## 10.17.4

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.17.4

## 10.17.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.17.3

## 10.17.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.17.2

## 10.17.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.17.1

## 10.17.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.17.0

## 10.16.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.16.5

## 10.16.4

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.16.4

## 10.16.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.16.3

## 10.16.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.16.2

## 10.16.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.16.1

## 10.16.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.16.0

## 10.15.6

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.15.6

## 10.15.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.15.5

## 10.15.4

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.15.4

## 10.15.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.15.3

## 10.15.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.15.2

## 10.15.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.15.1

## 10.15.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.15.0

## 10.14.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.14.5

## 10.14.4

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.14.4

## 10.14.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.14.3

## 10.14.2

## 10.13.5

### Patch Changes

-
- Updated dependencies

  - msun-editor@10.14.2

  - msun-editor@10.13.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.14.1

## 10.14.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.14.0
  - msun-editor@10.13.4

## 10.13.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.13.3

## 10.13.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.13.2

## 10.13.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.13.1

## 10.13.0

### Minor Changes

-

## 10.12.10

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.12.10

## 10.12.9

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.12.9

## 10.12.8

### Patch Changes

- Updated dependencies
  - msun-editor@10.13.0

## 10.12.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.12.5

## 10.12.4

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.12.4

## 10.12.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.12.3

## 10.12.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.12.2

## 10.12.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.12.1

## 10.12.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.12.0

## 10.11.9

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.9

## 10.11.8

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.8

## 10.11.7

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.7

## 10.11.6

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.6

## 10.11.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.5

## 10.11.4

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.4

## 10.11.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.3

## 10.11.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.2

## 10.11.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.11.1

## 10.11.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.11.0

## 10.10.12

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.10.16

## 10.10.11

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.10.12

## 10.10.10

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.10.10

## 10.10.9

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.10.9

## 10.10.8

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.10.8

## 10.10.7

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.10.7

## 10.10.6

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.10.6

## 10.9.27

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.27

## 10.9.26

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.10.5

## 10.9.26

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.25

## 10.9.24

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.24

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.23

## 10.9.22

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.22

## 10.9.21

-
- Updated dependencies
  - msun-editor@10.9.21

## 10.9.20

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.20

## 10.9.19

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.19

## 10.9.18

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.18

## 10.9.17

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.17

## 10.9.16

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.16

## 10.9.15

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.15

## 10.9.14

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.14

## 10.9.13

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.13

## 10.9.12

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.9.12

## 10.8.23

### Patch Changes

-
- Updated dependencies

  - msun-editor@10.9.11

  - msun-editor@10.8.23

-
- Updated dependencies

  - msun-editor@10.9.10

## 10.9.9

-
- Updated dependencies

  - msun-editor@10.9.6

## 10.9.5

### Patch Changes

- Updated dependencies
  - msun-editor@10.8.20

## 10.8.19

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.19

## 10.8.18

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.18

## 10.8.17

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.17

## 10.8.16

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.16

## 10.8.15

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.15

## 10.8.14

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.14

## 10.8.13

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.13

## 10.8.12

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.12

## 10.8.11

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.11

## 10.8.10

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.10

## 10.8.9

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.9

## 10.8.8

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.8

## 10.8.7

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.8.7

## 10.7.12

### Patch Changes

-
- Updated dependencies

  - msun-editor@10.8.6

## 10.8.5

### Patch Changes

- Updated dependencies

  - msun-editor@10.8.5

## 10.8.4

=======

- msun-editor@10.7.11

## 10.7.10

> > > > > > > release-V1.55.0

### Patch Changes

-
- Updated dependencies

  - msun-editor@10.8.4

## 10.8.3

=======

- msun-editor@10.7.10

## 10.7.9

> > > > > > > release-V1.55.0

### Patch Changes

-
- Updated dependencies

  - # msun-editor@10.8.3
  - msun-editor@10.7.9
    > > > > > > > release-V1.55.0

## 10.6.12

### Patch Changes

-
- Updated dependencies

  - msun-editor@10.7.8

## 10.7.7

-
- Updated dependencies
  - msun-editor@10.6.11

## 10.6.10

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.6.10

## 10.6.9

### Patch Changes

-
- Updated dependencies

  - msun-editor@10.7.5

## 10.7.4

-
- Updated dependencies
  - msun-editor@10.6.8

## 10.6.7

### Patch Changes

-
- Updated dependencies

  - msun-editor@10.6.7

## 10.6.6

- msun-editor@10.5.15

## 10.5.14

- Updated dependencies

  - msun-editor@10.6.6

## 10.6.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.6.5

## 10.6.4

### Patch Changes

-
- Updated dependencies
  - # msun-editor@10.6.4
  - msun-editor@10.5.14
    > > > > > > > release-V1.53.0

## 10.5.13

### Patch Changes

-
- Updated dependencies

  - msun-editor@10.6.3

## 10.6.2

-
- Updated dependencies
  - msun-editor@10.5.12

## 10.5.11

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.11

## 10.5.10

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.10

## 10.5.9

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.9

## 10.5.8

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.8

## 10.5.7

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.7

## 10.5.6

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.6

## 10.5.5

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.5

## 10.4.25

### Patch Changes

- 表格撑满一页的问题
- Updated dependencies
  - msun-editor@10.4.25

## 10.4.24

### Patch Changes

- 边框问题
- Updated dependencies
  - msun-editor@10.4.24

## 10.4.23

### Patch Changes

- 表格相关
- Updated dependencies
  - msun-editor@10.4.23

## 10.4.22

### Patch Changes

- 时间框修改
- Updated dependencies
  - msun-editor@10.4.22

## 10.4.21

### Patch Changes

- 表格固定样式不允许双击表格线
- Updated dependencies
  - msun-editor@10.4.21

## 10.4.20

### Patch Changes

- 重新发版
- Updated dependencies
  - msun-editor@10.4.20

## 10.4.19

### Patch Changes

- contentborder 版本兼容
- Updated dependencies
  - msun-editor@10.4.19

## 10.4.18

### Patch Changes

- 边框修改
- Updated dependencies
  - msun-editor@10.4.18

## 10.4.17

### Patch Changes

- 表格属性问题修改
- Updated dependencies
  - msun-editor@10.4.17

## 10.4.16

### Patch Changes

- 新增 page 边框
- Updated dependencies
  - msun-editor@10.4.16

## 10.4.15

### Patch Changes

- 表格撑满整页修改
- Updated dependencies
  - msun-editor@10.4.15

## 10.4.14

### Patch Changes

- 优化代码
- Updated dependencies
  - msun-editor@10.4.14

## 10.4.13

### Patch Changes

- 最终发版
- Updated dependencies
  - msun-editor@10.4.13

## 10.4.12

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.4

## 10.5.3

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.3

## 10.5.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.2

## 10.5.1

### Patch Changes

-
- Updated dependencies
  - msun-editor@10.5.1

## 10.5.0

### Minor Changes

-

### Patch Changes

- Updated dependencies
  - msun-editor@10.5.0

## 10.4.11

### Patch Changes

- trans 服务端带公式病历记录转换失败问题
- Updated dependencies
  - msun-editor@10.4.11

## 10.4.10

### Patch Changes

- 修复文本域自动化选区取消问题
- Updated dependencies
  - msun-editor@10.4.10

## 10.4.9

### Patch Changes

- 文本域自动化 bug 修改
- Updated dependencies
  - msun-editor@10.4.9

## 10.4.8

### Patch Changes

- 解决移动端滚动问题
- Updated dependencies
  - msun-editor@10.4.8

## 10.4.7

### Patch Changes

- 默认打印报错处理
- Updated dependencies
  - msun-editor@10.4.7

## 10.4.6

### Patch Changes

- 移动端缩放之后点击位置不准确的问题
- Updated dependencies
  - msun-editor@10.4.6

## 10.4.5

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.4.5

## 10.4.4

### Patch Changes

- y
- Updated dependencies
  - msun-editor@10.4.4

## 10.4.3

### Patch Changes

- o
- Updated dependencies
  - msun-editor@10.4.3

## 10.4.2

### Patch Changes

- design 文档设计器常用语检索字段时滚动条问题
- Updated dependencies
  - msun-editor@10.4.2

## 10.4.1

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.4.1

## 10.4.0

### Minor Changes

- 52 发版

### Patch Changes

- Updated dependencies
  - msun-editor@10.4.0

## 10.3.18

### Patch Changes

- msun-editor@10.3.18

## 10.3.17

### Patch Changes

- 死循环问题修改
- Updated dependencies
  - msun-editor@10.3.17

## 10.3.16

### Patch Changes

- 筏板错误修改
- Updated dependencies
  - msun-editor@10.3.16

## 10.3.15

### Patch Changes

- vant 修改
- Updated dependencies
  - msun-editor@10.3.15

## 10.3.14

### Patch Changes

- 测试
- Updated dependencies
  - msun-editor@10.3.14

## 10.3.13

### Patch Changes

- bug 测试
- Updated dependencies
  - msun-editor@10.3.13

## 10.3.12

### Patch Changes

- 双面打印报错处理
- Updated dependencies
  - msun-editor@10.3.12

## 10.3.11

### Patch Changes

- 表格自动替换排版问题
- Updated dependencies
  - msun-editor@10.3.11

## 10.3.10

### Patch Changes

- 重新发
- Updated dependencies
  - msun-editor@10.3.10

## 10.3.9

### Patch Changes

- tab 跳转兼容老数据
- Updated dependencies
  - msun-editor@10.3.9

## 10.3.8

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.3.8

## 10.3.7

### Patch Changes

- vant 相关
- Updated dependencies
  - msun-editor@10.3.7

## 10.3.6

### Patch Changes

- vant 测试
- Updated dependencies
  - msun-editor@10.3.6

## 10.3.5

### Patch Changes

- 重新发版
- Updated dependencies
  - msun-editor@10.3.5

## 10.3.4

### Patch Changes

- vant 降低版本
- Updated dependencies
  - msun-editor@10.3.4

## 10.3.3

### Patch Changes

- 文本域占位打印问题
- Updated dependencies
  - msun-editor@10.3.3

## 10.3.2

### Patch Changes

- 字段树检索卡顿问题优化
- Updated dependencies
  - msun-editor@10.3.2

## 10.3.1

### Patch Changes

- 代码走查后发版
- Updated dependencies
  - msun-editor@10.3.1

## 10.3.0

### Minor Changes

- 51 第一次发版

### Patch Changes

- Updated dependencies
  - msun-editor@10.3.0

## 10.2.27

### Patch Changes

- 文本域下拉列表确定按钮位置调整
- Updated dependencies
  - msun-editor@10.2.27

## 10.2.26

### Patch Changes

- 双面打印报错处理
- Updated dependencies
  - msun-editor@10.2.26

## 10.1.32

### Patch Changes

- 双面打印报错解决
- Updated dependencies
  - msun-editor@10.1.32

## 10.1.31

### Patch Changes

- 解决分组上边的空行
- Updated dependencies
  - msun-editor@10.2.25

## 10.2.24

### Patch Changes

- 1
- Updated dependencies
  - msun-editor@10.2.24

## 10.2.23

### Patch Changes

- 中草药修改
- Updated dependencies
  - msun-editor@10.2.23

## 10.2.22

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.2.22

## 10.2.21

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.2.21

## 10.2.20

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.2.20

## 10.2.19

### Patch Changes

- 发版测试
- Updated dependencies
  - msun-editor@10.2.19

## 10.2.18

### Patch Changes

- 发版测试
- Updated dependencies
  - msun-editor@10.2.18

## 10.2.17

### Patch Changes

- exports 问题解决
- Updated dependencies
  - msun-editor@10.2.17

## 10.2.16

### Patch Changes

- base 还原
- Updated dependencies
  - msun-editor@10.2.16

## 10.2.15

### Patch Changes

- base 增加构建 cjs
- Updated dependencies
  - msun-editor@10.2.15

## 10.2.14

### Patch Changes

- 表格自动填充增加文本域自动填充图片
- Updated dependencies
  - msun-editor@10.2.14

## 10.2.13

### Patch Changes

- 一些 bug 解决
- Updated dependencies
  - msun-editor@10.2.13

## 10.2.12

### Patch Changes

- 表单模式下，上下键进入表格光标位置不正确问题及解决
- Updated dependencies
  - msun-editor@10.2.12

## 10.2.11

### Patch Changes

- 快速录入模式和表格跳转冲突问题解决
- Updated dependencies
  - msun-editor@10.2.11

## 10.2.10

### Patch Changes

- skipmode 保存相关
- Updated dependencies
  - msun-editor@10.2.10

## 10.2.9

### Patch Changes

- tab 键相关功能
- Updated dependencies
  - msun-editor@10.2.9

## 10.2.8

### Patch Changes

- 点击图片出发 contentchanged 的 bug 修改
- Updated dependencies
  - msun-editor@10.2.8

## 10.2.7

### Patch Changes

- 修改说明图标颜色
- Updated dependencies
  - msun-editor@10.2.7

## 10.2.6

### Patch Changes

- 50 发版测试
- Updated dependencies
  - msun-editor@10.2.6

## 10.2.5

### Patch Changes

- 50 编译 bug
- Updated dependencies
  - msun-editor@10.2.5

## 10.2.4

### Patch Changes

- exports 导致的发版错误修改
- Updated dependencies
  - msun-editor@10.2.4

## 10.2.3

### Patch Changes

- modelPath 没有转 paraPath 导致的 tab 键跳转报错处理
- Updated dependencies
  - msun-editor@10.2.3

## 10.2.2

### Patch Changes

- 50 发版
- Updated dependencies
  - msun-editor@10.2.2

## 10.2.1

### Patch Changes

- 50 发版
- 50 发版
- Updated dependencies
- Updated dependencies
  - msun-editor@10.2.1

## 10.2.0

### Minor Changes

- 50 版本发版

### Patch Changes

- Updated dependencies
  - msun-editor@10.2.0

## 10.1.27

### Patch Changes

- design 被误还原代码修正
- Updated dependencies
  - msun-editor@10.1.27

## 10.1.26

### Patch Changes

- design 相关
- Updated dependencies
  - msun-editor@10.1.26

## 10.1.25

### Patch Changes

- 痕迹对比修复
- Updated dependencies
  - msun-editor@10.1.25

## 10.1.24

### Patch Changes

- 解决开拼音之后插入红色文字再改回黑色，输入中文为红色的问题
- Updated dependencies
  - msun-editor@10.1.24

## 10.1.23

### Patch Changes

- 智能模版引用报错
- Updated dependencies
  - msun-editor@10.1.23

## 10.1.22

### Patch Changes

- 四个一行，放不下整体往下移动
- Updated dependencies
  - msun-editor@10.1.22

## 10.1.21

### Patch Changes

- 分组痕迹对比
- Updated dependencies
  - msun-editor@10.1.21

## 10.1.20

### Patch Changes

- design 将 vue 打到包里
- Updated dependencies
  - msun-editor@10.1.20

## 10.1.19

### Patch Changes

- 发版测试
- Updated dependencies
  - msun-editor@10.1.19

## 10.1.18

### Patch Changes

- 干掉 vue 的 debgger
- Updated dependencies
  - msun-editor@10.1.18

## 10.1.17

### Patch Changes

- Updated dependencies
  - msun-editor@10.1.17

## 10.1.16

### Patch Changes

- 分组痕迹对比两个分组都不存在的情况处理
- Updated dependencies
  - msun-editor@10.1.16

## 10.1.15

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.1.15

## 10.1.14

### Patch Changes

- 四个一行增加返回值
- Updated dependencies
  - msun-editor@10.1.14

## 10.1.13

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.1.13

## 10.1.12

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.1.12

## 10.1.11

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.1.11

## 10.1.10

### Patch Changes

- Updated dependencies
  - msun-editor@10.1.10

## 10.1.9

### Patch Changes

- 暴露初始化数据集选项接口
- Updated dependencies
  - msun-editor@10.1.9

## 10.1.8

### Patch Changes

- 发版修改
  - msun-editor@10.1.8

## 10.1.7

### Patch Changes

- 保存数据错误解决
- Updated dependencies
  - msun-editor@10.1.7

## 10.1.6

### Patch Changes

- 发版测试
- Updated dependencies
  - msun-editor@10.1.6

## 10.1.5

### Patch Changes

- 发版 bug 修改
- Updated dependencies
  - msun-editor@10.1.5

## 10.1.4

### Patch Changes

- 自定义批注引用接口，自定义大小存储再打开配置重载报错
- Updated dependencies
  - msun-editor@10.1.4

## 10.1.3

### Patch Changes

- 相同的图片进入痕迹对比的 bug 修改
- Updated dependencies
  - msun-editor@10.1.3

## 10.1.2

### Patch Changes

- msun-editor@10.1.2

## 10.1.1

### Patch Changes

- 去掉了 js-base64 和 pako
- Updated dependencies
  - msun-editor@10.1.1

## 10.1.0

### Minor Changes

- 49 第一次发版

### Patch Changes

- Updated dependencies
  - msun-editor@10.1.0

## 10.0.20

### Patch Changes

- msun-editor@10.0.20

## 10.0.19

### Patch Changes

- Updated dependencies
  - msun-editor@10.0.19

## 10.0.18

### Patch Changes

- Updated dependencies
  - msun-editor@10.0.18

## 10.0.17

### Patch Changes

- M2V 中的 this 错误修改
  - msun-editor@10.0.17

## 10.0.16

### Patch Changes

- 修改 calss 名
- Updated dependencies
  - msun-editor@10.0.16

## 10.0.15

### Patch Changes

- 修改 class 名
- Updated dependencies
  - msun-editor@10.0.15

## 10.0.14

### Patch Changes

- 解决没有 testrawdata vue 上展示数据报错的问题
- Updated dependencies
  - msun-editor@10.0.14

## 10.0.13

### Patch Changes

- 修改点击滚动条后的事件逻辑
- 修改了编辑器 click 的出发调教
  - msun-editor@10.0.13

## 10.0.12

### Patch Changes

- msun-editor@10.0.12

## 10.0.11

### Patch Changes

- msun-editor@10.0.11

## 10.0.10

### Patch Changes

- 增加真实版本号以及 M2V 分组分页内容判断
  - msun-editor@10.0.10

## 10.0.9

### Patch Changes

- msun-editor@10.0.9

## 10.0.8

### Patch Changes

- transform 不使用 editor.raw 使用传入的 rawData
- Updated dependencies
  - msun-editor@10.0.8

## 10.0.7

### Patch Changes

- 10.0.7 发版测试
- Updated dependencies
  - msun-editor@10.0.7

## 10.0.6

### Patch Changes

- 全部加上 exports
- Updated dependencies
  - msun-editor@10.0.6

## 10.0.5

### Patch Changes

- 去掉所有的 exports 给 pacs 用
- Updated dependencies
  - msun-editor@10.0.5

## 10.0.4

### Patch Changes

- 改了 package.json 的 exports
  - msun-editor@10.0.4

## 10.0.3

### Patch Changes

- 选区删除 bug 需修改
- Updated dependencies
  - msun-editor@10.0.3

## 10.0.2

### Patch Changes

- 升级版本号
- Updated dependencies
  - msun-editor@10.0.2

## 10.0.1

### Patch Changes

- 48 版本新代码发版
- Updated dependencies
  - msun-editor@10.0.1

## 10.0.1

## 2.32.25

### Patch Changes

- 升级一下版本号
- Updated dependencies
  - msun-editor@2.32.25

## 2.32.24

### Patch Changes

- Updated dependencies
  - msun-editor@2.32.24

## 2.32.23

### Patch Changes

- a
- Updated dependencies
  - msun-editor@10.0.1
  - msun-editor@2.32.23

## 2.32.22

### Patch Changes

- 重新发版
- Updated dependencies
  - msun-editor@2.32.22

## 2.32.21

### Patch Changes

- 重置参数位置和作用范围修改
- Updated dependencies
  - msun-editor@2.32.21

## 2.32.20

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.32.20

## 2.32.19

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.32.19

## 2.32.18

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.32.18

## 2.32.17

### Patch Changes

- 修复单选框生成 pdf 不显示的 bug

## 2.32.16

### Patch Changes

- 光标不存在报错问题解决
- Updated dependencies
  - msun-editor@2.32.16

## 2.32.15

### Patch Changes

- 升级版本
- Updated dependencies
  - msun-editor@2.32.15

## 2.32.14

### Patch Changes

- 升级版本
- Updated dependencies
  - msun-editor@2.32.14

## 2.32.13

### Patch Changes

- Updated dependencies
  - msun-editor@2.32.13

## 2.32.12

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.32.12

## 2.32.11

### Patch Changes

- 修复文本域最大值最小值设置问题，半个文本域自动修复
- Updated dependencies
  - msun-editor@2.32.11

## 2.32.10

### Patch Changes

- Updated dependencies
  - msun-editor@2.32.10

## 2.32.9

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.32.8

## 2.32.8

### Patch Changes

- 5
- Updated dependencies
  - msun-editor@2.32.8

## 2.32.7

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.32.7

## 2.32.6

### Patch Changes

- 1
- Updated dependencies
  - msun-editor@2.32.6

## 2.32.5

### Patch Changes

- 解决了无边框模式
- Updated dependencies
  - msun-editor@2.32.5

## 2.32.4

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.32.4

## 2.31.26

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.32.3

## 2.31.25

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.31.25

## 2.31.24

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.31.24

## 2.31.23

### Patch Changes

- 啊啊
- Updated dependencies
  - msun-editor@2.31.23

## 2.31.22

### Patch Changes

- Updated dependencies
  - msun-editor@2.31.22

## 2.31.21

### Patch Changes

- Updated dependencies
  - msun-editor@2.31.21

## 2.31.20

### Patch Changes

- Updated dependencies
  - msun-editor@2.31.20

## 2.31.19

### Patch Changes

- 锚点文本域的若干修改，代码走查的错误修改
- Updated dependencies
  - msun-editor@2.31.19

## 2.31.18

### Patch Changes

- 1.图片编辑的拖杆修改 2.修复文本域校验
- Updated dependencies
  - msun-editor@2.31.18

## 2.31.17

### Patch Changes

- 1
- Updated dependencies
  - msun-editor@2.31.17

## 2.31.16

### Patch Changes

- 1
- Updated dependencies
  - msun-editor@2.31.16

## 2.31.15

### Patch Changes

- 尝试发一版新版批注的
- Updated dependencies
  - msun-editor@2.31.15

## 2.31.14

### Patch Changes

- 1.段落重排 2.文本域时间计算公式 3.自定义页面默认大小
- Updated dependencies
  - msun-editor@2.31.14

## 2.31.13

### Patch Changes

- 正序逆序保存问题修改以及单元格背景颜色问题修改
- Updated dependencies
  - msun-editor@2.31.13

## 2.31.12

### Patch Changes

- Updated dependencies
  - msun-editor@2.31.12

## 2.31.11

### Patch Changes

- Updated dependencies
  - msun-editor@2.31.11

## 2.31.10

### Patch Changes

- 更新了 c++打印的线条样式
- Updated dependencies
  - msun-editor@2.31.10

## 2.31.9

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.31.9

## 2.31.8

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.31.8

## 2.31.7

### Patch Changes

- 啊
- 啊
- Updated dependencies
- Updated dependencies
  - msun-editor@2.31.7

## 2.31.6

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.31.6

## 2.31.5

### Patch Changes

- a
- Updated dependencies
  - msun-editor@2.31.5

## 2.31.4

### Patch Changes

- sad
- Updated dependencies
  - msun-editor@2.31.4

## 2.31.3

### Patch Changes

- 去掉 exports 之后的发版测试
- Updated dependencies
  - msun-editor@2.31.3

## 2.31.2

### Patch Changes

-
- Updated dependencies
  - msun-editor@2.31.2

## 2.31.1

### Patch Changes

- 46 monorepo 第一次发版
- Updated dependencies
  - msun-editor@2.31.1
    `2023-11-20`
- 直接双面打印不生效 bug 修改

## 2.31.0

`2023-11-16`

- 46 分支发版

#

## 2.30.14

`2023-11-10`

- 适配 base

## 2.30.13

`2023-11-10`

- 适配 base

## 2.30.11

`2023-11-09`

- 适配 base

## 2.30.10

`2023-11-06`

- 适配 base

## 2.30.9

`2023-11-05`

- 适配 base

## 2.30.8

`2023-11-03`

- 打印报错
- 点击清除本地配置后直接点系统打印报错问题
- 痕迹对比颜色修改

## 2.30.7

`2023-11-02`

- 文本域打印边框功能
- 增加了配置记忆功能
- 打印机记忆功能修改
- 新增文本域对齐功能

## 2.30.1

`2023-10-23`

- 适配 base

## 2.30.0

`2023-10-16`

- 升级版本号

## 2.29.15

`2023-10-16`

- base 上已经从 editor 上挪到了 internal 上的方法,vue 上进行修改调用方式
- 下拉框编辑选项样式调整
- 新增插入矩形
- 新增水平线弹出框类型
- 痕迹对比测试按钮问题

## 2.29.22

`2023-11-03`

- 文本域打印边框功能

## 2.29.20

`2023-10-25`

- 增加批量打印双面打印功能

## 2.29.19

`2023-10-20`

- c++批量打印超过 20 份乱序问题

## 2.29.18

`2023-10-20`

- 适配 base

## 2.29.17

`2023-10-16`

- 和 45 做分割的一个版本

## 2.29.16

`2023-10-16`

- 适配 base

## 2.29.14

`2023-10-12`

- 适配 base

## 2.29.13

`2023-10-12`

- 增加网格线切换

## 2.29.12

`2023-10-11` -双击没有批注的文字报错

## 2.29.11

`2023-10-10`

- 文本域提示信息在顶部时展示在下方

## 2.29.10

`2023-10-10`

- 打开批注列表之后 canvas 的尺寸没有响应变化修改
- 批注列表关闭的时候也要更新一下 canvas 的尺寸
- 文本域提示信息在顶部时展示在下方
- 文本域提示信息在顶部时展示在下方
- 文本域提示信息在顶部时展示在下方

## 2.29.7

`2023-10-8`

- 1、提高文本域提示层级 2、初始化本地配置时调整
- 修复选区设置字体时如果开头位置为单选复选框或其他非字符类，则字号回显为 0，确定后排版错乱的问题
- 获取本地配置信息报错问题
- 打印配置默认为单面打印
- 读取打印配置信息逻辑修改

## 2.29.6

`2023-10-7`

- 添加批注报错处理

## 2.29.5

`2023-10-7`

- 双击打开批注编辑框

## 2.29.4

`2023-10-7`

- 适配 base 修改

## 2.29.3

`2023-10-7`

- 字体新增对齐方式
- 优化回显逻辑

## 2.29.2

`2023-10-7`

- 修改文本域提示框样式
- 使用 transUse 把新老批注进行隔离

## 2.29.1

`2023-9-27`

- 修复双面打印回显错误的问题
- 文本域提示信息支持换行功能
- 文本域提示信息样式微调
- 冒号缺失
- 修复特殊字符 pdf 预览不显示的问题
- 悬浮球样式调整
- 悬浮球初始位置调整

## 2.29.0

`2023-9-25`

- 新增调试工具级修改联宽度的方法
- 修复文本域公式编辑时会出现乱跳的情况
- 修复 JSPDF 绘制实心圆报错的问题
- 删除空配置时不应删除值为 0 的配置
- 增加 pdf 打印所需字体文件在众阳浏览器下可通过本地文件读取
- 修复复选框编辑添加按钮被遮盖的问题

## 2.28.14

`2023-10-17`

- 适配 base

## 2.28.13

`2023-10-16`

- 适配 base

## 2.28.9

`2023-9-19`

- 众阳浏览器下批量打印顺序问题

## 2.28.8

`2023-9-18`

- 保存修改痕迹兼容老数据

## 2.28.7

`2023-9-18`

- 保存修改痕迹兼容老数据
- 修复文本域弹窗层级问题
- 新增双面打印
- 修复级联文本域替换文本域到选择 name 的文本域的问题

## 2.28.6

`2023-9-15`

- 修复文本域级联点开报错的问题

## 2.28.5

`2023-9-14`

- 更新水印编辑模式与图形编辑模式接口
- 打印预览增加根据配置回显单双面打印状
- 修复时间框位置不正确的问题

## 2.28.4

`2023-9-14`

- 加了几个 log

## 2.28.3

`2023-9-14`

- 增加新旧逻辑切换按钮
- 把级联文本域从文本域设置中分离出来
- 暴露水印模式与形状编辑模式接口
- 去掉升级说明改为特殊功能
- 新增点击级联便捷插入功能
- 修复取消按钮不能关闭级联功能便捷插入的问题

## 2.28.2

`2023-9-11`

- 适配 base2.28.2

## 2.28.1

`2023-9-7`

- 文本域属性编辑性能优化
- 新增右键取消插入功能，新增右键点击和悬浮条联动功能
- 修复组件渲染钩子函数执行顺序问题
- 新增悬浮条河右键继续绘制折线功能及按钮随图形几何中折线的存在与否展示消失功能

## 2.28.0

`2023-9-5`

- 去掉了批注列表变化事件
- 去掉了几个没用的接口

## 2.27.27

`2023-9-11`

- 修复本地配置需求修改钩子函数异步导致其他产品使用报错问题

## 2.27.26

`2023-9-4` -适配 base2.27.22

## 2.27.5

`2023-8-24`

- 修复图片编辑在编辑完自定义线段后编辑圆或矩形报错问题

## 2.27.4

`2023-8-23`

- 新增 shape 悬浮球功能
- 第一次点击文本域属性弹窗位置不准确问题修复
- 添加自定义批注
- 新增水印便捷框
- 批量打印数据重复问题

## 2.27.3

`2023-8-18`

- 发版测试

## 2.27.2

`2023-8-18`

- 页面滚动的时候隐藏数字选择器
- 修复文本域公式不能在光标位置插入元素的问题
- 增加修改痕迹鼠标移入时显示新增还是删除
- 新增文本域公式点击标点符号后标记点击的符号
- 修改痕迹优化，增加图片鼠标移上去显示修改信息
- 自定义批注弹窗转移到 demo，右键菜单的添加自定义批注选项删除
- 修复切换减号后再点击文本域编辑器公式提示公式格式不正确

## 2.27.1

`2023-8-11`

- 调试工具和文本域属性弹窗同时存在时，页面交互 bug 修改
- 悬浮按钮拆分单元格接口调整
- 拆分单元格默认两列一行
- 暴露自定义批注的接口
- 本地设置功能优化，增加调试工具中设置固定纸张按钮
- 新增点击文本域公式 div 也能弹窗的功能

## 2.27.0

`2023-8-11`

- 新增文本域公式说明
- 修改批注之后编辑器设置为只读模式 bug 修改
- 批注替换之后编辑器变为只读
- 批量打印每份数据调用 afterPrint 在 42 上不用 setViewMode("normal")了,因为 base 上已经改了,简洁模式也可以打字
- 批量打印,里边的每份数据不管有没有 afterPrint 都要调用 update
- 自定义批注
- 增加文本域显示模式切换

## 2.26.7

`2023-8-9`

- 文本域公式新增>=<+功能
- 新增文本域公式选区后再点按钮插入数字应该清除选区再插入
- 文本域公式新增分号及修复点击问题
- 修复文本域公式新插入文本域时报错的问题
- pdf 打印内存增长问题优化
- 优化文本域公式输入框

## 2.26.6

`2023-8-7`

- 新增右键图形编辑
- 替换 icon 文件,修复右键菜单中单元格内的两条斜线不展示图标的问题
- 修改批注列表抽屉的展示层级改为 1,云病历那边弹窗太多,他们改成 2
- 文本域属性框标题修改

## 2.26.5

`2023-8-4`

- 分割单个单元格 bug 修改
- 软分割的时候处理合并单元格不绘制的线
- 选中整个表格在锁定分组只读模式,表单模式下不允许剪切删除
- 将 Ctrl+V 的判断挪到 paste 里边去
- 解决新版表格拆分一个单元格无限软分割的问题
- 修复 shape 报错问题

## 2.26.4

`2023-8-2`

- 增加拆分单个单元格的功能
- 解决编辑器克隆接口内存泄漏问题及性能优化
- 新版表格拆分基础班软分割逻辑完成
- 新版表格拆分硬分割时,row_size 不对的问题修复
- 修复敏感词检测返回敏感词数量跟真实存在的敏感词数量不一致的问题
- 文本域属性弹窗置顶连续插入报错处理
- 连续插入文本域和修改文本域之间切换报错修改

## 2.26.3

`2023-8-1`

- 用户登录接口调整
- 新增文本域划线功能

## 2.26.2

`2023-7-28`

- 修复 shape 点击编辑角拉长后保存位置不正确的问题
- 新增 C++打印 shape 的画圈和画 X

## 2.26.1

`2023-7-26`

- 医学计算公式,点击确定插入文本域带着结果,右键菜单也显示正确的公式

## 2.26.0

`2023-7-26`

- 医学计算公式获取不到年龄报错的 bug 修复
- 解决医学计算公式获取性别时,没性别不默认的 bug

## 2.25.14

`2023-8-2`

- 选中整个表格在锁定分组只读模式,表单模式下不允许剪切删除
- 将 Ctrl+V 的判断挪到 paste 里边去

## 2.25.13

`2023-7-28`

- 分组表单模式影响痕迹视图用户名展示问题

## 2.25.12

`2023-7-18`

- 暴露打开医学计算公式弹窗的接口
- pdf 所需字体文件主动加载接口暴露

## 2.25.11

`2023-7-17`

- 修复文本域公式不能二次修改 bug
- 都昌模板转换增加一些判断防止报错
- 修复文本域公式点击提示纯数字的问题
- 修改弹窗输入框不能点击,不显示光标的问题
- 添加提示 title

## 2.25.10

`2023-7-14`

- 修复文本域公式能打字的问题

## 2.25.9

`2023-7-11`

- 调试工具中增加管理员模式
- 文本域公式
- 统一名称
- 新增文本域公式回显功能
- 公式点击插入时增加一个间隔符
- 文本域公式最后一个删除不掉的问题
- 修复回显不正确的问题

## 2.25.8

`2023-7-7`

- 打印数据生成时如果图片缺少 src 则会报错的问题
- 表单模式下，选区删除文本域内容导致文本域被清空的 bug 修改
- (之前发版一直报错，前几版都发失败了，2.25.7 是最近的一版)

## 2.25.6

`2023-7-7`

- (环境出了问题，2.25.5 发版没成功，2.25.6 对应 base 的 2.25.9 版本)
- 选取字体增大缩小报错修改
- 在 modelPath2ParaPath 之前加了判断 modelPath 是否存在
-

## 2.25.5

`2023-7-4`

- 医学计算公式获取性别和年龄自动指定正确的弹框
- 选区字体缩放，文本域边框不变的 bug 修复

## 2.25.4

`2023-6-30`

- 数字类型的文本域增加双击激活方式
- 解决水印模式下输入文字时,右键菜单不显示的问题
- 增加辅助按钮设置后常驻
- 右键菜单医学公式调整位置并且增加图标
- 双击文本域展示医学计算公式弹窗,并根据 placeholder 默认选项
- 弹窗移动后 ctrl+a 全选失效问题修复
- 医学公式增加路径导航
- 医学计算公式点击确定填写到文本域内,小数点精确到三位小数
- 多选问题修复

## 2.25.3

`2023-6-28`

- 痕迹对比弹窗的若干修改

## 2.25.2

`2023-6-27`

- 修复文本域公式以；分割的多个公式不生效的问题

## 2.25.1

`2023-6-26`

- 优化更新文本域公式的方法
- 增加了数字类型位数无限制
- 修改了小数四舍五入的规则
- 新增判断大小文本域公式逻辑

## 2.25.0

`2023-6-20`

- 40 迭代需求

## 2.24.10、

`2023-7-10`

- - 表单模式下选区删除导致文本域清空的补丁

## 2.24.9

`2023-7-5`

- 适配 base2.24.9

## 2.24.8

`2023-7-4`

- 因为 base 上修改了 copyEditor,新增了 getPrintEditor,所以 vue 上对应修改

## 2.24.7

`2023-7-3`

- 增加 internal 属性,transformData,在每次执行 rawData2ModelData 的时候,收集图片和文本域数据
- beforePaste 事件,返回数据改为对象,增加返回数据转换后的 transformData

## 2.24.6

`2023-6-27`

- 表单模式下选区删除错误修改
- 都昌模板转换图片大小问题修复
- 选择框点击文本选中时触发选区问题
- 增加路径有效性判断,解决 refreshDocument 强制更新时,数据变化导致路径转换时报错的问题

## 2.24.5

`2023-6-26`

- 增加图片信息异常提示
- 单元格复制样式问题
- shift 选区 bug 修改
- pdf 打印将图片转白色背景时直接使用 canvas
- 首行缩进和 tab 键缩进，在选区包含表格的情况下报错处理

## 2.24.4

`2023-6-25`

- 暂时注释 jspdf 优化代码，观察医生签名图片生成 pdf 时丢失问题
- 解决右键菜单的问题

## 2.24.3

`2023-6-20`

- 增加辅助按钮文本域最小宽度调整
- 解决右键菜单位置不对,高分辨率下有些选项展示不全的问题
- 调整部分依赖的版本
- 解决右键菜单点击屏幕下方,右键菜单非常矮的问题

## 2.24.2

`2023-6-19`

- 模板转换报错
- 显示隐藏按钮不生效问题
- 解决单元格软分割时,段落 children 为空,导致获取字符串位置为空的问题
- 去掉了一个选区删除的判断
- 解决调用 updateFieldText 找错文本域的 bug
- 改了文本域属性弹窗的样式
- 增加本地启动命令，解决 node18 启动报错问题

## 2.24.1

`2023-6-14`

- shift 添加选区功能
- 切换构建工具为 vite
- ts 报错问题
- 使用 vite 后不能热更新的问题
- 使用 useLetterPlaceholder 属性将输入法输入时字母占位的新旧功能进行了隔离
- XField.ts 中,定位到文本域的方法,加是否在正文是否是编辑页眉页脚模式的判断,解决循环全文档文本域调用 editor.reviseFieldAttr 报错的问题

## 2.24.0

`2023-6-13`

- 39 迭代需求

## 2.23.18

`2023-6-15`

- 解决单元格软分割时,段落 children 为空,导致获取字符串位置为空的问题

## 2.23.17

`2023-6-13`

- 修改获取字符串位置,字符串在分页表格中位置不对的 bug
- 修改获取字符串位置中表格内的数据页码不对的 bug

## 2.23.16

`2023-6-12`

- 获取字符串相对于页面位置中干掉了测试的值
- 获取字符串相对位置乘以 0.749
- 修复获取字符串相对位置里边获取最后一个字符下标不对的问题

## 2.23.15

`2023-6-12`

- 只有一个表格的模板打开报错问题
- 修改获取字符串位置,改为获取最后一个字符的位置

## 2.23.14

`2023-6-9`

- 修復弹出框监听事件冲突问题
- 门诊未知原因导致 vue 与 base 版本不一致问题报错

## 2.23.13

`2023-6-7`

- 修復模態框拖拽移動問題

## 2.23.12

`2023-6-7`

- 适配 base2.23.15

## 2.23.11

`2023-6-6`

- 都昌模板转换图片数据增加判断
- 服务端调用接口无需受只读模式影响
- 服务端调用接口无需受只读模式影响及转打印数据时判断错误
- 修复滚动表格拉到最后删除滚动不正确的问题
- 还原 set_cell_height 命名

## 2.23.10

`2023-6-2`

- 性能监控只监控电脑端
- 仅选择模式改成了单独的属性
- 增加性能监控信息初始化时判断，防止不同操作系统不兼容
- 水印模式下弹窗问题

## 2.23.9

`2023-6-1`

- 增加图片排版 3,3,4 的情况

## 2.23.8

`2023-6-1`

- ImageTable.ts 中 updateSerialNum 更新时判断是否是序号再更新值,解决交换图片顺序之后全都改成序号的 bug
- 解决调用 insertImageTable 的时候表格没有创建成功报错的 bug
- insert_raw 里边给 ImageTable 属性赋值增加 imageList 解决撤销后再插入报错的 bug
- 文档对齐不同步 bug 修改
- 模板转换后级联失效问题

## 2.23.7

`2023-5-30`

- 增加字号增减的图标
- 移动端报错问题

## 2.23.6

`2023-5-30`

- 增加悬浮按钮字号增减、字符间距还是放在字体中
- 增加帮助信息
- 性能监测时间显示格式化错误

## 2.23.5

`2023-5-29`

- 解决不展示拼音输入内容换行时自动删除内容的 bug

## 2.23.4

`2023-5-26`

- 增加性能监测窗，用于性能监测
- 新增滚动单元格逻辑

## 2.23.3

`2023-5-26`

- 增加单元格内边距按钮
- 悬浮按钮增加按住持续触发功能
- 悬浮球字符间距 bug 修改
- 悬浮按钮增加属性控制是否可连续触发
- 悬浮按钮增加段落重排
- 悬浮按钮增加插入按钮
- 右键菜单微调

## 2.23.2

`2023-5-23`

- 实现图片排版中创建合并后表格的功能
- 固定单元格高度滚动功能
- removeParagraphElement 参数形式修改
- 多层嵌套级联文本域设置

## 2.23.1

`2023-5-19`

- 解决新版表格拆分逻辑 row_size 分配不对的问题和 cell.split 传参不对的问题，错误效果是有空白窜罗和分页单元格内容永远在上一页，没有被拆分
- 复制粘贴表格数据样式调整
- 新增指定文本域高亮功能
- 修复页眉中插入表格表格内无内容的问题
- 新增获取所有图片的接口
- 重整批量打印方法

## 2.23.0

`2023-5-17`

- 只读模式下，允许删除,添加批注
- 悬浮球里的字符间距功能
- 修复右键菜单位置不准确的问题
- 增加”仅选择“功能
- 去掉右键菜单 style 中的最大高度属性值
- 修改右键菜单最大高度的计算方法

## 2.22.18

`2023-6-7`

- 增加了字母占位的配置

## 2.22.17

`2023-6-1`

- 移动端报错问题

## 2.22.16

`2023-5-29`

- 适配 base2.22.13 修改

## 2.22.15

`2023-5-16`

- 修复右键菜单位置不准确的问题

## 2.22.14

`2023-5-15`

- 增加悬浮按钮配置控制

## 2.22.13

`2023-5-15`

- 修改 font_style 的 ts 类型和上标下标的常量表示
- 干掉所有 ScriptEnum 替换成 ScriptType
- 临时解决 XField.toggleSymbol 卡顿问题，调用 refreshDocument(true)
- 给 cell.updateChildren 参数增加 ts 类型校验，优化逻辑代码，减少 get 属性的重复调用
- 表格 copy 增加 imageList 的 copy 解决 pacs 模板替换之后排版中的图片不能删除的问题
- 调整 json 对比接口，增加可传入忽略属性参数
- 解决模板替换，原来只读文本域插入后变成非只读的问题
- 修改右键菜单最大高度的计算方法

## 2.22.12

`2023-5-12`

- 修复批注信息滚动时造成排版错乱问题
- beforeFieldSelect 事件支持返回值设置 await

## 2.22.11

`2023-5-12`

- 文本域最小宽度换行时处理
- 同一个分组内，外面内容不能往表格中拖拽的 bug
- 修改 Paragraph.ts 文件中的 updateChildren 方法，更简化
- 优化全选逻辑
- 取消高亮优化
- 消息事件提示参数增加校验
- 抽屉组件自行实现，解决右键菜单二级菜单显示超出屏幕问题
- 兼容抽屉批注显示与菜单显示问题

## 2.22.10

`2023-5-10`

- 分组表单模式分组定位视图滚动不正确问题

## 2.22.9

`2023-5-9`

- 解决中文输入时，设置字体样式只闪一下，继续打字不生效的问题
- 优化设置文本域最小宽度问题，文本域换行时仍存在问题
- 分组表单模式光标问题

## 2.22.8

`2023-5-9`

- 分组表单模式开启后不允许通过光标移动跳分组
- 调整文本域最小与最大宽度设置逻辑
- 解决段落和文本域 replaceWith 中 cell 位置判断不准的 bug
- 插入模板增加参数是否替换成 rawData，判断是否有 hasLastLineBreak 和是否走插入模板逻辑，解决文本域替换时，段落不居中的问题
- 修改了 caret_move 方法使用的 direction 的 ts 类型，修改了参数名
- 蓝色字体颜色设置不生效问题修复

## 2.22.7

- 悬浮球在 demo 位置不正确问题

## 2.22.6

- 发版错误，重新发了一次

## 2.22.5

`2023-5-8`

- 悬浮按钮完善
- 双面打印机打印设置后提示调整

## 2.22.4

`2023-5-5`

- 修复文本域边框置空后删除报错的问题
- 实现文本域的 getRawData 方法
- PDF 和 C++打印绘制斜线
- 在 Editor.ts 文件中暴露批次获取容器的 rawData 的方法和批量替换的方法
- 修改文本域，段落和表格的 replaceWith 方法中的插入模板，由 editor 调用改为 EditorHelper 调用，避免在历史堆栈中记录
- 悬浮按钮完善

## 2.22.3

`2023-5-4`

- 在 Utils 中增加抽离出的初始化 cell 的方法，修改了 Selection 中 getRawData 逻辑，并给获取文本域的 rawData 方法开个头
- 段落排版相关代码整理
- 增加悬浮球本地开关
- 双面打印

## 2.22.2

`2023-4-28`

- 修复改变文本域边框边框位置不正确的问题

## 2.22.1

`2023-4-28`

- 优化文本域边框逻辑，高度跟随最高的元素
- 修复 input 文本域有时画不上的问题

## 2.22.0

`2023-4-27`

- 37 版本全部需求

## 2.21.13

`2023-5-16`

- 临时解决表格隐藏显示边文本域框卡顿问题

## 2.21.12

`2023-5-9`

- 解决稍微拖拽 label 标签，位置不变，getFocusField 不对的 bug
- 增加根据 base64 导出文件工具函数
- 设置单元格边距文字重叠 bug 修改
- 修复文本域边框置空后删除报错的问题
- 修复 c++打印报错问题

## 2.21.11

`2023-4-26`

- 修改 insertText 方法
- 修复 PDF 打印预览报错问题
- 嵌套文本域跨多段时数据转换结构错乱
- 微调模型数据转原始数据逻辑
- 修改了替换换行符的函数名
- 修改 Paragraph.ts 中的 insertElements 的参数名字等
- 按钮移入移出效果问题修复
- 页眉文本域 updateFieldText 结果为 undefined 的问题处理
- 对比 rawDatabug
- 对比 rawData 函数逻辑优化

## 2.21.10

`2023-4-24`

- 修复低版本众阳浏览器奇偶页不生效的问题
- 背景色设置报错、去除默认的自定义右键菜单

## 2.21.9

`2023-4-23`

- pdf 图片打印报错

## 2.21.8

`2023-4-23`

- 痕迹对比内容回显不正确问题

## 2.21.7

`2023-4-21`

- 解决嵌套文本域数据转换的 bug

## 2.21.6

`2023-4-21`

- 解决中文输入法的时候在锁定分组内仍然能够编辑的 bug
- 解决撤销错乱的问题
- 修复字符对齐问题
- 解决数据转换有引用，导致撤销时数据错乱的 bug
- 修复在表格内首行缩进报错的问题
- 修改替换特殊字符的方法
- 修改合并单元格和判断是否可以合并的函数名字

## 2.21.5

`2023-4-20`

- 增加字符对齐兼容性处理逻辑
- 增加隐藏批注里的替换按钮
- 文本域不可编辑下的提示语

## 2.21.4

`2023-4-20`

- 尝试修复组件没有注册的问题
- 时间下拉框编译后不显示修复测试

## 2.21.3

`2023-4-19`

- 解决修改单元格内容，其他非上对齐单元格内容位置不变的 bug，解决新版表格拆分，第二页往后表格 top 值计算不对的 bug，去掉嵌套文本域数据转换原来的 userLocal
- 增加低版本浏览器复制粘贴纯文本
- 非 https 协议打开的数据复制，放进剪切板纯文本
- 复制粘贴时只有不是本地的时候才进行版本和 https 协议的判断

## 2.21.2

`2023-4-18`

- 文本域边框样式与其中文本样式一致时内容丢失 bug
- 嵌套文本域数据转换增加判断
- 插入模板单选框外框变蓝色问题修复
- 修改插入图片排版和删除控制序列号的逻辑

## 2.21.1

`2023-4-17`

- 复制粘贴不带样式，压缩数据部分恢复
- 增加打印前事件，用于修改打印配置
- 快速录入模式 bug 修复（getnextField 方法还没改）
- 解決跨段嵌套文本域 rawData 转 modelData 造成被嵌套的文本域跟外层平级的 bug，删除报 removeElement 的那个
- 修复文档对齐的复制问题
- 撤销不显示的 bug 修改
- 修复文本域日期选择器 show_format 属性如果通过接口设置会造成展示与替换格式不匹配的问题
- 删除老版 JSPDF 打印
- 增加打印前事件用于修改打印参数

## 2.21.0

`2023-4-10`

- 36 版本全部需求

## 2.20.13

`2023-4-13`

- 修复文档对齐的复制问题
- 修复区域打印在表格内多打一行的问题
- 增加文本域面板选择前事件

## 2.20.12

`2023-4-12`

- 修复字符对齐导致的报错问题

## 2.20.11

`2023-4-11`

- 增加打印前事件，用于修改打印配置
- 修复打印预览和结果对不齐的问题

## 2.20.10

`2023-4-10`

- 补充缺失的中文字体
- 日期选择框新增汉字秒类型

## 2.20.9

`2023-4-7`

- 修复字符对齐在简洁模式下对不齐的问题

## 2.20.8

`2023-4-6`

- 打印内容缺失问题修复
- 去掉 modelDataToRawData 中的序列化代码，解决 pacs 打字操作卡顿的问题
- 修复字符对齐删除时位置不正确的问题

## 2.20.7

`2023-4-4`

- 增加对比两个 json 是否一致接口，忽略一些 id 类属性

## 2.20.6

`2023-4-4`

- 修复 ImageTable 数据转换时部分属性未保存
- 文本域 style 属性中缺少 family 与 height 时打印众阳浏览器闪退问题

## 2.20.5

`2023-4-3`

- 检查分组 id 的方法提取
- 解决了一个根据 path 获取不到内容的报错
- 暂不使用图片优化,先使用本地测试

## 2.20.4

`2023-4-3`

- 增加分组内容转换
- 优化模板转换分组相关代码
- 移除新增加的修改图片 dpi 方法
- 插入图片排版测试按钮问题修复、modelData 转 Dom 逻辑优化
- 插入模板图片丢失问题修复
- 修复 editor 数据改动导致的打印预览报错问题
- 取消背景按钮显示 bug 修改

## 2.20.3

`2023-3-24`

- 判断分组路径 bug 修改
- 优化批量打印有关 PDF 打印的相关逻辑
- 字体颜色 colorPicker 背景色修改

## 2.20.2

`2023-3-24`

- 复制 editor 接口增加 imageMap 处理
- 判断分组路径修改

## 2.20.1

`2023-3-23`

- 修复打印预览时没有图片的问题

## 2.20.0

`2023-3-22`

- 35 版本需求

## 2.19.18

`2023-4-7`

- 数据转换时序列化复制性能问题

## 2.19.17

`2023-3-28`

- 新增自适应窗口大小接口

## 2.19.16

`2023-3-24`

- 解决打字还没回车或者空格的时候就删除选区的 bug

## 2.19.15

`2023-3-21`

- recordVersionInfo 修改

## 2.19.14

`2023-3-21`

- 解决表单模式下，在可编辑表格内，点击空文本域，光标位置在开头的 bug
- 修改了文本域边框保留的版本号判定
- 表单模式下，在可编辑的表格内按方向键移动光标不受表单模式影响

## 2.19.13

`2023-3-20`

- 分组页眉信息替换功能 bug 修复
- 图片排版的表格内的图片不让放大缩小
- 解决选区末尾坐标在段落开头时删除会多删除一行的 bug，统一单元格内外的复制逻辑
- 都昌模板转换未保留单元格左右内边距问题

## 2.19.12

`2023-3-15`

- 修改表格属性，去掉单元格的最大高度设置时也更新
- contentChanged 事件中添加处理固定单元格高度的逻辑

## 2.19.11

`2023-3-15`

- 表单模式下护理表单点击右边纸张外区域报错

## 2.19.10

`2023-3-14`

- 表格拆分时，处理透明度的线
- 修复单元格内选区到下一段开头时设置选区内容与复制粘贴内容错误 bug
- 表单模式中空文本域使用 delete 删除导致光标位置错误
- 修复续打时会多打印上一行的签名的问题
- 段落重排接口支持只清空空段
- 修复低版本谷歌打印预览报错问题

## 2.19.9

`2023-3-9`

- 查找替换接口调整，优化高亮逻辑，返回查找数量
- 编辑器只读时可弹出图片编辑窗口问题修复

## 2.19.8

`2023-3-7`

- drawCell 传入 page 时绘制列表接口报错
- 表格拆分逻辑，拆分成两页情况，基本实现
- 插入多段内容报错问题修复
- 修复表单模式下粘贴时偶发粘贴到文本域外的问题
- 修复字符对齐时换行导致对不齐的问题

## 2.19.7

`2023-3-3`

- 修复分组内段落重排时将所有段落全部移除 bug

## 2.19.6

`2023-3-3`

- 修复打印接口阻塞的问题

## 2.19.5

`2023-3-3`

- 修复打印接口阻塞的问题

## 2.19.4

`2023-3-2`

- 调整进入页眉页脚编辑状态接口，增加剪切板写入与读取接口
- 每次设置焦点元素时先清空焦点元素

## 2.19.3

`2023-3-2`

- 重写表格拆分逻辑
- 新增段落重排剩余最后一段时需要判断是否删除
- 修改内部使用接口名 focus->setSelectImageOrWidget
- 修改获取当前焦点元素方法并加入本地测试
- 增加内部使用变量及函数管理类
- 修复批量打印打印顺序不正确问题
- 获取打印机列表失败后及时退出续打模式及显示文本域边框
- 新增打印预览刷新打印机列表功能
- 修复众阳浏览器不能记录选择的打印机问题处理
- 托盘手动重连不生效问题

## 2.19.2

`2023-2-27`

- 修复整合后 pdf 打印水印偏移的问题
- 修复区域打印选区不准确的问题
- 修复整合后水印绘制顺序
- 只有下拉框和日期选择面板只读时提示
- 增加动态请求下拉框不走缓存参数控制
- 修复打印当前页打印全部的问题
- 修复打印预览无法输入页码的问题

## 2.19.1

`2023-2-23`

- 增加文本域下拉列表动态异步请求方式支持

## 2.19.0

`2023-2-23`

- 34 迭代全部需求，打印相关提到 base

## 2.18.15

`2023-3-9`

- 单元格内选区到下一段开头时设置选区内容与复制粘贴内容错误 bug

## 2.18.14

`2023-3-6`

- 将白色为#FFF 的值转为透明背景色，之后新设置的白色背景需要设置成#FFFFFF,目的是为了解决之前修改过字体样式后会自动设置白色背景色问题

## 2.18.13

`2023-3-6`

- 解决文本域内字体缩放的 bug

## 2.18.12

`2023-3-6`

- 文本域设置最大高度后，复制内容粘贴到选区内容时无法替换选区内容
- 打印带列表的内容时报错
- cpp 打印列表报错

## 2.18.11

`2023-2-27`

- 修复打印当前页打印全部的问题
- 修复打印预览无法输入页码的问题

## 2.18.10

`2023-2-21`

- 可编辑表格在表单模式下里边的图片是可以选中的
- 分组增加获取纯段落属性
- 获取文档内所有段落增加可获取纯段落

## 2.18.9

`2023-2-16`

- placeholder 颜色错误修改

## 2.18.8

`2023-2-15`

- 匿名化星号展示规则问题修复

## 2.18.7

`2023-2-14`

- 匿名化星号展示规则问题修复
- 解决在空文本域内粘贴字体不缩放的问题

## 2.18.6

`2023-2-13`

- 匿名化问题修复
- 增加生僻字转图片方法，暂不启用
- 段落重排传入表格报错

## 2.18.5

`2023-2-10`

- 匿名化页眉页脚不生效问题修复

## 2.18.4

`2023-2-10`

- 图片打印匿名

## 2.18.3

`2023-2-9`

- 都昌模板转换增加服务端兼容
- 在 contentChanged 中添加处理文本域字体缩放的逻辑

## 2.18.2

`2023-2-9`

- 都昌模板转换增加服务端兼容

## 2.18.1

`2023-2-1`

- 新增正序逆序功能
- 调试工具修改
- 增加设置当前分组新页展示属性
- 优化绘制临时 border 的方法，减少传参

## 2.18.0

`2023-1-31`

- 33 版本全部需求

## 2.17.10

`2023-2-15`

- 优化设置文本域内字体缩放的方法和文本域增加 rows 的计算属性
- 文本域内字体缩放给放大的循环 z 增加次数限制，避免死循环

## 2.17.9

`2023-2-10`

- 修改文本域获取高度的方法，设置字体大小加基础高度 d 的参数
- 修改文本域字体缩放逻辑

## 2.17.8

`2023-2-10`

- 去掉 Editor.ts 文件中 setCharacterSize 中的 @undoStackRecord(commands.uncertainty)

## 2.17.7

`2023-2-10`

- 增加文本域设置字体缩放的接口
- 文本域和段落设置字体大小方法增加参数，可传最大高度
- 设置字体大小添加到历史堆栈中
- 处理文本域内字体缩放，粘贴的时候也能正常执行

## 2.17.6

`2023-2-08`

- 增加生僻字支持，修改弹窗方式

## 2.17.5

`2023-1-31`

- 新增配置可控制编辑器展示字体
- 修复批量打印报错

## 2.17.4

`2023-1-29`

- 修复段落重排段落全是空行的情况
- 分组属性新增分组表单设置按钮

## 2.17.3

`2023-1-29`

- 修复分组表单模式点击切换分组时的问题

## 2.17.2

`2023-1-20`

- 新增分组表单模式

## 2.17.1

`2023-1-19`

- 新增表格指定行转换接口
- 新增表格转换原始数据时合并单元格的处理
- 修复文本域替换图片接口不支持字符串宽高的问题
- 新增按配置加载接口

## 2.17.0

`2023-1-18`

- 修复表格线在放大时位置不准确的问题
- 根据版本号判断文本域边框宽度是否保留

## 2.16.10

`2023-1-13`

- 修复插入模板时合并文本域样式时未加判断的问题

## 2.16.9

`2023-1-13`

- 插入模板时统一字体字号时兼容老模板

## 2.16.8

`2023-1-13`

- 新增服务端获取在线图片接口判断
- 新增插入模板是否使用默认字体字号配置

## 2.16.7

`2023-1-11`

- 新增服务端重置状态接口
- 修复时间框快捷输入时分秒不回显的问题
- 修复鼠标停留提示信息显示位置不正确的问题
- 注释数据压缩接口

## 2.16.6

`2023-1-9`

- 修改表单模式下文本域粘贴到文本域之外的 bug

## 2.16.5

`2023-1-6`

- 修复图片编辑报错的问题

## 2.16.4

`2023-1-5`

- 修复 C++打印水印图片第二页之后不显示的问题

## 2.16.3

`2023-1-5`

- 修复 C++打印水印图片第二页之后不显示的问题

## 2.16.2

`2023-1-5`

- 修复表格背景色复制换页问题
- imageMap 中图片加载无需等待图片加载完后在放置
- 新增从 imageMap 获取不到数据时的判断

## 2.16.1

`2023-1-4`

- 修复页边距 40 时选中表格最后一列选区不对的问题
- 新增改变表格背景色的问题
- 新增授权码过期提示水印接口
- 图片打印修改 dpi 报错问题
- 优化图片编辑画笔颜色逻辑
- 新增表格选区改变背景颜色
- 注释授权码校验逻辑
- cell 中的 bgcolor 放到 style 中

## 2.16.0

`2023-1-3`

- 31 版本需求
- 增加表单只读属性，防止与编辑器只读混乱

## 2.15.7

`2022-12-30`

- 修复表格在表单模式下可编辑没有保存的问题
- 绘制表单模式下表格可编辑提示框
- 处理特殊字符，底层逻辑会将其转换成乱码问题
- 去掉 image.onload 中白色背景逻辑
- reInitRaw 中添加 watermark 逻辑
- 修复水印图片第二页 pdf 不打印问题

## 2.15.6

`2022-12-28`

- 还原打包配置

## 2.15.5

`2022-12-28`

- 修复背景绿色问题

## 2.15.4

`2022-12-28`

- 修复移动端编辑器页面偏移问题

## 2.15.3

`2022-12-27`

- 修复水印输入框拖动位置不准确的问题
- 修复 reInitRaw 之后打字撤销字体样式变粗的问题
- 表单模式下按住鼠标左键不允许粘贴

## 2.15.2

`2022-12-26`

- 水印文字功能提交
- 右键菜单事件更改变量名
- 增加 id 重复校验，增加按照时间顺序插入分组
- 修复插入空行问题，记忆插入分组
- 将所有的 window.electron 进行调整，取不到时从 top 中取

## 2.15.1

`2022-12-19`

- 时间框输入校验

## 2.15.0

`2022-12-16`

- 30 迭代发版

## 2.14.11

`2022-12-14`

- 修复打印预览图片跨域问题
- 新增字体高度配置给出不符合要求的提示

## 2.14.10

`2022-12-14`

- 修复当特殊字符在字典文件中找不到报错的问题
- 新增水印图片删除的方法

## 2.14.9

`2022-12-13`

- 修复水印图片模式切换位置问题
- 修复 pdf 图片打印和 c++打印中水印图片位置不正确的问题

## 2.14.8

`2022-12-13`

- 恢复文本域样式优化

## 2.14.7

`2022-12-13`

- 修复引入模板数据插入到文本域外部的问题
- 校验功能修复

## 2.14.6

`2022-12-12`

- 修复固定单元格高度后，内容满了再删除，单元格高度变大的问题
- 修复字体缩放到一页后删除内容导致报错的问题
- 修复背景绿色问题

## 2.14.5

`2022-12-12`

- 修复背景绿色问题

## 2.14.4

`2022-12-9`

- 修复固定单元格高度输入内容会导致表格下方内容消失的问题

## 2.14.3

`2022-12-9`

- 配置字体缩小到一页
- 修复移动端水印多出的 div 的问题
- 修复模板字符串报错的问题
- 修复右键菜单位置不正确的问题
- 表格属性中新增单元格固定高度
- 修复下拉列表替换分隔符不生效的问题

## 2.14.2

`2022-12-6`

- 修复固定单元格高度，单元格内文本域没有缩小的问题
- 解决下拉框问题

## 2.14.1

`2022-12-5`

- 增加自定义扩展信息属性
- 修复加密数据打开报错
- 增加压缩功能后，另存 json 接口调整
- 在插入分组并排序的方法中加上清空选区的操作
- 单选搜索内容恢复

## 2.14.0

`2022-12-5`

- 先发一版

## 2.13.3

`2022-11-30`

- 修复都昌模板转换时设置级联文本域报错问题及下拉选择框激活模式问题
- 新增暴露压缩解压的接口
- 新增组件销毁前事件解除绑定

## 2.13.2

`2022-11-27`

- 修复往分组内粘贴内容跑到分组外的问题
- 去掉判断解决粘贴段落内部分内容插入空行的问题
- 开启 dpi 修改后所有图片都进行转换，否则有部分图片不能正常展示
- 修复修改纸张类型与修改横纵向不生效的问题

## 2.13.1

`2022-11-25`

- 解决图片排版不能保存图片之间间距的问题
- 修改 getRawData 和 reInitRaw 可以获取压缩后的数据和加载后的数据
- 修复快速点击文本域紧接着输入内容回电到文本域外边的问题
- 新增单选双选模式切换
- 新增多选分隔号选择
- 修复不能粘贴图片的问题
- 内存回收问题处理，修改 bus 传值逻辑去掉 webWorker 增加组件销毁前解绑事件逻辑

## 2.13.0

`2022-11-21`

- 新增从外部复制图片到编辑器中
- 修复 C++打印有序列表序号不齐的问题
- 编辑器接口整理
- 新增选区复制某些单元格可直接粘贴到另一个表格的选区中
- 编辑器加载数据时可读取数据中的配置
- 编辑器数据结构优化
- 编辑器新增水印接口
- 编辑器表单模式下实现表格可编辑

## 2.12.8

`2022-11-23`

- 修复页面切换后调用 updateCanvasSize 方法会将页面恢复到光标位置的问题

## 2.12.7

`2022-11-16`

- 修复签名图片不能打印的问题

## 2.12.6

`2022-11-15`

- 编译不成功重新发版

## 2.12.5

`2022-11-15`

- 编译不成功重新发版

## 2.12.4

`2022-11-15`

- 调试工具增加强制刷新按钮

## 2.12.3

`2022-11-10`

- 修复表格分页时插入行携带数据报错的问题

## 2.12.2

`2022-11-8`

- 修复浏览器打印会打印文本域边框及背景文本问题修复
- 更换图片编辑 id 获取方式，加入 editorId

## 2.12.1

`2022-11-7`

- 修复云病历转科后导致页眉与正文之间出现多行空白的问题
- 找回 editor.ts 文件中表格是否可以删除行和列的方法
- 修复图片因 dpi 不同导致在个别电脑打印不全的问题
- 优化图片编辑逻辑
-

## 2.12.0

`2022-11-4`

- 选区内容可以放大缩小
- 增加粘贴前事件，可对要粘贴的内容进行处理
- 表格新增行时可自动携带上一行内容
- 增加编辑器服务端原始 json 转 PDF Base64 接口(详细信息见编辑器在线 API 文档)
- ctrl+tab 能够在表格内设置缩进
- 图形编辑功能扩展，增加矩形绘制、文本插入、矩形背景设置

## 2.11.7

`2022-11-7`

- 修复云病历转科后导致页眉与正文之间多出多行空白问题

## 2.11.6

`2022-11-1`

- 修改文本域属性接口，增加判断文本域是否已经被删除

## 2.11.5

`2022-11-1`

- 修复修改页眉中文本域报错的问题

## 2.11.4

`2022-10-27`

- 修复修改页眉中文本域报错的问题
- 修复续打模式下页眉下多一个空行的问题

## 2.11.3

`2022-10-26`

- 新增服务端启动传入 rawData
- 修复插入图片不显示及透明背景图片打印绿色问题
- 修复段落重排有空字符串报错和表格内无反应问题
- 新增\r 字符宽度判断
- 修复 C++精度问题引起的续打位置不正确的问题

## 2.11.2

`2022-10-24`

- 新增服务端启动传入配置项
- 图片背景改变还原

## 2.11.1

`2022-10-21`

- 修复段落重排首行不缩进情况不清除空格的问题

## 2.11.0

`2022-10-21`

- 调整段落重排接口，通过参数可控制是否清除段落所有的空格、是否需要设置段落首行缩进。
- 新增将光标定位到单元格接口
- 删除或者清空分组的时候，没有删除 cell 内对应的文本域
- 获取多个文本域，但是只给一个文本域赋新值，没有赋新值但是又调用 updateFieldText 的方法的文本域，值会变成 undefined
- 痕迹视图中的痕迹文本背景色不应该被复制

## 2.10.6

`2022-10-28`

- 修复续打模式下页眉下多一个空行的问题

## 2.10.5

`2022-10-19`

- 修复都昌模板转换后图片变大、下划线不显示、字体颜色与背景色未转换、表格线显示隐藏的问题
- 修复没有任何批注信息时切换批注模式报错的问题
- 新增文档无批注信息时友好提示

## 2.10.4

`2022-10-11`

- 修复文本域简单复选框点击报错问题
- 修复切换文档后批注刷新问题

## 2.10.3

`2022-10-10`

- 修复页眉分组关联导致页眉高度问题
- 新增是否展示图片弹框方法

## 2.10.2

`2022-10-10`

- 版本加载问题重发

## 2.10.1

`2022-10-10`

- 新增双击图片弹出图片编辑框判断
- 修复护眼模式下复选框背景颜色问题
- 修复背景色使用批注颜色的问题

## 2.10.0

`2022-10-10`

- 新增护眼模式
- 去除 pdf 打印所需字体文件请求时的版本号,避免每次升级后都重新请求字体文件
- 优化单元格复制、数据更新代码逻辑以提升编辑时性能
- 修复光标定位到文档末尾接口 bug
- 增加图片编辑功能，初步先实现可在图片上绘制线条及线条的编辑功能。
- 优化云病历病程记录多页时卡顿问题
- 修复清空内容按钮的 BUG
- 优化表格插入行列时边框线显示隐藏效果

## 2.9.1

`2022-09-23`

- 修复文本域日期选择器 HH：mm 格式类型错误问题
- 补全自定义文本域校验

## 2.9.0

`2022-09-23`

- 文本域日期选择器增加 HH：mm 格式类型
- 修复表格分页时上下拖动横线，单元格上下居中，下对齐的内容没有实时变化问题
- 增加手动设置双面打印机功能
- 日期选择面板有时候会遮挡输入框问题修复
- 修复拖拽标签文本域到另一个普通文本域中，直接右键修改标签文本域内容会将普通文本域整个替换问题
- 解决设置字符对齐的模板调用更新文本域接口时传入字符对齐参数后报错问题
- 优化编辑器加载多页内容时编辑器滚动效果

## 2.8.1

`2022-09-14`

- 修复表格分页时，上下拖动表格线，重排部分单元格，对齐实时生效的问题
- 修复批注 commentsIDSet 为空值的问题
- 修复多个文本域粘贴报错的问题
- 新增图片编辑功能
- 新增支持双面打印功能

## 2.8.0

`2022-09-08`

- 新增续打病历时可选择页码功能
- 新增单元格中 Tab 键跳至下一单元格功能以及追加一行功能
- 优化多页内容时拖动滚动条时体验
- 新增批注功能
- 新增单元格锁定功能
- 修复使用 Delete 键删除内容时偶发输入 null 字符的问题
- 新增插入线条功能
- 都昌模板转换增加首行缩进，多选下拉框，文本域边框隐藏功能
- 优化复制粘贴文本域保证正确顺序
- 优化都昌模板转换逻辑，增加级联文本域转换

## 2.7.11

`2022-09-05`

- 修复多选框打印没有边框的问题

## 2.7.10

`2022-09-05`

- 合并都昌模板转换修改到 22 版本发布新版

## 2.7.9

`2022-09-03`

- 都昌模板转换表格嵌套异常提示移除

## 2.7.8

`2022-09-03`

- 都昌模板转换逻辑只允许出现一次的节点未重新初始化

## 2.7.7

`2022-09-03`

- 都昌模板转换优化

## 2.7.6

`2022-09-03`

- 修复修复模板转换报错的问题

## 2.7.5

`2022-09-01`

- 修复替换文本域在某些情况下报错的问题

## 2.7.4

`2022-08-31`

- 修复滚动条不能拖动的问题

## 2.7.3

`2022-08-30`

- 修复移动端不能滑动的问题

## 2.7.2

`2022-08-26`

- 修复转换为 rawData 时 shapes 为空报错问题

## 2.7.1

`2022-08-25`

- 修复点击日期面板外不能正常关闭问题

## 2.7.0

`2022-08-25`

- 新增文本域自定义校验功能
- 新增通过 delete 键可以删除表格上方空行
- 新增选择框只有一个对号的样式
- 修复配置默认行倍距不生效问题
- 新增增加自定义线条功能
- 修复病历检索界面打开病历书写页面后无法点选下拉选择文本域的问题
- 修复其他产品嵌入编辑器后会出现可视范围内编辑器外的其他内容不可点击的情况的问题
- 修复右键设置表格名称时不能正常回显表格名称，并且关闭录入窗口时未清空输入框内容的问题
- 新增编辑器调试工具，ctrl+shift+F12 打开

## 2.6.3

`2022-08-17`

- 修复托盘使用新版打印回调函数不生效的问题

## 2.6.2

`2022-08-12`

- 优化 Pacs 插入图片排版
- 替换文本域 raw 丢失字符问题
- 新增移除绑定事件接口
- 修改提示最大消息数目为 1

## 2.6.1

`2022-08-11`

- 修复复选框事件中级联逻辑与 contentChanged 事件冲突问题
- 新增添加级联注意事项

## 2.6.0

`2022-08-11`

- 复制跨段文本域粘贴到其他文本域后保存再重新加载数据后排版错乱。
- 表格性能优化。
- 移动端多页内容展示不全问题修复。
- 表单模式下使用 delete 键删除时光标移动位置不正确。
- 双面打印机病程记录续打开始页为偶数页时打印内容覆盖。
- 增加文本域级联功能。
- 可根据名称获取图片排版表格。
- 优化 Pacs 插入图片排版。
- 表格固定表头。

## 2.5.7

`2022-08-02`

- 修复生成 pdf base64 时偶发生成不了图片的问题
- 修复表单模式下移动光标输入会一直提示表单模式不可输入问题

## 2.5.6

`2022-08-02`

- 修复生成 pdf base64 时偶发生成不了图片的问题

## 2.5.5

`2022-08-01`

- 修复系统打印预览带有文本域边框的问题

## 2.5.4

`2022-08-01`

- 修复打印预览后加载模板失败的问题

## 2.5.3

`2022-08-01`

- 修复打印预览后加载模板失败的问题

## 2.5.2

`2022-07-29`

- 修复单机双击下拉框列表不显示问题

## 2.5.1

`2022-07-29`

- 修复替换文本与内容的问题

## 2.5.0

`2022-07-28`

- 修复在右键菜单事件中重载自定义菜单自定义的回调函数不生效的问题
- 修复新版打印下标变上标的问题
- 修复在光标处右键改为宋体，插入输入助手中的诊断内容，此时插入的诊断内容还是仿宋体的问题
- 全局配置转为实例配置
- 修复段落重排丢失段首文字的问题
- 修复替换页眉文本域内容，若接口参数中未添加 isHF 的值，那么替换内容将会以文本的形式插入到文档开头的问题
- 修复文本域日期选择框与下拉框双击激活的问题

## 2.4.2

`2022-07-15`

- 修复文本域校验样式问题

## 2.4.1

`2022-07-14`

- 段落重排逻辑优化
- 修复 antDesign 样式重叠问题
- 修复文本域时间框便捷输入时分秒空白时默认参数问题

## 2.4.0

`2022-07-13`

- 段落重排逻辑优化
- 修复文本域日期选择框日期正则表达式不正确的问题
- 移动端下不在页面上方 input 元素
- 新增敏感词检测接口
- 新增文本域校验接口
- 修复字符对齐段落自上而下选区设置左对齐报错问题
- 修复移除嵌套文本域报错问题
- 修复 pdf 打印透明背景图片时背景颜色为黑色

## 2.3.6

`2022-07-07`

- 修复文本域时间框遮盖问题
- 优化文本域校验提示逻辑
- 文本域校验属性回显问题修复
- 修复首次加载不能 pdf 打印问题

## 2.3.5

`2022-07-06`

- 修复切换组件后获取不到 jsPdf 对象的问题

## 2.3.4

`2022-07-01`

- jsPdf 加载字体文件逻辑调整

## 2.3.3

`2022-06-29`

- 修复插入只有一个段落的模板不能缩进的问题
- 修复调用 instance 处 this 指向问题

## 2.3.2

`2022-06-29`

- 修复英文输入法时按住 shift 不能输入的问题
- 新增插入模板时判断模板开头是否为表格的逻辑
- 修复 webWorker 里边访问 instance 报错问题

## 2.3.1

`2022-06-28`

- 修复字符对齐排版错位问题
- 修复非文档首插入模板（模板开头为表格）多出空行问题
- 修复表单模式下连续文本域删除报错问题

## 2.3.0

`2022-06-27`

- 修复 pacs 修改字体样式编辑器崩溃问题
- 修复 pacs 表单模式拖拽编辑器崩溃
- 修复多编辑器下字体配置文件覆盖问题
- 修复标签和时间框类型的文本域替换文本字符中存在\n 编辑器报错问题
- 新增新建病程记录,查房记录时输入域默认首行缩进 2 字符
- 修复文本域时间框显示 Invalid date 问题
- 修复文本域设置只读时，光标定位到文本域开始边框字符后使用 Backspace 键删除会将整个文本域删掉的问题
- 修复空文本域内插入水平线位置不正确问题
- 修复边点击边删除文本域内容报错的问题
- 批量打印优化
- 文本域时间框属性优化

## 2.2.2

`2022-06-17`

- 修复众阳浏览器中使用新版打印的判断逻辑问题

## 2.2.1

`2022-06-17`

- 修复众阳浏览器内无法获取版本号的问题

## 2.2.0

`2022-06-14`

- 修改版本号判断逻辑
- 修复打印选取打印范围错误的问题（7-10）

## 2.1.1

`2022-06-02`

- 修复编辑器在移动端下不缩放的问题
- 修复新托盘下批量打印不生效问题

## 2.1.0

`2022-05-31`

- 新增表单模式下拖拽功能
- 新增文本域时间框禁止时间参数
- 新增失焦后光标不闪烁配置
- 修复删除导致的文本域字符与段落字符不一致的问题
- 修复列表序号重排不保存的问题
- 修复表单模式下选中文本域开始边框删除光标位置跑到第一个文本域内的问题
- 修复点击页面外光标定位不准确的问题
- 修复页眉点击时光标不能正常定位的问题
- 新增 pointerUp 事件
- 文本域日期选择框新增禁止时间功能

## 2.0.5

`2022-05-24`

- 修复批量打印在众阳浏览器旧版本不能打印的问题

## 2.0.4

`2022-05-24`

- 修复向文本域中复制粘贴带换行符内容时造成文本域对象中字符缺失，继续往其中粘贴内容会导致 field.text 获取内容乱序的问题

## 2.0.3

`2022-05-20`

- C++图片打印位置矫正
- 修改托盘版本获取方法
- 修复自定义复选框修改后再次插入后位置不正确的问题
- 回退文本域日期选择优化功能

## 2.0.2

`2022-05-18`

-版本错误重新发版

## 2.0.1

`2022-05-18`

-合并 1.14.1 版本修改补丁

## 2.0.0

`2022-05-18`

- 添加图片打印间距
- 新增表格 copy 的时候增加 name 属性赋值
- 修复页脚表格线横向不能连续拖动的问题
- 正常显示隐藏页脚表格线
- 修复点击滚动条外部也能实现滚动条滚动的问题
- 定位到指定文本域开头位置
- C++打印相关

## 1.5.5

`2022-05-17`

- 发版错误重新发版

## 1.5.4

`2022-05-17`

- C++打印默认自动缩放，续打表格上边线不显示问题修复
- 修改打印批量打印续打位置不正确的问题
- 修复众阳浏览器获取版本报错问题
- 添加获取托盘版本号接口

## 1.5.3

`2022-05-13`

- 新增单独控制行首字符及数字排版功能更改
- 新增插入图片排版可以固定表格高度
- 插入图片排版新增各种判断，避免文本域、页眉页脚内报错错乱问题
- 新增图片排版序号与文本域可控制是否展示
- 修复进入页眉页脚编辑模式时进入页脚编辑的问题
- 图片排版双击图片不能删除

## 1.5.2

`2022-05-11`

- 新增符号排序
- 添加段落构造函数里对排版的复制控制
- 新增 ctrl+shift+F10 切换行首字符排版功能

## 1.5.1

`2022-05-07`

- 修改数字排版兼容问题导致的字符排版问题初夏那多余空行 id 的情况

## 1.5.0

`2022-05-07`

- 新增图片排版下方第二个文本域可控制不传
- 修改单元格内容导致表格行高发生变化，设置为非上对齐的单元格内容位置不变的 bug 修复
- 修复页眉设置选中文本字号后文本宽度不能立即生效的 bug
- 修复复制单元格时里边的下拉文本域没有下拉选项的问题
- 新增设置是否启用数字排版的接口
- 修改数字排版新段落的生成逻辑
- 添加排版变化后刷新页面现有数据

## 1.4.7

`2022-04-26`

- 新增兼容性数字排版
- 新增 ctrl+shift+f11 快捷键开启关闭数字排版

## 1.4.6

`2022-04-25`

- 痕迹对比可传字符串或对象
- 修复打字时滚动条消失的问题
- 给移动端遮罩层添加 ID
- 痕迹对比横纵向按照新版本来

## 1.4.5

`2022-04-24`

- 修复偶发获取 pathName 不正确导致的字体文件加载异常问题
- 添加打印 PDF 列表的情况
- 修复续打位置不正确的问题

## 1.4.4

`2022-04-22`

- 放开排版逻辑

## 1.4.3

`2022-04-22`

- 误升级版本号

## 1.4.2

`2022-04-22`

- 编译不成功重新发版

## 1.4.1

`2022-04-21`

- 修复文本域设置只读后仍能弹出医学表达式弹框的问题
- 优化医学表达式排版
- 修复表格变化时对比报错
- 修复痕迹对比不展示人信息的问题
- 痕迹对比不展示分组信息

## 1.4.0

`2022-04-14`

- 新增痕迹对比功能
- 新增恒牙和乳牙复杂牙位图功能
- 新增数字和英文的排版逻辑
- 修复编辑器中含有符号时打印预览不显示问题

## 1.3.8

`2022-04-13`

- 修复文本域替换图片后，数字排版报错，删除文本域失败的问题

## 1.3.7

`2022-04-12`

- 修复 16K 大小打印预览显示超宽问题

## 1.3.6

`2022-04-08`

- 修复含有文本域的单元格合并拆分报错问题
- 修复关闭 F12 后编辑器大片空白的问题
- 修复文本域隐藏后报错问题

## 1.3.5

`2022-04-07`

- 修复关闭 F12 后编辑器大片空白的问题
- 修复字体文件更换后不生效的问题

## 1.3.4

`2022-04-07`

- 修改无序列表图标样式
- 修复单个段落的段落重排
- 新增替换或追加文本域元素接口
- 修复分页情况下双击表格线导致 row_size 不正确的问题
- 修复无法置空 placeholder 的问题
- 修复文本域替换文本接口传入多个相同文本域对象会将内容替换为 undefined 的问题
- 优化模型数据转原始数据文本域会多生成空 text 的问题
- 优化配置报错提示

## 1.3.3

`2022-03-31`

- 编译错误重新发版

## 1.3.2

`2022-03-31`

- 新增返回编辑器版本信息的方法

## 1.3.1

`2022-03-29`

- 修改插入图片排版自动排列序号
- 修复增加 editor 配置后导致插入模板报错问题

## 1.3.0

`2022-03-29`

- 新增编辑器独立配置
- 修复图片放大后调整横纵向图片超出页面的问题
- 新增影响编辑器加载的配置校验
- 新增段落重排功能
- 新增文本域替换原始 json 数据接口
- 文本域新增月日格式
- 新增十字牙位图
- 删除添加常用语弹框
- 绘制 pdf 时增加 url 图片绘制
- 常用语添加 keyword 为空的判断

## 1.2.36

`2022-03-22`

- 修复下载 pdf 不成功的问题
- 托盘所需参数矫正

## 1.2.35

`2022-03-21`

- 新增 config 配置错误抛出异常的校验
- 修复选区光标在下方空白处移动时不能选区最后一段换行符问题
- 修复放大后更新 canvas 大小不准确的问题
- 新增表格内沿用 rawData 的表格 id

## 1.2.34

`2022-03-17`

- 修复横向滚动条展示错误的问题

## 1.2.33

`2022-03-17`

- 直接打印后取消续打模式
- 修复校验纸张大小的方法
- 修改删除线位置
- 修复最后一页滚动问题

## 1.2.32

`2022-03-15`

- 修复插入横向模板时未正常转为横向的问题
- 修复云浏览器立即打印未能取消续打模式的 bug。
- 添加静默打印添加未能获取打印机列表时的提示。
- 插入表格时取消最大行列限制。
- 添加插入超出计算最大接受列数的提示。并提示最大承受列数。

## 1.2.31

`2022-03-14`

- 修复插入横向模板时未正常转为横向的问题
- 修复编辑器隐藏状态下报错的问题
- 修复退出页眉页脚编辑模式后光标不准确的问题
- 修复复选框回显、清空问题
- 修复无纸化生成 base64 时异步可能导致的生成内容不对的问题
- 托盘打印打印机参数修正
- 众阳浏览器新增打印份数
- 常用语判断条件添加可配置属性
- 优化区域打印遮罩颜色

## 1.2.30

`2022-03-02`

- 优化文本域修改提示文本域为空
- 修复打印预览图片偏移问题
- 修复插入图片排版撤销不生效问题
- 修改单元格对齐在初次渲染数据时不正确的问题
- 新增获取选区纯文本的方法
- 新增批量打印关于新浏览器的判断
- 修复医学表达式 1 时间格式回显问题
- 新增常用语关键词搜索功能

## 1.2.29

`2022-02-23`

- 修复表格硬分割判断问题
- 修复横向滚动条不显示的问题
- 新增自动判断新浏览器是否支持批量同步打印，不支持自动降级为异步打印，但是异步打印存在打印乱序的问题

## 1.2.28

`2022-02-22`

- 修复滚筒条随页面宽度改变不显示的问题
- 修复当前有选区且选区中包含文本域时调用移除文本域接口报错问题
- 修复硬分割判断的问题
- 修复设置简介模式时光标位置问题
- 修复批量打印乱序、漏打的问题

## 1.2.27

`2022-02-21`

- 修复关闭日期选择框和下拉选择框的相关问题

## 1.2.26

`2022-02-21`

- 修复关闭日期选择框和下拉选择框的相关问题
- 修改页面左右滚动条逻辑

## 1.2.25

`2022-02-18`

- 因编译问题升级版本号

## 1.2.24

`2022-02-18`

- 修复点击编辑器外关不掉文本域弹框的问题
- 修复立即打印时导致的绘制内容不全的问题
- 修改打印接口成全文转换出来后打印

## 1.2.23

`2022-02-17`

- 修复都昌模板转换不成功问题
- 优化鼠标移动到文本区域外时的展示状态
- 修复 vue-worker 位置异常导致的未正常打包问题

## 1.2.22

`2022-02-16`

- 修改新浏览器预览后续打状态关闭
- 修复都昌新老版本数据接口不一致导致的模板转换报错问题

## 1.2.21

`2022-02-16`

- 修复医学表达式修改后 pdf 绘制异常问题

## 1.2.20

`2022-02-16`

- 修复批量打印为判断是否在新浏览器中

## 1.2.19

`2022-02-16`

- 图片 src 为空时，打印报错

## 1.2.18

`2022-02-11`

- 修复续打和区域打印模式下仍能弹出下拉框问题
- 处理表格合并单元格时绘制超出页面与回车时表格 rowSize 设置错误问题
- 优化页眉页脚复制逻辑和文本域边框显示隐藏逻辑
- 优化连续选中复选框情况下触发选区问题
- 修复多次撤销时撤销未初始化导致的撤销结果不对问题
- 修复页眉页脚修改后分散对齐导致的位置计算错误的问题
- 修复分散对齐时跨文本选区删除报错的问题
- 修复单选/复选框在表单模式下无法被选中的问题
- 新增 ctrl+shift+F12 展示当前版本号

## 1.2.17

`2022-02-08`

- 新增 pdf 立即续打功能
- 新增 vue-worker 处理转 base64 逻辑
- 修复图片编辑框不消失问题
- 修复光标移动时三连击从页眉到正文报错及复选框连续选中问题
- 修复新浏览器获取打印机列表方法问题
- 修复新浏览器立即打印问题

## 1.2.16

`2022-02-07`

- 因测试环境编译问题升级，代码无变动

## 1.2.15

`2022-02-07`

- 修复简单单选框/复选框无法被选中问题
- 修复从后往前选中多行删除报错问题
- 新增判断避免页眉信息重复替换
- 修复打印托盘判断问题
- 新增对 electron 支持

## 1.2.14

`2022-01-26`

- 修复单选框在跨页表格内选中成多选效果的问题
- 修改自定义多选框没有分组名的情况下处理逻辑
- 修复自动以复选框回显问题
- 修复选区状态下文本域修改属性报错的问题
- 修改打印参数，解决打印偏移量问题

## 1.2.13

`2022-01-25`

- 修改插入图片排版，可传入图片宽高设置比例
- 修复表格内有文本域时跨页报错的问题
- 新增段落复制增加字符对齐属性复制

## 1.2.12

`2022-01-24`

- 新增插入 pacs 需求排版图片的方法
- 新增打印配置，控制 ODF 打印偏移问题
- 对接新浏览器打印功能
- 对接浏览器打印的快捷键功能
- 修改插入表格传参逻辑

## 1.2.11

`2022-01-21`

- 修复打印预览窗口重复弹出问题

## 1.2.10

`2022-01-21`

- 修改打印逻辑，兼容打印托盘
- 修复绘制 PDF 插入图片后第二次绘制不压缩的问题

## 1.2.9

`2022-01-18`

- 新增调整页面大小后图片自适应的方法
- 修复分散对齐对于嵌套文本域最后字符错乱的问题
- 修复格式刷导致的排版错乱问题
- 修改都昌模板转换逻辑，增加固定宽度文本域转换
- 新增绘制 PDF 水平线绘制逻辑
- 新增元素双击事件
- 增加文档转 base64 码接口
- 调整月经史公式 1 插入后展示问题
- 新增字体文件路径名可配置，用于嵌套 iframe 使用的情况

## 1.2.8

`2022-01-13`

- 修复剪切板为空时拖拽报错问题
- 修复点击其他图片时原带编辑框图片编辑框不消失问题
- 修复无序列表有序列表混用时，有序列表序号出现混乱的问题
- 修改插入分组的菜单信息，保证在分组外插入分组时只能选择下方插入
- 修改下拉选择框判断下方展示空间计算错误问题

## 1.2.7

`2022-01-12`

- 新增 ctrl+c 在编辑期内粘贴为带格式粘贴，在编辑器外粘贴为纯文本粘贴，删除 ctrl+shift+c 和 ctrl+shift+v 快捷键，用 ctrl+c 和 ctrl+v 替代
- 新增分组关联页眉兼容老数据
- 修复高清屏下鼠标下拉超越边界滚动不生效问题
- 修复右键文本域报错问题
- 新增 jsPdf 打印回调

## 1.2.6

`2022-01-10`

- 修复高清屏下鼠标下拉超越边界滚动不生效问题

## 1.2.5

`2022-01-10`

- 修复打印预览不显示问题

## 1.2.4

`2022-01-10`

- 修复 modal 拖拽问题
- 修复文本域提示及文本域校验提示在编辑器放大缩小后位置错误问题
- 修改字体文件加载方式
- 修复序号列表错乱问题
- 新增分组关联页眉信息方式

## 1.2.3

`2022-01-07`

- 修复分组锁定情况下，右键菜单中表格操作禁用问题
- 修复医学表达式过大问题
- 增加下拉选择框与日期选择框选择后事件，解决日期选择器选中后执行两边替换逻辑的问题
- 更新月经表达式 1 加载图片

## 1.2.2

`2022-01-04`

- 修复 pdf 分开打印问题
- 修复系统打印续打和区域打印不生效的问题
- 修复立即打印不生效问题
- 修复页眉中表格操作问题修复

## 1.2.1

`2022-01-04`

- 优化右键事件，减少 vue 挂载数据
- 直接打印参数完善
- 修改字体引入方式

## 1.2.0

`2022-01-04`

- 新增 pdf 打印方法
- 优化右键菜单续打和区域打印展示内容
- 修复校验码无法校验月份的问题
- 调整打印方向配置和批量打印逻辑
- 优化页眉表格线拖动，没编辑页眉不展示表格线
- 新增 copyEditor 接口
- 修复右键触发 pointer_down 的问题
- 优化续打遮罩表格线问题

## 1.1.4

`2021-12-30`

- 新增超过边界滚动选区功能
- 优化水平线实现逻辑
- 修复一倍状态下预览图片偏移问题
- 优化获取 blob 数组的调用和解决获取 blob 的问题

## 1.1.3

`2021-12-27`

- 优化医学表达式默认不展示时间框
- 优化下拉检索框获得焦点后不能删除文本域内容问题
- 优化打印逻辑

## 1.1.2

`2021-12-21`

- 修复编辑器配置传入错误

## 1.1.1

`2021-12-21`

- 新增退出编辑页眉页脚接口
- 新增移动端功能
- 修复删除选区开始为表格时删除后排版错乱问题
- 修复续打模式下整页不打印页眉页脚的问题
- 修复表格下方文字盖住表格线的问题
- 修复医学表达式模糊问题
- 优化打印方法位置
- 优化组装原始段落信息逻辑

## 1.1.0

`2021-12-17`

- 新增传递文件流按钮
- 修改文档刷新按钮
- 修改页眉页脚水平线显示隐藏控制接口
- 新版打印插件配置修改

## 1.0.65

`2021-12-16`

- 文本域替换图片接口更改
- 新增下拉框在未有匹配项时回车自动替换检索框内容
- 修复单元格复制未复制文本域问题
- 修复跨页表格内复选框点击不生效问题
- 修复跨页表格点击单元格报错问题

## 1.0.64

`2021-12-14`

- 新增浏览器打印回调
- 表格线粗细优化
- 优化续打模式点击页脚时全选页面
- 优化原始数据过大问题
- 修复页眉页脚在光标移动后不可编辑的问题
- 修复只读文本域内不允许插入文本域的问题
- 修复替换文本域时传入数字类型报错问题
- 修复部分点击单元格报错问题
- 新增自定义开始页码及页数接口
- 优化纯文本复制
- 修复替换元素到文本域对象接口问题
- 修复医学表达式在分组下还能打开的问题
- 调整右键插入表格最大行列数

## 1.0.63

`2021-12-09`

- 新增 config 配置项 font_family
- 修复区域打印从下网上选中问题
- 新增按住 ctrl 拖拽不删除原来选区功能
- 修复页眉页脚打印问题

## 1.0.62

`2021-12-08`

- 新增打印控制偏移参数

## 1.0.61

`2021-12-07`

- 更换复制和复制文本快捷键
- 新增区域打印功能
- 修复字体放大后表格线被遮挡的问题
- 修复空文本域打印时最小宽度不生效问题
- 修复 mac 系统快捷键问题
- 修复页眉页脚连续删除渲染异常问题
- 修改打印纸张大小参数配置

## 1.0.60

`2021-12-05`

- 打印还原

## 1.0.59

`2021-12-03`

- 新增文本域校验功能总开关
- 新增双下划线配置
- 修改页眉页脚遮盖后清晰度
- 修改默认字体配置，修改堆栈逻辑
- 修复快捷输入不准确的问题

## 1.0.58

`2021-12-02`

- 新增表格插入多行多列的接口
- 新增背景阴影配置项

## 1.0.57

`2021-12-01`

- 右键菜单中，将表格的插入行和列移入到表格操作中
- 修复常用语靠下位置不准确问题
- 修复快捷输入展示框折行混乱的问题
- 打印逻辑修改，防止顺序错乱，内容遗漏，根据类型控制校验规则体现

## 1.0.56

`2021-12-01`

- 新增 API 启用快捷输入功能
- 修复快捷输入不展示问题

## 1.0.55

`2021-12-01`

- 新增文本域最大宽度与最小宽度配置功能
- 修改文本域最大宽度和最小宽度属性
- 修复医学表达式改变大小后宽高不准确的问题

## 1.0.54

`2021-11-30`

- 新增文档信息可存储页眉页脚水平线展示配置
- 新增批量打印的方法
- 修改横向展示后页面大小不自适应的问题

## 1.0.53

`2021-11-29`

- 修复图片点击报错问题
- 修复模板数据加载报错的问题
- 修复滚动到文档最后，编辑时页面会自动滚动一下的问题

## 1.0.52

`2021-11-28`

- 依赖更新

## 1.0.51

`2021-11-28`

- 增加普通字体宽度，色值，加粗字体宽度，背景色的可配置功能。
- 修复了 shift 区域选择不生效的 bug。

## 1.0.50

`2021-11-25`

- 新增快捷输入弹框不可越界功能
- 增加复选框同组名成组功能。
- 增加配置项重置接口。
- 修复模态框拖出可视区域无法关闭的问题
- 修复只读文本域仍能够粘贴内容 BUG。
- 修复分组锁定后仍能点击设置复选框问题。
- 修复查找替换 tip 弹出框关闭后打不开的问题

## 1.0.49

`2021-11-24`

- 回车事件方向按钮事件禁用
- 增加编辑常用语弹窗
- 修复二级菜单被遮挡问题，调整层级
- 修改所有通过 id 获取 dom 方式，解决打开文件方法调用后内容加载到其他编辑器问题。

`2021-11-22`

- 增加获取搜索关键词的方法
- 增加根据表格 name 值获取表格数组的方法
- 增加获取光标位置处表格的方法
- 增加 keydownbefore 事件
- 增加横向纵向打印参数
- 增加中文输入开始和结束事件

## 1.0.48

`2021-11-19`

- 增加文本域只读时判断，不可通过接口再插入其他内容。
- 增加通过类型获取文本域数组接口。
- 修复表格内拖动图片报错的 bug

## 1.0.47

`2021-11-19`

- 增加右键菜单不兼容上版本
- 增加页眉页脚水平线显示隐藏控制。
- 优化段落居中逻辑，应用到每一行。

## 1.0.46

`2021-11-19`

- 增加 右键菜单增加配置
- 增加自定义多选框（新增、修改）
- 增加右键菜单文本域和多选框控制逻辑
- 修复只读文本域不能通过回退键删除只能通过选区删除的问题。
- 修复向空文本域中插入自定多选框未清空背景文本问题。
- 修复 Shift 键选区操作时，点击已选中区域会使选区消失问题。
- 修复插入非成组自定义多选框时不能设置禁用 BUG。

## 1.0.45

`2021-11-15`

- 初次上线稳定版本

## 2.30.15

`2023-11-20`

- 直接双面打印不生效 bug 修改

## 2.30.14

`2023-11-10`

- 适配 base

## 2.30.13

`2023-11-10`

- 适配 base

## 2.30.11

`2023-11-09`

- 适配 base

## 2.30.10

`2023-11-06`

- 适配 base

## 2.30.9

`2023-11-05`

- 适配 base

## 2.30.8

`2023-11-03`

- 打印报错
- 点击清除本地配置后直接点系统打印报错问题
- 痕迹对比颜色修改

## 2.30.7

`2023-11-02`

- 文本域打印边框功能
- 增加了配置记忆功能
- 打印机记忆功能修改
- 新增文本域对齐功能

## 2.30.1

`2023-10-23`

- 适配 base

## 2.30.0

`2023-10-16`

- 升级版本号

## 2.29.15

`2023-10-16`

- base 上已经从 editor 上挪到了 internal 上的方法,vue 上进行修改调用方式
- 下拉框编辑选项样式调整
- 新增插入矩形
- 新增水平线弹出框类型
- 痕迹对比测试按钮问题

## 2.29.22

`2023-11-03`

- 文本域打印边框功能

## 2.29.20

`2023-10-25`

- 增加批量打印双面打印功能

## 2.29.19

`2023-10-20`

- c++批量打印超过 20 份乱序问题

## 2.29.18

`2023-10-20`

- 适配 base

## 2.29.17

`2023-10-16`

- 和 45 做分割的一个版本

## 2.29.16

`2023-10-16`

- 适配 base

## 2.29.14

`2023-10-12`

- 适配 base

## 2.29.13

`2023-10-12`

- 增加网格线切换

## 2.29.12

`2023-10-11` -双击没有批注的文字报错

## 2.29.11

`2023-10-10`

- 文本域提示信息在顶部时展示在下方

## 2.29.10

`2023-10-10`

- 打开批注列表之后 canvas 的尺寸没有响应变化修改
- 批注列表关闭的时候也要更新一下 canvas 的尺寸
- 文本域提示信息在顶部时展示在下方
- 文本域提示信息在顶部时展示在下方
- 文本域提示信息在顶部时展示在下方

## 2.29.7

`2023-10-8`

- 1、提高文本域提示层级 2、初始化本地配置时调整
- 修复选区设置字体时如果开头位置为单选复选框或其他非字符类，则字号回显为 0，确定后排版错乱的问题
- 获取本地配置信息报错问题
- 打印配置默认为单面打印
- 读取打印配置信息逻辑修改

## 2.29.6

`2023-10-7`

- 添加批注报错处理

## 2.29.5

`2023-10-7`

- 双击打开批注编辑框

## 2.29.4

`2023-10-7`

- 适配 base 修改

## 2.29.3

`2023-10-7`

- 字体新增对齐方式
- 优化回显逻辑

## 2.29.2

`2023-10-7`

- 修改文本域提示框样式
- 使用 transUse 把新老批注进行隔离

## 2.29.1

`2023-9-27`

- 修复双面打印回显错误的问题
- 文本域提示信息支持换行功能
- 文本域提示信息样式微调
- 冒号缺失
- 修复特殊字符 pdf 预览不显示的问题
- 悬浮球样式调整
- 悬浮球初始位置调整

## 2.29.0

`2023-9-25`

- 新增调试工具级修改联宽度的方法
- 修复文本域公式编辑时会出现乱跳的情况
- 修复 JSPDF 绘制实心圆报错的问题
- 删除空配置时不应删除值为 0 的配置
- 增加 pdf 打印所需字体文件在众阳浏览器下可通过本地文件读取
- 修复复选框编辑添加按钮被遮盖的问题

## 2.28.14

`2023-10-17`

- 适配 base

## 2.28.13

`2023-10-16`

- 适配 base

## 2.28.9

`2023-9-19`

- 众阳浏览器下批量打印顺序问题

## 2.28.8

`2023-9-18`

- 保存修改痕迹兼容老数据

## 2.28.7

`2023-9-18`

- 保存修改痕迹兼容老数据
- 修复文本域弹窗层级问题
- 新增双面打印
- 修复级联文本域替换文本域到选择 name 的文本域的问题

## 2.28.6

`2023-9-15`

- 修复文本域级联点开报错的问题

## 2.28.5

`2023-9-14`

- 更新水印编辑模式与图形编辑模式接口
- 打印预览增加根据配置回显单双面打印状
- 修复时间框位置不正确的问题

## 2.28.4

`2023-9-14`

- 加了几个 log

## 2.28.3

`2023-9-14`

- 增加新旧逻辑切换按钮
- 把级联文本域从文本域设置中分离出来
- 暴露水印模式与形状编辑模式接口
- 去掉升级说明改为特殊功能
- 新增点击级联便捷插入功能
- 修复取消按钮不能关闭级联功能便捷插入的问题

## 2.28.2

`2023-9-11`

- 适配 base2.28.2

## 2.28.1

`2023-9-7`

- 文本域属性编辑性能优化
- 新增右键取消插入功能，新增右键点击和悬浮条联动功能
- 修复组件渲染钩子函数执行顺序问题
- 新增悬浮条河右键继续绘制折线功能及按钮随图形几何中折线的存在与否展示消失功能

## 2.28.0

`2023-9-5`

- 去掉了批注列表变化事件
- 去掉了几个没用的接口

## 2.27.27

`2023-9-11`

- 修复本地配置需求修改钩子函数异步导致其他产品使用报错问题

## 2.27.26

`2023-9-4` -适配 base2.27.22

## 2.27.5

`2023-8-24`

- 修复图片编辑在编辑完自定义线段后编辑圆或矩形报错问题

## 2.27.4

`2023-8-23`

- 新增 shape 悬浮球功能
- 第一次点击文本域属性弹窗位置不准确问题修复
- 添加自定义批注
- 新增水印便捷框
- 批量打印数据重复问题

## 2.27.3

`2023-8-18`

- 发版测试

## 2.27.2

`2023-8-18`

- 页面滚动的时候隐藏数字选择器
- 修复文本域公式不能在光标位置插入元素的问题
- 增加修改痕迹鼠标移入时显示新增还是删除
- 新增文本域公式点击标点符号后标记点击的符号
- 修改痕迹优化，增加图片鼠标移上去显示修改信息
- 自定义批注弹窗转移到 demo，右键菜单的添加自定义批注选项删除
- 修复切换减号后再点击文本域编辑器公式提示公式格式不正确

## 2.27.1

`2023-8-11`

- 调试工具和文本域属性弹窗同时存在时，页面交互 bug 修改
- 悬浮按钮拆分单元格接口调整
- 拆分单元格默认两列一行
- 暴露自定义批注的接口
- 本地设置功能优化，增加调试工具中设置固定纸张按钮
- 新增点击文本域公式 div 也能弹窗的功能

## 2.27.0

`2023-8-11`

- 新增文本域公式说明
- 修改批注之后编辑器设置为只读模式 bug 修改
- 批注替换之后编辑器变为只读
- 批量打印每份数据调用 afterPrint 在 42 上不用 setViewMode("normal")了,因为 base 上已经改了,简洁模式也可以打字
- 批量打印,里边的每份数据不管有没有 afterPrint 都要调用 update
- 自定义批注
- 增加文本域显示模式切换

## 2.26.7

`2023-8-9`

- 文本域公式新增>=<+功能
- 新增文本域公式选区后再点按钮插入数字应该清除选区再插入
- 文本域公式新增分号及修复点击问题
- 修复文本域公式新插入文本域时报错的问题
- pdf 打印内存增长问题优化
- 优化文本域公式输入框

## 2.26.6

`2023-8-7`

- 新增右键图形编辑
- 替换 icon 文件,修复右键菜单中单元格内的两条斜线不展示图标的问题
- 修改批注列表抽屉的展示层级改为 1,云病历那边弹窗太多,他们改成 2
- 文本域属性框标题修改

## 2.26.5

`2023-8-4`

- 分割单个单元格 bug 修改
- 软分割的时候处理合并单元格不绘制的线
- 选中整个表格在锁定分组只读模式,表单模式下不允许剪切删除
- 将 Ctrl+V 的判断挪到 paste 里边去
- 解决新版表格拆分一个单元格无限软分割的问题
- 修复 shape 报错问题

## 2.26.4

`2023-8-2`

- 增加拆分单个单元格的功能
- 解决编辑器克隆接口内存泄漏问题及性能优化
- 新版表格拆分基础班软分割逻辑完成
- 新版表格拆分硬分割时,row_size 不对的问题修复
- 修复敏感词检测返回敏感词数量跟真实存在的敏感词数量不一致的问题
- 文本域属性弹窗置顶连续插入报错处理
- 连续插入文本域和修改文本域之间切换报错修改

## 2.26.3

`2023-8-1`

- 用户登录接口调整
- 新增文本域划线功能

## 2.26.2

`2023-7-28`

- 修复 shape 点击编辑角拉长后保存位置不正确的问题
- 新增 C++打印 shape 的画圈和画 X

## 2.26.1

`2023-7-26`

- 医学计算公式,点击确定插入文本域带着结果,右键菜单也显示正确的公式

## 2.26.0

`2023-7-26`

- 医学计算公式获取不到年龄报错的 bug 修复
- 解决医学计算公式获取性别时,没性别不默认的 bug

## 2.25.14

`2023-8-2`

- 选中整个表格在锁定分组只读模式,表单模式下不允许剪切删除
- 将 Ctrl+V 的判断挪到 paste 里边去

## 2.25.13

`2023-7-28`

- 分组表单模式影响痕迹视图用户名展示问题

## 2.25.12

`2023-7-18`

- 暴露打开医学计算公式弹窗的接口
- pdf 所需字体文件主动加载接口暴露

## 2.25.11

`2023-7-17`

- 修复文本域公式不能二次修改 bug
- 都昌模板转换增加一些判断防止报错
- 修复文本域公式点击提示纯数字的问题
- 修改弹窗输入框不能点击,不显示光标的问题
- 添加提示 title

## 2.25.10

`2023-7-14`

- 修复文本域公式能打字的问题

## 2.25.9

`2023-7-11`

- 调试工具中增加管理员模式
- 文本域公式
- 统一名称
- 新增文本域公式回显功能
- 公式点击插入时增加一个间隔符
- 文本域公式最后一个删除不掉的问题
- 修复回显不正确的问题

## 2.25.8

`2023-7-7`

- 打印数据生成时如果图片缺少 src 则会报错的问题
- 表单模式下，选区删除文本域内容导致文本域被清空的 bug 修改
- (之前发版一直报错，前几版都发失败了，2.25.7 是最近的一版)

## 2.25.6

`2023-7-7`

- (环境出了问题，2.25.5 发版没成功，2.25.6 对应 base 的 2.25.9 版本)
- 选取字体增大缩小报错修改
- 在 modelPath2ParaPath 之前加了判断 modelPath 是否存在
-

## 2.25.5

`2023-7-4`

- 医学计算公式获取性别和年龄自动指定正确的弹框
- 选区字体缩放，文本域边框不变的 bug 修复

## 2.25.4

`2023-6-30`

- 数字类型的文本域增加双击激活方式
- 解决水印模式下输入文字时,右键菜单不显示的问题
- 增加辅助按钮设置后常驻
- 右键菜单医学公式调整位置并且增加图标
- 双击文本域展示医学计算公式弹窗,并根据 placeholder 默认选项
- 弹窗移动后 ctrl+a 全选失效问题修复
- 医学公式增加路径导航
- 医学计算公式点击确定填写到文本域内,小数点精确到三位小数
- 多选问题修复

## 2.25.3

`2023-6-28`

- 痕迹对比弹窗的若干修改

## 2.25.2

`2023-6-27`

- 修复文本域公式以；分割的多个公式不生效的问题

## 2.25.1

`2023-6-26`

- 优化更新文本域公式的方法
- 增加了数字类型位数无限制
- 修改了小数四舍五入的规则
- 新增判断大小文本域公式逻辑

## 2.25.0

`2023-6-20`

- 40 迭代需求

## 2.24.10、

`2023-7-10`

- - 表单模式下选区删除导致文本域清空的补丁

## 2.24.9

`2023-7-5`

- 适配 base2.24.9

## 2.24.8

`2023-7-4`

- 因为 base 上修改了 copyEditor,新增了 getPrintEditor,所以 vue 上对应修改

## 2.24.7

`2023-7-3`

- 增加 internal 属性,transformData,在每次执行 rawData2ModelData 的时候,收集图片和文本域数据
- beforePaste 事件,返回数据改为对象,增加返回数据转换后的 transformData

## 2.24.6

`2023-6-27`

- 表单模式下选区删除错误修改
- 都昌模板转换图片大小问题修复
- 选择框点击文本选中时触发选区问题
- 增加路径有效性判断,解决 refreshDocument 强制更新时,数据变化导致路径转换时报错的问题

## 2.24.5

`2023-6-26`

- 增加图片信息异常提示
- 单元格复制样式问题
- shift 选区 bug 修改
- pdf 打印将图片转白色背景时直接使用 canvas
- 首行缩进和 tab 键缩进，在选区包含表格的情况下报错处理

## 2.24.4

`2023-6-25`

- 暂时注释 jspdf 优化代码，观察医生签名图片生成 pdf 时丢失问题
- 解决右键菜单的问题

## 2.24.3

`2023-6-20`

- 增加辅助按钮文本域最小宽度调整
- 解决右键菜单位置不对,高分辨率下有些选项展示不全的问题
- 调整部分依赖的版本
- 解决右键菜单点击屏幕下方,右键菜单非常矮的问题

## 2.24.2

`2023-6-19`

- 模板转换报错
- 显示隐藏按钮不生效问题
- 解决单元格软分割时,段落 children 为空,导致获取字符串位置为空的问题
- 去掉了一个选区删除的判断
- 解决调用 updateFieldText 找错文本域的 bug
- 改了文本域属性弹窗的样式
- 增加本地启动命令，解决 node18 启动报错问题

## 2.24.1

`2023-6-14`

- shift 添加选区功能
- 切换构建工具为 vite
- ts 报错问题
- 使用 vite 后不能热更新的问题
- 使用 useLetterPlaceholder 属性将输入法输入时字母占位的新旧功能进行了隔离
- XField.ts 中,定位到文本域的方法,加是否在正文是否是编辑页眉页脚模式的判断,解决循环全文档文本域调用 editor.reviseFieldAttr 报错的问题

## 2.24.0

`2023-6-13`

- 39 迭代需求

## 2.23.18

`2023-6-15`

- 解决单元格软分割时,段落 children 为空,导致获取字符串位置为空的问题

## 2.23.17

`2023-6-13`

- 修改获取字符串位置,字符串在分页表格中位置不对的 bug
- 修改获取字符串位置中表格内的数据页码不对的 bug

## 2.23.16

`2023-6-12`

- 获取字符串相对于页面位置中干掉了测试的值
- 获取字符串相对位置乘以 0.749
- 修复获取字符串相对位置里边获取最后一个字符下标不对的问题

## 2.23.15

`2023-6-12`

- 只有一个表格的模板打开报错问题
- 修改获取字符串位置,改为获取最后一个字符的位置

## 2.23.14

`2023-6-9`

- 修復弹出框监听事件冲突问题
- 门诊未知原因导致 vue 与 base 版本不一致问题报错

## 2.23.13

`2023-6-7`

- 修復模態框拖拽移動問題

## 2.23.12

`2023-6-7`

- 适配 base2.23.15

## 2.23.11

`2023-6-6`

- 都昌模板转换图片数据增加判断
- 服务端调用接口无需受只读模式影响
- 服务端调用接口无需受只读模式影响及转打印数据时判断错误
- 修复滚动表格拉到最后删除滚动不正确的问题
- 还原 set_cell_height 命名

## 2.23.10

`2023-6-2`

- 性能监控只监控电脑端
- 仅选择模式改成了单独的属性
- 增加性能监控信息初始化时判断，防止不同操作系统不兼容
- 水印模式下弹窗问题

## 2.23.9

`2023-6-1`

- 增加图片排版 3,3,4 的情况

## 2.23.8

`2023-6-1`

- ImageTable.ts 中 updateSerialNum 更新时判断是否是序号再更新值,解决交换图片顺序之后全都改成序号的 bug
- 解决调用 insertImageTable 的时候表格没有创建成功报错的 bug
- insert_raw 里边给 ImageTable 属性赋值增加 imageList 解决撤销后再插入报错的 bug
- 文档对齐不同步 bug 修改
- 模板转换后级联失效问题

## 2.23.7

`2023-5-30`

- 增加字号增减的图标
- 移动端报错问题

## 2.23.6

`2023-5-30`

- 增加悬浮按钮字号增减、字符间距还是放在字体中
- 增加帮助信息
- 性能监测时间显示格式化错误

## 2.23.5

`2023-5-29`

- 解决不展示拼音输入内容换行时自动删除内容的 bug

## 2.23.4

`2023-5-26`

- 增加性能监测窗，用于性能监测
- 新增滚动单元格逻辑

## 2.23.3

`2023-5-26`

- 增加单元格内边距按钮
- 悬浮按钮增加按住持续触发功能
- 悬浮球字符间距 bug 修改
- 悬浮按钮增加属性控制是否可连续触发
- 悬浮按钮增加段落重排
- 悬浮按钮增加插入按钮
- 右键菜单微调

## 2.23.2

`2023-5-23`

- 实现图片排版中创建合并后表格的功能
- 固定单元格高度滚动功能
- removeParagraphElement 参数形式修改
- 多层嵌套级联文本域设置

## 2.23.1

`2023-5-19`

- 解决新版表格拆分逻辑 row_size 分配不对的问题和 cell.split 传参不对的问题，错误效果是有空白窜罗和分页单元格内容永远在上一页，没有被拆分
- 复制粘贴表格数据样式调整
- 新增指定文本域高亮功能
- 修复页眉中插入表格表格内无内容的问题
- 新增获取所有图片的接口
- 重整批量打印方法

## 2.23.0

`2023-5-17`

- 只读模式下，允许删除,添加批注
- 悬浮球里的字符间距功能
- 修复右键菜单位置不准确的问题
- 增加”仅选择“功能
- 去掉右键菜单 style 中的最大高度属性值
- 修改右键菜单最大高度的计算方法

## 2.22.18

`2023-6-7`

- 增加了字母占位的配置

## 2.22.17

`2023-6-1`

- 移动端报错问题

## 2.22.16

`2023-5-29`

- 适配 base2.22.13 修改

## 2.22.15

`2023-5-16`

- 修复右键菜单位置不准确的问题

## 2.22.14

`2023-5-15`

- 增加悬浮按钮配置控制

## 2.22.13

`2023-5-15`

- 修改 font_style 的 ts 类型和上标下标的常量表示
- 干掉所有 ScriptEnum 替换成 ScriptType
- 临时解决 XField.toggleSymbol 卡顿问题，调用 refreshDocument(true)
- 给 cell.updateChildren 参数增加 ts 类型校验，优化逻辑代码，减少 get 属性的重复调用
- 表格 copy 增加 imageList 的 copy 解决 pacs 模板替换之后排版中的图片不能删除的问题
- 调整 json 对比接口，增加可传入忽略属性参数
- 解决模板替换，原来只读文本域插入后变成非只读的问题
- 修改右键菜单最大高度的计算方法

## 2.22.12

`2023-5-12`

- 修复批注信息滚动时造成排版错乱问题
- beforeFieldSelect 事件支持返回值设置 await

## 2.22.11

`2023-5-12`

- 文本域最小宽度换行时处理
- 同一个分组内，外面内容不能往表格中拖拽的 bug
- 修改 Paragraph.ts 文件中的 updateChildren 方法，更简化
- 优化全选逻辑
- 取消高亮优化
- 消息事件提示参数增加校验
- 抽屉组件自行实现，解决右键菜单二级菜单显示超出屏幕问题
- 兼容抽屉批注显示与菜单显示问题

## 2.22.10

`2023-5-10`

- 分组表单模式分组定位视图滚动不正确问题

## 2.22.9

`2023-5-9`

- 解决中文输入时，设置字体样式只闪一下，继续打字不生效的问题
- 优化设置文本域最小宽度问题，文本域换行时仍存在问题
- 分组表单模式光标问题

## 2.22.8

`2023-5-9`

- 分组表单模式开启后不允许通过光标移动跳分组
- 调整文本域最小与最大宽度设置逻辑
- 解决段落和文本域 replaceWith 中 cell 位置判断不准的 bug
- 插入模板增加参数是否替换成 rawData，判断是否有 hasLastLineBreak 和是否走插入模板逻辑，解决文本域替换时，段落不居中的问题
- 修改了 caret_move 方法使用的 direction 的 ts 类型，修改了参数名
- 蓝色字体颜色设置不生效问题修复

## 2.22.7

- 悬浮球在 demo 位置不正确问题

## 2.22.6

- 发版错误，重新发了一次

## 2.22.5

`2023-5-8`

- 悬浮按钮完善
- 双面打印机打印设置后提示调整

## 2.22.4

`2023-5-5`

- 修复文本域边框置空后删除报错的问题
- 实现文本域的 getRawData 方法
- PDF 和 C++打印绘制斜线
- 在 Editor.ts 文件中暴露批次获取容器的 rawData 的方法和批量替换的方法
- 修改文本域，段落和表格的 replaceWith 方法中的插入模板，由 editor 调用改为 EditorHelper 调用，避免在历史堆栈中记录
- 悬浮按钮完善

## 2.22.3

`2023-5-4`

- 在 Utils 中增加抽离出的初始化 cell 的方法，修改了 Selection 中 getRawData 逻辑，并给获取文本域的 rawData 方法开个头
- 段落排版相关代码整理
- 增加悬浮球本地开关
- 双面打印

## 2.22.2

`2023-4-28`

- 修复改变文本域边框边框位置不正确的问题

## 2.22.1

`2023-4-28`

- 优化文本域边框逻辑，高度跟随最高的元素
- 修复 input 文本域有时画不上的问题

## 2.22.0

`2023-4-27`

- 37 版本全部需求

## 2.21.13

`2023-5-16`

- 临时解决表格隐藏显示边文本域框卡顿问题

## 2.21.12

`2023-5-9`

- 解决稍微拖拽 label 标签，位置不变，getFocusField 不对的 bug
- 增加根据 base64 导出文件工具函数
- 设置单元格边距文字重叠 bug 修改
- 修复文本域边框置空后删除报错的问题
- 修复 c++打印报错问题

## 2.21.11

`2023-4-26`

- 修改 insertText 方法
- 修复 PDF 打印预览报错问题
- 嵌套文本域跨多段时数据转换结构错乱
- 微调模型数据转原始数据逻辑
- 修改了替换换行符的函数名
- 修改 Paragraph.ts 中的 insertElements 的参数名字等
- 按钮移入移出效果问题修复
- 页眉文本域 updateFieldText 结果为 undefined 的问题处理
- 对比 rawDatabug
- 对比 rawData 函数逻辑优化

## 2.21.10

`2023-4-24`

- 修复低版本众阳浏览器奇偶页不生效的问题
- 背景色设置报错、去除默认的自定义右键菜单

## 2.21.9

`2023-4-23`

- pdf 图片打印报错

## 2.21.8

`2023-4-23`

- 痕迹对比内容回显不正确问题

## 2.21.7

`2023-4-21`

- 解决嵌套文本域数据转换的 bug

## 2.21.6

`2023-4-21`

- 解决中文输入法的时候在锁定分组内仍然能够编辑的 bug
- 解决撤销错乱的问题
- 修复字符对齐问题
- 解决数据转换有引用，导致撤销时数据错乱的 bug
- 修复在表格内首行缩进报错的问题
- 修改替换特殊字符的方法
- 修改合并单元格和判断是否可以合并的函数名字

## 2.21.5

`2023-4-20`

- 增加字符对齐兼容性处理逻辑
- 增加隐藏批注里的替换按钮
- 文本域不可编辑下的提示语

## 2.21.4

`2023-4-20`

- 尝试修复组件没有注册的问题
- 时间下拉框编译后不显示修复测试

## 2.21.3

`2023-4-19`

- 解决修改单元格内容，其他非上对齐单元格内容位置不变的 bug，解决新版表格拆分，第二页往后表格 top 值计算不对的 bug，去掉嵌套文本域数据转换原来的 userLocal
- 增加低版本浏览器复制粘贴纯文本
- 非 https 协议打开的数据复制，放进剪切板纯文本
- 复制粘贴时只有不是本地的时候才进行版本和 https 协议的判断

## 2.21.2

`2023-4-18`

- 文本域边框样式与其中文本样式一致时内容丢失 bug
- 嵌套文本域数据转换增加判断
- 插入模板单选框外框变蓝色问题修复
- 修改插入图片排版和删除控制序列号的逻辑

## 2.21.1

`2023-4-17`

- 复制粘贴不带样式，压缩数据部分恢复
- 增加打印前事件，用于修改打印配置
- 快速录入模式 bug 修复（getnextField 方法还没改）
- 解決跨段嵌套文本域 rawData 转 modelData 造成被嵌套的文本域跟外层平级的 bug，删除报 removeElement 的那个
- 修复文档对齐的复制问题
- 撤销不显示的 bug 修改
- 修复文本域日期选择器 show_format 属性如果通过接口设置会造成展示与替换格式不匹配的问题
- 删除老版 JSPDF 打印
- 增加打印前事件用于修改打印参数

## 2.21.0

`2023-4-10`

- 36 版本全部需求

## 2.20.13

`2023-4-13`

- 修复文档对齐的复制问题
- 修复区域打印在表格内多打一行的问题
- 增加文本域面板选择前事件

## 2.20.12

`2023-4-12`

- 修复字符对齐导致的报错问题

## 2.20.11

`2023-4-11`

- 增加打印前事件，用于修改打印配置
- 修复打印预览和结果对不齐的问题

## 2.20.10

`2023-4-10`

- 补充缺失的中文字体
- 日期选择框新增汉字秒类型

## 2.20.9

`2023-4-7`

- 修复字符对齐在简洁模式下对不齐的问题

## 2.20.8

`2023-4-6`

- 打印内容缺失问题修复
- 去掉 modelDataToRawData 中的序列化代码，解决 pacs 打字操作卡顿的问题
- 修复字符对齐删除时位置不正确的问题

## 2.20.7

`2023-4-4`

- 增加对比两个 json 是否一致接口，忽略一些 id 类属性

## 2.20.6

`2023-4-4`

- 修复 ImageTable 数据转换时部分属性未保存
- 文本域 style 属性中缺少 family 与 height 时打印众阳浏览器闪退问题

## 2.20.5

`2023-4-3`

- 检查分组 id 的方法提取
- 解决了一个根据 path 获取不到内容的报错
- 暂不使用图片优化,先使用本地测试

## 2.20.4

`2023-4-3`

- 增加分组内容转换
- 优化模板转换分组相关代码
- 移除新增加的修改图片 dpi 方法
- 插入图片排版测试按钮问题修复、modelData 转 Dom 逻辑优化
- 插入模板图片丢失问题修复
- 修复 editor 数据改动导致的打印预览报错问题
- 取消背景按钮显示 bug 修改

## 2.20.3

`2023-3-24`

- 判断分组路径 bug 修改
- 优化批量打印有关 PDF 打印的相关逻辑
- 字体颜色 colorPicker 背景色修改

## 2.20.2

`2023-3-24`

- 复制 editor 接口增加 imageMap 处理
- 判断分组路径修改

## 2.20.1

`2023-3-23`

- 修复打印预览时没有图片的问题

## 2.20.0

`2023-3-22`

- 35 版本需求

## 2.19.18

`2023-4-7`

- 数据转换时序列化复制性能问题

## 2.19.17

`2023-3-28`

- 新增自适应窗口大小接口

## 2.19.16

`2023-3-24`

- 解决打字还没回车或者空格的时候就删除选区的 bug

## 2.19.15

`2023-3-21`

- recordVersionInfo 修改

## 2.19.14

`2023-3-21`

- 解决表单模式下，在可编辑表格内，点击空文本域，光标位置在开头的 bug
- 修改了文本域边框保留的版本号判定
- 表单模式下，在可编辑的表格内按方向键移动光标不受表单模式影响

## 2.19.13

`2023-3-20`

- 分组页眉信息替换功能 bug 修复
- 图片排版的表格内的图片不让放大缩小
- 解决选区末尾坐标在段落开头时删除会多删除一行的 bug，统一单元格内外的复制逻辑
- 都昌模板转换未保留单元格左右内边距问题

## 2.19.12

`2023-3-15`

- 修改表格属性，去掉单元格的最大高度设置时也更新
- contentChanged 事件中添加处理固定单元格高度的逻辑

## 2.19.11

`2023-3-15`

- 表单模式下护理表单点击右边纸张外区域报错

## 2.19.10

`2023-3-14`

- 表格拆分时，处理透明度的线
- 修复单元格内选区到下一段开头时设置选区内容与复制粘贴内容错误 bug
- 表单模式中空文本域使用 delete 删除导致光标位置错误
- 修复续打时会多打印上一行的签名的问题
- 段落重排接口支持只清空空段
- 修复低版本谷歌打印预览报错问题

## 2.19.9

`2023-3-9`

- 查找替换接口调整，优化高亮逻辑，返回查找数量
- 编辑器只读时可弹出图片编辑窗口问题修复

## 2.19.8

`2023-3-7`

- drawCell 传入 page 时绘制列表接口报错
- 表格拆分逻辑，拆分成两页情况，基本实现
- 插入多段内容报错问题修复
- 修复表单模式下粘贴时偶发粘贴到文本域外的问题
- 修复字符对齐时换行导致对不齐的问题

## 2.19.7

`2023-3-3`

- 修复分组内段落重排时将所有段落全部移除 bug

## 2.19.6

`2023-3-3`

- 修复打印接口阻塞的问题

## 2.19.5

`2023-3-3`

- 修复打印接口阻塞的问题

## 2.19.4

`2023-3-2`

- 调整进入页眉页脚编辑状态接口，增加剪切板写入与读取接口
- 每次设置焦点元素时先清空焦点元素

## 2.19.3

`2023-3-2`

- 重写表格拆分逻辑
- 新增段落重排剩余最后一段时需要判断是否删除
- 修改内部使用接口名 focus->setSelectImageOrWidget
- 修改获取当前焦点元素方法并加入本地测试
- 增加内部使用变量及函数管理类
- 修复批量打印打印顺序不正确问题
- 获取打印机列表失败后及时退出续打模式及显示文本域边框
- 新增打印预览刷新打印机列表功能
- 修复众阳浏览器不能记录选择的打印机问题处理
- 托盘手动重连不生效问题

## 2.19.2

`2023-2-27`

- 修复整合后 pdf 打印水印偏移的问题
- 修复区域打印选区不准确的问题
- 修复整合后水印绘制顺序
- 只有下拉框和日期选择面板只读时提示
- 增加动态请求下拉框不走缓存参数控制
- 修复打印当前页打印全部的问题
- 修复打印预览无法输入页码的问题

## 2.19.1

`2023-2-23`

- 增加文本域下拉列表动态异步请求方式支持

## 2.19.0

`2023-2-23`

- 34 迭代全部需求，打印相关提到 base

## 2.18.15

`2023-3-9`

- 单元格内选区到下一段开头时设置选区内容与复制粘贴内容错误 bug

## 2.18.14

`2023-3-6`

- 将白色为#FFF 的值转为透明背景色，之后新设置的白色背景需要设置成#FFFFFF,目的是为了解决之前修改过字体样式后会自动设置白色背景色问题

## 2.18.13

`2023-3-6`

- 解决文本域内字体缩放的 bug

## 2.18.12

`2023-3-6`

- 文本域设置最大高度后，复制内容粘贴到选区内容时无法替换选区内容
- 打印带列表的内容时报错
- cpp 打印列表报错

## 2.18.11

`2023-2-27`

- 修复打印当前页打印全部的问题
- 修复打印预览无法输入页码的问题

## 2.18.10

`2023-2-21`

- 可编辑表格在表单模式下里边的图片是可以选中的
- 分组增加获取纯段落属性
- 获取文档内所有段落增加可获取纯段落

## 2.18.9

`2023-2-16`

- placeholder 颜色错误修改

## 2.18.8

`2023-2-15`

- 匿名化星号展示规则问题修复

## 2.18.7

`2023-2-14`

- 匿名化星号展示规则问题修复
- 解决在空文本域内粘贴字体不缩放的问题

## 2.18.6

`2023-2-13`

- 匿名化问题修复
- 增加生僻字转图片方法，暂不启用
- 段落重排传入表格报错

## 2.18.5

`2023-2-10`

- 匿名化页眉页脚不生效问题修复

## 2.18.4

`2023-2-10`

- 图片打印匿名

## 2.18.3

`2023-2-9`

- 都昌模板转换增加服务端兼容
- 在 contentChanged 中添加处理文本域字体缩放的逻辑

## 2.18.2

`2023-2-9`

- 都昌模板转换增加服务端兼容

## 2.18.1

`2023-2-1`

- 新增正序逆序功能
- 调试工具修改
- 增加设置当前分组新页展示属性
- 优化绘制临时 border 的方法，减少传参

## 2.18.0

`2023-1-31`

- 33 版本全部需求

## 2.17.10

`2023-2-15`

- 优化设置文本域内字体缩放的方法和文本域增加 rows 的计算属性
- 文本域内字体缩放给放大的循环 z 增加次数限制，避免死循环

## 2.17.9

`2023-2-10`

- 修改文本域获取高度的方法，设置字体大小加基础高度 d 的参数
- 修改文本域字体缩放逻辑

## 2.17.8

`2023-2-10`

- 去掉 Editor.ts 文件中 setCharacterSize 中的 @undoStackRecord(commands.uncertainty)

## 2.17.7

`2023-2-10`

- 增加文本域设置字体缩放的接口
- 文本域和段落设置字体大小方法增加参数，可传最大高度
- 设置字体大小添加到历史堆栈中
- 处理文本域内字体缩放，粘贴的时候也能正常执行

## 2.17.6

`2023-2-08`

- 增加生僻字支持，修改弹窗方式

## 2.17.5

`2023-1-31`

- 新增配置可控制编辑器展示字体
- 修复批量打印报错

## 2.17.4

`2023-1-29`

- 修复段落重排段落全是空行的情况
- 分组属性新增分组表单设置按钮

## 2.17.3

`2023-1-29`

- 修复分组表单模式点击切换分组时的问题

## 2.17.2

`2023-1-20`

- 新增分组表单模式

## 2.17.1

`2023-1-19`

- 新增表格指定行转换接口
- 新增表格转换原始数据时合并单元格的处理
- 修复文本域替换图片接口不支持字符串宽高的问题
- 新增按配置加载接口

## 2.17.0

`2023-1-18`

- 修复表格线在放大时位置不准确的问题
- 根据版本号判断文本域边框宽度是否保留

## 2.16.10

`2023-1-13`

- 修复插入模板时合并文本域样式时未加判断的问题

## 2.16.9

`2023-1-13`

- 插入模板时统一字体字号时兼容老模板

## 2.16.8

`2023-1-13`

- 新增服务端获取在线图片接口判断
- 新增插入模板是否使用默认字体字号配置

## 2.16.7

`2023-1-11`

- 新增服务端重置状态接口
- 修复时间框快捷输入时分秒不回显的问题
- 修复鼠标停留提示信息显示位置不正确的问题
- 注释数据压缩接口

## 2.16.6

`2023-1-9`

- 修改表单模式下文本域粘贴到文本域之外的 bug

## 2.16.5

`2023-1-6`

- 修复图片编辑报错的问题

## 2.16.4

`2023-1-5`

- 修复 C++打印水印图片第二页之后不显示的问题

## 2.16.3

`2023-1-5`

- 修复 C++打印水印图片第二页之后不显示的问题

## 2.16.2

`2023-1-5`

- 修复表格背景色复制换页问题
- imageMap 中图片加载无需等待图片加载完后在放置
- 新增从 imageMap 获取不到数据时的判断

## 2.16.1

`2023-1-4`

- 修复页边距 40 时选中表格最后一列选区不对的问题
- 新增改变表格背景色的问题
- 新增授权码过期提示水印接口
- 图片打印修改 dpi 报错问题
- 优化图片编辑画笔颜色逻辑
- 新增表格选区改变背景颜色
- 注释授权码校验逻辑
- cell 中的 bgcolor 放到 style 中

## 2.16.0

`2023-1-3`

- 31 版本需求
- 增加表单只读属性，防止与编辑器只读混乱

## 2.15.7

`2022-12-30`

- 修复表格在表单模式下可编辑没有保存的问题
- 绘制表单模式下表格可编辑提示框
- 处理特殊字符，底层逻辑会将其转换成乱码问题
- 去掉 image.onload 中白色背景逻辑
- reInitRaw 中添加 watermark 逻辑
- 修复水印图片第二页 pdf 不打印问题

## 2.15.6

`2022-12-28`

- 还原打包配置

## 2.15.5

`2022-12-28`

- 修复背景绿色问题

## 2.15.4

`2022-12-28`

- 修复移动端编辑器页面偏移问题

## 2.15.3

`2022-12-27`

- 修复水印输入框拖动位置不准确的问题
- 修复 reInitRaw 之后打字撤销字体样式变粗的问题
- 表单模式下按住鼠标左键不允许粘贴

## 2.15.2

`2022-12-26`

- 水印文字功能提交
- 右键菜单事件更改变量名
- 增加 id 重复校验，增加按照时间顺序插入分组
- 修复插入空行问题，记忆插入分组
- 将所有的 window.electron 进行调整，取不到时从 top 中取

## 2.15.1

`2022-12-19`

- 时间框输入校验

## 2.15.0

`2022-12-16`

- 30 迭代发版

## 2.14.11

`2022-12-14`

- 修复打印预览图片跨域问题
- 新增字体高度配置给出不符合要求的提示

## 2.14.10

`2022-12-14`

- 修复当特殊字符在字典文件中找不到报错的问题
- 新增水印图片删除的方法

## 2.14.9

`2022-12-13`

- 修复水印图片模式切换位置问题
- 修复 pdf 图片打印和 c++打印中水印图片位置不正确的问题

## 2.14.8

`2022-12-13`

- 恢复文本域样式优化

## 2.14.7

`2022-12-13`

- 修复引入模板数据插入到文本域外部的问题
- 校验功能修复

## 2.14.6

`2022-12-12`

- 修复固定单元格高度后，内容满了再删除，单元格高度变大的问题
- 修复字体缩放到一页后删除内容导致报错的问题
- 修复背景绿色问题

## 2.14.5

`2022-12-12`

- 修复背景绿色问题

## 2.14.4

`2022-12-9`

- 修复固定单元格高度输入内容会导致表格下方内容消失的问题

## 2.14.3

`2022-12-9`

- 配置字体缩小到一页
- 修复移动端水印多出的 div 的问题
- 修复模板字符串报错的问题
- 修复右键菜单位置不正确的问题
- 表格属性中新增单元格固定高度
- 修复下拉列表替换分隔符不生效的问题

## 2.14.2

`2022-12-6`

- 修复固定单元格高度，单元格内文本域没有缩小的问题
- 解决下拉框问题

## 2.14.1

`2022-12-5`

- 增加自定义扩展信息属性
- 修复加密数据打开报错
- 增加压缩功能后，另存 json 接口调整
- 在插入分组并排序的方法中加上清空选区的操作
- 单选搜索内容恢复

## 2.14.0

`2022-12-5`

- 先发一版

## 2.13.3

`2022-11-30`

- 修复都昌模板转换时设置级联文本域报错问题及下拉选择框激活模式问题
- 新增暴露压缩解压的接口
- 新增组件销毁前事件解除绑定

## 2.13.2

`2022-11-27`

- 修复往分组内粘贴内容跑到分组外的问题
- 去掉判断解决粘贴段落内部分内容插入空行的问题
- 开启 dpi 修改后所有图片都进行转换，否则有部分图片不能正常展示
- 修复修改纸张类型与修改横纵向不生效的问题

## 2.13.1

`2022-11-25`

- 解决图片排版不能保存图片之间间距的问题
- 修改 getRawData 和 reInitRaw 可以获取压缩后的数据和加载后的数据
- 修复快速点击文本域紧接着输入内容回电到文本域外边的问题
- 新增单选双选模式切换
- 新增多选分隔号选择
- 修复不能粘贴图片的问题
- 内存回收问题处理，修改 bus 传值逻辑去掉 webWorker 增加组件销毁前解绑事件逻辑

## 2.13.0

`2022-11-21`

- 新增从外部复制图片到编辑器中
- 修复 C++打印有序列表序号不齐的问题
- 编辑器接口整理
- 新增选区复制某些单元格可直接粘贴到另一个表格的选区中
- 编辑器加载数据时可读取数据中的配置
- 编辑器数据结构优化
- 编辑器新增水印接口
- 编辑器表单模式下实现表格可编辑

## 2.12.8

`2022-11-23`

- 修复页面切换后调用 updateCanvasSize 方法会将页面恢复到光标位置的问题

## 2.12.7

`2022-11-16`

- 修复签名图片不能打印的问题

## 2.12.6

`2022-11-15`

- 编译不成功重新发版

## 2.12.5

`2022-11-15`

- 编译不成功重新发版

## 2.12.4

`2022-11-15`

- 调试工具增加强制刷新按钮

## 2.12.3

`2022-11-10`

- 修复表格分页时插入行携带数据报错的问题

## 2.12.2

`2022-11-8`

- 修复浏览器打印会打印文本域边框及背景文本问题修复
- 更换图片编辑 id 获取方式，加入 editorId

## 2.12.1

`2022-11-7`

- 修复云病历转科后导致页眉与正文之间出现多行空白的问题
- 找回 editor.ts 文件中表格是否可以删除行和列的方法
- 修复图片因 dpi 不同导致在个别电脑打印不全的问题
- 优化图片编辑逻辑
-

## 2.12.0

`2022-11-4`

- 选区内容可以放大缩小
- 增加粘贴前事件，可对要粘贴的内容进行处理
- 表格新增行时可自动携带上一行内容
- 增加编辑器服务端原始 json 转 PDF Base64 接口(详细信息见编辑器在线 API 文档)
- ctrl+tab 能够在表格内设置缩进
- 图形编辑功能扩展，增加矩形绘制、文本插入、矩形背景设置

## 2.11.7

`2022-11-7`

- 修复云病历转科后导致页眉与正文之间多出多行空白问题

## 2.11.6

`2022-11-1`

- 修改文本域属性接口，增加判断文本域是否已经被删除

## 2.11.5

`2022-11-1`

- 修复修改页眉中文本域报错的问题

## 2.11.4

`2022-10-27`

- 修复修改页眉中文本域报错的问题
- 修复续打模式下页眉下多一个空行的问题

## 2.11.3

`2022-10-26`

- 新增服务端启动传入 rawData
- 修复插入图片不显示及透明背景图片打印绿色问题
- 修复段落重排有空字符串报错和表格内无反应问题
- 新增\r 字符宽度判断
- 修复 C++精度问题引起的续打位置不正确的问题

## 2.11.2

`2022-10-24`

- 新增服务端启动传入配置项
- 图片背景改变还原

## 2.11.1

`2022-10-21`

- 修复段落重排首行不缩进情况不清除空格的问题

## 2.11.0

`2022-10-21`

- 调整段落重排接口，通过参数可控制是否清除段落所有的空格、是否需要设置段落首行缩进。
- 新增将光标定位到单元格接口
- 删除或者清空分组的时候，没有删除 cell 内对应的文本域
- 获取多个文本域，但是只给一个文本域赋新值，没有赋新值但是又调用 updateFieldText 的方法的文本域，值会变成 undefined
- 痕迹视图中的痕迹文本背景色不应该被复制

## 2.10.6

`2022-10-28`

- 修复续打模式下页眉下多一个空行的问题

## 2.10.5

`2022-10-19`

- 修复都昌模板转换后图片变大、下划线不显示、字体颜色与背景色未转换、表格线显示隐藏的问题
- 修复没有任何批注信息时切换批注模式报错的问题
- 新增文档无批注信息时友好提示

## 2.10.4

`2022-10-11`

- 修复文本域简单复选框点击报错问题
- 修复切换文档后批注刷新问题

## 2.10.3

`2022-10-10`

- 修复页眉分组关联导致页眉高度问题
- 新增是否展示图片弹框方法

## 2.10.2

`2022-10-10`

- 版本加载问题重发

## 2.10.1

`2022-10-10`

- 新增双击图片弹出图片编辑框判断
- 修复护眼模式下复选框背景颜色问题
- 修复背景色使用批注颜色的问题

## 2.10.0

`2022-10-10`

- 新增护眼模式
- 去除 pdf 打印所需字体文件请求时的版本号,避免每次升级后都重新请求字体文件
- 优化单元格复制、数据更新代码逻辑以提升编辑时性能
- 修复光标定位到文档末尾接口 bug
- 增加图片编辑功能，初步先实现可在图片上绘制线条及线条的编辑功能。
- 优化云病历病程记录多页时卡顿问题
- 修复清空内容按钮的 BUG
- 优化表格插入行列时边框线显示隐藏效果

## 2.9.1

`2022-09-23`

- 修复文本域日期选择器 HH：mm 格式类型错误问题
- 补全自定义文本域校验

## 2.9.0

`2022-09-23`

- 文本域日期选择器增加 HH：mm 格式类型
- 修复表格分页时上下拖动横线，单元格上下居中，下对齐的内容没有实时变化问题
- 增加手动设置双面打印机功能
- 日期选择面板有时候会遮挡输入框问题修复
- 修复拖拽标签文本域到另一个普通文本域中，直接右键修改标签文本域内容会将普通文本域整个替换问题
- 解决设置字符对齐的模板调用更新文本域接口时传入字符对齐参数后报错问题
- 优化编辑器加载多页内容时编辑器滚动效果

## 2.8.1

`2022-09-14`

- 修复表格分页时，上下拖动表格线，重排部分单元格，对齐实时生效的问题
- 修复批注 commentsIDSet 为空值的问题
- 修复多个文本域粘贴报错的问题
- 新增图片编辑功能
- 新增支持双面打印功能

## 2.8.0

`2022-09-08`

- 新增续打病历时可选择页码功能
- 新增单元格中 Tab 键跳至下一单元格功能以及追加一行功能
- 优化多页内容时拖动滚动条时体验
- 新增批注功能
- 新增单元格锁定功能
- 修复使用 Delete 键删除内容时偶发输入 null 字符的问题
- 新增插入线条功能
- 都昌模板转换增加首行缩进，多选下拉框，文本域边框隐藏功能
- 优化复制粘贴文本域保证正确顺序
- 优化都昌模板转换逻辑，增加级联文本域转换

## 2.7.11

`2022-09-05`

- 修复多选框打印没有边框的问题

## 2.7.10

`2022-09-05`

- 合并都昌模板转换修改到 22 版本发布新版

## 2.7.9

`2022-09-03`

- 都昌模板转换表格嵌套异常提示移除

## 2.7.8

`2022-09-03`

- 都昌模板转换逻辑只允许出现一次的节点未重新初始化

## 2.7.7

`2022-09-03`

- 都昌模板转换优化

## 2.7.6

`2022-09-03`

- 修复修复模板转换报错的问题

## 2.7.5

`2022-09-01`

- 修复替换文本域在某些情况下报错的问题

## 2.7.4

`2022-08-31`

- 修复滚动条不能拖动的问题

## 2.7.3

`2022-08-30`

- 修复移动端不能滑动的问题

## 2.7.2

`2022-08-26`

- 修复转换为 rawData 时 shapes 为空报错问题

## 2.7.1

`2022-08-25`

- 修复点击日期面板外不能正常关闭问题

## 2.7.0

`2022-08-25`

- 新增文本域自定义校验功能
- 新增通过 delete 键可以删除表格上方空行
- 新增选择框只有一个对号的样式
- 修复配置默认行倍距不生效问题
- 新增增加自定义线条功能
- 修复病历检索界面打开病历书写页面后无法点选下拉选择文本域的问题
- 修复其他产品嵌入编辑器后会出现可视范围内编辑器外的其他内容不可点击的情况的问题
- 修复右键设置表格名称时不能正常回显表格名称，并且关闭录入窗口时未清空输入框内容的问题
- 新增编辑器调试工具，ctrl+shift+F12 打开

## 2.6.3

`2022-08-17`

- 修复托盘使用新版打印回调函数不生效的问题

## 2.6.2

`2022-08-12`

- 优化 Pacs 插入图片排版
- 替换文本域 raw 丢失字符问题
- 新增移除绑定事件接口
- 修改提示最大消息数目为 1

## 2.6.1

`2022-08-11`

- 修复复选框事件中级联逻辑与 contentChanged 事件冲突问题
- 新增添加级联注意事项

## 2.6.0

`2022-08-11`

- 复制跨段文本域粘贴到其他文本域后保存再重新加载数据后排版错乱。
- 表格性能优化。
- 移动端多页内容展示不全问题修复。
- 表单模式下使用 delete 键删除时光标移动位置不正确。
- 双面打印机病程记录续打开始页为偶数页时打印内容覆盖。
- 增加文本域级联功能。
- 可根据名称获取图片排版表格。
- 优化 Pacs 插入图片排版。
- 表格固定表头。

## 2.5.7

`2022-08-02`

- 修复生成 pdf base64 时偶发生成不了图片的问题
- 修复表单模式下移动光标输入会一直提示表单模式不可输入问题

## 2.5.6

`2022-08-02`

- 修复生成 pdf base64 时偶发生成不了图片的问题

## 2.5.5

`2022-08-01`

- 修复系统打印预览带有文本域边框的问题

## 2.5.4

`2022-08-01`

- 修复打印预览后加载模板失败的问题

## 2.5.3

`2022-08-01`

- 修复打印预览后加载模板失败的问题

## 2.5.2

`2022-07-29`

- 修复单机双击下拉框列表不显示问题

## 2.5.1

`2022-07-29`

- 修复替换文本与内容的问题

## 2.5.0

`2022-07-28`

- 修复在右键菜单事件中重载自定义菜单自定义的回调函数不生效的问题
- 修复新版打印下标变上标的问题
- 修复在光标处右键改为宋体，插入输入助手中的诊断内容，此时插入的诊断内容还是仿宋体的问题
- 全局配置转为实例配置
- 修复段落重排丢失段首文字的问题
- 修复替换页眉文本域内容，若接口参数中未添加 isHF 的值，那么替换内容将会以文本的形式插入到文档开头的问题
- 修复文本域日期选择框与下拉框双击激活的问题

## 2.4.2

`2022-07-15`

- 修复文本域校验样式问题

## 2.4.1

`2022-07-14`

- 段落重排逻辑优化
- 修复 antDesign 样式重叠问题
- 修复文本域时间框便捷输入时分秒空白时默认参数问题

## 2.4.0

`2022-07-13`

- 段落重排逻辑优化
- 修复文本域日期选择框日期正则表达式不正确的问题
- 移动端下不在页面上方 input 元素
- 新增敏感词检测接口
- 新增文本域校验接口
- 修复字符对齐段落自上而下选区设置左对齐报错问题
- 修复移除嵌套文本域报错问题
- 修复 pdf 打印透明背景图片时背景颜色为黑色

## 2.3.6

`2022-07-07`

- 修复文本域时间框遮盖问题
- 优化文本域校验提示逻辑
- 文本域校验属性回显问题修复
- 修复首次加载不能 pdf 打印问题

## 2.3.5

`2022-07-06`

- 修复切换组件后获取不到 jsPdf 对象的问题

## 2.3.4

`2022-07-01`

- jsPdf 加载字体文件逻辑调整

## 2.3.3

`2022-06-29`

- 修复插入只有一个段落的模板不能缩进的问题
- 修复调用 instance 处 this 指向问题

## 2.3.2

`2022-06-29`

- 修复英文输入法时按住 shift 不能输入的问题
- 新增插入模板时判断模板开头是否为表格的逻辑
- 修复 webWorker 里边访问 instance 报错问题

## 2.3.1

`2022-06-28`

- 修复字符对齐排版错位问题
- 修复非文档首插入模板（模板开头为表格）多出空行问题
- 修复表单模式下连续文本域删除报错问题

## 2.3.0

`2022-06-27`

- 修复 pacs 修改字体样式编辑器崩溃问题
- 修复 pacs 表单模式拖拽编辑器崩溃
- 修复多编辑器下字体配置文件覆盖问题
- 修复标签和时间框类型的文本域替换文本字符中存在\n 编辑器报错问题
- 新增新建病程记录,查房记录时输入域默认首行缩进 2 字符
- 修复文本域时间框显示 Invalid date 问题
- 修复文本域设置只读时，光标定位到文本域开始边框字符后使用 Backspace 键删除会将整个文本域删掉的问题
- 修复空文本域内插入水平线位置不正确问题
- 修复边点击边删除文本域内容报错的问题
- 批量打印优化
- 文本域时间框属性优化

## 2.2.2

`2022-06-17`

- 修复众阳浏览器中使用新版打印的判断逻辑问题

## 2.2.1

`2022-06-17`

- 修复众阳浏览器内无法获取版本号的问题

## 2.2.0

`2022-06-14`

- 修改版本号判断逻辑
- 修复打印选取打印范围错误的问题（7-10）

## 2.1.1

`2022-06-02`

- 修复编辑器在移动端下不缩放的问题
- 修复新托盘下批量打印不生效问题

## 2.1.0

`2022-05-31`

- 新增表单模式下拖拽功能
- 新增文本域时间框禁止时间参数
- 新增失焦后光标不闪烁配置
- 修复删除导致的文本域字符与段落字符不一致的问题
- 修复列表序号重排不保存的问题
- 修复表单模式下选中文本域开始边框删除光标位置跑到第一个文本域内的问题
- 修复点击页面外光标定位不准确的问题
- 修复页眉点击时光标不能正常定位的问题
- 新增 pointerUp 事件
- 文本域日期选择框新增禁止时间功能

## 2.0.5

`2022-05-24`

- 修复批量打印在众阳浏览器旧版本不能打印的问题

## 2.0.4

`2022-05-24`

- 修复向文本域中复制粘贴带换行符内容时造成文本域对象中字符缺失，继续往其中粘贴内容会导致 field.text 获取内容乱序的问题

## 2.0.3

`2022-05-20`

- C++图片打印位置矫正
- 修改托盘版本获取方法
- 修复自定义复选框修改后再次插入后位置不正确的问题
- 回退文本域日期选择优化功能

## 2.0.2

`2022-05-18`

-版本错误重新发版

## 2.0.1

`2022-05-18`

-合并 1.14.1 版本修改补丁

## 2.0.0

`2022-05-18`

- 添加图片打印间距
- 新增表格 copy 的时候增加 name 属性赋值
- 修复页脚表格线横向不能连续拖动的问题
- 正常显示隐藏页脚表格线
- 修复点击滚动条外部也能实现滚动条滚动的问题
- 定位到指定文本域开头位置
- C++打印相关

## 1.5.5

`2022-05-17`

- 发版错误重新发版

## 1.5.4

`2022-05-17`

- C++打印默认自动缩放，续打表格上边线不显示问题修复
- 修改打印批量打印续打位置不正确的问题
- 修复众阳浏览器获取版本报错问题
- 添加获取托盘版本号接口

## 1.5.3

`2022-05-13`

- 新增单独控制行首字符及数字排版功能更改
- 新增插入图片排版可以固定表格高度
- 插入图片排版新增各种判断，避免文本域、页眉页脚内报错错乱问题
- 新增图片排版序号与文本域可控制是否展示
- 修复进入页眉页脚编辑模式时进入页脚编辑的问题
- 图片排版双击图片不能删除

## 1.5.2

`2022-05-11`

- 新增符号排序
- 添加段落构造函数里对排版的复制控制
- 新增 ctrl+shift+F10 切换行首字符排版功能

## 1.5.1

`2022-05-07`

- 修改数字排版兼容问题导致的字符排版问题初夏那多余空行 id 的情况

## 1.5.0

`2022-05-07`

- 新增图片排版下方第二个文本域可控制不传
- 修改单元格内容导致表格行高发生变化，设置为非上对齐的单元格内容位置不变的 bug 修复
- 修复页眉设置选中文本字号后文本宽度不能立即生效的 bug
- 修复复制单元格时里边的下拉文本域没有下拉选项的问题
- 新增设置是否启用数字排版的接口
- 修改数字排版新段落的生成逻辑
- 添加排版变化后刷新页面现有数据

## 1.4.7

`2022-04-26`

- 新增兼容性数字排版
- 新增 ctrl+shift+f11 快捷键开启关闭数字排版

## 1.4.6

`2022-04-25`

- 痕迹对比可传字符串或对象
- 修复打字时滚动条消失的问题
- 给移动端遮罩层添加 ID
- 痕迹对比横纵向按照新版本来

## 1.4.5

`2022-04-24`

- 修复偶发获取 pathName 不正确导致的字体文件加载异常问题
- 添加打印 PDF 列表的情况
- 修复续打位置不正确的问题

## 1.4.4

`2022-04-22`

- 放开排版逻辑

## 1.4.3

`2022-04-22`

- 误升级版本号

## 1.4.2

`2022-04-22`

- 编译不成功重新发版

## 1.4.1

`2022-04-21`

- 修复文本域设置只读后仍能弹出医学表达式弹框的问题
- 优化医学表达式排版
- 修复表格变化时对比报错
- 修复痕迹对比不展示人信息的问题
- 痕迹对比不展示分组信息

## 1.4.0

`2022-04-14`

- 新增痕迹对比功能
- 新增恒牙和乳牙复杂牙位图功能
- 新增数字和英文的排版逻辑
- 修复编辑器中含有符号时打印预览不显示问题

## 1.3.8

`2022-04-13`

- 修复文本域替换图片后，数字排版报错，删除文本域失败的问题

## 1.3.7

`2022-04-12`

- 修复 16K 大小打印预览显示超宽问题

## 1.3.6

`2022-04-08`

- 修复含有文本域的单元格合并拆分报错问题
- 修复关闭 F12 后编辑器大片空白的问题
- 修复文本域隐藏后报错问题

## 1.3.5

`2022-04-07`

- 修复关闭 F12 后编辑器大片空白的问题
- 修复字体文件更换后不生效的问题

## 1.3.4

`2022-04-07`

- 修改无序列表图标样式
- 修复单个段落的段落重排
- 新增替换或追加文本域元素接口
- 修复分页情况下双击表格线导致 row_size 不正确的问题
- 修复无法置空 placeholder 的问题
- 修复文本域替换文本接口传入多个相同文本域对象会将内容替换为 undefined 的问题
- 优化模型数据转原始数据文本域会多生成空 text 的问题
- 优化配置报错提示

## 1.3.3

`2022-03-31`

- 编译错误重新发版

## 1.3.2

`2022-03-31`

- 新增返回编辑器版本信息的方法

## 1.3.1

`2022-03-29`

- 修改插入图片排版自动排列序号
- 修复增加 editor 配置后导致插入模板报错问题

## 1.3.0

`2022-03-29`

- 新增编辑器独立配置
- 修复图片放大后调整横纵向图片超出页面的问题
- 新增影响编辑器加载的配置校验
- 新增段落重排功能
- 新增文本域替换原始 json 数据接口
- 文本域新增月日格式
- 新增十字牙位图
- 删除添加常用语弹框
- 绘制 pdf 时增加 url 图片绘制
- 常用语添加 keyword 为空的判断

## 1.2.36

`2022-03-22`

- 修复下载 pdf 不成功的问题
- 托盘所需参数矫正

## 1.2.35

`2022-03-21`

- 新增 config 配置错误抛出异常的校验
- 修复选区光标在下方空白处移动时不能选区最后一段换行符问题
- 修复放大后更新 canvas 大小不准确的问题
- 新增表格内沿用 rawData 的表格 id

## 1.2.34

`2022-03-17`

- 修复横向滚动条展示错误的问题

## 1.2.33

`2022-03-17`

- 直接打印后取消续打模式
- 修复校验纸张大小的方法
- 修改删除线位置
- 修复最后一页滚动问题

## 1.2.32

`2022-03-15`

- 修复插入横向模板时未正常转为横向的问题
- 修复云浏览器立即打印未能取消续打模式的 bug。
- 添加静默打印添加未能获取打印机列表时的提示。
- 插入表格时取消最大行列限制。
- 添加插入超出计算最大接受列数的提示。并提示最大承受列数。

## 1.2.31

`2022-03-14`

- 修复插入横向模板时未正常转为横向的问题
- 修复编辑器隐藏状态下报错的问题
- 修复退出页眉页脚编辑模式后光标不准确的问题
- 修复复选框回显、清空问题
- 修复无纸化生成 base64 时异步可能导致的生成内容不对的问题
- 托盘打印打印机参数修正
- 众阳浏览器新增打印份数
- 常用语判断条件添加可配置属性
- 优化区域打印遮罩颜色

## 1.2.30

`2022-03-02`

- 优化文本域修改提示文本域为空
- 修复打印预览图片偏移问题
- 修复插入图片排版撤销不生效问题
- 修改单元格对齐在初次渲染数据时不正确的问题
- 新增获取选区纯文本的方法
- 新增批量打印关于新浏览器的判断
- 修复医学表达式 1 时间格式回显问题
- 新增常用语关键词搜索功能

## 1.2.29

`2022-02-23`

- 修复表格硬分割判断问题
- 修复横向滚动条不显示的问题
- 新增自动判断新浏览器是否支持批量同步打印，不支持自动降级为异步打印，但是异步打印存在打印乱序的问题

## 1.2.28

`2022-02-22`

- 修复滚筒条随页面宽度改变不显示的问题
- 修复当前有选区且选区中包含文本域时调用移除文本域接口报错问题
- 修复硬分割判断的问题
- 修复设置简介模式时光标位置问题
- 修复批量打印乱序、漏打的问题

## 1.2.27

`2022-02-21`

- 修复关闭日期选择框和下拉选择框的相关问题

## 1.2.26

`2022-02-21`

- 修复关闭日期选择框和下拉选择框的相关问题
- 修改页面左右滚动条逻辑

## 1.2.25

`2022-02-18`

- 因编译问题升级版本号

## 1.2.24

`2022-02-18`

- 修复点击编辑器外关不掉文本域弹框的问题
- 修复立即打印时导致的绘制内容不全的问题
- 修改打印接口成全文转换出来后打印

## 1.2.23

`2022-02-17`

- 修复都昌模板转换不成功问题
- 优化鼠标移动到文本区域外时的展示状态
- 修复 vue-worker 位置异常导致的未正常打包问题

## 1.2.22

`2022-02-16`

- 修改新浏览器预览后续打状态关闭
- 修复都昌新老版本数据接口不一致导致的模板转换报错问题

## 1.2.21

`2022-02-16`

- 修复医学表达式修改后 pdf 绘制异常问题

## 1.2.20

`2022-02-16`

- 修复批量打印为判断是否在新浏览器中

## 1.2.19

`2022-02-16`

- 图片 src 为空时，打印报错

## 1.2.18

`2022-02-11`

- 修复续打和区域打印模式下仍能弹出下拉框问题
- 处理表格合并单元格时绘制超出页面与回车时表格 rowSize 设置错误问题
- 优化页眉页脚复制逻辑和文本域边框显示隐藏逻辑
- 优化连续选中复选框情况下触发选区问题
- 修复多次撤销时撤销未初始化导致的撤销结果不对问题
- 修复页眉页脚修改后分散对齐导致的位置计算错误的问题
- 修复分散对齐时跨文本选区删除报错的问题
- 修复单选/复选框在表单模式下无法被选中的问题
- 新增 ctrl+shift+F12 展示当前版本号

## 1.2.17

`2022-02-08`

- 新增 pdf 立即续打功能
- 新增 vue-worker 处理转 base64 逻辑
- 修复图片编辑框不消失问题
- 修复光标移动时三连击从页眉到正文报错及复选框连续选中问题
- 修复新浏览器获取打印机列表方法问题
- 修复新浏览器立即打印问题

## 1.2.16

`2022-02-07`

- 因测试环境编译问题升级，代码无变动

## 1.2.15

`2022-02-07`

- 修复简单单选框/复选框无法被选中问题
- 修复从后往前选中多行删除报错问题
- 新增判断避免页眉信息重复替换
- 修复打印托盘判断问题
- 新增对 electron 支持

## 1.2.14

`2022-01-26`

- 修复单选框在跨页表格内选中成多选效果的问题
- 修改自定义多选框没有分组名的情况下处理逻辑
- 修复自动以复选框回显问题
- 修复选区状态下文本域修改属性报错的问题
- 修改打印参数，解决打印偏移量问题

## 1.2.13

`2022-01-25`

- 修改插入图片排版，可传入图片宽高设置比例
- 修复表格内有文本域时跨页报错的问题
- 新增段落复制增加字符对齐属性复制

## 1.2.12

`2022-01-24`

- 新增插入 pacs 需求排版图片的方法
- 新增打印配置，控制 ODF 打印偏移问题
- 对接新浏览器打印功能
- 对接浏览器打印的快捷键功能
- 修改插入表格传参逻辑

## 1.2.11

`2022-01-21`

- 修复打印预览窗口重复弹出问题

## 1.2.10

`2022-01-21`

- 修改打印逻辑，兼容打印托盘
- 修复绘制 PDF 插入图片后第二次绘制不压缩的问题

## 1.2.9

`2022-01-18`

- 新增调整页面大小后图片自适应的方法
- 修复分散对齐对于嵌套文本域最后字符错乱的问题
- 修复格式刷导致的排版错乱问题
- 修改都昌模板转换逻辑，增加固定宽度文本域转换
- 新增绘制 PDF 水平线绘制逻辑
- 新增元素双击事件
- 增加文档转 base64 码接口
- 调整月经史公式 1 插入后展示问题
- 新增字体文件路径名可配置，用于嵌套 iframe 使用的情况

## 1.2.8

`2022-01-13`

- 修复剪切板为空时拖拽报错问题
- 修复点击其他图片时原带编辑框图片编辑框不消失问题
- 修复无序列表有序列表混用时，有序列表序号出现混乱的问题
- 修改插入分组的菜单信息，保证在分组外插入分组时只能选择下方插入
- 修改下拉选择框判断下方展示空间计算错误问题

## 1.2.7

`2022-01-12`

- 新增 ctrl+c 在编辑期内粘贴为带格式粘贴，在编辑器外粘贴为纯文本粘贴，删除 ctrl+shift+c 和 ctrl+shift+v 快捷键，用 ctrl+c 和 ctrl+v 替代
- 新增分组关联页眉兼容老数据
- 修复高清屏下鼠标下拉超越边界滚动不生效问题
- 修复右键文本域报错问题
- 新增 jsPdf 打印回调

## 1.2.6

`2022-01-10`

- 修复高清屏下鼠标下拉超越边界滚动不生效问题

## 1.2.5

`2022-01-10`

- 修复打印预览不显示问题

## 1.2.4

`2022-01-10`

- 修复 modal 拖拽问题
- 修复文本域提示及文本域校验提示在编辑器放大缩小后位置错误问题
- 修改字体文件加载方式
- 修复序号列表错乱问题
- 新增分组关联页眉信息方式

## 1.2.3

`2022-01-07`

- 修复分组锁定情况下，右键菜单中表格操作禁用问题
- 修复医学表达式过大问题
- 增加下拉选择框与日期选择框选择后事件，解决日期选择器选中后执行两边替换逻辑的问题
- 更新月经表达式 1 加载图片

## 1.2.2

`2022-01-04`

- 修复 pdf 分开打印问题
- 修复系统打印续打和区域打印不生效的问题
- 修复立即打印不生效问题
- 修复页眉中表格操作问题修复

## 1.2.1

`2022-01-04`

- 优化右键事件，减少 vue 挂载数据
- 直接打印参数完善
- 修改字体引入方式

## 1.2.0

`2022-01-04`

- 新增 pdf 打印方法
- 优化右键菜单续打和区域打印展示内容
- 修复校验码无法校验月份的问题
- 调整打印方向配置和批量打印逻辑
- 优化页眉表格线拖动，没编辑页眉不展示表格线
- 新增 copyEditor 接口
- 修复右键触发 pointer_down 的问题
- 优化续打遮罩表格线问题

## 1.1.4

`2021-12-30`

- 新增超过边界滚动选区功能
- 优化水平线实现逻辑
- 修复一倍状态下预览图片偏移问题
- 优化获取 blob 数组的调用和解决获取 blob 的问题

## 1.1.3

`2021-12-27`

- 优化医学表达式默认不展示时间框
- 优化下拉检索框获得焦点后不能删除文本域内容问题
- 优化打印逻辑

## 1.1.2

`2021-12-21`

- 修复编辑器配置传入错误

## 1.1.1

`2021-12-21`

- 新增退出编辑页眉页脚接口
- 新增移动端功能
- 修复删除选区开始为表格时删除后排版错乱问题
- 修复续打模式下整页不打印页眉页脚的问题
- 修复表格下方文字盖住表格线的问题
- 修复医学表达式模糊问题
- 优化打印方法位置
- 优化组装原始段落信息逻辑

## 1.1.0

`2021-12-17`

- 新增传递文件流按钮
- 修改文档刷新按钮
- 修改页眉页脚水平线显示隐藏控制接口
- 新版打印插件配置修改

## 1.0.65

`2021-12-16`

- 文本域替换图片接口更改
- 新增下拉框在未有匹配项时回车自动替换检索框内容
- 修复单元格复制未复制文本域问题
- 修复跨页表格内复选框点击不生效问题
- 修复跨页表格点击单元格报错问题

## 1.0.64

`2021-12-14`

- 新增浏览器打印回调
- 表格线粗细优化
- 优化续打模式点击页脚时全选页面
- 优化原始数据过大问题
- 修复页眉页脚在光标移动后不可编辑的问题
- 修复只读文本域内不允许插入文本域的问题
- 修复替换文本域时传入数字类型报错问题
- 修复部分点击单元格报错问题
- 新增自定义开始页码及页数接口
- 优化纯文本复制
- 修复替换元素到文本域对象接口问题
- 修复医学表达式在分组下还能打开的问题
- 调整右键插入表格最大行列数

## 1.0.63

`2021-12-09`

- 新增 config 配置项 font_family
- 修复区域打印从下网上选中问题
- 新增按住 ctrl 拖拽不删除原来选区功能
- 修复页眉页脚打印问题

## 1.0.62

`2021-12-08`

- 新增打印控制偏移参数

## 1.0.61

`2021-12-07`

- 更换复制和复制文本快捷键
- 新增区域打印功能
- 修复字体放大后表格线被遮挡的问题
- 修复空文本域打印时最小宽度不生效问题
- 修复 mac 系统快捷键问题
- 修复页眉页脚连续删除渲染异常问题
- 修改打印纸张大小参数配置

## 1.0.60

`2021-12-05`

- 打印还原

## 1.0.59

`2021-12-03`

- 新增文本域校验功能总开关
- 新增双下划线配置
- 修改页眉页脚遮盖后清晰度
- 修改默认字体配置，修改堆栈逻辑
- 修复快捷输入不准确的问题

## 1.0.58

`2021-12-02`

- 新增表格插入多行多列的接口
- 新增背景阴影配置项

## 1.0.57

`2021-12-01`

- 右键菜单中，将表格的插入行和列移入到表格操作中
- 修复常用语靠下位置不准确问题
- 修复快捷输入展示框折行混乱的问题
- 打印逻辑修改，防止顺序错乱，内容遗漏，根据类型控制校验规则体现

## 1.0.56

`2021-12-01`

- 新增 API 启用快捷输入功能
- 修复快捷输入不展示问题

## 1.0.55

`2021-12-01`

- 新增文本域最大宽度与最小宽度配置功能
- 修改文本域最大宽度和最小宽度属性
- 修复医学表达式改变大小后宽高不准确的问题

## 1.0.54

`2021-11-30`

- 新增文档信息可存储页眉页脚水平线展示配置
- 新增批量打印的方法
- 修改横向展示后页面大小不自适应的问题

## 1.0.53

`2021-11-29`

- 修复图片点击报错问题
- 修复模板数据加载报错的问题
- 修复滚动到文档最后，编辑时页面会自动滚动一下的问题

## 1.0.52

`2021-11-28`

- 依赖更新

## 1.0.51

`2021-11-28`

- 增加普通字体宽度，色值，加粗字体宽度，背景色的可配置功能。
- 修复了 shift 区域选择不生效的 bug。

## 1.0.50

`2021-11-25`

- 新增快捷输入弹框不可越界功能
- 增加复选框同组名成组功能。
- 增加配置项重置接口。
- 修复模态框拖出可视区域无法关闭的问题
- 修复只读文本域仍能够粘贴内容 BUG。
- 修复分组锁定后仍能点击设置复选框问题。
- 修复查找替换 tip 弹出框关闭后打不开的问题

## 1.0.49

`2021-11-24`

- 回车事件方向按钮事件禁用
- 增加编辑常用语弹窗
- 修复二级菜单被遮挡问题，调整层级
- 修改所有通过 id 获取 dom 方式，解决打开文件方法调用后内容加载到其他编辑器问题。

`2021-11-22`

- 增加获取搜索关键词的方法
- 增加根据表格 name 值获取表格数组的方法
- 增加获取光标位置处表格的方法
- 增加 keydownbefore 事件
- 增加横向纵向打印参数
- 增加中文输入开始和结束事件

## 1.0.48

`2021-11-19`

- 增加文本域只读时判断，不可通过接口再插入其他内容。
- 增加通过类型获取文本域数组接口。
- 修复表格内拖动图片报错的 bug

## 1.0.47

`2021-11-19`

- 增加右键菜单不兼容上版本
- 增加页眉页脚水平线显示隐藏控制。
- 优化段落居中逻辑，应用到每一行。

## 1.0.46

`2021-11-19`

- 增加 右键菜单增加配置
- 增加自定义多选框（新增、修改）
- 增加右键菜单文本域和多选框控制逻辑
- 修复只读文本域不能通过回退键删除只能通过选区删除的问题。
- 修复向空文本域中插入自定多选框未清空背景文本问题。
- 修复 Shift 键选区操作时，点击已选中区域会使选区消失问题。
- 修复插入非成组自定义多选框时不能设置禁用 BUG。

## 1.0.45

`2021-11-15`

- 初次上线稳定版本

## 2.30.16

`2023-11-21`

- 适配 base

## 2.30.15

`2023-11-20`

- 直接双面打印不生效 bug 修改
  > > > > > > > ba528d77195cde18bf80eaa026eb6ff360f2ecb2

## 2.30.14

`2023-11-10`

- 适配 base

## 2.30.13

`2023-11-10`

- 适配 base

## 2.30.11

`2023-11-09`

- 适配 base

## 2.30.10

`2023-11-06`

- 适配 base

## 2.30.9

`2023-11-05`

- 适配 base

## 2.30.8

`2023-11-03`

- 打印报错
- 点击清除本地配置后直接点系统打印报错问题
- 痕迹对比颜色修改

## 2.30.7

`2023-11-02`

- 文本域打印边框功能
- 增加了配置记忆功能
- 打印机记忆功能修改
- 新增文本域对齐功能

## 2.30.1

`2023-10-23`

- 适配 base

## 2.30.0

`2023-10-16`

- 升级版本号

## 2.29.15

`2023-10-16`

- base 上已经从 editor 上挪到了 internal 上的方法,vue 上进行修改调用方式
- 下拉框编辑选项样式调整
- 新增插入矩形
- 新增水平线弹出框类型
- 痕迹对比测试按钮问题

## 2.29.22

`2023-11-03`

- 文本域打印边框功能

## 2.29.20

`2023-10-25`

- 增加批量打印双面打印功能

## 2.29.19

`2023-10-20`

- c++批量打印超过 20 份乱序问题

## 2.29.18

`2023-10-20`

- 适配 base

## 2.29.17

`2023-10-16`

- 和 45 做分割的一个版本

## 2.29.16

`2023-10-16`

- 适配 base

## 2.29.14

`2023-10-12`

- 适配 base

## 2.29.13

`2023-10-12`

- 增加网格线切换

## 2.29.12

`2023-10-11` -双击没有批注的文字报错

## 2.29.11

`2023-10-10`

- 文本域提示信息在顶部时展示在下方

## 2.29.10

`2023-10-10`

- 打开批注列表之后 canvas 的尺寸没有响应变化修改
- 批注列表关闭的时候也要更新一下 canvas 的尺寸
- 文本域提示信息在顶部时展示在下方
- 文本域提示信息在顶部时展示在下方
- 文本域提示信息在顶部时展示在下方

## 2.29.7

`2023-10-8`

- 1、提高文本域提示层级 2、初始化本地配置时调整
- 修复选区设置字体时如果开头位置为单选复选框或其他非字符类，则字号回显为 0，确定后排版错乱的问题
- 获取本地配置信息报错问题
- 打印配置默认为单面打印
- 读取打印配置信息逻辑修改

## 2.29.6

`2023-10-7`

- 添加批注报错处理

## 2.29.5

`2023-10-7`

- 双击打开批注编辑框

## 2.29.4

`2023-10-7`

- 适配 base 修改

## 2.29.3

`2023-10-7`

- 字体新增对齐方式
- 优化回显逻辑

## 2.29.2

`2023-10-7`

- 修改文本域提示框样式
- 使用 transUse 把新老批注进行隔离

## 2.29.1

`2023-9-27`

- 修复双面打印回显错误的问题
- 文本域提示信息支持换行功能
- 文本域提示信息样式微调
- 冒号缺失
- 修复特殊字符 pdf 预览不显示的问题
- 悬浮球样式调整
- 悬浮球初始位置调整

## 2.29.0

`2023-9-25`

- 新增调试工具级修改联宽度的方法
- 修复文本域公式编辑时会出现乱跳的情况
- 修复 JSPDF 绘制实心圆报错的问题
- 删除空配置时不应删除值为 0 的配置
- 增加 pdf 打印所需字体文件在众阳浏览器下可通过本地文件读取
- 修复复选框编辑添加按钮被遮盖的问题

## 2.28.14

`2023-10-17`

- 适配 base

## 2.28.13

`2023-10-16`

- 适配 base

## 2.28.9

`2023-9-19`

- 众阳浏览器下批量打印顺序问题

## 2.28.8

`2023-9-18`

- 保存修改痕迹兼容老数据

## 2.28.7

`2023-9-18`

- 保存修改痕迹兼容老数据
- 修复文本域弹窗层级问题
- 新增双面打印
- 修复级联文本域替换文本域到选择 name 的文本域的问题

## 2.28.6

`2023-9-15`

- 修复文本域级联点开报错的问题

## 2.28.5

`2023-9-14`

- 更新水印编辑模式与图形编辑模式接口
- 打印预览增加根据配置回显单双面打印状
- 修复时间框位置不正确的问题

## 2.28.4

`2023-9-14`

- 加了几个 log

## 2.28.3

`2023-9-14`

- 增加新旧逻辑切换按钮
- 把级联文本域从文本域设置中分离出来
- 暴露水印模式与形状编辑模式接口
- 去掉升级说明改为特殊功能
- 新增点击级联便捷插入功能
- 修复取消按钮不能关闭级联功能便捷插入的问题

## 2.28.2

`2023-9-11`

- 适配 base2.28.2

## 2.28.1

`2023-9-7`

- 文本域属性编辑性能优化
- 新增右键取消插入功能，新增右键点击和悬浮条联动功能
- 修复组件渲染钩子函数执行顺序问题
- 新增悬浮条河右键继续绘制折线功能及按钮随图形几何中折线的存在与否展示消失功能

## 2.28.0

`2023-9-5`

- 去掉了批注列表变化事件
- 去掉了几个没用的接口

## 2.27.27

`2023-9-11`

- 修复本地配置需求修改钩子函数异步导致其他产品使用报错问题

## 2.27.26

`2023-9-4` -适配 base2.27.22

## 2.27.5

`2023-8-24`

- 修复图片编辑在编辑完自定义线段后编辑圆或矩形报错问题

## 2.27.4

`2023-8-23`

- 新增 shape 悬浮球功能
- 第一次点击文本域属性弹窗位置不准确问题修复
- 添加自定义批注
- 新增水印便捷框
- 批量打印数据重复问题

## 2.27.3

`2023-8-18`

- 发版测试

## 2.27.2

`2023-8-18`

- 页面滚动的时候隐藏数字选择器
- 修复文本域公式不能在光标位置插入元素的问题
- 增加修改痕迹鼠标移入时显示新增还是删除
- 新增文本域公式点击标点符号后标记点击的符号
- 修改痕迹优化，增加图片鼠标移上去显示修改信息
- 自定义批注弹窗转移到 demo，右键菜单的添加自定义批注选项删除
- 修复切换减号后再点击文本域编辑器公式提示公式格式不正确

## 2.27.1

`2023-8-11`

- 调试工具和文本域属性弹窗同时存在时，页面交互 bug 修改
- 悬浮按钮拆分单元格接口调整
- 拆分单元格默认两列一行
- 暴露自定义批注的接口
- 本地设置功能优化，增加调试工具中设置固定纸张按钮
- 新增点击文本域公式 div 也能弹窗的功能

## 2.27.0

`2023-8-11`

- 新增文本域公式说明
- 修改批注之后编辑器设置为只读模式 bug 修改
- 批注替换之后编辑器变为只读
- 批量打印每份数据调用 afterPrint 在 42 上不用 setViewMode("normal")了,因为 base 上已经改了,简洁模式也可以打字
- 批量打印,里边的每份数据不管有没有 afterPrint 都要调用 update
- 自定义批注
- 增加文本域显示模式切换

## 2.26.7

`2023-8-9`

- 文本域公式新增>=<+功能
- 新增文本域公式选区后再点按钮插入数字应该清除选区再插入
- 文本域公式新增分号及修复点击问题
- 修复文本域公式新插入文本域时报错的问题
- pdf 打印内存增长问题优化
- 优化文本域公式输入框

## 2.26.6

`2023-8-7`

- 新增右键图形编辑
- 替换 icon 文件,修复右键菜单中单元格内的两条斜线不展示图标的问题
- 修改批注列表抽屉的展示层级改为 1,云病历那边弹窗太多,他们改成 2
- 文本域属性框标题修改

## 2.26.5

`2023-8-4`

- 分割单个单元格 bug 修改
- 软分割的时候处理合并单元格不绘制的线
- 选中整个表格在锁定分组只读模式,表单模式下不允许剪切删除
- 将 Ctrl+V 的判断挪到 paste 里边去
- 解决新版表格拆分一个单元格无限软分割的问题
- 修复 shape 报错问题

## 2.26.4

`2023-8-2`

- 增加拆分单个单元格的功能
- 解决编辑器克隆接口内存泄漏问题及性能优化
- 新版表格拆分基础班软分割逻辑完成
- 新版表格拆分硬分割时,row_size 不对的问题修复
- 修复敏感词检测返回敏感词数量跟真实存在的敏感词数量不一致的问题
- 文本域属性弹窗置顶连续插入报错处理
- 连续插入文本域和修改文本域之间切换报错修改

## 2.26.3

`2023-8-1`

- 用户登录接口调整
- 新增文本域划线功能

## 2.26.2

`2023-7-28`

- 修复 shape 点击编辑角拉长后保存位置不正确的问题
- 新增 C++打印 shape 的画圈和画 X

## 2.26.1

`2023-7-26`

- 医学计算公式,点击确定插入文本域带着结果,右键菜单也显示正确的公式

## 2.26.0

`2023-7-26`

- 医学计算公式获取不到年龄报错的 bug 修复
- 解决医学计算公式获取性别时,没性别不默认的 bug

## 2.25.14

`2023-8-2`

- 选中整个表格在锁定分组只读模式,表单模式下不允许剪切删除
- 将 Ctrl+V 的判断挪到 paste 里边去

## 2.25.13

`2023-7-28`

- 分组表单模式影响痕迹视图用户名展示问题

## 2.25.12

`2023-7-18`

- 暴露打开医学计算公式弹窗的接口
- pdf 所需字体文件主动加载接口暴露

## 2.25.11

`2023-7-17`

- 修复文本域公式不能二次修改 bug
- 都昌模板转换增加一些判断防止报错
- 修复文本域公式点击提示纯数字的问题
- 修改弹窗输入框不能点击,不显示光标的问题
- 添加提示 title

## 2.25.10

`2023-7-14`

- 修复文本域公式能打字的问题

## 2.25.9

`2023-7-11`

- 调试工具中增加管理员模式
- 文本域公式
- 统一名称
- 新增文本域公式回显功能
- 公式点击插入时增加一个间隔符
- 文本域公式最后一个删除不掉的问题
- 修复回显不正确的问题

## 2.25.8

`2023-7-7`

- 打印数据生成时如果图片缺少 src 则会报错的问题
- 表单模式下，选区删除文本域内容导致文本域被清空的 bug 修改
- (之前发版一直报错，前几版都发失败了，2.25.7 是最近的一版)

## 2.25.6

`2023-7-7`

- (环境出了问题，2.25.5 发版没成功，2.25.6 对应 base 的 2.25.9 版本)
- 选取字体增大缩小报错修改
- 在 modelPath2ParaPath 之前加了判断 modelPath 是否存在
-

## 2.25.5

`2023-7-4`

- 医学计算公式获取性别和年龄自动指定正确的弹框
- 选区字体缩放，文本域边框不变的 bug 修复

## 2.25.4

`2023-6-30`

- 数字类型的文本域增加双击激活方式
- 解决水印模式下输入文字时,右键菜单不显示的问题
- 增加辅助按钮设置后常驻
- 右键菜单医学公式调整位置并且增加图标
- 双击文本域展示医学计算公式弹窗,并根据 placeholder 默认选项
- 弹窗移动后 ctrl+a 全选失效问题修复
- 医学公式增加路径导航
- 医学计算公式点击确定填写到文本域内,小数点精确到三位小数
- 多选问题修复

## 2.25.3

`2023-6-28`

- 痕迹对比弹窗的若干修改

## 2.25.2

`2023-6-27`

- 修复文本域公式以；分割的多个公式不生效的问题

## 2.25.1

`2023-6-26`

- 优化更新文本域公式的方法
- 增加了数字类型位数无限制
- 修改了小数四舍五入的规则
- 新增判断大小文本域公式逻辑

## 2.25.0

`2023-6-20`

- 40 迭代需求

## 2.24.10、

`2023-7-10`

- - 表单模式下选区删除导致文本域清空的补丁

## 2.24.9

`2023-7-5`

- 适配 base2.24.9

## 2.24.8

`2023-7-4`

- 因为 base 上修改了 copyEditor,新增了 getPrintEditor,所以 vue 上对应修改

## 2.24.7

`2023-7-3`

- 增加 internal 属性,transformData,在每次执行 rawData2ModelData 的时候,收集图片和文本域数据
- beforePaste 事件,返回数据改为对象,增加返回数据转换后的 transformData

## 2.24.6

`2023-6-27`

- 表单模式下选区删除错误修改
- 都昌模板转换图片大小问题修复
- 选择框点击文本选中时触发选区问题
- 增加路径有效性判断,解决 refreshDocument 强制更新时,数据变化导致路径转换时报错的问题

## 2.24.5

`2023-6-26`

- 增加图片信息异常提示
- 单元格复制样式问题
- shift 选区 bug 修改
- pdf 打印将图片转白色背景时直接使用 canvas
- 首行缩进和 tab 键缩进，在选区包含表格的情况下报错处理

## 2.24.4

`2023-6-25`

- 暂时注释 jspdf 优化代码，观察医生签名图片生成 pdf 时丢失问题
- 解决右键菜单的问题

## 2.24.3

`2023-6-20`

- 增加辅助按钮文本域最小宽度调整
- 解决右键菜单位置不对,高分辨率下有些选项展示不全的问题
- 调整部分依赖的版本
- 解决右键菜单点击屏幕下方,右键菜单非常矮的问题

## 2.24.2

`2023-6-19`

- 模板转换报错
- 显示隐藏按钮不生效问题
- 解决单元格软分割时,段落 children 为空,导致获取字符串位置为空的问题
- 去掉了一个选区删除的判断
- 解决调用 updateFieldText 找错文本域的 bug
- 改了文本域属性弹窗的样式
- 增加本地启动命令，解决 node18 启动报错问题

## 2.24.1

`2023-6-14`

- shift 添加选区功能
- 切换构建工具为 vite
- ts 报错问题
- 使用 vite 后不能热更新的问题
- 使用 useLetterPlaceholder 属性将输入法输入时字母占位的新旧功能进行了隔离
- XField.ts 中,定位到文本域的方法,加是否在正文是否是编辑页眉页脚模式的判断,解决循环全文档文本域调用 editor.reviseFieldAttr 报错的问题

## 2.24.0

`2023-6-13`

- 39 迭代需求

## 2.23.18

`2023-6-15`

- 解决单元格软分割时,段落 children 为空,导致获取字符串位置为空的问题

## 2.23.17

`2023-6-13`

- 修改获取字符串位置,字符串在分页表格中位置不对的 bug
- 修改获取字符串位置中表格内的数据页码不对的 bug

## 2.23.16

`2023-6-12`

- 获取字符串相对于页面位置中干掉了测试的值
- 获取字符串相对位置乘以 0.749
- 修复获取字符串相对位置里边获取最后一个字符下标不对的问题

## 2.23.15

`2023-6-12`

- 只有一个表格的模板打开报错问题
- 修改获取字符串位置,改为获取最后一个字符的位置

## 2.23.14

`2023-6-9`

- 修復弹出框监听事件冲突问题
- 门诊未知原因导致 vue 与 base 版本不一致问题报错

## 2.23.13

`2023-6-7`

- 修復模態框拖拽移動問題

## 2.23.12

`2023-6-7`

- 适配 base2.23.15

## 2.23.11

`2023-6-6`

- 都昌模板转换图片数据增加判断
- 服务端调用接口无需受只读模式影响
- 服务端调用接口无需受只读模式影响及转打印数据时判断错误
- 修复滚动表格拉到最后删除滚动不正确的问题
- 还原 set_cell_height 命名

## 2.23.10

`2023-6-2`

- 性能监控只监控电脑端
- 仅选择模式改成了单独的属性
- 增加性能监控信息初始化时判断，防止不同操作系统不兼容
- 水印模式下弹窗问题

## 2.23.9

`2023-6-1`

- 增加图片排版 3,3,4 的情况

## 2.23.8

`2023-6-1`

- ImageTable.ts 中 updateSerialNum 更新时判断是否是序号再更新值,解决交换图片顺序之后全都改成序号的 bug
- 解决调用 insertImageTable 的时候表格没有创建成功报错的 bug
- insert_raw 里边给 ImageTable 属性赋值增加 imageList 解决撤销后再插入报错的 bug
- 文档对齐不同步 bug 修改
- 模板转换后级联失效问题

## 2.23.7

`2023-5-30`

- 增加字号增减的图标
- 移动端报错问题

## 2.23.6

`2023-5-30`

- 增加悬浮按钮字号增减、字符间距还是放在字体中
- 增加帮助信息
- 性能监测时间显示格式化错误

## 2.23.5

`2023-5-29`

- 解决不展示拼音输入内容换行时自动删除内容的 bug

## 2.23.4

`2023-5-26`

- 增加性能监测窗，用于性能监测
- 新增滚动单元格逻辑

## 2.23.3

`2023-5-26`

- 增加单元格内边距按钮
- 悬浮按钮增加按住持续触发功能
- 悬浮球字符间距 bug 修改
- 悬浮按钮增加属性控制是否可连续触发
- 悬浮按钮增加段落重排
- 悬浮按钮增加插入按钮
- 右键菜单微调

## 2.23.2

`2023-5-23`

- 实现图片排版中创建合并后表格的功能
- 固定单元格高度滚动功能
- removeParagraphElement 参数形式修改
- 多层嵌套级联文本域设置

## 2.23.1

`2023-5-19`

- 解决新版表格拆分逻辑 row_size 分配不对的问题和 cell.split 传参不对的问题，错误效果是有空白窜罗和分页单元格内容永远在上一页，没有被拆分
- 复制粘贴表格数据样式调整
- 新增指定文本域高亮功能
- 修复页眉中插入表格表格内无内容的问题
- 新增获取所有图片的接口
- 重整批量打印方法

## 2.23.0

`2023-5-17`

- 只读模式下，允许删除,添加批注
- 悬浮球里的字符间距功能
- 修复右键菜单位置不准确的问题
- 增加”仅选择“功能
- 去掉右键菜单 style 中的最大高度属性值
- 修改右键菜单最大高度的计算方法

## 2.22.18

`2023-6-7`

- 增加了字母占位的配置

## 2.22.17

`2023-6-1`

- 移动端报错问题

## 2.22.16

`2023-5-29`

- 适配 base2.22.13 修改

## 2.22.15

`2023-5-16`

- 修复右键菜单位置不准确的问题

## 2.22.14

`2023-5-15`

- 增加悬浮按钮配置控制

## 2.22.13

`2023-5-15`

- 修改 font_style 的 ts 类型和上标下标的常量表示
- 干掉所有 ScriptEnum 替换成 ScriptType
- 临时解决 XField.toggleSymbol 卡顿问题，调用 refreshDocument(true)
- 给 cell.updateChildren 参数增加 ts 类型校验，优化逻辑代码，减少 get 属性的重复调用
- 表格 copy 增加 imageList 的 copy 解决 pacs 模板替换之后排版中的图片不能删除的问题
- 调整 json 对比接口，增加可传入忽略属性参数
- 解决模板替换，原来只读文本域插入后变成非只读的问题
- 修改右键菜单最大高度的计算方法

## 2.22.12

`2023-5-12`

- 修复批注信息滚动时造成排版错乱问题
- beforeFieldSelect 事件支持返回值设置 await

## 2.22.11

`2023-5-12`

- 文本域最小宽度换行时处理
- 同一个分组内，外面内容不能往表格中拖拽的 bug
- 修改 Paragraph.ts 文件中的 updateChildren 方法，更简化
- 优化全选逻辑
- 取消高亮优化
- 消息事件提示参数增加校验
- 抽屉组件自行实现，解决右键菜单二级菜单显示超出屏幕问题
- 兼容抽屉批注显示与菜单显示问题

## 2.22.10

`2023-5-10`

- 分组表单模式分组定位视图滚动不正确问题

## 2.22.9

`2023-5-9`

- 解决中文输入时，设置字体样式只闪一下，继续打字不生效的问题
- 优化设置文本域最小宽度问题，文本域换行时仍存在问题
- 分组表单模式光标问题

## 2.22.8

`2023-5-9`

- 分组表单模式开启后不允许通过光标移动跳分组
- 调整文本域最小与最大宽度设置逻辑
- 解决段落和文本域 replaceWith 中 cell 位置判断不准的 bug
- 插入模板增加参数是否替换成 rawData，判断是否有 hasLastLineBreak 和是否走插入模板逻辑，解决文本域替换时，段落不居中的问题
- 修改了 caret_move 方法使用的 direction 的 ts 类型，修改了参数名
- 蓝色字体颜色设置不生效问题修复

## 2.22.7

- 悬浮球在 demo 位置不正确问题

## 2.22.6

- 发版错误，重新发了一次

## 2.22.5

`2023-5-8`

- 悬浮按钮完善
- 双面打印机打印设置后提示调整

## 2.22.4

`2023-5-5`

- 修复文本域边框置空后删除报错的问题
- 实现文本域的 getRawData 方法
- PDF 和 C++打印绘制斜线
- 在 Editor.ts 文件中暴露批次获取容器的 rawData 的方法和批量替换的方法
- 修改文本域，段落和表格的 replaceWith 方法中的插入模板，由 editor 调用改为 EditorHelper 调用，避免在历史堆栈中记录
- 悬浮按钮完善

## 2.22.3

`2023-5-4`

- 在 Utils 中增加抽离出的初始化 cell 的方法，修改了 Selection 中 getRawData 逻辑，并给获取文本域的 rawData 方法开个头
- 段落排版相关代码整理
- 增加悬浮球本地开关
- 双面打印

## 2.22.2

`2023-4-28`

- 修复改变文本域边框边框位置不正确的问题

## 2.22.1

`2023-4-28`

- 优化文本域边框逻辑，高度跟随最高的元素
- 修复 input 文本域有时画不上的问题

## 2.22.0

`2023-4-27`

- 37 版本全部需求

## 2.21.13

`2023-5-16`

- 临时解决表格隐藏显示边文本域框卡顿问题

## 2.21.12

`2023-5-9`

- 解决稍微拖拽 label 标签，位置不变，getFocusField 不对的 bug
- 增加根据 base64 导出文件工具函数
- 设置单元格边距文字重叠 bug 修改
- 修复文本域边框置空后删除报错的问题
- 修复 c++打印报错问题

## 2.21.11

`2023-4-26`

- 修改 insertText 方法
- 修复 PDF 打印预览报错问题
- 嵌套文本域跨多段时数据转换结构错乱
- 微调模型数据转原始数据逻辑
- 修改了替换换行符的函数名
- 修改 Paragraph.ts 中的 insertElements 的参数名字等
- 按钮移入移出效果问题修复
- 页眉文本域 updateFieldText 结果为 undefined 的问题处理
- 对比 rawDatabug
- 对比 rawData 函数逻辑优化

## 2.21.10

`2023-4-24`

- 修复低版本众阳浏览器奇偶页不生效的问题
- 背景色设置报错、去除默认的自定义右键菜单

## 2.21.9

`2023-4-23`

- pdf 图片打印报错

## 2.21.8

`2023-4-23`

- 痕迹对比内容回显不正确问题

## 2.21.7

`2023-4-21`

- 解决嵌套文本域数据转换的 bug

## 2.21.6

`2023-4-21`

- 解决中文输入法的时候在锁定分组内仍然能够编辑的 bug
- 解决撤销错乱的问题
- 修复字符对齐问题
- 解决数据转换有引用，导致撤销时数据错乱的 bug
- 修复在表格内首行缩进报错的问题
- 修改替换特殊字符的方法
- 修改合并单元格和判断是否可以合并的函数名字

## 2.21.5

`2023-4-20`

- 增加字符对齐兼容性处理逻辑
- 增加隐藏批注里的替换按钮
- 文本域不可编辑下的提示语

## 2.21.4

`2023-4-20`

- 尝试修复组件没有注册的问题
- 时间下拉框编译后不显示修复测试

## 2.21.3

`2023-4-19`

- 解决修改单元格内容，其他非上对齐单元格内容位置不变的 bug，解决新版表格拆分，第二页往后表格 top 值计算不对的 bug，去掉嵌套文本域数据转换原来的 userLocal
- 增加低版本浏览器复制粘贴纯文本
- 非 https 协议打开的数据复制，放进剪切板纯文本
- 复制粘贴时只有不是本地的时候才进行版本和 https 协议的判断

## 2.21.2

`2023-4-18`

- 文本域边框样式与其中文本样式一致时内容丢失 bug
- 嵌套文本域数据转换增加判断
- 插入模板单选框外框变蓝色问题修复
- 修改插入图片排版和删除控制序列号的逻辑

## 2.21.1

`2023-4-17`

- 复制粘贴不带样式，压缩数据部分恢复
- 增加打印前事件，用于修改打印配置
- 快速录入模式 bug 修复（getnextField 方法还没改）
- 解決跨段嵌套文本域 rawData 转 modelData 造成被嵌套的文本域跟外层平级的 bug，删除报 removeElement 的那个
- 修复文档对齐的复制问题
- 撤销不显示的 bug 修改
- 修复文本域日期选择器 show_format 属性如果通过接口设置会造成展示与替换格式不匹配的问题
- 删除老版 JSPDF 打印
- 增加打印前事件用于修改打印参数

## 2.21.0

`2023-4-10`

- 36 版本全部需求

## 2.20.13

`2023-4-13`

- 修复文档对齐的复制问题
- 修复区域打印在表格内多打一行的问题
- 增加文本域面板选择前事件

## 2.20.12

`2023-4-12`

- 修复字符对齐导致的报错问题

## 2.20.11

`2023-4-11`

- 增加打印前事件，用于修改打印配置
- 修复打印预览和结果对不齐的问题

## 2.20.10

`2023-4-10`

- 补充缺失的中文字体
- 日期选择框新增汉字秒类型

## 2.20.9

`2023-4-7`

- 修复字符对齐在简洁模式下对不齐的问题

## 2.20.8

`2023-4-6`

- 打印内容缺失问题修复
- 去掉 modelDataToRawData 中的序列化代码，解决 pacs 打字操作卡顿的问题
- 修复字符对齐删除时位置不正确的问题

## 2.20.7

`2023-4-4`

- 增加对比两个 json 是否一致接口，忽略一些 id 类属性

## 2.20.6

`2023-4-4`

- 修复 ImageTable 数据转换时部分属性未保存
- 文本域 style 属性中缺少 family 与 height 时打印众阳浏览器闪退问题

## 2.20.5

`2023-4-3`

- 检查分组 id 的方法提取
- 解决了一个根据 path 获取不到内容的报错
- 暂不使用图片优化,先使用本地测试

## 2.20.4

`2023-4-3`

- 增加分组内容转换
- 优化模板转换分组相关代码
- 移除新增加的修改图片 dpi 方法
- 插入图片排版测试按钮问题修复、modelData 转 Dom 逻辑优化
- 插入模板图片丢失问题修复
- 修复 editor 数据改动导致的打印预览报错问题
- 取消背景按钮显示 bug 修改

## 2.20.3

`2023-3-24`

- 判断分组路径 bug 修改
- 优化批量打印有关 PDF 打印的相关逻辑
- 字体颜色 colorPicker 背景色修改

## 2.20.2

`2023-3-24`

- 复制 editor 接口增加 imageMap 处理
- 判断分组路径修改

## 2.20.1

`2023-3-23`

- 修复打印预览时没有图片的问题

## 2.20.0

`2023-3-22`

- 35 版本需求

## 2.19.18

`2023-4-7`

- 数据转换时序列化复制性能问题

## 2.19.17

`2023-3-28`

- 新增自适应窗口大小接口

## 2.19.16

`2023-3-24`

- 解决打字还没回车或者空格的时候就删除选区的 bug

## 2.19.15

`2023-3-21`

- recordVersionInfo 修改

## 2.19.14

`2023-3-21`

- 解决表单模式下，在可编辑表格内，点击空文本域，光标位置在开头的 bug
- 修改了文本域边框保留的版本号判定
- 表单模式下，在可编辑的表格内按方向键移动光标不受表单模式影响

## 2.19.13

`2023-3-20`

- 分组页眉信息替换功能 bug 修复
- 图片排版的表格内的图片不让放大缩小
- 解决选区末尾坐标在段落开头时删除会多删除一行的 bug，统一单元格内外的复制逻辑
- 都昌模板转换未保留单元格左右内边距问题

## 2.19.12

`2023-3-15`

- 修改表格属性，去掉单元格的最大高度设置时也更新
- contentChanged 事件中添加处理固定单元格高度的逻辑

## 2.19.11

`2023-3-15`

- 表单模式下护理表单点击右边纸张外区域报错

## 2.19.10

`2023-3-14`

- 表格拆分时，处理透明度的线
- 修复单元格内选区到下一段开头时设置选区内容与复制粘贴内容错误 bug
- 表单模式中空文本域使用 delete 删除导致光标位置错误
- 修复续打时会多打印上一行的签名的问题
- 段落重排接口支持只清空空段
- 修复低版本谷歌打印预览报错问题

## 2.19.9

`2023-3-9`

- 查找替换接口调整，优化高亮逻辑，返回查找数量
- 编辑器只读时可弹出图片编辑窗口问题修复

## 2.19.8

`2023-3-7`

- drawCell 传入 page 时绘制列表接口报错
- 表格拆分逻辑，拆分成两页情况，基本实现
- 插入多段内容报错问题修复
- 修复表单模式下粘贴时偶发粘贴到文本域外的问题
- 修复字符对齐时换行导致对不齐的问题

## 2.19.7

`2023-3-3`

- 修复分组内段落重排时将所有段落全部移除 bug

## 2.19.6

`2023-3-3`

- 修复打印接口阻塞的问题

## 2.19.5

`2023-3-3`

- 修复打印接口阻塞的问题

## 2.19.4

`2023-3-2`

- 调整进入页眉页脚编辑状态接口，增加剪切板写入与读取接口
- 每次设置焦点元素时先清空焦点元素

## 2.19.3

`2023-3-2`

- 重写表格拆分逻辑
- 新增段落重排剩余最后一段时需要判断是否删除
- 修改内部使用接口名 focus->setSelectImageOrWidget
- 修改获取当前焦点元素方法并加入本地测试
- 增加内部使用变量及函数管理类
- 修复批量打印打印顺序不正确问题
- 获取打印机列表失败后及时退出续打模式及显示文本域边框
- 新增打印预览刷新打印机列表功能
- 修复众阳浏览器不能记录选择的打印机问题处理
- 托盘手动重连不生效问题

## 2.19.2

`2023-2-27`

- 修复整合后 pdf 打印水印偏移的问题
- 修复区域打印选区不准确的问题
- 修复整合后水印绘制顺序
- 只有下拉框和日期选择面板只读时提示
- 增加动态请求下拉框不走缓存参数控制
- 修复打印当前页打印全部的问题
- 修复打印预览无法输入页码的问题

## 2.19.1

`2023-2-23`

- 增加文本域下拉列表动态异步请求方式支持

## 2.19.0

`2023-2-23`

- 34 迭代全部需求，打印相关提到 base

## 2.18.15

`2023-3-9`

- 单元格内选区到下一段开头时设置选区内容与复制粘贴内容错误 bug

## 2.18.14

`2023-3-6`

- 将白色为#FFF 的值转为透明背景色，之后新设置的白色背景需要设置成#FFFFFF,目的是为了解决之前修改过字体样式后会自动设置白色背景色问题

## 2.18.13

`2023-3-6`

- 解决文本域内字体缩放的 bug

## 2.18.12

`2023-3-6`

- 文本域设置最大高度后，复制内容粘贴到选区内容时无法替换选区内容
- 打印带列表的内容时报错
- cpp 打印列表报错

## 2.18.11

`2023-2-27`

- 修复打印当前页打印全部的问题
- 修复打印预览无法输入页码的问题

## 2.18.10

`2023-2-21`

- 可编辑表格在表单模式下里边的图片是可以选中的
- 分组增加获取纯段落属性
- 获取文档内所有段落增加可获取纯段落

## 2.18.9

`2023-2-16`

- placeholder 颜色错误修改

## 2.18.8

`2023-2-15`

- 匿名化星号展示规则问题修复

## 2.18.7

`2023-2-14`

- 匿名化星号展示规则问题修复
- 解决在空文本域内粘贴字体不缩放的问题

## 2.18.6

`2023-2-13`

- 匿名化问题修复
- 增加生僻字转图片方法，暂不启用
- 段落重排传入表格报错

## 2.18.5

`2023-2-10`

- 匿名化页眉页脚不生效问题修复

## 2.18.4

`2023-2-10`

- 图片打印匿名

## 2.18.3

`2023-2-9`

- 都昌模板转换增加服务端兼容
- 在 contentChanged 中添加处理文本域字体缩放的逻辑

## 2.18.2

`2023-2-9`

- 都昌模板转换增加服务端兼容

## 2.18.1

`2023-2-1`

- 新增正序逆序功能
- 调试工具修改
- 增加设置当前分组新页展示属性
- 优化绘制临时 border 的方法，减少传参

## 2.18.0

`2023-1-31`

- 33 版本全部需求

## 2.17.10

`2023-2-15`

- 优化设置文本域内字体缩放的方法和文本域增加 rows 的计算属性
- 文本域内字体缩放给放大的循环 z 增加次数限制，避免死循环

## 2.17.9

`2023-2-10`

- 修改文本域获取高度的方法，设置字体大小加基础高度 d 的参数
- 修改文本域字体缩放逻辑

## 2.17.8

`2023-2-10`

- 去掉 Editor.ts 文件中 setCharacterSize 中的 @undoStackRecord(commands.uncertainty)

## 2.17.7

`2023-2-10`

- 增加文本域设置字体缩放的接口
- 文本域和段落设置字体大小方法增加参数，可传最大高度
- 设置字体大小添加到历史堆栈中
- 处理文本域内字体缩放，粘贴的时候也能正常执行

## 2.17.6

`2023-2-08`

- 增加生僻字支持，修改弹窗方式

## 2.17.5

`2023-1-31`

- 新增配置可控制编辑器展示字体
- 修复批量打印报错

## 2.17.4

`2023-1-29`

- 修复段落重排段落全是空行的情况
- 分组属性新增分组表单设置按钮

## 2.17.3

`2023-1-29`

- 修复分组表单模式点击切换分组时的问题

## 2.17.2

`2023-1-20`

- 新增分组表单模式

## 2.17.1

`2023-1-19`

- 新增表格指定行转换接口
- 新增表格转换原始数据时合并单元格的处理
- 修复文本域替换图片接口不支持字符串宽高的问题
- 新增按配置加载接口

## 2.17.0

`2023-1-18`

- 修复表格线在放大时位置不准确的问题
- 根据版本号判断文本域边框宽度是否保留

## 2.16.10

`2023-1-13`

- 修复插入模板时合并文本域样式时未加判断的问题

## 2.16.9

`2023-1-13`

- 插入模板时统一字体字号时兼容老模板

## 2.16.8

`2023-1-13`

- 新增服务端获取在线图片接口判断
- 新增插入模板是否使用默认字体字号配置

## 2.16.7

`2023-1-11`

- 新增服务端重置状态接口
- 修复时间框快捷输入时分秒不回显的问题
- 修复鼠标停留提示信息显示位置不正确的问题
- 注释数据压缩接口

## 2.16.6

`2023-1-9`

- 修改表单模式下文本域粘贴到文本域之外的 bug

## 2.16.5

`2023-1-6`

- 修复图片编辑报错的问题

## 2.16.4

`2023-1-5`

- 修复 C++打印水印图片第二页之后不显示的问题

## 2.16.3

`2023-1-5`

- 修复 C++打印水印图片第二页之后不显示的问题

## 2.16.2

`2023-1-5`

- 修复表格背景色复制换页问题
- imageMap 中图片加载无需等待图片加载完后在放置
- 新增从 imageMap 获取不到数据时的判断

## 2.16.1

`2023-1-4`

- 修复页边距 40 时选中表格最后一列选区不对的问题
- 新增改变表格背景色的问题
- 新增授权码过期提示水印接口
- 图片打印修改 dpi 报错问题
- 优化图片编辑画笔颜色逻辑
- 新增表格选区改变背景颜色
- 注释授权码校验逻辑
- cell 中的 bgcolor 放到 style 中

## 2.16.0

`2023-1-3`

- 31 版本需求
- 增加表单只读属性，防止与编辑器只读混乱

## 2.15.7

`2022-12-30`

- 修复表格在表单模式下可编辑没有保存的问题
- 绘制表单模式下表格可编辑提示框
- 处理特殊字符，底层逻辑会将其转换成乱码问题
- 去掉 image.onload 中白色背景逻辑
- reInitRaw 中添加 watermark 逻辑
- 修复水印图片第二页 pdf 不打印问题

## 2.15.6

`2022-12-28`

- 还原打包配置

## 2.15.5

`2022-12-28`

- 修复背景绿色问题

## 2.15.4

`2022-12-28`

- 修复移动端编辑器页面偏移问题

## 2.15.3

`2022-12-27`

- 修复水印输入框拖动位置不准确的问题
- 修复 reInitRaw 之后打字撤销字体样式变粗的问题
- 表单模式下按住鼠标左键不允许粘贴

## 2.15.2

`2022-12-26`

- 水印文字功能提交
- 右键菜单事件更改变量名
- 增加 id 重复校验，增加按照时间顺序插入分组
- 修复插入空行问题，记忆插入分组
- 将所有的 window.electron 进行调整，取不到时从 top 中取

## 2.15.1

`2022-12-19`

- 时间框输入校验

## 2.15.0

`2022-12-16`

- 30 迭代发版

## 2.14.11

`2022-12-14`

- 修复打印预览图片跨域问题
- 新增字体高度配置给出不符合要求的提示

## 2.14.10

`2022-12-14`

- 修复当特殊字符在字典文件中找不到报错的问题
- 新增水印图片删除的方法

## 2.14.9

`2022-12-13`

- 修复水印图片模式切换位置问题
- 修复 pdf 图片打印和 c++打印中水印图片位置不正确的问题

## 2.14.8

`2022-12-13`

- 恢复文本域样式优化

## 2.14.7

`2022-12-13`

- 修复引入模板数据插入到文本域外部的问题
- 校验功能修复

## 2.14.6

`2022-12-12`

- 修复固定单元格高度后，内容满了再删除，单元格高度变大的问题
- 修复字体缩放到一页后删除内容导致报错的问题
- 修复背景绿色问题

## 2.14.5

`2022-12-12`

- 修复背景绿色问题

## 2.14.4

`2022-12-9`

- 修复固定单元格高度输入内容会导致表格下方内容消失的问题

## 2.14.3

`2022-12-9`

- 配置字体缩小到一页
- 修复移动端水印多出的 div 的问题
- 修复模板字符串报错的问题
- 修复右键菜单位置不正确的问题
- 表格属性中新增单元格固定高度
- 修复下拉列表替换分隔符不生效的问题

## 2.14.2

`2022-12-6`

- 修复固定单元格高度，单元格内文本域没有缩小的问题
- 解决下拉框问题

## 2.14.1

`2022-12-5`

- 增加自定义扩展信息属性
- 修复加密数据打开报错
- 增加压缩功能后，另存 json 接口调整
- 在插入分组并排序的方法中加上清空选区的操作
- 单选搜索内容恢复

## 2.14.0

`2022-12-5`

- 先发一版

## 2.13.3

`2022-11-30`

- 修复都昌模板转换时设置级联文本域报错问题及下拉选择框激活模式问题
- 新增暴露压缩解压的接口
- 新增组件销毁前事件解除绑定

## 2.13.2

`2022-11-27`

- 修复往分组内粘贴内容跑到分组外的问题
- 去掉判断解决粘贴段落内部分内容插入空行的问题
- 开启 dpi 修改后所有图片都进行转换，否则有部分图片不能正常展示
- 修复修改纸张类型与修改横纵向不生效的问题

## 2.13.1

`2022-11-25`

- 解决图片排版不能保存图片之间间距的问题
- 修改 getRawData 和 reInitRaw 可以获取压缩后的数据和加载后的数据
- 修复快速点击文本域紧接着输入内容回电到文本域外边的问题
- 新增单选双选模式切换
- 新增多选分隔号选择
- 修复不能粘贴图片的问题
- 内存回收问题处理，修改 bus 传值逻辑去掉 webWorker 增加组件销毁前解绑事件逻辑

## 2.13.0

`2022-11-21`

- 新增从外部复制图片到编辑器中
- 修复 C++打印有序列表序号不齐的问题
- 编辑器接口整理
- 新增选区复制某些单元格可直接粘贴到另一个表格的选区中
- 编辑器加载数据时可读取数据中的配置
- 编辑器数据结构优化
- 编辑器新增水印接口
- 编辑器表单模式下实现表格可编辑

## 2.12.8

`2022-11-23`

- 修复页面切换后调用 updateCanvasSize 方法会将页面恢复到光标位置的问题

## 2.12.7

`2022-11-16`

- 修复签名图片不能打印的问题

## 2.12.6

`2022-11-15`

- 编译不成功重新发版

## 2.12.5

`2022-11-15`

- 编译不成功重新发版

## 2.12.4

`2022-11-15`

- 调试工具增加强制刷新按钮

## 2.12.3

`2022-11-10`

- 修复表格分页时插入行携带数据报错的问题

## 2.12.2

`2022-11-8`

- 修复浏览器打印会打印文本域边框及背景文本问题修复
- 更换图片编辑 id 获取方式，加入 editorId

## 2.12.1

`2022-11-7`

- 修复云病历转科后导致页眉与正文之间出现多行空白的问题
- 找回 editor.ts 文件中表格是否可以删除行和列的方法
- 修复图片因 dpi 不同导致在个别电脑打印不全的问题
- 优化图片编辑逻辑
-

## 2.12.0

`2022-11-4`

- 选区内容可以放大缩小
- 增加粘贴前事件，可对要粘贴的内容进行处理
- 表格新增行时可自动携带上一行内容
- 增加编辑器服务端原始 json 转 PDF Base64 接口(详细信息见编辑器在线 API 文档)
- ctrl+tab 能够在表格内设置缩进
- 图形编辑功能扩展，增加矩形绘制、文本插入、矩形背景设置

## 2.11.7

`2022-11-7`

- 修复云病历转科后导致页眉与正文之间多出多行空白问题

## 2.11.6

`2022-11-1`

- 修改文本域属性接口，增加判断文本域是否已经被删除

## 2.11.5

`2022-11-1`

- 修复修改页眉中文本域报错的问题

## 2.11.4

`2022-10-27`

- 修复修改页眉中文本域报错的问题
- 修复续打模式下页眉下多一个空行的问题

## 2.11.3

`2022-10-26`

- 新增服务端启动传入 rawData
- 修复插入图片不显示及透明背景图片打印绿色问题
- 修复段落重排有空字符串报错和表格内无反应问题
- 新增\r 字符宽度判断
- 修复 C++精度问题引起的续打位置不正确的问题

## 2.11.2

`2022-10-24`

- 新增服务端启动传入配置项
- 图片背景改变还原

## 2.11.1

`2022-10-21`

- 修复段落重排首行不缩进情况不清除空格的问题

## 2.11.0

`2022-10-21`

- 调整段落重排接口，通过参数可控制是否清除段落所有的空格、是否需要设置段落首行缩进。
- 新增将光标定位到单元格接口
- 删除或者清空分组的时候，没有删除 cell 内对应的文本域
- 获取多个文本域，但是只给一个文本域赋新值，没有赋新值但是又调用 updateFieldText 的方法的文本域，值会变成 undefined
- 痕迹视图中的痕迹文本背景色不应该被复制

## 2.10.6

`2022-10-28`

- 修复续打模式下页眉下多一个空行的问题

## 2.10.5

`2022-10-19`

- 修复都昌模板转换后图片变大、下划线不显示、字体颜色与背景色未转换、表格线显示隐藏的问题
- 修复没有任何批注信息时切换批注模式报错的问题
- 新增文档无批注信息时友好提示

## 2.10.4

`2022-10-11`

- 修复文本域简单复选框点击报错问题
- 修复切换文档后批注刷新问题

## 2.10.3

`2022-10-10`

- 修复页眉分组关联导致页眉高度问题
- 新增是否展示图片弹框方法

## 2.10.2

`2022-10-10`

- 版本加载问题重发

## 2.10.1

`2022-10-10`

- 新增双击图片弹出图片编辑框判断
- 修复护眼模式下复选框背景颜色问题
- 修复背景色使用批注颜色的问题

## 2.10.0

`2022-10-10`

- 新增护眼模式
- 去除 pdf 打印所需字体文件请求时的版本号,避免每次升级后都重新请求字体文件
- 优化单元格复制、数据更新代码逻辑以提升编辑时性能
- 修复光标定位到文档末尾接口 bug
- 增加图片编辑功能，初步先实现可在图片上绘制线条及线条的编辑功能。
- 优化云病历病程记录多页时卡顿问题
- 修复清空内容按钮的 BUG
- 优化表格插入行列时边框线显示隐藏效果

## 2.9.1

`2022-09-23`

- 修复文本域日期选择器 HH：mm 格式类型错误问题
- 补全自定义文本域校验

## 2.9.0

`2022-09-23`

- 文本域日期选择器增加 HH：mm 格式类型
- 修复表格分页时上下拖动横线，单元格上下居中，下对齐的内容没有实时变化问题
- 增加手动设置双面打印机功能
- 日期选择面板有时候会遮挡输入框问题修复
- 修复拖拽标签文本域到另一个普通文本域中，直接右键修改标签文本域内容会将普通文本域整个替换问题
- 解决设置字符对齐的模板调用更新文本域接口时传入字符对齐参数后报错问题
- 优化编辑器加载多页内容时编辑器滚动效果

## 2.8.1

`2022-09-14`

- 修复表格分页时，上下拖动表格线，重排部分单元格，对齐实时生效的问题
- 修复批注 commentsIDSet 为空值的问题
- 修复多个文本域粘贴报错的问题
- 新增图片编辑功能
- 新增支持双面打印功能

## 2.8.0

`2022-09-08`

- 新增续打病历时可选择页码功能
- 新增单元格中 Tab 键跳至下一单元格功能以及追加一行功能
- 优化多页内容时拖动滚动条时体验
- 新增批注功能
- 新增单元格锁定功能
- 修复使用 Delete 键删除内容时偶发输入 null 字符的问题
- 新增插入线条功能
- 都昌模板转换增加首行缩进，多选下拉框，文本域边框隐藏功能
- 优化复制粘贴文本域保证正确顺序
- 优化都昌模板转换逻辑，增加级联文本域转换

## 2.7.11

`2022-09-05`

- 修复多选框打印没有边框的问题

## 2.7.10

`2022-09-05`

- 合并都昌模板转换修改到 22 版本发布新版

## 2.7.9

`2022-09-03`

- 都昌模板转换表格嵌套异常提示移除

## 2.7.8

`2022-09-03`

- 都昌模板转换逻辑只允许出现一次的节点未重新初始化

## 2.7.7

`2022-09-03`

- 都昌模板转换优化

## 2.7.6

`2022-09-03`

- 修复修复模板转换报错的问题

## 2.7.5

`2022-09-01`

- 修复替换文本域在某些情况下报错的问题

## 2.7.4

`2022-08-31`

- 修复滚动条不能拖动的问题

## 2.7.3

`2022-08-30`

- 修复移动端不能滑动的问题

## 2.7.2

`2022-08-26`

- 修复转换为 rawData 时 shapes 为空报错问题

## 2.7.1

`2022-08-25`

- 修复点击日期面板外不能正常关闭问题

## 2.7.0

`2022-08-25`

- 新增文本域自定义校验功能
- 新增通过 delete 键可以删除表格上方空行
- 新增选择框只有一个对号的样式
- 修复配置默认行倍距不生效问题
- 新增增加自定义线条功能
- 修复病历检索界面打开病历书写页面后无法点选下拉选择文本域的问题
- 修复其他产品嵌入编辑器后会出现可视范围内编辑器外的其他内容不可点击的情况的问题
- 修复右键设置表格名称时不能正常回显表格名称，并且关闭录入窗口时未清空输入框内容的问题
- 新增编辑器调试工具，ctrl+shift+F12 打开

## 2.6.3

`2022-08-17`

- 修复托盘使用新版打印回调函数不生效的问题

## 2.6.2

`2022-08-12`

- 优化 Pacs 插入图片排版
- 替换文本域 raw 丢失字符问题
- 新增移除绑定事件接口
- 修改提示最大消息数目为 1

## 2.6.1

`2022-08-11`

- 修复复选框事件中级联逻辑与 contentChanged 事件冲突问题
- 新增添加级联注意事项

## 2.6.0

`2022-08-11`

- 复制跨段文本域粘贴到其他文本域后保存再重新加载数据后排版错乱。
- 表格性能优化。
- 移动端多页内容展示不全问题修复。
- 表单模式下使用 delete 键删除时光标移动位置不正确。
- 双面打印机病程记录续打开始页为偶数页时打印内容覆盖。
- 增加文本域级联功能。
- 可根据名称获取图片排版表格。
- 优化 Pacs 插入图片排版。
- 表格固定表头。

## 2.5.7

`2022-08-02`

- 修复生成 pdf base64 时偶发生成不了图片的问题
- 修复表单模式下移动光标输入会一直提示表单模式不可输入问题

## 2.5.6

`2022-08-02`

- 修复生成 pdf base64 时偶发生成不了图片的问题

## 2.5.5

`2022-08-01`

- 修复系统打印预览带有文本域边框的问题

## 2.5.4

`2022-08-01`

- 修复打印预览后加载模板失败的问题

## 2.5.3

`2022-08-01`

- 修复打印预览后加载模板失败的问题

## 2.5.2

`2022-07-29`

- 修复单机双击下拉框列表不显示问题

## 2.5.1

`2022-07-29`

- 修复替换文本与内容的问题

## 2.5.0

`2022-07-28`

- 修复在右键菜单事件中重载自定义菜单自定义的回调函数不生效的问题
- 修复新版打印下标变上标的问题
- 修复在光标处右键改为宋体，插入输入助手中的诊断内容，此时插入的诊断内容还是仿宋体的问题
- 全局配置转为实例配置
- 修复段落重排丢失段首文字的问题
- 修复替换页眉文本域内容，若接口参数中未添加 isHF 的值，那么替换内容将会以文本的形式插入到文档开头的问题
- 修复文本域日期选择框与下拉框双击激活的问题

## 2.4.2

`2022-07-15`

- 修复文本域校验样式问题

## 2.4.1

`2022-07-14`

- 段落重排逻辑优化
- 修复 antDesign 样式重叠问题
- 修复文本域时间框便捷输入时分秒空白时默认参数问题

## 2.4.0

`2022-07-13`

- 段落重排逻辑优化
- 修复文本域日期选择框日期正则表达式不正确的问题
- 移动端下不在页面上方 input 元素
- 新增敏感词检测接口
- 新增文本域校验接口
- 修复字符对齐段落自上而下选区设置左对齐报错问题
- 修复移除嵌套文本域报错问题
- 修复 pdf 打印透明背景图片时背景颜色为黑色

## 2.3.6

`2022-07-07`

- 修复文本域时间框遮盖问题
- 优化文本域校验提示逻辑
- 文本域校验属性回显问题修复
- 修复首次加载不能 pdf 打印问题

## 2.3.5

`2022-07-06`

- 修复切换组件后获取不到 jsPdf 对象的问题

## 2.3.4

`2022-07-01`

- jsPdf 加载字体文件逻辑调整

## 2.3.3

`2022-06-29`

- 修复插入只有一个段落的模板不能缩进的问题
- 修复调用 instance 处 this 指向问题

## 2.3.2

`2022-06-29`

- 修复英文输入法时按住 shift 不能输入的问题
- 新增插入模板时判断模板开头是否为表格的逻辑
- 修复 webWorker 里边访问 instance 报错问题

## 2.3.1

`2022-06-28`

- 修复字符对齐排版错位问题
- 修复非文档首插入模板（模板开头为表格）多出空行问题
- 修复表单模式下连续文本域删除报错问题

## 2.3.0

`2022-06-27`

- 修复 pacs 修改字体样式编辑器崩溃问题
- 修复 pacs 表单模式拖拽编辑器崩溃
- 修复多编辑器下字体配置文件覆盖问题
- 修复标签和时间框类型的文本域替换文本字符中存在\n 编辑器报错问题
- 新增新建病程记录,查房记录时输入域默认首行缩进 2 字符
- 修复文本域时间框显示 Invalid date 问题
- 修复文本域设置只读时，光标定位到文本域开始边框字符后使用 Backspace 键删除会将整个文本域删掉的问题
- 修复空文本域内插入水平线位置不正确问题
- 修复边点击边删除文本域内容报错的问题
- 批量打印优化
- 文本域时间框属性优化

## 2.2.2

`2022-06-17`

- 修复众阳浏览器中使用新版打印的判断逻辑问题

## 2.2.1

`2022-06-17`

- 修复众阳浏览器内无法获取版本号的问题

## 2.2.0

`2022-06-14`

- 修改版本号判断逻辑
- 修复打印选取打印范围错误的问题（7-10）

## 2.1.1

`2022-06-02`

- 修复编辑器在移动端下不缩放的问题
- 修复新托盘下批量打印不生效问题

## 2.1.0

`2022-05-31`

- 新增表单模式下拖拽功能
- 新增文本域时间框禁止时间参数
- 新增失焦后光标不闪烁配置
- 修复删除导致的文本域字符与段落字符不一致的问题
- 修复列表序号重排不保存的问题
- 修复表单模式下选中文本域开始边框删除光标位置跑到第一个文本域内的问题
- 修复点击页面外光标定位不准确的问题
- 修复页眉点击时光标不能正常定位的问题
- 新增 pointerUp 事件
- 文本域日期选择框新增禁止时间功能

## 2.0.5

`2022-05-24`

- 修复批量打印在众阳浏览器旧版本不能打印的问题

## 2.0.4

`2022-05-24`

- 修复向文本域中复制粘贴带换行符内容时造成文本域对象中字符缺失，继续往其中粘贴内容会导致 field.text 获取内容乱序的问题

## 2.0.3

`2022-05-20`

- C++图片打印位置矫正
- 修改托盘版本获取方法
- 修复自定义复选框修改后再次插入后位置不正确的问题
- 回退文本域日期选择优化功能

## 2.0.2

`2022-05-18`

-版本错误重新发版

## 2.0.1

`2022-05-18`

-合并 1.14.1 版本修改补丁

## 2.0.0

`2022-05-18`

- 添加图片打印间距
- 新增表格 copy 的时候增加 name 属性赋值
- 修复页脚表格线横向不能连续拖动的问题
- 正常显示隐藏页脚表格线
- 修复点击滚动条外部也能实现滚动条滚动的问题
- 定位到指定文本域开头位置
- C++打印相关

## 1.5.5

`2022-05-17`

- 发版错误重新发版

## 1.5.4

`2022-05-17`

- C++打印默认自动缩放，续打表格上边线不显示问题修复
- 修改打印批量打印续打位置不正确的问题
- 修复众阳浏览器获取版本报错问题
- 添加获取托盘版本号接口

## 1.5.3

`2022-05-13`

- 新增单独控制行首字符及数字排版功能更改
- 新增插入图片排版可以固定表格高度
- 插入图片排版新增各种判断，避免文本域、页眉页脚内报错错乱问题
- 新增图片排版序号与文本域可控制是否展示
- 修复进入页眉页脚编辑模式时进入页脚编辑的问题
- 图片排版双击图片不能删除

## 1.5.2

`2022-05-11`

- 新增符号排序
- 添加段落构造函数里对排版的复制控制
- 新增 ctrl+shift+F10 切换行首字符排版功能

## 1.5.1

`2022-05-07`

- 修改数字排版兼容问题导致的字符排版问题初夏那多余空行 id 的情况

## 1.5.0

`2022-05-07`

- 新增图片排版下方第二个文本域可控制不传
- 修改单元格内容导致表格行高发生变化，设置为非上对齐的单元格内容位置不变的 bug 修复
- 修复页眉设置选中文本字号后文本宽度不能立即生效的 bug
- 修复复制单元格时里边的下拉文本域没有下拉选项的问题
- 新增设置是否启用数字排版的接口
- 修改数字排版新段落的生成逻辑
- 添加排版变化后刷新页面现有数据

## 1.4.7

`2022-04-26`

- 新增兼容性数字排版
- 新增 ctrl+shift+f11 快捷键开启关闭数字排版

## 1.4.6

`2022-04-25`

- 痕迹对比可传字符串或对象
- 修复打字时滚动条消失的问题
- 给移动端遮罩层添加 ID
- 痕迹对比横纵向按照新版本来

## 1.4.5

`2022-04-24`

- 修复偶发获取 pathName 不正确导致的字体文件加载异常问题
- 添加打印 PDF 列表的情况
- 修复续打位置不正确的问题

## 1.4.4

`2022-04-22`

- 放开排版逻辑

## 1.4.3

`2022-04-22`

- 误升级版本号

## 1.4.2

`2022-04-22`

- 编译不成功重新发版

## 1.4.1

`2022-04-21`

- 修复文本域设置只读后仍能弹出医学表达式弹框的问题
- 优化医学表达式排版
- 修复表格变化时对比报错
- 修复痕迹对比不展示人信息的问题
- 痕迹对比不展示分组信息

## 1.4.0

`2022-04-14`

- 新增痕迹对比功能
- 新增恒牙和乳牙复杂牙位图功能
- 新增数字和英文的排版逻辑
- 修复编辑器中含有符号时打印预览不显示问题

## 1.3.8

`2022-04-13`

- 修复文本域替换图片后，数字排版报错，删除文本域失败的问题

## 1.3.7

`2022-04-12`

- 修复 16K 大小打印预览显示超宽问题

## 1.3.6

`2022-04-08`

- 修复含有文本域的单元格合并拆分报错问题
- 修复关闭 F12 后编辑器大片空白的问题
- 修复文本域隐藏后报错问题

## 1.3.5

`2022-04-07`

- 修复关闭 F12 后编辑器大片空白的问题
- 修复字体文件更换后不生效的问题

## 1.3.4

`2022-04-07`

- 修改无序列表图标样式
- 修复单个段落的段落重排
- 新增替换或追加文本域元素接口
- 修复分页情况下双击表格线导致 row_size 不正确的问题
- 修复无法置空 placeholder 的问题
- 修复文本域替换文本接口传入多个相同文本域对象会将内容替换为 undefined 的问题
- 优化模型数据转原始数据文本域会多生成空 text 的问题
- 优化配置报错提示

## 1.3.3

`2022-03-31`

- 编译错误重新发版

## 1.3.2

`2022-03-31`

- 新增返回编辑器版本信息的方法

## 1.3.1

`2022-03-29`

- 修改插入图片排版自动排列序号
- 修复增加 editor 配置后导致插入模板报错问题

## 1.3.0

`2022-03-29`

- 新增编辑器独立配置
- 修复图片放大后调整横纵向图片超出页面的问题
- 新增影响编辑器加载的配置校验
- 新增段落重排功能
- 新增文本域替换原始 json 数据接口
- 文本域新增月日格式
- 新增十字牙位图
- 删除添加常用语弹框
- 绘制 pdf 时增加 url 图片绘制
- 常用语添加 keyword 为空的判断

## 1.2.36

`2022-03-22`

- 修复下载 pdf 不成功的问题
- 托盘所需参数矫正

## 1.2.35

`2022-03-21`

- 新增 config 配置错误抛出异常的校验
- 修复选区光标在下方空白处移动时不能选区最后一段换行符问题
- 修复放大后更新 canvas 大小不准确的问题
- 新增表格内沿用 rawData 的表格 id

## 1.2.34

`2022-03-17`

- 修复横向滚动条展示错误的问题

## 1.2.33

`2022-03-17`

- 直接打印后取消续打模式
- 修复校验纸张大小的方法
- 修改删除线位置
- 修复最后一页滚动问题

## 1.2.32

`2022-03-15`

- 修复插入横向模板时未正常转为横向的问题
- 修复云浏览器立即打印未能取消续打模式的 bug。
- 添加静默打印添加未能获取打印机列表时的提示。
- 插入表格时取消最大行列限制。
- 添加插入超出计算最大接受列数的提示。并提示最大承受列数。

## 1.2.31

`2022-03-14`

- 修复插入横向模板时未正常转为横向的问题
- 修复编辑器隐藏状态下报错的问题
- 修复退出页眉页脚编辑模式后光标不准确的问题
- 修复复选框回显、清空问题
- 修复无纸化生成 base64 时异步可能导致的生成内容不对的问题
- 托盘打印打印机参数修正
- 众阳浏览器新增打印份数
- 常用语判断条件添加可配置属性
- 优化区域打印遮罩颜色

## 1.2.30

`2022-03-02`

- 优化文本域修改提示文本域为空
- 修复打印预览图片偏移问题
- 修复插入图片排版撤销不生效问题
- 修改单元格对齐在初次渲染数据时不正确的问题
- 新增获取选区纯文本的方法
- 新增批量打印关于新浏览器的判断
- 修复医学表达式 1 时间格式回显问题
- 新增常用语关键词搜索功能

## 1.2.29

`2022-02-23`

- 修复表格硬分割判断问题
- 修复横向滚动条不显示的问题
- 新增自动判断新浏览器是否支持批量同步打印，不支持自动降级为异步打印，但是异步打印存在打印乱序的问题

## 1.2.28

`2022-02-22`

- 修复滚筒条随页面宽度改变不显示的问题
- 修复当前有选区且选区中包含文本域时调用移除文本域接口报错问题
- 修复硬分割判断的问题
- 修复设置简介模式时光标位置问题
- 修复批量打印乱序、漏打的问题

## 1.2.27

`2022-02-21`

- 修复关闭日期选择框和下拉选择框的相关问题

## 1.2.26

`2022-02-21`

- 修复关闭日期选择框和下拉选择框的相关问题
- 修改页面左右滚动条逻辑

## 1.2.25

`2022-02-18`

- 因编译问题升级版本号

## 1.2.24

`2022-02-18`

- 修复点击编辑器外关不掉文本域弹框的问题
- 修复立即打印时导致的绘制内容不全的问题
- 修改打印接口成全文转换出来后打印

## 1.2.23

`2022-02-17`

- 修复都昌模板转换不成功问题
- 优化鼠标移动到文本区域外时的展示状态
- 修复 vue-worker 位置异常导致的未正常打包问题

## 1.2.22

`2022-02-16`

- 修改新浏览器预览后续打状态关闭
- 修复都昌新老版本数据接口不一致导致的模板转换报错问题

## 1.2.21

`2022-02-16`

- 修复医学表达式修改后 pdf 绘制异常问题

## 1.2.20

`2022-02-16`

- 修复批量打印为判断是否在新浏览器中

## 1.2.19

`2022-02-16`

- 图片 src 为空时，打印报错

## 1.2.18

`2022-02-11`

- 修复续打和区域打印模式下仍能弹出下拉框问题
- 处理表格合并单元格时绘制超出页面与回车时表格 rowSize 设置错误问题
- 优化页眉页脚复制逻辑和文本域边框显示隐藏逻辑
- 优化连续选中复选框情况下触发选区问题
- 修复多次撤销时撤销未初始化导致的撤销结果不对问题
- 修复页眉页脚修改后分散对齐导致的位置计算错误的问题
- 修复分散对齐时跨文本选区删除报错的问题
- 修复单选/复选框在表单模式下无法被选中的问题
- 新增 ctrl+shift+F12 展示当前版本号

## 1.2.17

`2022-02-08`

- 新增 pdf 立即续打功能
- 新增 vue-worker 处理转 base64 逻辑
- 修复图片编辑框不消失问题
- 修复光标移动时三连击从页眉到正文报错及复选框连续选中问题
- 修复新浏览器获取打印机列表方法问题
- 修复新浏览器立即打印问题

## 1.2.16

`2022-02-07`

- 因测试环境编译问题升级，代码无变动

## 1.2.15

`2022-02-07`

- 修复简单单选框/复选框无法被选中问题
- 修复从后往前选中多行删除报错问题
- 新增判断避免页眉信息重复替换
- 修复打印托盘判断问题
- 新增对 electron 支持

## 1.2.14

`2022-01-26`

- 修复单选框在跨页表格内选中成多选效果的问题
- 修改自定义多选框没有分组名的情况下处理逻辑
- 修复自动以复选框回显问题
- 修复选区状态下文本域修改属性报错的问题
- 修改打印参数，解决打印偏移量问题

## 1.2.13

`2022-01-25`

- 修改插入图片排版，可传入图片宽高设置比例
- 修复表格内有文本域时跨页报错的问题
- 新增段落复制增加字符对齐属性复制

## 1.2.12

`2022-01-24`

- 新增插入 pacs 需求排版图片的方法
- 新增打印配置，控制 ODF 打印偏移问题
- 对接新浏览器打印功能
- 对接浏览器打印的快捷键功能
- 修改插入表格传参逻辑

## 1.2.11

`2022-01-21`

- 修复打印预览窗口重复弹出问题

## 1.2.10

`2022-01-21`

- 修改打印逻辑，兼容打印托盘
- 修复绘制 PDF 插入图片后第二次绘制不压缩的问题

## 1.2.9

`2022-01-18`

- 新增调整页面大小后图片自适应的方法
- 修复分散对齐对于嵌套文本域最后字符错乱的问题
- 修复格式刷导致的排版错乱问题
- 修改都昌模板转换逻辑，增加固定宽度文本域转换
- 新增绘制 PDF 水平线绘制逻辑
- 新增元素双击事件
- 增加文档转 base64 码接口
- 调整月经史公式 1 插入后展示问题
- 新增字体文件路径名可配置，用于嵌套 iframe 使用的情况

## 1.2.8

`2022-01-13`

- 修复剪切板为空时拖拽报错问题
- 修复点击其他图片时原带编辑框图片编辑框不消失问题
- 修复无序列表有序列表混用时，有序列表序号出现混乱的问题
- 修改插入分组的菜单信息，保证在分组外插入分组时只能选择下方插入
- 修改下拉选择框判断下方展示空间计算错误问题

## 1.2.7

`2022-01-12`

- 新增 ctrl+c 在编辑期内粘贴为带格式粘贴，在编辑器外粘贴为纯文本粘贴，删除 ctrl+shift+c 和 ctrl+shift+v 快捷键，用 ctrl+c 和 ctrl+v 替代
- 新增分组关联页眉兼容老数据
- 修复高清屏下鼠标下拉超越边界滚动不生效问题
- 修复右键文本域报错问题
- 新增 jsPdf 打印回调

## 1.2.6

`2022-01-10`

- 修复高清屏下鼠标下拉超越边界滚动不生效问题

## 1.2.5

`2022-01-10`

- 修复打印预览不显示问题

## 1.2.4

`2022-01-10`

- 修复 modal 拖拽问题
- 修复文本域提示及文本域校验提示在编辑器放大缩小后位置错误问题
- 修改字体文件加载方式
- 修复序号列表错乱问题
- 新增分组关联页眉信息方式

## 1.2.3

`2022-01-07`

- 修复分组锁定情况下，右键菜单中表格操作禁用问题
- 修复医学表达式过大问题
- 增加下拉选择框与日期选择框选择后事件，解决日期选择器选中后执行两边替换逻辑的问题
- 更新月经表达式 1 加载图片

## 1.2.2

`2022-01-04`

- 修复 pdf 分开打印问题
- 修复系统打印续打和区域打印不生效的问题
- 修复立即打印不生效问题
- 修复页眉中表格操作问题修复

## 1.2.1

`2022-01-04`

- 优化右键事件，减少 vue 挂载数据
- 直接打印参数完善
- 修改字体引入方式

## 1.2.0

`2022-01-04`

- 新增 pdf 打印方法
- 优化右键菜单续打和区域打印展示内容
- 修复校验码无法校验月份的问题
- 调整打印方向配置和批量打印逻辑
- 优化页眉表格线拖动，没编辑页眉不展示表格线
- 新增 copyEditor 接口
- 修复右键触发 pointer_down 的问题
- 优化续打遮罩表格线问题

## 1.1.4

`2021-12-30`

- 新增超过边界滚动选区功能
- 优化水平线实现逻辑
- 修复一倍状态下预览图片偏移问题
- 优化获取 blob 数组的调用和解决获取 blob 的问题

## 1.1.3

`2021-12-27`

- 优化医学表达式默认不展示时间框
- 优化下拉检索框获得焦点后不能删除文本域内容问题
- 优化打印逻辑

## 1.1.2

`2021-12-21`

- 修复编辑器配置传入错误

## 1.1.1

`2021-12-21`

- 新增退出编辑页眉页脚接口
- 新增移动端功能
- 修复删除选区开始为表格时删除后排版错乱问题
- 修复续打模式下整页不打印页眉页脚的问题
- 修复表格下方文字盖住表格线的问题
- 修复医学表达式模糊问题
- 优化打印方法位置
- 优化组装原始段落信息逻辑

## 1.1.0

`2021-12-17`

- 新增传递文件流按钮
- 修改文档刷新按钮
- 修改页眉页脚水平线显示隐藏控制接口
- 新版打印插件配置修改

## 1.0.65

`2021-12-16`

- 文本域替换图片接口更改
- 新增下拉框在未有匹配项时回车自动替换检索框内容
- 修复单元格复制未复制文本域问题
- 修复跨页表格内复选框点击不生效问题
- 修复跨页表格点击单元格报错问题

## 1.0.64

`2021-12-14`

- 新增浏览器打印回调
- 表格线粗细优化
- 优化续打模式点击页脚时全选页面
- 优化原始数据过大问题
- 修复页眉页脚在光标移动后不可编辑的问题
- 修复只读文本域内不允许插入文本域的问题
- 修复替换文本域时传入数字类型报错问题
- 修复部分点击单元格报错问题
- 新增自定义开始页码及页数接口
- 优化纯文本复制
- 修复替换元素到文本域对象接口问题
- 修复医学表达式在分组下还能打开的问题
- 调整右键插入表格最大行列数

## 1.0.63

`2021-12-09`

- 新增 config 配置项 font_family
- 修复区域打印从下网上选中问题
- 新增按住 ctrl 拖拽不删除原来选区功能
- 修复页眉页脚打印问题

## 1.0.62

`2021-12-08`

- 新增打印控制偏移参数

## 1.0.61

`2021-12-07`

- 更换复制和复制文本快捷键
- 新增区域打印功能
- 修复字体放大后表格线被遮挡的问题
- 修复空文本域打印时最小宽度不生效问题
- 修复 mac 系统快捷键问题
- 修复页眉页脚连续删除渲染异常问题
- 修改打印纸张大小参数配置

## 1.0.60

`2021-12-05`

- 打印还原

## 1.0.59

`2021-12-03`

- 新增文本域校验功能总开关
- 新增双下划线配置
- 修改页眉页脚遮盖后清晰度
- 修改默认字体配置，修改堆栈逻辑
- 修复快捷输入不准确的问题

## 1.0.58

`2021-12-02`

- 新增表格插入多行多列的接口
- 新增背景阴影配置项

## 1.0.57

`2021-12-01`

- 右键菜单中，将表格的插入行和列移入到表格操作中
- 修复常用语靠下位置不准确问题
- 修复快捷输入展示框折行混乱的问题
- 打印逻辑修改，防止顺序错乱，内容遗漏，根据类型控制校验规则体现

## 1.0.56

`2021-12-01`

- 新增 API 启用快捷输入功能
- 修复快捷输入不展示问题

## 1.0.55

`2021-12-01`

- 新增文本域最大宽度与最小宽度配置功能
- 修改文本域最大宽度和最小宽度属性
- 修复医学表达式改变大小后宽高不准确的问题

## 1.0.54

`2021-11-30`

- 新增文档信息可存储页眉页脚水平线展示配置
- 新增批量打印的方法
- 修改横向展示后页面大小不自适应的问题

## 1.0.53

`2021-11-29`

- 修复图片点击报错问题
- 修复模板数据加载报错的问题
- 修复滚动到文档最后，编辑时页面会自动滚动一下的问题

## 1.0.52

`2021-11-28`

- 依赖更新

## 1.0.51

`2021-11-28`

- 增加普通字体宽度，色值，加粗字体宽度，背景色的可配置功能。
- 修复了 shift 区域选择不生效的 bug。

## 1.0.50

`2021-11-25`

- 新增快捷输入弹框不可越界功能
- 增加复选框同组名成组功能。
- 增加配置项重置接口。
- 修复模态框拖出可视区域无法关闭的问题
- 修复只读文本域仍能够粘贴内容 BUG。
- 修复分组锁定后仍能点击设置复选框问题。
- 修复查找替换 tip 弹出框关闭后打不开的问题

## 1.0.49

`2021-11-24`

- 回车事件方向按钮事件禁用
- 增加编辑常用语弹窗
- 修复二级菜单被遮挡问题，调整层级
- 修改所有通过 id 获取 dom 方式，解决打开文件方法调用后内容加载到其他编辑器问题。

`2021-11-22`

- 增加获取搜索关键词的方法
- 增加根据表格 name 值获取表格数组的方法
- 增加获取光标位置处表格的方法
- 增加 keydownbefore 事件
- 增加横向纵向打印参数
- 增加中文输入开始和结束事件

## 1.0.48

`2021-11-19`

- 增加文本域只读时判断，不可通过接口再插入其他内容。
- 增加通过类型获取文本域数组接口。
- 修复表格内拖动图片报错的 bug

## 1.0.47

`2021-11-19`

- 增加右键菜单不兼容上版本
- 增加页眉页脚水平线显示隐藏控制。
- 优化段落居中逻辑，应用到每一行。

## 1.0.46

`2021-11-19`

- 增加 右键菜单增加配置
- 增加自定义多选框（新增、修改）
- 增加右键菜单文本域和多选框控制逻辑
- 修复只读文本域不能通过回退键删除只能通过选区删除的问题。
- 修复向空文本域中插入自定多选框未清空背景文本问题。
- 修复 Shift 键选区操作时，点击已选中区域会使选区消失问题。
- 修复插入非成组自定义多选框时不能设置禁用 BUG。

## 1.0.45

`2021-11-15`

- 初次上线稳定版本
