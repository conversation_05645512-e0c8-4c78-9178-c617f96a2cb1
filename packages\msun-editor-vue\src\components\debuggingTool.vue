<template>
  <modal
    :show="show"
    :width="modal_width"
    :bodyStyle="{ height: '305px', overflow: 'auto' }"
    :title="title"
    @cancel="cancel"
    class="editor-debugging-modal"
  >
    <div class="prop">
      <div class="prop-type">
        <strong>版本信息</strong>
        <hr class="hr-style" />
      </div>
      <div @click="versionShow" class="editor-debugging-content">
        <label>编辑器版本(vue)： </label>
        {{ versionInfo.version }}
        &nbsp;&nbsp;&nbsp;
        <template v-if="versionInfo.realVersion">
          <label> 真实版本： </label>
          {{ versionInfo.realVersion }}
        </template>
      </div>

      <template v-for="(item, index) in debugButtons">
        <div class="prop-type" :key="item.title">
          <strong>{{ item.title }}</strong>
          <hr class="hr-style" />
        </div>
        <div class="editor-debugging-content" :key="item.title + index">
          <a-button
            class="editor-debugging-content-button"
            v-for="(btn, idx) in item.btnList"
            :key="btn.name + idx"
            size="small"
            @click="btn.func"
            >{{ btn.name }}</a-button
          >
        </div>
      </template>
      <a-modal
        :visible="showDebugInputModal"
        :width="inputModalWidth"
        :bodyStyle="{ height: inputModalHeight + 'px', overflow: 'auto' }"
        :title="debugInputModalInfo.title"
        :mask="false"
        :zIndex="9999"
        @cancel="cancelChildModal"
        @ok="handleChildModalOk"
      >
        <a-textarea
          :placeholder="debugInputModalInfo.placeholder"
          v-model="inputValue"
          :rows="inputModalRows"
        >
        </a-textarea>
      </a-modal>
    </div>
    <div slot="editor-modal-footer" class="parent-footer"></div>
  </modal>
</template>

<script>
import modal from "./common/modal.vue";
export default {
  name: "debuggingTool",
  components: {
    modal,
  },
  data() {
    return {
      disabled: false,
      modal_width: 440,
      inputModalWidth: 440,
      inputModalHeight: "",
      title: "调试工具",
      inputValue: "",
      inputModalRows: 3,
    };
  },
  mounted() {},
  watch: {
    showDebugInputModal(val) {
      if (val) {
        this.inputValue = "";
        const { value, width, height } = this.debugInputModalInfo;
        value && (this.inputValue = value);
        width && (this.inputModalWidth = width);
        if (height) {
          this.inputModalHeight = height;
          this.inputModalRows = height / 23;
        }
      }
    },
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
    versionInfo: {
      type: Object,
      default: () => {},
    },
    debugButtons: {
      type: Array,
      default: () => [],
    },
    showDebugInputModal: {
      type: Boolean,
      default: false,
    },
    debugInputModalInfo: {
      type: Object,
      default: () => {},
    },
  },
  methods: {
    cancelChildModal() {
      this.$emit("cancelChildModal");
    },
    handleChildModalOk() {
      this.$emit(
        "handleChildModalOk",
        this.debugInputModalInfo.key,
        this.inputValue
      );
    },
    cancel() {
      this.$emit("cancel");
    },
    versionShow() {
      alert(
        "msun-editor当前版本为：" +
          this.versionInfo.dependencies["msun-editor"] +
          "\n" +
          "msun-editor-vue当前版本为：" +
          this.versionInfo.version
      );
    },
  },
};
</script>
<style scoped>
.editor-debugging-content {
  margin-left: 8px;
}
.editor-debugging-content-button {
  margin-left: 8px;
  margin-top: 5px;
}
.editor-debugging-modal /deep/ .ant-modal-wrap {
  pointer-events: none;
}
</style>
