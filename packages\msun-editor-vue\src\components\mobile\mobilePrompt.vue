<template>
  <div v-show="show" class="msun-editor-prompt-container">
    <div
      class="msun-editor-prompt-box"
      v-for="item in options"
      :key="item.code"
      @click="handleClick(item)"
    >
      {{ item.value }}
    </div>
  </div>
</template>

<script>
export default {
  name: "mobilePrompt",
  data() {
    return {};
  },
  props: {
    show: {
      default: false,
      type: Boolean,
    },
    options: {
      default: () => [],
      type: Array,
    },
  },
  watch: {},
  methods: {
    handleClick(item) {
      console.log(`Code: ${item.code}, value: ${item.value}`);
    },
  },
};
</script>
<style lang="less" scoped>
.msun-editor-prompt-container {
  display: flex;
  padding: 0 10px;
  position: absolute;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 4px 4px;
  top: 0;
  left: 0;
  line-height: 80px;
  text-align: center;
  .msun-editor-prompt-box {
    padding: 2px 4px;
    margin: 0 6px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #ccc;
    text-align: center;
    line-height: 16px;
  }
}
</style>
