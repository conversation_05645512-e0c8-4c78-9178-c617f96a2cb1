<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="formulaType[type]"
    @submit="submit"
    @cancel="cancel"
  >
    <div slot="title" class="title_medical_formula">
      <div>{{ formulaType[type] }}</div>
    </div>
    <div class="formula-list">
      <div class="formula-type">
        <component :is="formula[type]" :meta="meta"></component>
      </div>
    </div>
  </modal>
</template>
<script>
import toothBitmap from "./formula/toothBitmap.vue";
import normal from "./formula/normal.vue";
import menstruation1 from "./formula/menstruation1.vue";
import menstruation2 from "./formula/menstruation2.vue";
import menstruation3 from "./formula/menstruation3.vue";
import menstruation4 from "./formula/menstruation4.vue";
import pupil from "./formula/pupil.vue";
import optical from "./formula/optical.vue";
import fetalHeart from "./formula/fetalHeart.vue";
import PDTooth from "./formula/PDTooth.vue";
import diseasedLowerTooth from "./formula/diseasedLowerTooth.vue";
import diseasedUpperTooth from "./formula/diseasedUpperTooth.vue";
import permanentTeethBitmap from "./formula/permanentTeethBitmap.vue";
import primaryTeethBitmap from "./formula/primaryTeethBitmap.vue";
import modal from "./common/modal.vue";
export default {
  name: "medicalFormula",
  components: {
    modal,
    normal,
    menstruation1,
    menstruation2,
    menstruation3,
    menstruation4,
    pupil,
    optical,
    fetalHeart,
    PDTooth,
    diseasedLowerTooth,
    diseasedUpperTooth,
    toothBitmap,
    permanentTeethBitmap,
    primaryTeethBitmap,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    meta: {
      type: Object,
      default: () => {},
    },
    type: {
      type: Number,
      default: -1,
    },
  },
  data() {
    return {
      masks: true,
      modal_width: 524,
      formula: [
        "normal",
        "menstruation1",
        "menstruation2",
        "menstruation3",
        "menstruation4",
        "pupil",
        "optical",
        "fetalHeart",
        "PDTooth",
        "diseasedLowerTooth",
        "diseasedUpperTooth",
        "toothBitmap",
        "permanentTeethBitmap",
        "primaryTeethBitmap",
      ],
      formulaType: [
        "通用公式输入",
        "月经史公式输入",
        "月经史公式输入",
        "月经史公式输入",
        "月经史公式输入",
        "瞳孔图",
        "光定位图",
        "胎心图",
        "PD牙位图",
        "病变下牙牙位图",
        "病变上牙牙位图",
        "十字牙位图",
        "恒牙牙位图",
        "乳牙牙位图",
      ],
      formulaImage: null,
    };
  },

  methods: {
    submit() {
      this.$emit("submit", this.meta);
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>
<style scoped>
.title_medical_formula {
  height: 23px;
  display: flex;
}
.formula-list {
  width: 500px;
  user-select: none;
  -moz-user-select: none;
}
.formula-type {
  display: flex;
  cursor: pointer;
}
</style>
