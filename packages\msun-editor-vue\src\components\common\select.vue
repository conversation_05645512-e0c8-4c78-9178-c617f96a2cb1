<template>
  <div class="editor-select-x" :style="{ width: width + 'px' }">
    <a-input v-model="inputOption" />
    <div class="editor-select-bottom-x">
      <div
        v-for="(item, i) in data"
        :key="i"
        :style="{ fontFamily: item.option }"
        :class="index === i ? 'editor-selected-x' : 'editor-unselected-x'"
        :ref="item.option"
        @click="changeStyle(i)"
      >
        {{ item.option }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "select-modal",
  data() {
    return {
      inputOption: "",
      inputValue: null,
      index: 0,
      visible: false,
      masks: true,
    };
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    width: {
      type: Number,
      default: 80,
    },
    initData: {
      type: Object,
      default: () => {},
    },
  },
  watch: {
    show(e) {
      this.visible = e;
    },
    inputOption(e) {
      this.inputChange(e);
    },
    // initData: {
    //   handler(e) {
    //     this.inputOption = e.option;
    //     this.inputValue = e.value;
    //   },
    //   deep: true,
    // },
    immediate: true,
  },
  mounted() {
    this.inputOption = this.initData.option;
    this.inputValue = this.initData.value;
  },
  methods: {
    changeStyle(i) {
      this.index = i;
      this.inputOption = this.data[i].option;
      this.inputValue = this.data[i].value;
      this.$nextTick(() => {
        this.$refs[this.inputOption][0].scrollIntoView();
      });
    },
    inputChange(e) {
      for (let i = 0; i < this.data.length; i++) {
        if (e === this.data[i].option) {
          this.changeStyle(i);
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.editor-select-x {
  background-color: rgb(255, 255, 255);
}
.editor-select-x /deep/ .ant-input {
  border-radius: 0;
  padding: 0 0 0 5px;
  line-height: 21px;
  height: 21px;
  color: #000;
}
.editor-select-bottom-x {
  margin-top: 2px;
  height: 65px;
  border: 1px solid rgb(217, 217, 217);
  overflow-y: auto;
  overflow-x: hidden;
  background-color: rgb(255, 255, 255);
}
.editor-selected-x {
  background-color: rgb(0, 120, 215);
  margin-right: 3px;
  padding-left: 5px;
  color: rgb(255, 255, 255);
  cursor: pointer;
}
.editor-unselected-x {
  background-color: rgb(255, 255, 255);
  padding-left: 5px;
  cursor: pointer;
}
</style>
