<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="title"
    @cancel="cancel"
    @submit="submit"
  >
    <div class="prop">
      <div class="prop-div">
        <label>按钮文字：</label>

        <a-input
          class="prop-input"
          type="text"
          placeholder="按钮文字"
          v-model="text"
        />
      </div>
      <div class="prop-div">
        <label> 按钮颜色：</label>
        <colorPicker
          v-model="color"
          v-on:change="changeColor"
          class="color-picker"
        />
      </div>
      <div class="prop-div">
        <label>按钮宽度：</label>

        <a-input
          class="prop-input"
          type="text"
          placeholder="按钮宽度"
          v-model="width"
          @change="changeWidth"
        />
      </div>
      <div class="prop-div">
        <label> 按钮高度：</label>
        <a-input
          class="prop-input"
          id="field_name"
          type="text"
          placeholder="按钮高度"
          v-model="height"
          @change="changeHeight"
        />
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../components/common/modal.vue";
export default {
  name: "buttonPopup",
  components: {
    modal,
  },
  data() {
    return {
      modal_width: 524,
      title: "按钮设置",
      text: "",
      width: null,
      height: null,
      color: "rgb(78,169,252)",
    };
  },
  mounted() {},
  beforeDestroy() {},
  watch: {
    show: {
      handler(val) {
        if (val) {
          const buttonInfo = this.editor.editor._curButtonInfo;
          this.text = buttonInfo.value;
          this.width = buttonInfo.width;
          this.height = buttonInfo.height;
          this.color = buttonInfo.color;
        }
      },
    },
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    editorId: {
      type: String,
      default: "",
    },
  },
  methods: {
    //改变字体颜色
    changeColor(e) {
      this.color = e;
    },
    changeWidth() {
      if (this.width.match(/\d+/g)) {
        this.width = this.width.match(/\d+/g).join("");
      } else {
        this.width = null;
      }
    },
    changeHeight() {
      if (this.height.match(/\d+/g)) {
        this.height = this.height.match(/\d+/g).join("");
      } else {
        this.height = null;
      }
    },
    submit() {
      const params = {
        text: this.text,
        width: this.width,
        height: this.height,
        color: this.color,
      };
      this.$emit("submit", params);
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>
<style scoped>
.color-picker {
  top: 3px;
  cursor: pointer;
}
</style>
