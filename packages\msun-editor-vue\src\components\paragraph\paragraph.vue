<template>
  <modal
    :title="title"
    :show="isShowParaAttrModal"
    @cancel="cancel"
    @submit="submit"
    :width="modal_width"
  >
    <div class="prop" v-if="!isShowListModal">
      <div class="prop_align"><strong>对齐</strong></div>
      <div class="prop_align">
        <label>对齐方式：</label>
        <div class="border_css">
          <div
            class="align_icon_paragraph"
            v-for="(item, index) in icons"
            :key="index"
            @click="set_para_info(item.value, index)"
            :title="item.name"
            :class="{
              active_paragraph:
                item.value === paragraph_prop_info.align ||
                (item.value === paragraph_prop_info.vertical_align &&
                  paragraph_prop_info.vertical_align !== 'center') ||
                (paragraph_prop_info.vertical_align === 'center' &&
                  item.value === 'middle'),
            }"
          >
            <iconCommon :icon="item.icon"></iconCommon>
          </div>
        </div>
      </div>
      <div class="hr_div_paragraph">
        <hr class="hr_line" />
      </div>
      <div class="prop_align"><strong>缩进</strong></div>
      <div class="prop-div">
        <label>首行缩进：</label>
        <a-input-number
          size="small"
          :min="0"
          :max="32"
          :default-value="0"
          class="prop-input_num"
          v-model="paragraph_prop_info.indentation"
        />
        <span>字符</span>
      </div>
      <!-- <div class="prop-div" v-if="paragraph_prop_info.align === 'docuAlign'">
        <label>标题长度：</label>
        <a-input-number
          size="small"
          :min="0"
          :max="32"
          :default-value="0"
          class="prop-input_num"
          v-model="paragraph_prop_info.title_length"
        />
        <span>字符</span>
      </div> -->
      <div class="hr_div_paragraph">
        <hr class="hr_line" />
      </div>
      <div class="prop_align"><strong>间距</strong></div>
      <div class="prop-div">
        <label>段前间距：</label>
        <a-input-number
          size="small"
          :min="0"
          :max="6"
          :default-value="0"
          class="prop-input_num"
          v-model="paragraph_prop_info.before_paragraph_spacing"
        />
        <span>行</span>
      </div>
      <!-- <div class="prop-div">
        <label>段后间距：</label>
        <a-input-number
          size="small"
          :min="0"
          :max="6"
          :default-value="0"
          class="prop-input_num"
          v-model="paragraph_prop_info.after_paragraph_spacing"
        />
        <span>行</span>
      </div> -->
      <div class="prop-div">
        <label>行间距：</label>
        <a-input-number
          size="small"
          :min="0"
          :max="6"
          :default-value="1.4"
          class="prop-input_num"
          v-model="paragraph_prop_info.row_ratio"
        />
        <span>倍</span>
      </div>
    </div>
    <div class="prop" v-if="isShowListModal">
      <div class="prop-div">
        <label>列表类型：</label>
        <a-select
          class="prop-select"
          v-model="is_order"
          dropdownClassName="xeditor-input-up"
          @change="choosedListType"
        >
          <a-select-option
            v-for="(item, index) in list_type"
            :value="item.value"
            :key="index"
            >{{ item.name }}
          </a-select-option>
        </a-select>
      </div>
      <div class="prop-div" v-if="paragraph_prop_info.isOrder">
        <label>列表图标样式：</label>
        <a-select
          class="prop-select"
          v-model="paragraph_prop_info.listNumStyle"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option
            v-for="(item, index) in order_list_symbol"
            :value="item.value"
            :key="index"
            >{{ item.name }}
          </a-select-option>
        </a-select>
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../common/modal.vue";
import iconCommon from "../common/iconCommon.vue";
// import BUS from "@/assets/js/eventBus";
export default {
  name: "paragraph",
  components: {
    modal,
    iconCommon,
  },
  props: {
    isShowParaAttrModal: {
      type: Boolean,
      default: false,
    },
    // paragraph_prop: {
    //   type: Object,
    //   default: () => {},
    // },
    editorId: {
      type: String,
      default: "",
    },
    isShowListModal: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    // BUS.$on("editor_" + this.editorId, ({ paragraph_prop }) => {
    //   if (paragraph_prop) this.paragraph_prop = paragraph_prop;
    // });
  },
  watch: {
    // 监听展示段落属性弹窗，将对应的属性值赋给弹窗表单
    isShowParaAttrModal: {
      handler(val) {
        if (val) {
          for (const key in this.paragraph_prop_info) {
            if (key === "isOrder") {
              this.is_order = this.paragraph_prop[key] + 0;
            }
            this.paragraph_prop_info[key] = this.paragraph_prop[key];
          }
        }
      },
    },
    // 监听弹窗类型 修改弹窗标题
    isShowListModal: {
      handler(val) {
        this.title = val ? "列表属性" : this.title;
      },
    },
  },
  data() {
    return {
      title: "段落属性",
      modal_width: 540,
      icons: [
        { name: "左对齐", value: "left", icon: "icon-ziyuan" },
        { name: "居中对齐", value: "center", icon: "icon-juzhongduiqi" },
        { name: "右对齐", value: "right", icon: "icon-youduiqi" },
        { name: "分散对齐", value: "dispersed", icon: "icon-ziyuan1" },
        { name: "字符对齐", value: "docuAlign", icon: "icon-docuAlign" },
        { name: "上对齐", value: "top", icon: "icon-shangduiqi" },
        { name: "上下中对齐", value: "middle", icon: "icon-shangxiajuzhong" },
        { name: "下对齐", value: "bottom", icon: "icon-Q-xiaduiqi" },
      ],
      // 有序无序列表
      list_type: [
        { name: "无序列表 ", value: 0 },
        { name: "有序列表 ", value: 1 },
      ],
      // 有序列表图标类型
      order_list_symbol: [
        { name: "数字类型 ", value: "number" },
        { name: "汉字类型 ", value: "chinese" },
      ],
      // 绑定是否有序的变量
      is_order: 0,
      // 缩进量
      indentation: 0,
      // 表单提交的修改信息
      paragraph_prop_info: {
        align: "",
        vertical_align: "",
        indentation: 0,
        before_paragraph_spacing: 0,
        after_paragraph_spacing: 0,
        row_ratio: 1.4,
        isOrder: false,
        listNumStyle: "number",
      },
    };
  },
  methods: {
    choosedListType() {
      this.paragraph_prop_info.isOrder = this.is_order ? true : false;
    },
    cancel() {
      this.$emit("cancel");
    },
    submit() {
      for (const key in this.paragraph_prop_info) {
        if (Object.hasOwnProperty.call(this.paragraph_prop_info, key)) {
          if (
            this.paragraph_prop[key] === this.paragraph_prop_info[key] &&
            !this.isShowListModal
          ) {
            this.paragraph_prop_info[key] = undefined;
          }
        }
      }
      this.$emit("submit", this.paragraph_prop_info);
    },
    set_para_info(align_data, index) {
      if (index > 4) {
        this.paragraph_prop_info.vertical_align = align_data;
      } else {
        this.paragraph_prop_info.align = align_data;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.align_icon_paragraph {
  border-radius: 5px;
  -webkit-font-smoothing: antialiased;
}
.align_icon_paragraph:hover {
  -webkit-font-smoothing: antialiased;
  background-color: rgb(220, 225, 224);
}
.active_paragraph {
  -webkit-font-smoothing: antialiased;
  background-color: rgb(64, 169, 255);
}
.hr_div_paragraph {
  width: calc(80%);
  margin-left: 20px;
  margin-top: 10px;
  .hr_line {
    background-color: rgb(219, 219, 219);
    border: none;
    height: 1px;
  }
}
.xeditor-input-up {
  z-index: 99999;
}
</style>
