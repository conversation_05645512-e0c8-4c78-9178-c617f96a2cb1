const histories = [
  {
    createUserId: null,
    createUserName: "张鹏飞",
    hisHospitalId: null,
    hisHospitalName: null,
    hisOrgId: null,
    hisOrgName: null,
    reportVersionNumber: null,
    patchNumber: null,
    hisCreateTime: "2023-03-10 16:58:12",
    hisUpdateTime: "2023-03-10 16:58:31",
    logTime: "2023-03-10 16:58:31",
    sort: null,
    order: null,
    rn: null,
    pageSize: 0,
    pageNum: 1,
    userIp: null,
    reportFileName: "编辑器数据回显",
    reportGrf: {
      header: [
        {
          id: "para-60e59b9a",
          type: "p",
          align: "center",
          deepNum: 0,
          islist: false,
          isOrder: false,
          indentation: 0,
          dispersed_align: false,
          before_paragraph_spacing: 0,
          restart_list_index: false,
          row_ratio: 1.6,
          page_break: false,
          children: [
            { type: "text", value: "", font_id: "font-default-d1ff9648" },
          ],
          title_length: 0,
          content_padding_left: 0,
          vertical_align: "top",
          level: 0,
          listNumStyle: "number",
        },
      ],
      footer: [
        {
          id: "para-c79c5ea3",
          type: "p",
          align: "center",
          deepNum: 0,
          islist: false,
          isOrder: false,
          indentation: 0,
          dispersed_align: false,
          before_paragraph_spacing: 0,
          restart_list_index: false,
          row_ratio: 1.6,
          page_break: false,
          children: [
            { type: "text", value: "", font_id: "font-default-d1ff9648" },
          ],
          title_length: 0,
          content_padding_left: 0,
          vertical_align: "top",
          level: 0,
          listNumStyle: "number",
        },
      ],
      content: [
        {
          id: "para-bdd6bb0e",
          type: "p",
          align: "left",
          deepNum: 0,
          islist: false,
          isOrder: false,
          indentation: 0,
          dispersed_align: false,
          before_paragraph_spacing: 0,
          restart_list_index: false,
          row_ratio: 1.6,
          page_break: false,
          children: [
            {
              type: "widget",
              field_id: null,
              disabled: 0,
              selected: false,
              field_position: "normal",
              widgetType: "caliper",
              height: 50,
              params: { num: 10, spacing: 15 },
              border: "solid",
              font_id: "font-a3ab4671",
            },
            {
              type: "text",
              value: "阿达萨达阿萨德",
              font_id: "font-default-d1ff9648",
            },
          ],
          title_length: 0,
          content_padding_left: 0,
          vertical_align: "top",
          level: 0,
          listNumStyle: "number",
        },
      ],
      groups: [],
      fontMap: {
        "font-default-d1ff9648": {
          id: "font-default-d1ff9648",
          height: 16,
          family: "宋体",
          bold: false,
          italic: false,
          underline: false,
          dblUnderLine: false,
          strikethrough: false,
          script: 3,
          color: "#000",
          bgColor: null,
          highLight: null,
          characterSpacing: 0,
        },
        "font-a3ab4671": {
          id: "font-a3ab4671",
          height: 12,
          family: "宋体",
          bold: false,
          italic: false,
          underline: false,
          dblUnderLine: false,
          strikethrough: false,
          script: 3,
          color: "#000",
          bgColor: null,
          highLight: null,
          characterSpacing: 0,
        },
      },
      imageSrcObj: {},
      bodyText: "",
      shapes: [],
      waterMarks: [],
      config: {
        page_info: {
          default_font_style: {
            family: "宋体",
            height: 16,
            bold: false,
            italic: false,
            underline: false,
            strikethrough: false,
            dblUnderLine: false,
            script: 3,
            characterSpacing: 0,
            color: "#000",
            bgColor: null,
            highLight: null,
          },
          page_size_type: "A4",
          page_direction: "vertical",
          row_ratio: 1.6,
          editor_padding_top: 20,
          page_margin_bottom: 20,
          page_padding_left: 80,
          page_padding_top: 80,
          page_padding_right: 80,
          page_padding_bottom: 80,
          header_margin_top: 40,
          footer_margin_bottom: 42.5,
          content_margin_header: 5,
          content_margin_footer: 5,
          table_padding_horizontal: 5,
          page_size: { width: 793, height: 1121 },
        },
        direction: "vertical",
        header_horizontal: true,
        footer_horizontal: false,
        rowLineType: 0,
      },
      meta: {
        versionList: [
          { version: "2.32.18", time: 1703043312194 },
          { version: "2.32.19", time: 1703052961614 },
          { version: "10.0.16", time: 1705459170393 },
        ],
        fieldSymbolWidth: 0,
        newDocuAlign: true,
        useNewToggleSymbol: true,
      },
      customMeta: {},
      newDesignerType: "1",
      floatModelRaws: [],
    },

    reportMsun: null,
    reportHtml: null,
    reportFileId: null,
    reportMsunBackUp: null,
    logTimeStartTime: null,
    logTimeEndTime: null,
    reportFileLogId: "dB7TAaaLpU333333ArRu891hh3rK05cRID3VdD",
    reportMainId: null,
    reportFileNameNow: null,
    reportName: null,
    memo: null,
    reportState: null,
    reportDataSetList: null,
    logUserIp: null,
    page: null,
    rows: null,
  },
];
export default histories;
