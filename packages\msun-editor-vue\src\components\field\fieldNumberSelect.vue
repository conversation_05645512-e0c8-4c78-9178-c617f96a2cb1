<template>
  <div
    :style="{
      position: 'absolute',
      width: '0px',
      left: position.left + 'px',
      top: position.top + 'px',
      opacity: 0.9,
    }"
  >
    <a-dropdown v-if="visible" class="custom-dropdown">
      <a-menu class="custom-dropdown">
        <a-row :gutter="8">
          <a-col :span="8" v-for="number in numbers" :key="number">
            <a-button
              :class="['number-button']"
              @click="handleButtonClick(number)"
            >
              {{ number }}
            </a-button>
          </a-col>
        </a-row>
        <a-row :gutter="8">
          <a-col :span="8">
            <a-button class="number-button" @click="handleButtonClick('0')"
              >0</a-button
            >
          </a-col>
          <a-col :span="8">
            <a-button class="number-button" @click="handleButtonClick('.')"
              >.</a-button
            >
          </a-col>
          <a-col :span="8">
            <a-button class="delete-button" @click="handleDeleteClick"
              >del</a-button
            >
          </a-col>
        </a-row>
      </a-menu>
      <!-- <a-button class="selected-button">{{ formatSelectedNumbers() }}</a-button> -->
    </a-dropdown>
  </div>
</template>

<script>
export default {
  name: "fieldNumberSelect",
  data() {
    return {
      numbers: [1, 2, 3, 4, 5, 6, 7, 8, 9],
    };
  },
  props: {
    position: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {},
  computed: {},
  watch: {},
  methods: {
    handleButtonClick(number) {
      const editor = this.editor.editor;
      editor.insertText(number);
      editor.focus();
    },

    handleDeleteClick() {
      const field = this.editor.editor.selection.getFocusField();
      if (field.text) {
        this.editor.editor.delete_backward();
        this.editor.editor.focus();
      }
    },
  },
};
</script>
<style scoped>
.number-button {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
  margin-bottom: 5px;
  margin-left: 4px;
  padding: 0;
  width: 40px;
}

.delete-button {
  width: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
  font-size: 12px;
  padding: 0;
  margin-left: 4px;
}

.selected-button {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
  cursor: pointer;
  padding: 0;
}
.custom-dropdown {
  width: 160px; /* 设置下拉框的固定宽度为 200 像素 */
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
  padding-top: 5px;
}
</style>
