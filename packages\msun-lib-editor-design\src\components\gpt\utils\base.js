/**
 * 空操作函数
 */
export function noop() {}

/**
 * 截流函数
 * @param {Function} fn - 被截流的函数
 * @param {number} wait - 毫秒数
 * @param {boolean}
 * @returns
 */
export function throttle(fn, wait = 200, immediate = true) {
  let prev = performance.now();
  return function() {
    const ctx = this;
    const args = arguments;
    if (immediate === true) {
      fn.apply(ctx, args);
      prev = performance.now();
      immediate = false;
      return;
    }
    const now = performance.now();
    if (now - prev >= wait) {
      fn.apply(ctx, args);
      prev = performance.now();
    }
  };
}

/**
 * 生成UUID
 * @param {string} [prefix=''] - uuid的前缀
 * @return {string}
 */
export function generateUUID(prefix = '') {
  const id = 'xxxxxxxx'.replace(/[xy]/g, c => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
  return prefix + id;
}

/**
 * 从点击的节点开始，向上寻找指定class的元素
 * @param node 当前节点
 * @param str 类名
 */
export function getSomeClassParent(node, str) {
  let tmpNode = node;
  while (!tmpNode.classList.value.includes(str)) {
    tmpNode = tmpNode.parentNode;
    if (!tmpNode || tmpNode.tagName.toLowerCase() === 'body') { return null; }
  }
  return tmpNode;
}

/**
 * 从点击的节点开始，向上寻找指定name的元素
 * @param node 当前节点
 * @param name
 */
export function getSomeNameParent(node, name) {
  let tmpNode = node;
  while (!tmpNode.getAttribute('name')) {
    tmpNode = tmpNode.parentNode;
    if (!tmpNode || tmpNode.tagName.toLowerCase() === 'body') { return null; }
  }
  if (tmpNode && tmpNode.getAttribute('name') !== name) { return null; }

  return tmpNode;
}

/**
 * 根据某个key获取某个值在对象数组中的索引
 * @param arr 对象的数组
 * @param value 被对比的值
 * @param key 数组每项的哪个key的值进行对比
 */
export function getObjArrayIndex(arr, value, key = 'value') {
  let index = -1;
  arr.some((item, ind) => {
    if (item[key] === value) {
      index = ind;
      return true;
    }
  });
  return index;
}

/**
 * 将对象的数组按照某个key排序
 * @param {*} arr 对象的数组
 * @param {*} key 排序的key
 * @param {*} order  1 升序，默认升序；-1 降序
 */
export function sortArrByKey (arr, key, order = 1) {
  arr.sort((a, b) => {
    if (a[key] > b[key]) {
      return order;
    } else if (a[key] < b[key]) {
      return order * -1;
    } else {
      return 0;
    }
  });
  return arr;
}

/**
 * 深拷贝
 */
export function deepClone(source) {
  // 这里没考虑方法，方法还是同一个引用
  const targetObj = {};
  const targetArr = [];
  if ((source ?? 0) === 0) { return source; }
  if (source.constructor === Array) {
    source.forEach((item, index) => {
      if (item && typeof item === 'object') {
        targetArr[index] = deepClone(item);
      } else {
        targetArr[index] = item;
      }
    });
    return targetArr;
  } else {
    Object.keys(source).forEach(keys => {
      if (source[keys] && typeof source[keys] === 'object') {
        targetObj[String(keys)] = deepClone(source[keys]);
      } else {
        targetObj[keys] = source[keys];
      }
    });
    return targetObj;
  }
}

export function parseTime(time, cFormat = '{y}/{m}/{d} {h}:{i}:{s}') {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat;
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        time = parseInt(time);
      } else {
        time = time.replace(new RegExp(/-/gm), '/');
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{([ymdhisa])}/g, (result, key) => {
    const value = formatObj[key];
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value]; }
    return value.toString().padStart(2, '0');
  });
  return time_str;
}
