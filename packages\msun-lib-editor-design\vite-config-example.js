import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue2";
import path from "path";

/**
 * 此配置用于构建库生产代码
 */
const libConfig = defineConfig({
  plugins: [
    vue(),
    // {
    //   name: 'replace-html-name-in-code',
    //   apply: 'build',
    //   generateBundle(options, bundle) {
    //     let key = Object.keys(bundle).find(item => item.startsWith('assets/main') && item.endsWith('.js'));
    //     const js = bundle[key];
    //     js.code = js.code.replaceAll('import("../', 'import("./');
    //   }
    // },
  ],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
    extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
  },
  server: {
    host: "0.0.0.0",
    port: 8999,
    // open: "./example",
  },
  base: "./",
  build: {
    //minify: false,
    outDir: "exampleDist",
    target: "es2015",
    commonjsOptions: {
      transformMixedEsModules: true,
      include: [/node_modules/, /msun-sheet-vue/],
    },
    // rollupOptions: {
    // output: {
    //   chunkFileNames: (chunkInfo) => {
    //     let ids = chunkInfo.facadeModuleId?.split('/').slice(-3) || [];
    //     if (ids[0] === 'example' && ids[1] === 'examples') {
    //       return 'assets/[name].js'; // 不带哈希值
    //     }
    //     return 'assets/[name]-[hash].js'; // 带哈希值
    //   },
    //   assetFileNames: 'assets/[name]-[hash].[ext]'
    // },
    // input: {
    //   main: path.resolve(__dirname, "./example.html"),
    // },
    // },
  },
});

export default defineConfig(libConfig);
