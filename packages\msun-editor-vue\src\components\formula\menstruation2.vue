<template>
  <div class="menstruation2">
    <div class="editor_row_menstrual2">
      <div class="row-left">
        <div>初潮年龄</div>
        <a-input
          type="text"
          class="editor_input_menstrual2"
          v-model="meta.params[0]"
        />
      </div>
      <div class="editor_vertical_menstrual2"></div>
      <div class="row-right">
        <div>经期（天）</div>
        <a-input
          type="text"
          class="editor_input_menstrual2"
          v-model="meta.params[1]"
        />
      </div>
    </div>
    <div class="editor_horizontal_menstrual2"></div>
    <div class="editor_row_menstrual2">
      <div class="row-left-down">
        <div>末次月经/绝经年龄</div>
        <a-input
          type="text"
          class="editor_input_menstrual2"
          v-model="meta.params[2]"
        />
      </div>
      <div class="editor_vertical_menstrual2"></div>
      <div class="row-right-down">
        <div>周期（天）</div>
        <a-input
          type="text"
          class="editor_input_menstrual2"
          v-model="meta.params[3]"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "menstruation2",
  components: {},
  props: {
    meta: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>
<style scoped>
.menstruation2 {
  width: 100%;
  height: 100%;
}
.editor_horizontal_menstrual2 {
  height: 5px;
  width: 100%;
  background-color: black;
}
.editor_vertical_menstrual2 {
  height: 80px;
  width: 5px;
  background-color: rgb(85, 81, 81);
}
.editor_row_menstrual2 {
  display: flex;
  text-align: center;
}
.row-left {
  width: 242.5px;
}
.row-right {
  width: 242.5px;
}
.row-left-down,
.row-right-down {
  width: 242.5px;
  margin-top: 12px;
}

.editor_input_menstrual2 {
  margin-top: 10px;
  width: 170px;
}
</style>
