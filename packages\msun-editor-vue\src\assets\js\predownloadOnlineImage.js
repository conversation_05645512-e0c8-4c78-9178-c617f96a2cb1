/** @typedef {ReturnType<import('msun-sheet').WorkSpace['printView']>} WorkSpacePrint  */

const canvas = document.createElement('canvas');
const ctx = canvas.getContext('2d');

async function httpSrcToBase64(src) {
  // 先尝试直接用fetch获取图片，如果失败则用canvas转换
  try {

    const res = await fetch(src);
    const blob = await res.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });

  } catch (_e) {

    return new Promise((resolve, reject) => {
      const img = new Image();
      // img.crossOrigin = 'Anonymous';
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        resolve(canvas.toDataURL());
      };
      img.onerror = reject;
      img.src = src;
    });

  }
}

export async function predownloadOnlineImageForPrintJson(printJson) {
  const predownloadedPageData = await predownloadOnlineImage(printJson.pageData);
  return {
    ...printJson,
    pageData: predownloadedPageData
  };
}

/**
 * 预下载在线图片
 * @typedef {ReturnType<WorkSpacePrint['printDataTool']['translateViewData']>} PageData
 * @param {PageData} pageData
 * @returns {Promise<PageData>}
 */
async function predownloadOnlineImage(pageData) {

  const promises = [];

  for (const page of pageData) {
    for (const e of page) {
      if (e.type === 'image' || e.type === 'unloadedImage') {

        const src = e.src;
        // 被 imageMap 处理后可能会 src 为 undefined 的情况，所以要额外判空
        if (src && src.startsWith('http')) {
          promises.push(
            httpSrcToBase64(src)
              .then(base64 => {
                e.src = base64;
              })
              .catch(() => {
                console.error('预下载图片失败:', src);
              })
          );
        }

      }
    }
  }

  if (promises.length) {
    console.log(`有${promises.length}张在线图片需要加载`);
    console.time('在线图片加载耗时');

    await Promise.all(promises);

    console.timeEnd('在线图片加载耗时');
  }

  return pageData;
}
