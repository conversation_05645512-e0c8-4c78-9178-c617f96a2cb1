import Paragraph from "./Paragraph";
import Table from "./Table";
import Image from "./Image";
import Editor from "./Editor";
import { Config } from "./Config";
import Cell from "./Cell";
import XField from "./XField";
import PathUtils from "./Path";
import { isParagraph, uuid } from "./Utils";
import { SkipMode, TABLE_NAME_FOR_PACS_IMAGE_LAYOUT } from "./Constant";
import { isImage } from "./Helper";

class ImageTable extends Table {
  imageList: any[] = [];
  maxTableHeight: number = 1000;
  recordWidth: number = 100;
  recordHeight: number = 100;
  needSerialNum: boolean | undefined;

  isImageTable: true = true;

  constructor (
    editor: Editor,
    id: string,
    group_id: string | null,
    col_size: number[],
    row_size: number[],
    min_row_size: number[],
    parent: Cell,
    left: number,
    right: number,
    top: number,
    newPage: boolean,
    skipMode:SkipMode
  ) {
    super(editor, id, group_id, col_size, row_size, min_row_size, parent, left, right, top, newPage,skipMode);
    this.isImageTable = true;
    this.handleLineOpacity();
  }

  static createTableOrImageTable (row_num: number, col_num: number, isImageTable: boolean, editor: Editor, newPage: boolean): undefined | Table {
    // 行和列 有一个小于等于0的 不能创建表格
    if (row_num <= 0 || col_num <= 0) {
      return;
    }
    const tbl_width =
        editor.page_size.width - editor.config.page_padding_left - editor.config.page_padding_right;

    // 列宽已经达到最小值了 不能创建表格
    if (tbl_width / col_num <= Config.min_col_size) {
      editor.event.emit("message", { type: "warning", msg: `最大只能插入${Math.floor(tbl_width / Config.min_col_size)}列` });
      return; // TODO 这一步校验可以考虑在上层做，输入行列的时候判断宽度
    }

    const out_cell = editor.current_cell;

    // 根据行，列，行内高度生成原始表格
    const node = editor.generateRawTable(
      row_num,
      col_num,
      editor.config.default_font_style.height
    );

    const table = isImageTable
      ? new ImageTable(
        editor,
        node.id,
        null,
        node.col_size,
        node.row_size,
        node.min_row_size,
        out_cell,
        out_cell.left + out_cell.padding_left,
        out_cell.right - out_cell.padding_right,
        out_cell.cursor_position, // 这是光标距离顶部的距离 就是该表格的top值
        newPage,
        SkipMode.ROW
      )
      : new Table(
        editor,
        node.id,
        null,
        node.col_size,
        node.row_size,
        node.min_row_size,
        out_cell,
        out_cell.left + out_cell.padding_left,
        out_cell.right - out_cell.padding_right,
        out_cell.cursor_position, // 这是光标距离顶部的距离 就是该表格的top值
        newPage,
        SkipMode.ROW
      );

    for (let i = 0; i < node.cells.length; i++) {
      const cell_raw = node.cells[i];
      const new_cell = new Cell(
        editor,
        cell_raw.pos,
        cell_raw.colspan,
        cell_raw.rowspan,
        table
      );

      table.children.push(new_cell); // ① 这两个，就要先push在插入空段，如果颠倒顺序，在插入空段的时候，会计算表格内每个单元格的高度，就会少计算最后一行(先插空段，表格的children永远会少一个)
      if (i === node.cells.length - 1) {
        new_cell.insertEmptyParagraph(0); // ②
      } else {
        new_cell.insertEmptyParagraph(0, new_cell);
      }
    }
    return table;
  }

  static deletePictureByID (id: string, editor: Editor) {
    if (!id || editor.readonly) return;
    const rows = editor.current_cell.children;
    for (let i = 0; i < rows.length; i++) {
      const t = rows[i];
      if (t instanceof ImageTable) {
        const index = t.imageList.findIndex(img => img.id === id);
        if (t.children.length === 1 && index >= 0) {
          editor.selection.setCursorPosition([t.cell_index, 0, 0, 0]);
          editor.deleteTbl();
          return true;
        }
        if (index >= 0) {
          const imageList = [...t.imageList]; // 因为撤销重做的时候是直接赋值的 imageList 是个引用关系，所以直接修改 t.imageList 会修改撤销历史堆栈中的数据，这里在 splice 等修改之前先解构
          imageList.splice(index, 1);
          imageList.forEach((img, i) => {
            img.serialNum = i + 1;
          });
          t.imageList = imageList;
          ImageTable.insertImageTable({ index, width: t.recordWidth, height: t.recordHeight, maxTableHeight: t.maxTableHeight, needSerialNum: t.needSerialNum, editor });
          return true;
        }
      }
    }
  }

  // 当执行删除操作的时候是没有 src 的，也就是说 src 的值为 undefined
  static insertImageTable ({ src, field, index, width, height, maxTableHeight, id, editor, needSerialNum }: { src?: string, field?: XField, index?: number, width?: number, height?: number, maxTableHeight?: number, id?: string, editor: Editor, needSerialNum?: boolean }) {
    if (editor.is_edit_hf_mode) return; // 编辑页眉页脚模式不允许插入
    if (editor.selection.getFocusGroup()?.lock) return; // 分组锁定 不能插入表格
    if ((editor.view_mode === "form" && !editor.adminMode) || editor.readonly) return; // 表单模式 只读模式 不能插入

    maxTableHeight = maxTableHeight || 1000;
    let imageList = [];
    const saveFields: XField[][] = [];
    const saveParagraph: (Paragraph | Table)[][] = [];
    const current_table = editor.current_cell.children.find(table => table instanceof ImageTable);

    if (current_table) { // 文档中本来就有 就从该表格内取值
      const group = editor.selection.getGroupByGroupId((current_table as Table)?.group_id || "");
      if (group?.lock) return false;
      imageList = JSON.parse(JSON.stringify((current_table as ImageTable).imageList)); // 图片列表优先从typesetting中拿
      (current_table as ImageTable).children.forEach((cell, i) => {
        (i !== index) && saveFields.push(cell.fields);
        (i !== index) && saveParagraph.push(cell.paragraph);
      });
      editor.selection.setCursorPosition([current_table.cell_index, 0, 0, 0]);
    } else {
      if (editor.selection.getFocusField()) return; // 文本域内不能插入表格，如果已经存在排版了，光标很有可能在文本域里边，所以 if (current_talbe) 里边不用有这个判断
      if (PathUtils.isTablePath(editor.selection.start)) return; // 光标在表格内 不能插入表格
    }
    if (needSerialNum) {
      for (let i = 0; i < saveFields.length; i++) {
        saveFields[i][0] && saveFields[i][0].type === "normal" && saveFields[i][0].replaceText(i + 1 + "");
      }
    }
    id = id || uuid("image-type-");
    const serialNum = imageList.length + 1;
    src && imageList.push({ src, id, serialNum: needSerialNum ? serialNum : undefined });
    if (imageList.length > 12) return;
    let newTable;
    if (imageList.length === 1) {
      if (src) { // 只有一张图片 且还传值了 就全部用传进来的值
        newTable = editor.insertTable(1, 1, { isImageTable: true }) as ImageTable;
        if (!newTable) return;
        newTable.handleCellNew(imageList, src, editor, serialNum, id, field, width || 1, height || 1, needSerialNum);
      } else { // 只有一张图片 且没有传值 那就是删除调用的就用原来的单元格内容即可
        newTable = editor.createTable(1, 1, { isImageTable: true }) as ImageTable;
        newTable.group_id = (current_table as ImageTable)?.group_id;
        newTable.replace(current_table as Table, editor);
        newTable.handleCellOld(saveParagraph, saveFields, imageList, newTable.children.length, width || 1, height || 1, 0);
      }
      newTable.imageList = JSON.parse(JSON.stringify(imageList));
      newTable.handleLineOpacity();
    } else {
      // 创建表格 并合并单元格 ↓
      let table_rows = 1;
      if (imageList.length > 4 && imageList.length < 9) {
        table_rows = 2;
      } else if (imageList.length >= 9) {
        table_rows = 3;
      }
      newTable = editor.createTable(table_rows, 12, { isImageTable: true }) as ImageTable;

      newTable.handleLineOpacity();
      newTable.group_id = (current_table as ImageTable)?.group_id;
      newTable.replace(current_table as Table, editor);
      newTable.imageList = JSON.parse(JSON.stringify(imageList));
      if (imageList.length <= 4) {
        for (let i = 0; i < newTable.row_size.length; i++) {
          newTable.mergeCell(i, imageList.length, 12);
        }
      } else if (imageList.length === 5) {
        newTable.mergeCell(0, 2, 12);
        newTable.mergeCell(1, 3, 12);
      } else if (imageList.length === 6) {
        newTable.mergeCell(0, 3, 12);
        newTable.mergeCell(1, 3, 12);
      } else if (imageList.length === 7) {
        newTable.mergeCell(0, 3, 12);
        newTable.mergeCell(1, 4, 12);
      } else if (imageList.length === 8) {
        newTable.mergeCell(0, 4, 12);
        newTable.mergeCell(1, 4, 12);
      } else if (imageList.length === 9) {
        newTable.mergeCell(0, 3, 12);
        newTable.mergeCell(1, 3, 12);
        newTable.mergeCell(2, 3, 12);
      } else if (imageList.length === 10) {
        newTable.mergeCell(0, 3, 12);
        newTable.mergeCell(1, 3, 12);
        newTable.mergeCell(2, 4, 12);
      } else if (imageList.length === 11) {
        newTable.mergeCell(0, 3, 12);
        newTable.mergeCell(1, 4, 12);
        newTable.mergeCell(2, 4, 12);
      } else {
        newTable.mergeCell(0, 4, 12);
        newTable.mergeCell(1, 4, 12);
        newTable.mergeCell(2, 4, 12);
      }
      // 创建表格 并合并单元格 ↑
      if (src) {
        // 说明传值进来了 只有最后一个使用传进来的值
        newTable.handleCellOld(saveParagraph, saveFields, imageList, newTable.children.length - 1, width || 1, height || 1, 0);
        newTable.handleCellNew(imageList, src, editor, serialNum, id, field, width || 1, height || 1, needSerialNum);
      } else {
        // 没有传值进来 就全部使用保存好的
        newTable.handleCellOld(saveParagraph, saveFields, imageList, newTable.children.length, width || 1, height || 1, 0);
      }
    }

    // 考虑序号和文本域 可能高度不固定 所以找到最大row_size 计算图像大小
    if (newTable.height > maxTableHeight) {
      if (typeof index === "undefined") {
        const every_pic_should_sub_height = (newTable.height - maxTableHeight) / newTable.row_size.length;
        const saveFields: any = [];
        const saveParagraph: any = [];
        newTable.children.forEach(cell => {
          saveFields.push(cell.fields);
          saveParagraph.push(cell.paragraph);
        });
        newTable.handleCellOld(saveParagraph, saveFields, imageList, newTable.children.length, width || 1, height || 1, every_pic_should_sub_height);
      } else {
        const every_pic_should_sub_height = (newTable.height - maxTableHeight) / newTable.row_size.length;
        newTable.handleCellOld(saveParagraph, saveFields, imageList, newTable.children.length, width || 1, height || 1, every_pic_should_sub_height);
      }
    }
    newTable.maxTableHeight = maxTableHeight;
    newTable.recordWidth = width || 100;
    newTable.recordHeight = height || 100;
    newTable.name = TABLE_NAME_FOR_PACS_IMAGE_LAYOUT; // 写死 为了他们能够获取
    newTable.needSerialNum = needSerialNum;
    editor.update();
    editor.render();
    return true;
  }

  /**
     * 合并单元格
     * @param row_index 行号
     * @param retained_cells 该行要保留下来的单元格数量
     */
  mergeCell (row_index: number, retained_cells: number, table_cols: number) {
    const cells = this.children;
    const merge_num = table_cols / retained_cells; // 需要合并的单元格数量
    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i];
      if (cell.position[0] === row_index) {
        // 一切操作都应该是在这儿
        if (cell.position[1] % merge_num === 0) {
          cell.colspan = merge_num;
        }
      }
    }
    for (let i = 0; i < cells.length; i++) {
      const cell = cells[i];
      if (cell.position[0] === row_index && cell.colspan === 1) {
        this.notAllowDrawLine.col.push([cell.position[1], row_index]);
        cells.splice(i, 1);
        i--;
      }
    }
  }

  handleLineOpacity () {
    const rowLine = [];
    for (let i = 0; i <= this.row_size.length; i++) {
      for (let j = 0; j < this.col_size.length; j++) {
        rowLine.push([i, j]);
      }
    }
    this.notAllowDrawLine.changeOpacityRow = rowLine;

    const colLine = [];
    for (let i = 0; i <= this.col_size.length; i++) {
      for (let j = 0; j < this.row_size.length; j++) {
        colLine.push([i, j]);
      }
    }
    this.notAllowDrawLine.changeOpacityCol = colLine;
  }

  handleCellNew (imageList: any, src: any, editor: Editor, serialNum: any, id: any, field: any, originWidth: number, originHeight: number, needSerialNum: boolean | undefined) {
    const ratio = originWidth / originHeight;
    const arr1: any = [];
    let img;
    const width = this.width;
    const font = editor.contextState.getFontState();
    if (imageList.length < 4 || imageList.length === 5 || imageList.length === 6 || imageList.length === 9) {
      const w = width / 3;
      img = new Image(src, w, w / ratio, font, { serialNum, id });
    } else {
      const w = width / 4;
      img = new Image(src, w, w / ratio, font, { serialNum, id });
    }
    const current_cell = imageList.length === 1 ? this.children[0] : this.children[this.children.length - 1];
    editor.imageMap.addOnload(img);
    arr1.push(img);
    current_cell.appendElements(arr1);
    field && current_cell.insertEmptyParagraph(0);
    const arr2: any = [];
    if (needSerialNum) {
      const numField = editor.createElement("field") as XField;
      numField.readonly = 1;
      arr2.push(numField);
    }
    field && arr2.push(field);
    field && current_cell.appendElements(arr2);
    field && serialNum && needSerialNum && current_cell.fields[0].type === "normal" && current_cell.fields[0].replaceText(serialNum);
    if (imageList.length === 2) {
      current_cell.padding_right = current_cell.width - img.width - 10;
    }
    current_cell.paragraph.forEach(p => {
      if (isParagraph(p)) {
        p.changeAlign("center");
      }
    });
  }

  handleCellOld (saveParagraph: any, saveFields: any, imageList: any, len: number, originWidth: number, originHeight: number, everyPicShouldSubHeight: number/* , needSerialNum: boolean | undefined */) {
    const width = this.width;
    const ratio = originWidth / originHeight;
    for (let i = 0; i < len; i++) {
      const cell = this.children[i];
      cell.paragraph = saveParagraph[i];

      cell.handleFieldsAssignment(saveFields[i]);

      let lastW: number = 0;
      cell.paragraph.forEach((p) => {
        if (isParagraph(p)) {
          p.cell = cell;
          const img = p.characters.find(char => isImage(char));

          if (imageList?.length < 4 || imageList.length === 5 || imageList.length === 6 || imageList.length === 9) {
            const w = width / 3;
            const h = w / ratio;
            const lastH = h - everyPicShouldSubHeight;
            lastW = everyPicShouldSubHeight > 0 ? ratio * lastH : w;
            img && (img.width = lastW);
            img && (img.height = lastH);
          } else {
            const w = width / 4;
            const h = w / ratio;
            const lastH = h - everyPicShouldSubHeight;
            lastW = everyPicShouldSubHeight > 0 ? ratio * lastH : w;
            img && (img.width = lastW);
            img && (img.height = lastH);
          }
          // 让个别图片间距小点
          if (imageList.length === 2) {
            if (i === 0) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW - 10;
            }
            if (i === 1) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW - 10;
            }
          }

          if (imageList.length === 3) {
            if (i === 0) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 4) {
            if (i === 0 || i === 2) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 1 || i === 3) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 5) {
            if (i === 0) {
              p.cell.padding_left = p.cell.width - lastW - 10;
            }
            if (i === 1) {
              p.cell.padding_right = p.cell.width - lastW - 10;
            }
            if (i === 2) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 4) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 6) {
            if (i === 0 || i === 3) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2 || i === 5) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 7) {
            if (i === 0 || i === 3) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2 || i === 6) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 8) {
            if (i === 0 || i === 4) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 3 || i === 7) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 9) {
            if (i === 0 || i === 3 || i === 6) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2 || i === 5 || i === 8) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 10) {
            if (i === 0 || i === 3 || i === 6) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2 || i === 5 || i === 9) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 11) {
            if (i === 0 || i === 3 || i === 7) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2 || i === 6 || i === 10) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 12) {
            if (i === 0 || i === 4 || i === 8) { // 第0个单元格
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 3 || i === 7 || i === 11) { // 第1个单元格
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (([7, 10, 11] as any).includes(imageList.length)) {
            const w = (lastW + 10) / 4;
            p.cell.parent!.col_size[4] = w;
            p.cell.parent!.col_size[5] = w;
            p.cell.parent!.col_size[6] = w;
            p.cell.parent!.col_size[7] = w;
            const w1 = (p.cell.parent!.width - lastW - 10 - p.cell.parent!.col_size[0] * 6) / 2;
            p.cell.parent!.col_size[3] = w1;
            p.cell.parent!.col_size[8] = w1;
            if (i === 0) {
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2) {
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 9) {
            if (i === 0 || i === 3 || i === 6) {
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2 || i === 5 || i === 8) {
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          if (imageList.length === 10) {
            if (i === 0 || i === 3) {
              p.cell.padding_left = p.cell.width - lastW;
            }
            if (i === 2 || i === 5) {
              p.cell.padding_right = p.cell.width - lastW;
            }
          }
          p.updateChildren();
        }
      });
    }
  }

  /**
   * 重新给 imageList 排序 在
   * @param moveCell 移动的单元格
   * @param targetCell 目标单元格 是排序还是交换位置的单元格
   * @param relativePosition 相对位置
   */
  reorderImageList (moveCell: Cell, targetCell: Cell, relativePosition: "front" | "behind" | "situ" | "swap") {
    this.sortingCells();
    const moveIndex = this.children.findIndex(cell => cell === moveCell);
    const targetIndex = this.children.findIndex(cell => cell === targetCell);
    const moveImages = this.imageList.splice(moveIndex, 1, null);
    if (relativePosition === "swap") {
      const targetImage = this.imageList.splice(targetIndex, 1, null);
      this.imageList[targetIndex] = moveImages[0];
      this.imageList[moveIndex] = targetImage[0];
    } else {
      if (relativePosition === "front") {
        this.imageList.splice(targetIndex, 0, moveImages[0]);
      } else if (relativePosition === "behind") {
        this.imageList.splice(targetIndex + 1, 0, moveImages[0]);
      }
    }
    this.imageList = this.imageList.filter(Boolean);
  }

  /**
   * 更新序号
   */
  updateSerialNum () {
    this.sortingCells();
    const cells = this.children;
    cells.forEach((cell, index) => {
      const firstField = cell.fields[0];
      if (firstField && firstField.type === "normal") {
        firstField.replaceText(String(index + 1));
      }
      this.imageList[index].serialNum = index + 1;
    });
  }
}
export default ImageTable;
