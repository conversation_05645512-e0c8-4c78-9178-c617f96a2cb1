<template>
  <modal
    :show="show"
    :width="modal_width"
    :title="title"
    @submit="submit"
    @cancel="cancel"
    class="chart-setting-content"
  >
    <div class="prop-div">
      <label>类型：</label>
      <a-select
        v-model="innerChartType"
        class="prop-select"
        dropdownClassName="xeditor-input-up"
      >
        <a-select-option :value="'bar'">柱状图</a-select-option>
        <a-select-option :value="'pie'">饼状图</a-select-option>
        <a-select-option :value="'line'">折线图</a-select-option>
      </a-select>
    </div>
    <div class="prop-div">
      <label>x轴：</label>
      <a-select
        v-model="xAxisData"
        class="prop-select"
        dropdownClassName="xeditor-input-up"
      >
        <a-select-option v-for="item of chartKeys" :key="item" :value="item">{{
          item
        }}</a-select-option>
      </a-select>
    </div>
    <div class="prop-div">
      <label>y轴：</label>
      <a-select
        mode="multiple"
        v-model="yAxisData"
        class="prop-select"
        dropdownClassName="xeditor-input-up"
      >
        <a-select-option v-for="item of chartKeys" :key="item" :value="item">{{
          item
        }}</a-select-option>
      </a-select>
    </div>
    <div slot="editor-modal-footer">
      <a-button type="default" @click="cancel">取消</a-button>
      <a-button type="primary" @click="submit">完成</a-button>
    </div>
  </modal>
</template>

<script>
import modal from "./common/modal.vue";
export default {
  name: "imageEditing",
  components: {
    modal,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    chartType: {
      type: String,
      default: "bar",
    },
    xAxisData: {
      type: String,
      default: "",
    },
    yAxisData: {
      type: Array,
      default: () => [],
    },
    imageMeta: {
      type: Object,
      default: () => {},
    },
    editorId: {
      type: String,
      default: "",
    },
    chartKeys: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    innerChartType: {
      get() {
        return this.chartType;
      },
      set(newVal) {
        this.$emit("updateChartType", newVal); // 触发 input 事件更新父组件
      },
    },
  },
  data() {
    return {
      title: "图表设置",
      modal_width: 798,
    };
  },

  watch: {
    show: {
      handler(val) {
        if (val) {
          console.log("展示出来了");
        }
      },
    },
    immediate: true,
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    submit() {
      this.$emit("submit", {
        chartType: this.chartType,
        xAxisData: this.xAxisData,
        yAxisData: this.yAxisData,
      });
      this.cancel();
    },
    cancel() {
      this.$emit("cancel");
    },
  },
};
</script>

<style scoped>
.chart-setting-content /deep/.ant-modal-body {
  padding: 0 12px 12px 12px;
  overflow: hidden;
}
</style>
