<template>
  <modal
    title="分组属性"
    :show="isShowGroupModal"
    :width="modal_width"
    @cancel="cancel"
  >
    <div class="prop_group">
      <div class="prop-div_group">
        <label>分组ID：</label>
        <a-input
          class="prop-input_id"
          v-model="group_info.id"
          @change="handleChange"
          placeholder="可不填，将会生成默认ID"
        ></a-input>
        <span class="repeat_prompt">该分组ID已经存在!</span>
      </div>
      <div class="prop-div_group">
        <label>分组Name：</label>
        <a-input
          class="prop-input_id"
          v-model="group_info.name"
          placeholder="分组名称"
        ></a-input>
      </div>
      <div class="prop-div_group">
        <label>日期、时间：</label>
        <a-date-picker
          :locale="locale"
          :default-value="moment(group_info.date, 'YYYY-MM-DD HH:mm:ss')"
          show-time
          placeholder="选择日期"
          class="prop-date_picker"
          dropdownClassName="xeditor-input-up"
          @ok="onOk"
        />
      </div>
      <div class="prop-div_group" v-show="show_direction">
        <label>插入方式：</label>
        <a-select
          class="prop-select"
          v-model="group_info.insertBy"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option
            v-for="(item, index) in insertBy"
            :value="item.type"
            :key="index"
            >{{ item.name }}
          </a-select-option>
        </a-select>
      </div>
      <div class="prop-div_group" v-show="modifyProperty">
        <label>表单模式：</label>
        <a-select
          class="prop-select"
          v-model="group_info.isForm"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option
            v-for="(item, index) in isForm"
            :value="item.value"
            :key="index"
            >{{ item.name }}
          </a-select-option>
        </a-select>
        <span v-show="group_info.isForm" class="form_prompt"
          >开启后，禁用编辑器表单模式!</span
        >
      </div>
      <div
        class="prop-div_group"
        v-show="show_direction"
        v-if="group_info.insertBy === 'position'"
      >
        <label>插入位置：</label>
        <a-select
          class="prop-select"
          v-model="group_info.insert_direction"
          dropdownClassName="xeditor-input-up"
        >
          <a-select-option
            v-for="(item, index) in insert_direction"
            :value="item.value"
            :key="index"
            :disabled="item.disabled"
            >{{ item.name }}
          </a-select-option>
        </a-select>
      </div>
      <div class="prop-div_group">
        <label>开启分页：</label>
        <a-switch v-model="group_info.new_page" /> &nbsp;&nbsp;&nbsp;
        <label>分组锁定：</label>
        <a-switch v-model="group_info.lock" />
      </div>
    </div>
    <div slot="editor-modal-footer" class="footer">
      <div>
        <a-button type="defalut" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit" :disabled="disabled"
          >确定</a-button
        >
      </div>
    </div>
  </modal>
</template>

<script>
import modal from "../common/modal.vue";
import moment from "moment";
import locale from "ant-design-vue/es/date-picker/locale/zh_CN";
// import BUS from "@/assets/js/eventBus";
export default {
  name: "group",
  components: {
    modal,
  },
  props: {
    isShowGroupModal: {
      type: Boolean,
      default: false,
    },
    // group: {
    //   type: Object,
    //   default: () => {},
    // },
    editorId: {
      type: String,
      default: "",
    },
    modifyProperty: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    modifyProperty: {
      handler(val) {
        if (val) {
          this.show_direction = false;
          this.group_info.modifyProperty = val;
          const group = this.editor.selection.getFocusGroup() ?? null;
          this.group_info.date = moment(parseInt(group.date));
          this.group_info.id = group.id;
          this.group_info.name = group.name;
          this.group_info.lock = group.lock;
          this.group_info.isForm = group.is_form ? 1 : 0;
          this.group_info.new_page = group.new_page;
        } else {
          this.show_direction = true;
        }
      },
    },
    isShowGroupModal: {
      handler(val) {
        if (val) {
          const group = this.editor.selection.getFocusGroup() ?? null;
          if (group) {
            this.group_info.date = moment(parseInt(group.date));
            this.showDirectionOption();
          } else {
            this.group_info.date = new Date();
            this.insert_direction.forEach((e) => {
              if (e.value !== 3) e.disabled = true;
            });
          }
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      modal_width: 540,
      disabled: false,
      locale,
      show_direction: true,
      insertBy: [
        { name: "位置顺序", type: "position" },
        { name: "时间顺序", type: "time" },
      ],
      insert_direction: [
        { name: "上方插入", value: 2, disabled: false },
        { name: "下方插入", value: 3, disabled: false },
      ],
      isForm: [
        { name: "开启", value: 1 },
        { name: "关闭", value: 0 },
      ],
      indentation: 0,
      group_info: {
        id: "",
        name: "",
        modifyProperty: false,
        date: "",
        insertBy: "position",
        isForm: 0,
        insert_direction: 3,
        header_info: {},
        new_page: false,
        lock: false,
      },
    };
  },
  mounted() {
    // BUS.$on("editor_" + this.editorId, ({ group }) => {
    //   if (group) this.group = group;
    // });
    // if (group) {
    //   this.group_info.date = moment(parseInt(group.date));
    //   this.showDirectionOption();
    // } else {
    //   this.group_info.date = new Date();
    //   this.insert_direction.forEach((e) => {
    //     if (e.value !== 3) e.disabled = true;
    //   });
    // }
  },
  methods: {
    moment,
    cancel() {
      this.$emit("cancel");
    },
    submit() {
      this.$emit("submit", this.group_info);
    },
    onOk(value) {
      this.group_info.date = value;
    },
    clearTemplateState() {
      this.show_direction = true;
      this.disabled = false;
      this.group_info = {
        id: "",
        name: "",
        modifyProperty: false,
        date: new Date(),
        insertBy: this.group_info.insertBy,
        isForm: 0,
        lock: false,
        insert_direction: 3,
        header_info: {},
        new_page: false,
      };
    },
    showDirectionOption() {
      this.insert_direction.forEach((e) => {
        e.disabled = false;
      });
    },
    handleChange() {
      const repeatGroup = this.editor.selection.getGroupByGroupId(
        this.group_info.id
      );
      const repeatPrompt = document.querySelector(".repeat_prompt");
      const focusGroup = this.editor.selection.getFocusGroup();
      const groups = this.editor.getAllGroup();
      if (
        repeatGroup &&
        (repeatGroup !== focusGroup ||
          (groups.length >= 1 && !this.modifyProperty))
      ) {
        repeatPrompt.style.display = "inline";
        this.disabled = true;
      } else {
        repeatPrompt.style.display = "none";
        this.disabled = false;
      }
    },
  },
};
</script>
<style lang="less" scoped>
.prop-div_group label {
  margin-left: 0px;
}
label {
  width: 20%;
}
.prop-input_id,
.prop-date_picker,
.prop-select {
  width: 40%;
}
.prop_group {
  padding: 0 0 10px 10px;
}
.xeditor-input-up {
  z-index: 99999 !important;
}
.prop-div_group {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}
.repeat_prompt {
  margin-left: 5px;
  color: red;
  display: none;
}
.form_prompt {
  margin-left: 5px;
  color: red;
  // display: none;
}
</style>
