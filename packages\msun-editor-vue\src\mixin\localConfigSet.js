const localConfigSet = {
  data() {
    return {
      configFileName: "NewEditorConfig",
      configFilePath: "c:/Msunsoft/MsunEditor/",
      // 编辑器客户端本地配置
      localConfig: {
        other: {},
        system: {}, // base基础配置
        print: {}, // 打印需要的配置
      },
    };
  },
  methods: {
    writeFile(path, fileName, content, resolve, reject) {
      if (this.isElectron) {
        const electron = window.electron ?? window.top.electron;
        if (electron.filesOper && electron.filesOper.writeFile) {
          electron.filesOper
            .writeFile({
              fileName: path + fileName,
              data: content,
              option: {
                encoding: "utf8",
              },
            })
            .then(() => {
              resolve && resolve();
            })
            .catch(() => {
              reject && reject();
            });
        }
      }
    },
    readFile(path, fileName) {
      return new Promise((resolve, reject) => {
        const electron = window.electron ?? window.top.electron;
        if (electron && electron.filesOper && electron.filesOper.readFile) {
          electron.filesOper
            .readFile({
              fileName: path + fileName,
              option: {
                encoding: "utf8",
              },
            })
            .then((data) => {
              resolve(data);
            })
            .catch(() => {
              reject();
            });
        }
      });
    },
    writeLocalConfig() {
      const config = this.localConfig;
      const fileName = this.configFileName;
      const path = this.configFilePath;
      localStorage.setItem(fileName, JSON.stringify(config));
      this.writeFile(path, fileName + ".txt", config);
    },
    // 对老的配置方式做兼容
    handleOldConfig() {
      let isNeedWriteConfig = false;
      const resetLocalPrintConfig = (oldKey, newKey) => {
        const pdxVal = localStorage.getItem(oldKey);
        if (pdxVal !== undefined) {
          localStorage.removeItem(oldKey);
          if (Number(pdxVal) === 1 || pdxVal === true) {
            if (!this.localConfig.print) {
              this.localConfig.print = {};
            }
            this.localConfig.print[newKey] = true;
            isNeedWriteConfig = true;
          }
        }
      };
      resetLocalPrintConfig("EditorPrintDuplex", "isDuplex");
      if (isNeedWriteConfig) {
        this.writeLocalConfig();
      }
    },
    initReadConfigInfo(systemConfig) {
      try {
        const source = systemConfig?.source;
        // 一个用户或者一个产品用一个配置，防止配置串
        if (source) {
          this.configFileName += source;
        }
        // if (this.editor.user?.id) {
        //   this.configFileName += this.editor.user.id;
        // }
        this.readLocalConfig(systemConfig);
      } catch (e) {
        console.error("获取本地配置信息出错" + e.message);
      }
    },
    checkPrintConfig(config) {
      if (!config || typeof config !== "object") {
        config = {
          other: {},
          system: {},
          print: {},
        };
      } else {
        if (!config.other || typeof config.other !== "object") {
          config.other = {};
        }
        if (!config.system || typeof config.system !== "object") {
          config.system = {};
        }
        if (!config.print || typeof config.print !== "object") {
          config.print = {};
        }
      }
      return config;
    },
    readLocalConfig(systemConfig) {
      const fileName = this.configFileName;
      const path = this.configFilePath;
      let config = localStorage.getItem(fileName);
      const getConfigFromFile = () => {
        if (this.isElectron) {
          this.readFile(path, fileName + ".txt").then((result) => {
            try {
              config = JSON.parse(result.data);
              if (config && typeof config === "object") {
                this.localConfig = this.checkPrintConfig(config);
                // 判断此时是否已经初始化
                localStorage.setItem(fileName, result.data);
                this.handleOldConfig();
                Object.assign(systemConfig, this.getSystemConfig());
                // 判断此时编辑器实例是否已经初始化，如果已经初始化完成则需要重新对编辑器配置进行初始化
                if (this.instance) {
                  this.instance.editor.reInitConfig(systemConfig);
                }
              } else {
                localStorage.removeItem(fileName);
              }
            } catch (e) {
              localStorage.removeItem(fileName);
              // console.error(e);
            }
          });
        } else {
          localStorage.removeItem(fileName);
        }
      };
      if (config) {
        try {
          const newConfig = JSON.parse(config);
          if (typeof newConfig === "object") {
            this.localConfig = this.checkPrintConfig(newConfig);
            Object.assign(systemConfig, this.getSystemConfig());
            this.handleOldConfig();
          } else {
            getConfigFromFile();
          }
        } catch (e) {
          getConfigFromFile();
        }
      } else {
        getConfigFromFile();
      }
    },
    clearLocalConfig() {
      this.localConfig = {
        other: {},
        system: {}, // base基础配置
        print: {}, // 打印需要的配置
      };
      this.writeLocalConfig();
    },
    setLocalConfig(module, key, val) {
      // 如果不传值则默认为切换 0 1
      if (val === undefined) {
        const oldVal = this.localConfig[module][key];
        this.localConfig[module][key] = Number(oldVal) === 1 ? 0 : 1;
        this.writeLocalConfig();
        return !Number(oldVal);
      } else if (val !== this.localConfig[module][key]) {
        this.localConfig[module][key] = val;
        this.writeLocalConfig();
        return val;
      } else {
        return val;
      }
    },
    //设置本地打印设置
    setLocalPrintConfig(key, val) {
      return this.setLocalConfig("print", key, val);
    },
    setLocalOtherConfig(key, val) {
      return this.setLocalConfig("other", key, val);
    },
    setLocalSystemConfig(key, val) {
      return this.setLocalConfig("system", key, val);
    },
    getLocalConfig(module, key) {
      if (typeof this.localConfig[module] !== "object") {
        return {};
      }
      if (
        key &&
        this.localConfig[module][key] !== null &&
        this.localConfig[module][key] !== undefined
      ) {
        return this.localConfig[module][key];
      }
      if (this.localConfig[module] && !key) {
        return this.localConfig[module];
      }
      return {};
    },
    getPrintConfig(key) {
      return this.getLocalConfig("print", key);
    },
    getOtherConfig(key) {
      return this.getLocalConfig("other", key);
    },
    getSystemConfig(key) {
      return this.getLocalConfig("system", key);
    },
  },
};
export default localConfigSet;
