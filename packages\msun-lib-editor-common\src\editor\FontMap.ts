import Font, { FontStyle } from "./Font";

export default class FontMap {
  fontMap: Map<string, Font> = new Map();

  get () {
    return this.fontMap;
  }

  getStyleById (id: string): FontStyle | undefined {
    return this.fontMap.get(id)?.style;
  }

  add (fontStyle: FontStyle, id: null | string = null): Font {
    if (id) {
      const target_font = new Font(fontStyle, id);
      this.fontMap.set(target_font.id, target_font); // 改变了同一个 id 的引用地址(换了个新的 font,这里改变的是 FontMap) 之前根据 id 从 FontMap 中取的 font 就不会改变了 所以 复制临时背景色的文字 虽然 两个 id 一样 但是一个有背景色 一个没有
      return target_font;
    }
    const font_styles = Array.from(this.fontMap.values());

    let target_font: Font | undefined = font_styles.find((item) =>
      Font.isEqual(item, fontStyle)
    );

    if (!target_font) {
      target_font = new Font(fontStyle);
      this.fontMap.set(target_font.id, target_font);
    }
    return target_font;
  }

  clear () {
    this.fontMap.clear();
  }
}
