/**
 * 提供给其他团队使用的接口
 */

import Editor from "./Editor";
import Paragraph from "./Paragraph";
import { deepClone, isTable } from "./Utils";


export default class Ext {
    editor: Editor;
    constructor(editor: Editor){
      this.editor = editor;
    }

    /**
     * 
     * @param keywords 关键字数组 删除从第一个关键字到第二个关键字之间的内容 找不到第二个关键字就去找第三个关键字
     * @param cell 删除哪个 cell 里边的段落
     * @returns 删除成功了就返回 删除的 rawData 否则就啥也不返回
     */
    deleteParasBetweenKeywords(keywords: string[], cell = this.editor.root_cell) {
      const originAdminMode = this.editor.adminMode;
      this.editor.adminMode = true;
    
      // 先获取到当前文档的所有段落
      const paras = cell.paragraph;

      const { sParaIndex, eParaIndex } = this.editor.getParaIndexWithKeyword(keywords, paras);
      if (eParaIndex - sParaIndex <= 1) return // 中间什么都没有的时候 return掉

      for(let i = sParaIndex;i < eParaIndex; i++){ // 存在表格时  不做任何处理
        if(isTable(paras[i])) {
          return
        }
      }

      // 根据坐标删除两个坐标间的内容
      const prePara = paras[eParaIndex - 1] as Paragraph;
      const startPosi = isTable(paras[sParaIndex + 1])
        ? [sParaIndex + 1, 0, 0, 0]
        : [sParaIndex + 1, 0]
      const endPosi = [eParaIndex - 1, prePara.characters.length - 1]


      // 设置选区
      this.editor.selection.setSelectionByPath(startPosi, endPosi)

      const rawData = this.editor.getRawDataBySelection()

      this.editor.delete_backward();
      this.editor.adminMode = originAdminMode;
      return rawData;
    }

    insertData(datas: any[] = []){
      const originAdminMode = this.editor.adminMode;
      this.editor.adminMode = true;
      for (let i = 0; i < datas.length; i++) {
        const data = datas[i];
        if (data.type === "rawData") {
          const raw = deepClone(data.value); // JSON.parse(JSON.stringify(data.value)); // 如果不克隆的话 如果传入同一个对象 有的左对齐有的居中就实现不了啦
          if (data.align) {
            for (const para of raw.content) {
              para.align = data.align;
            }
          }
          this.editor.insertTemplateData(raw);
        } else if (data.type === "content") {
          // 用这个 有图片就废了 因为 content 里边只存了 id 没有 src
          const raw = {
            header: [],
            footer: [],
            content: deepClone(data.value),
            groups: [],
            fontMap: {},
            bodyText: "",
            config: {},
            meta: {}
          };
          this.editor.insertTemplateData(raw);
        } else if (data.type === "text") {
          if (data.align) {
            const currentPara = this.editor.selection.getFocusParagraph();
            currentPara.align = data.align;
          }
          this.editor.insertText(data.value);
        }
          
        if (i < datas.length - 1) {
          this.editor.enterDown();
        }
      }
      this.editor.adminMode = originAdminMode;
    }
    
}
