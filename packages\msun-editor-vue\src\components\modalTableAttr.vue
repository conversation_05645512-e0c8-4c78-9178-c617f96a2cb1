<template>
  <modal
    title="表格属性"
    :show="show"
    :width="width"
    @submit="submit"
    @cancel="cancel"
  >
    <a-row>
      <a-col :span="12">
        <a-checkbox :checked="isShowTblLine_" v-model="isShowTblLine_">
          隐藏表格线
        </a-checkbox>
      </a-col>
      <a-col :span="12">
        <a-checkbox :checked="isCanNotDelTbl_" v-model="isCanNotDelTbl_">
          禁止删除该表格
        </a-checkbox>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-checkbox :checked="isShowUpLine_" v-model="isShowUpLine_">
          展示上边线
        </a-checkbox>
      </a-col>
      <a-col :span="12">
        <a-checkbox :checked="isShowRightLine_" v-model="isShowRightLine_">
          展示右边线
        </a-checkbox>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-checkbox :checked="isShowDownLine_" v-model="isShowDownLine_">
          展示下边线
        </a-checkbox>
      </a-col>

      <a-col :span="12">
        <a-checkbox :checked="isShowLeftLine_" v-model="isShowLeftLine_">
          展示左边线
        </a-checkbox>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="12">
        <a-checkbox :checked="isShowSlashUp_" v-model="isShowSlashUp_">
          切换左上角开始的斜线
        </a-checkbox>
      </a-col>
      <a-col :span="12">
        <a-checkbox :checked="isShowSlashDown_" v-model="isShowSlashDown_">
          切换左下角开始的斜线
        </a-checkbox>
      </a-col>
    </a-row>
  </modal>
</template>

<script>
import modal from "./common/modal.vue";
export default {
  data: function () {
    return {
      isShowUpLine_: false, // 单元格是否显示上边线
      isShowRightLine_: false, // 单元格是否显示右边线
      isShowDownLine_: false, // 单元格是否显示下边线
      isShowLeftLine_: false, // 单元格是否显示左边线
      isShowSlashUp_: false, // 单元格是否显示左上角开始的斜线
      isShowSlashDown_: false, // 单元格是否显示左下角开始的斜线

      isShowTblLine_: false, // 是否显示表格线
      isCanNotDelTbl_: false, // 是否禁止删除该表格
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 524,
    },
    // editor: {},
    isShowUpLine: {
      type: Boolean,
      default: false,
    }, // 单元格是否显示上边线
    isShowRightLine: {
      type: Boolean,
      default: false,
    }, // 单元格是否显示右边线
    isShowDownLine: {
      type: Boolean,
      default: false,
    }, // 单元格是否显示下边线
    isShowLeftLine: {
      type: Boolean,
      default: false,
    }, // 单元格是否显示左边线
    isShowSlashUp: {
      type: Boolean,
      default: false,
    }, // 单元格是否显示左上角开始的斜线
    isShowSlashDown: {
      type: Boolean,
      default: false,
    }, // 单元格是否显示左下角开始的斜线
    isCanNotDelTbl: {
      type: Boolean,
      default: false,
    },
    isShowTblLine: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    show: {
      handler(newValue) {
        if (newValue) {
          this.isShowUpLine_ = this.isShowUpLine;
          this.isShowRightLine_ = this.isShowRightLine;
          this.isShowDownLine_ = this.isShowDownLine;
          this.isShowLeftLine_ = this.isShowLeftLine;
          this.isShowSlashUp_ = this.isShowSlashUp;
          this.isShowSlashDown_ = this.isShowSlashDown;

          this.isCanNotDelTbl_ = this.isCanNotDelTbl;
          this.isShowTblLine_ = this.isShowTblLine;
        }
      },
      immediate: true,
    },
  },
  methods: {
    submit() {
      this.$emit(
        "submit",
        this.isShowUpLine_,
        this.isShowRightLine_,
        this.isShowDownLine_,
        this.isShowLeftLine_,
        this.isShowSlashUp_,
        this.isShowSlashDown_,
        this.isCanNotDelTbl_,
        this.isShowTblLine_
      );
    },
    cancel() {
      this.$emit("cancel");
    },
  },
  components: {
    modal,
  },
};
</script>

<style lang="less" scoped></style>
