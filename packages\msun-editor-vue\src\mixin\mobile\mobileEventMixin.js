import moment from "moment";

const mobileEventMixin = {
  data() {
    return {
      firstTouch: false, // 是否第一次触摸
      mobileFloatBallText: "编辑",
      firstTouchTime: 0, // 第一次触摸的时间
      DBL_TOUCH_INTERVAL_TIME: 300, // 双击间隔时间
      touchTimer: null,
      initTouchStartX: 0, //
      initTouchStartY: 0, // 这个是在 move 的过程中不变的值 为了惯性滚动
      touchStartX: 0, // touch 事件 点击的起点 x
      touchStartY: 0, // touch 事件 点击的起点 y
      touchStartTime: 0, // 开始触摸的时间
      animationFrameTranslate: null,
      animationFrameScale: null,
      // 缩放 ↓

      currentScale: 1,
      initScale: -1,
      initOffsetX: 0, // 初始的 offsetX 值
      touchPoints: {},
      lastTestData1: 0, // 最后存一下是因为我放大之后就不能单指滚动了.
      lastTestData2: 0,
      initScrollTop: 0, // 缩放
      //
      initialDistance: 0, // 初始两指间的距离
      currentDistanceBetweenFingers: 0, // 两指间的距离 初始值为 0
      fingerPoint: { x: 0, y: 0 }, // 手指落在屏幕上的位置 就是相对于画板左上角原点的距离
      // offsetX: 0,
      // offsetY: 0, // 他有最大最小值(最大不能超过配置的外上边距，最小不能小于所有页的高度加上所有页外边距的高度，这是负值)
      offset_y: 0,
      step: 0.1, // 缩放率 相当于 步幅
      // 缩放 ↑
      // 拖拽
      targetX: 0, // 手指落在画板上的的点位
      targetY: 0, // 手指落在画板上的的点位
      dragOriginX: 0, // 手指落在画板上那一刻的 画布偏移量
      dragOriginY: 0, // 手指落在画板上那一刻的 画布偏移量
      multipleFingers: false,
      lastTouchTime: 0, // 双击事件用 每次触摸屏幕保存的时间
      windowHeight: 0, // 记录窗口高度
      maxOffsetY: 0,
      minOffsetY: 0,
      inputMethodPopupHeight: -1, // 输入法弹窗的高度
      difference: 0, // 原始位置跟放大之后位置的差值
      movement: 0, // 缩小时 x 应该移动的距离 计算出个死值 缩小过程中不变
      touchPointX: 0,
      touchPointY: 0,
      pointDown: [0, 0],
      mobileChange: false,
      moveDistance: 0, //移动距离
      mobilePosition: {
        left: "0px",
        top: "0px",
      },
      mobileBtnData: [
        {
          name: "复制",
          type: "string",
          children: [],
        },
        {
          name: "粘贴",
          type: "string",
          children: [],
        },
        {
          name: "剪切",
          type: "string",
          children: [],
        },
      ],
    };
  },
  mounted() {
    window.addEventListener("resize", () => {
      if (!this.instance) return;
      const { editor } = this.instance;
      if (this.inputMethodPopupHeight === -1) {
        this.inputMethodPopupHeight =
          parseFloat(editor.init_canvas.style.height) - window.innerHeight;
      }
      editor.init_canvas.style.height = window.innerHeight + "px";
      editor.init_canvas.height =
        window.innerHeight * editor.config.devicePixelRatio;

      const { max, min } = this.getOffsetYRange();
      if (editor.offsetY < min) {
        editor.offsetY = min;
      }
      if (editor.offsetY > max) {
        editor.offsetY = max;
      }

      editor.refreshDocument();
    });
    this.$nextTick(() => {
      this.initMobileWin();
    });
  },
  methods: {
    initMobileWin(isTest) {
      if (!this.instance) return;
      const { max, min } = this.getOffsetYRange();
      this.maxOffsetY = max;
      this.minOffsetY = min;
      const { editor } = this.instance;
      const width = parseInt(String(editor.init_canvas.style.width));
      let initialOffsetX = (width - width * editor.viewScale) / 2;
      this.initOffsetX = initialOffsetX;
      editor.offsetX = initialOffsetX;
      this.currentScale = editor.viewScale;
      const msg = `"max,min>>>>>", ${max}, ${min} "canvasWidth>>>>>", ${width} "viewScale>>>>>", ${editor.viewScale} "offsetX>>>>>", ${editor.offsetX}`;
      if (isTest) {
        this.$editor.info(msg);
      }
      editor.refreshDocument();
    },
    // 获取设备类型 是手机还是平板
    getDeviceType() {
      // 获取屏幕宽度
      const screenWidth =
        window.innerWidth ||
        document.documentElement.clientWidth ||
        document.body.clientWidth;

      // 判断是否是手机
      const isMobile = screenWidth < 768;

      // 判断是否是平板电脑
      const isTablet = screenWidth >= 768 && screenWidth < 1024;
      if (isMobile) return "mobile";
      if (isTablet) return "tablet";
    },
    getDistanceOfTwoPoints(x1, y1, x2, y2) {
      return Math.hypot(x2 - x1, y2 - y1);
    },
    // 缩放 ↓
    getOffsetYByRange(value) {
      const { max: maxValue, min: minValue } = this.getOffsetYRange();
      this.maxOffsetY = maxValue;
      this.minOffsetY = minValue;
      if (value <= maxValue && value >= minValue) {
        return value;
      } else {
        if (value > maxValue) {
          return maxValue;
        } else if (value < minValue) {
          return minValue;
        }
      }
    },
    getOffsetYRange() {
      const { editor } = this.instance;
      const max = 0;
      let min =
        ((editor.pages[editor.pages.length - 1].bottom +
          editor.config.page_margin_bottom) *
          editor.viewScale -
          parseInt(editor.init_canvas.style.height)) *
        -1;
      min = Math.ceil(min);
      return {
        max,
        min,
      };
    },
    getOffsetXRange() {
      // 经测试 editor.page_size.height + editor.config.page_margin_bottom 和 editor.pages[editor.pages.length - 1].bottom 结果是相等的
      const { editor } = this.instance;
      let min =
        (editor.page_size.width + editor.page_left) * editor.viewScale -
        parseInt(editor.init_canvas.style.width) +
        10;
      min *= -1;
      let max = 0;
      if (this.getDeviceType === "tablet") {
        max =
          this.initOffsetX * editor.viewScale -
          editor.page_left * editor.viewScale +
          10;
      } else {
        max = Math.abs(editor.page_left * editor.viewScale) + 10;
      }
      return {
        max,
        min,
      };
    },
    calculateDistance(touches) {
      if (touches.length < 2) return 0;
      return this.getDistanceOfTwoPoints(
        touches[0].clientX,
        touches[0].clientY,
        touches[1].clientX,
        touches[1].clientY
      );
    },
    // 缩放 ↑
    mobileDatePickerConfirm(time) {
      const date = moment(time).format("YYYY-MM-DD HH:mm:ss");
      this.replaceFieldText(date, "date");
      this.mobileDatePickerShow = false;
    },
    mobileDatePickerCancel() {
      this.mobileDatePickerShow = false;
    },
    // 切换是否添加输入框 也就是控制是否能进行编辑
    toggleInputDOM() {
      const { editor } = this.instance;
      const input = editor.getInputDOM();
      if (input) {
        editor.removeInputDOM();
        editor.render();
        this.mobileFloatBallText = "编辑";
      } else {
        editor.mountInputDOM();
        this.mobileBtnData = [
          {
            name: "复制",
            type: "string",
            children: [],
          },
          {
            name: "粘贴",
            type: "string",
            children: [],
          },
          {
            name: "剪切",
            type: "string",
            children: [],
          },
        ];
        this.mobileFloatBallText = "取消";
      }
      editor.internal.VL.is_mobile_selection = false;
      editor.internal.VL.is_mobile_button = false;
      editor.internal.is_mobile_edit = false;
      editor.selection.clearSelectedInfo();
    },

    showMsg(msg) {
      const div = document.createElement("div");
      div.style.padding = "10px 10px";
      div.style.backgroundColor = "gray";
      div.style.position = "fixed";
      div.style.top = "20px";
      div.style.left = "50%";
      div.style.transform = "translate(-50%, 0)";
      div.style.textAlign = "center";
      div.innerHTML = msg;
      document.body.appendChild(div);
      setTimeout(() => {
        div.remove();
      }, 1000);
    },
    getFingerMiddlePoint(touches) {
      if (touches.length < 2) return;
      const p0 = touches[0];
      const p1 = touches[1];
      const maxX = Math.max(p0.clientX, p1.clientX);
      const minX = Math.min(p0.clientX, p1.clientX);
      const maxY = Math.max(p0.clientY, p1.clientY);
      const minY = Math.min(p0.clientY, p1.clientY);
      return {
        x: minX + (maxX - minX) / 2,
        y: minY + (maxY - minY) / 2,
      };
    },
    // 给缩小准备数据
    recordNeedData() {
      const { editor } = this.instance;
      this.difference = this.initOffsetX - editor.offsetX;
      this.movement = (this.difference / editor.viewScale) * 0.2; // 0.2 是一点点测出来的
    },
    getButtonPosition(editor) {
      const startInfo = editor.getElementByModelPath(
        [...editor.selection.focus],
        editor
      );
      const viewPath = editor.modelPath2viewPath(editor.selection.focus);
      const xy = editor.getElementAbsolutePositionByViewPath(viewPath);
      const result = editor.compareAnchorAndFocusIsBig(
        editor.selection.anchor,
        editor.selection.focus
      );
      let offsetX = (-48 * this.mobileBtnData.length) / 2;
      let offsetY = 20;
      let height = startInfo.row.height;

      if (result === "anchor") {
        offsetX = 0;
      }
      if (editor.selection.selected_para_info.rows.length > 1) {
        if (result === "anchor") {
          height = 0;
          offsetY = -45;
        }
      }

      this.mobilePosition = {
        left: xy.x * editor.viewScale + editor.offsetX + offsetX + "px",
        top:
          (xy.y + height) * editor.viewScale +
          editor.offsetY +
          offsetY +
          this.$refs.content.offsetTop +
          "px",
      };
      if (editor.internal.is_mobile_edit) {
        this.mobilePosition = {
          left: xy.x * editor.viewScale + editor.offsetX + offsetX + "px",
          top:
            xy.y * editor.viewScale +
            editor.offsetY -
            35 +
            this.$refs.content.offsetTop +
            "px",
        };
      }
      if (
        xy.x * editor.viewScale - offsetX >
        (editor.page_left + editor.page_size.width) * editor.viewScale
      ) {
        //10是输入框的两边的padding，预留了两个像素
        this.mobilePosition.left =
          (editor.page_left + editor.page_size.width) * editor.viewScale +
          editor.offsetX +
          2 * offsetX -
          12 +
          "px";
      }
      if (parseInt(this.mobilePosition.left) < 0) {
        this.mobilePosition.left = 0 + "px";
      }
    },
    mobileBtnClick(name) {
      if (name === "复制") {
        this.editor.selection.copy();
      } else if (name === "粘贴") {
        this.editor.paste(false);
      } else if (name === "剪切") {
        this.editor.cut();
      } else if (name === "删除") {
        this.editor.delete_backward();
      }
      this.editor.internal.VL.is_mobile_button = false;
    },
    boundTouchEvent() {
      if (!this.instance) return;
      // 移动端处理 ↓
      const { editor } = this.instance;
      const canvasDOM = document.getElementById(this.editorId);
      if (!canvasDOM || !editor) return;

      // 设置一个定时器变量，以便稍后取消定时器
      let timeout = null;
      canvasDOM.addEventListener(
        "touchstart",
        (ev) => {
          if (editor.isIOS()) {
            ev.preventDefault(); // 这个阻止默认行为 有个副作用 就是编辑模式下 关掉输入法以后再点击页面输入法不弹窗了 需要判断是否是苹果系统
          }
          clearInterval(this.touchTimer);
          cancelAnimationFrame(this.animationFrameTranslate);
          this.moveDistance = 0;
          if (this.mobileFloatBallText === "取消") {
            editor.internal.is_mobile_edit = true;
          }
          this.pointDown = [ev.touches[0].clientX, ev.touches[0].clientY];
          editor.internal.VL.is_mobile_button = false;
          if (editor.internal.VL.is_mobile_selection) {
            const event = editor.internal.pointer_down_ev;
            const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
            const result = editor.isInMobileEdit(x, y);
            if (result) {
              editor.hold_mouse = true;
              editor.internal.is_drag_mobile = true;
              const anchor = editor.selection.anchor;
              const focus = editor.selection.focus;
              const compareResult = editor.compareAnchorAndFocusIsBig(
                anchor,
                focus
              );
              if (
                (result === "start" && compareResult === "focus") ||
                (result === "end" && compareResult === "anchor")
              ) {
                const path = editor.selection.anchor;
                editor.selection.anchor = editor.selection.focus;
                editor.selection.focus = path;
              }
            }
            editor.pointer_down(x, y, event.shiftKey);
          }
          // 因为内容改动光标位置要始终在视口内 所以有改动 editor.offsetX 和 editor.offsetY 的值 这里要更正
          // TODO 但是 offsetX 和 offsetY 是有操作区间的 这个后边应该考虑下如何处理
          // this.$editor.warning(`${editor.config.page_padding_top}, 内边距`);
          if (ev.touches.length > 1) {
            this.mobileChange = true;
            // 记录手指触摸的点位以及两指间的距离
            this.fingerPoint = this.getFingerMiddlePoint(ev.touches);
            this.initialDistance = this.calculateDistance(ev.touches);
            this.multipleFingers = true;
          } else {
            if (!this.VL.is_mobile_selection) {
              this.mobileBtnData = [
                {
                  name: "粘贴",
                  type: "string",
                  children: [],
                },
              ];
              if (editor.internal.VL.is_mobile_button) {
                this.getButtonPosition(editor);
              }

              timeout = window.setTimeout(() => {
                let y =
                  ev.touches[0].clientY - canvasDOM.getBoundingClientRect().top;

                let x = ev.touches[0].clientX;

                x = x / editor.viewScale - editor.offsetX / editor.viewScale;
                y = y / editor.viewScale - editor.offsetY / editor.viewScale;

                const focusElement = editor.getContainerInfoByPointHelper(x, y);
                if (!focusElement.element) {
                  if (editor.internal.is_mobile_edit) {
                    editor.internal.VL.is_mobile_button = true;
                    this.getButtonPosition(editor);
                  }
                  return;
                }

                editor.setMobileSelection(focusElement);
                if (this.mobileFloatBallText === "编辑") {
                  this.mobileBtnData = [
                    {
                      name: "复制",
                      type: "string",
                      children: [],
                    },
                  ];
                } else {
                  this.mobileBtnData = [
                    {
                      name: "复制",
                      type: "string",
                      children: [],
                    },
                    {
                      name: "粘贴",
                      type: "string",
                      children: [],
                    },
                    {
                      name: "剪切",
                      type: "string",
                      children: [],
                    },
                    {
                      name: "删除",
                      type: "string",
                      children: [],
                    },
                  ];
                  editor.internal.is_mobile_edit = false;
                }
                editor.internal.VL.is_mobile_button = true;
                this.getButtonPosition(editor);
                editor.hold_mouse = true;
                editor.internal.is_drag_mobile = true;

                return;
              }, 150);
            }

            const currentX = ev.touches[0].clientX;
            const currentY = ev.touches[0].clientY;
            const d = this.getDistanceOfTwoPoints(
              currentX,
              currentY,
              this.touchPointX,
              this.touchPointY
            );
            this.touchPointX = currentX;
            this.touchPointY = currentY;
            // 双击编辑
            const now = Date.now();
            if (
              now - this.lastTouchTime < this.DBL_TOUCH_INTERVAL_TIME &&
              d < 15
            ) {
              if (!editor.getInputDOM()) {
                this.toggleInputDOM();
              }
            }
            this.lastTouchTime = Date.now();
          }
          if (this.initScale === -1) {
            this.initScale = editor.viewScale;
          }
          this.mobilePromptShow = false;

          this.touchStartTime = Date.now();

          this.targetX = ev.touches[0].clientX;
          this.targetY = ev.touches[0].clientY;

          this.dragOriginX = editor.offsetX;
          this.dragOriginY = editor.offsetY;
        },
        { passive: false }
      );

      canvasDOM.addEventListener(
        "touchmove",
        (ev) => {
          ev.preventDefault();
          const spacing = this.getDistanceOfTwoPoints(
            this.pointDown[0],
            this.pointDown[1],
            ev.touches[0].clientX,
            ev.touches[0].clientY
          );
          if (spacing < 3) return;
          clearTimeout(timeout);
          this.mobileChange = true;
          const event = editor.internal.pointer_move_ev;
          const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
          editor.pointer_move(x, y);
          const input = editor.getInputDOM();
          if (input) {
            editor.removeInputDOM();
            editor.render();
          }
          if (editor.internal.VL.is_mobile_selection) {
            const canvasDOMRect = editor.init_canvas.getBoundingClientRect();
            const y = ev.touches[0].clientY;
            let height = 0;
            if (y >= canvasDOMRect.height - 100) {
              const max =
                ((editor.pages[editor.pages.length - 1].bottom +
                  editor.config.page_margin_bottom) *
                  editor.viewScale -
                  parseInt(editor.init_canvas.style.height)) *
                  -1 +
                100 * editor.viewScale;

              height = Math.abs(y - canvasDOMRect.height + 100);
              if (editor.offsetY <= max) {
                editor.render();
                return;
              }
            } else if (y <= 20) {
              if (editor.offsetY >= 0) {
                editor.render();
                return;
              }
              height = Math.abs(y);
            }
            editor.offsetY -= height / 3;
            editor.render();
            return;
          }
          // 如果使用 ev.touches 就会造成两个手指还在屏幕上的时候 .length 就为 1 了
          if (ev.changedTouches.length > 1) {
            this.currentDistanceBetweenFingers = this.calculateDistance(
              ev.changedTouches
            );
            const moveDistance =
              this.currentDistanceBetweenFingers - this.initialDistance;
            // clearInterval(this.touchTimer);
            // cancelAnimationFrame(this.animationFrameTranslate);
            // 缩放 ↓
            if (Math.abs(moveDistance) > 2) {
              this.animationFrameScale = requestAnimationFrame(() => {
                let x = this.fingerPoint.x - editor.offsetX;
                let y = this.fingerPoint.y - editor.offsetY;

                let newOffsetX = (x / editor.viewScale) * this.step;
                let newOffsetY = (y / editor.viewScale) * this.step;
                if (moveDistance > 0) {
                  if (this.currentScale >= 2.5) {
                    this.$editor.info(`已放大到最大值`);
                    newOffsetX = 0;
                    newOffsetY = 0;
                    this.currentScale -= this.step;
                  }
                  editor.offsetX -= newOffsetX;
                  editor.offsetY -= newOffsetY; // 加上范围限制 那么在最后一页末尾缩放的时候位置是不准的
                  // const value = editor.offsetY - newOffsetY;
                  // const res = this.getOffsetYByRange(value);
                  // if (res !== undefined) {
                  //   editor.offsetY = res;
                  // }
                  this.currentScale += this.step;
                  this.recordNeedData();
                } else if (this.currentScale > this.initScale) {
                  this.recordNeedData();
                  if (
                    Math.abs(this.initOffsetX - editor.offsetX) > this.movement
                  ) {
                    editor.offsetX += this.movement;
                  } else {
                    editor.offsetX = this.initOffsetX;
                  }

                  const value = editor.offsetY + newOffsetY;
                  const res = this.getOffsetYByRange(value);
                  if (res !== undefined) {
                    editor.offsetY = res;
                  }
                  this.currentScale -= this.step;
                  if (this.currentScale <= this.initScale) {
                    this.$editor.info(`已缩小到最小值`);
                    editor.offsetX = this.initOffsetX;
                    this.currentScale = this.initScale;
                  }
                }

                this.initialDistance = this.currentDistanceBetweenFingers;
                editor.viewScale = this.currentScale;
                editor.render();
                // editor.setViewScale(this.currentScale);
              });
            }

            // 缩放 ↑
          } else if (ev.touches.length === 1 && !this.multipleFingers) {
            // 加 multipleFingers 的判断，是因为多指缩放完了以后 只抬起一根手指 页面会偏移
            // 使用 changedTouches 有问题 页面会位移
            this.animationFrameTranslate = requestAnimationFrame(() => {
              if (editor.viewScale <= this.initScale) {
                editor.offsetX = this.initOffsetX;
              } else {
                editor.offsetX =
                  this.dragOriginX + (ev.touches[0].clientX - this.targetX);
              }

              const newOffsetY =
                this.dragOriginY + (ev.touches[0].clientY - this.targetY);
              const { max, min } = this.getOffsetYRange();
              this.maxOffsetY = max;
              this.minOffsetY = min;
              if (
                newOffsetY >= this.minOffsetY &&
                newOffsetY <= this.maxOffsetY
              ) {
                const res = this.getOffsetYByRange(newOffsetY);
                if (res !== undefined) {
                  editor.offsetY = res;
                }
              }
              const { max: maxX, min: minX } = this.getOffsetXRange();
              if (editor.offsetX > maxX && editor.viewScale > this.initScale) {
                editor.offsetX = maxX;
              }
              if (editor.offsetX < minX && editor.viewScale > this.initScale) {
                editor.offsetX = minX;
              }
              this.recordNeedData();
              editor.render();
            });
          }
        },
        { passive: false }
      );

      canvasDOM.addEventListener("touchend", (ev) => {
        clearTimeout(timeout);
        this.moveDistance = this.targetY - ev.changedTouches[0].clientY;
        if (
          !this.mobileChange &&
          !editor.internal.VL.is_mobile_selection &&
          Math.abs(this.moveDistance) < 10
        ) {
          if (this.mobileFloatBallText === "取消") {
            // 设置一个定时器，在1000毫秒后执行log操作
            const event = editor.internal.pointer_down_ev;
            const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
            const { field } = editor.getElementByPoint(x, y);
            if (field && (field.type === "date" || field.type === "select")) {
              const input = editor.getInputDOM();
              if (input) {
                editor.removeInputDOM();
                editor.render();
              }
            } else {
              editor.mountInputDOM();
              editor.pointer_down(x, y, event.shiftKey);
            }
            // event.preventDefault();

            this.getButtonPosition(editor);
          }
          if (this.curClickInfo) {
            this.handleFieldClick();
          }
        }
        //处理文本域点击事件  只有左键点击生效
        // (当点击任意功能按钮调用了编辑器接口时会使编辑器获得焦点，此时curClickInfo还未赋值就触发了pointerUp事件，所以需增加curClickInfo判断)

        const event = editor.internal.pointer_up_ev;
        const { x, y } = editor.getNeedXYbyXY(event.offsetX, event.offsetY);
        editor.pointer_up(x, y);

        if (editor.internal.VL.is_mobile_selection) {
          this.getButtonPosition(editor);
          return;
        }
        cancelAnimationFrame(this.animationFrameTranslate); // 必须在这里取消 否则值还在变
        cancelAnimationFrame(this.animationFrameScale); // 这个没测出什么效果 但是取消应该是好的
        const endTime = Date.now();
        const durition = endTime - this.touchStartTime;
        if (
          durition < 550 &&
          Math.abs(this.moveDistance) > 10 &&
          !this.multipleFingers
        ) {
          const coefficient = 40; // 系数
          const velocity = this.moveDistance / durition; // 速度
          let distance = coefficient * velocity; // 最终再滚动的距离 手指往上滑动为正 往下滑动为负

          const greaterThanZero = distance > 0;
          this.touchTimer = setInterval(() => {
            // 加定时器是为了更加丝滑 有加速度
            if (greaterThanZero) {
              // 往上滑动 distance 为正值
              distance -= 2;
              distance < 0 && clearInterval(this.touchTimer);
              // this.offsetY 应该越来越小
            } else {
              // 往下滑动
              distance += 2;
              distance > 0 && clearInterval(this.touchTimer);
              // this.offsetY 应该越来越大
            }
            const { max, min } = this.getOffsetYRange();
            this.maxOffsetY = max;
            this.minOffsetY = min;
            const value = editor.offsetY - distance;
            if (value >= this.minOffsetY && value <= this.maxOffsetY) {
              const res = this.getOffsetYByRange(value);
              if (res !== undefined) {
                editor.offsetY = res;
              }
            }
            this.recordNeedData();
            editor.render();
          }, 14);
        }
        // else if (
        //   durition > 300 &&
        //   Math.abs(this.initTouchStartY - ev.changedTouches[0].clientY) < 3 &&
        //   Math.abs(this.initTouchStartX - ev.changedTouches[0].clientX) < 3
        // ) {
        //   const mobilePromptDom = this.$refs.mobilePromptRef.$el;
        //   if (mobilePromptDom) {
        //     this.mobilePromptShow = true;
        //     this.$nextTick(() => {
        //       // 如果没有 $nextTick 改为 True 之后马上获取的宽高都为 0
        //       const rectObject = mobilePromptDom.getBoundingClientRect();
        //       mobilePromptDom.style.top =
        //         ev.changedTouches[0].clientY - rectObject.height - 2 + "px";
        //       mobilePromptDom.style.left =
        //         ev.changedTouches[0].clientX - rectObject.width / 2 + "px";
        //     });
        //   }
        // }
        this.touchStartX = 0;
        this.initTouchStartY = 0;
        this.initTouchStartX = 0;
        // 放到最后 解决多指快速放下又抬起的时候触发页面滚动逻辑 - 这样多指就不能进行页面拖动和滚动了，可以后期再优化，先解决要紧的问题
        if (ev.touches.length === 0) {
          this.mobileChange = false;
          this.multipleFingers = false;
          this.recordNeedData();
        }
      });
      // const that = this;
      canvasDOM.addEventListener("click", async function () {
        try {
          const granted = await navigator.permissions.query({
            name: "clipboard-read",
          });
          if (granted.state === "prompt") {
            await navigator.clipboard.readText();
          }
        } catch (error) {
          // that.$editor.info(11111, error);
        }
      });
      // 移动端处理 ↑
    },
  },
};
export default mobileEventMixin;
