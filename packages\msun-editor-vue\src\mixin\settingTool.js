const settingToolMixin = {
  data() {
    return {
      isOpenSettingTool: false,
    };
  },
  methods: {
    openSettingTool() {
      this.isOpenSettingTool = true;
    },
    closeSettingTool() {
      this.isOpenSettingTool = false;
    },
    changeEditorConfig(changeConfig) {
      const config = this.instance.editor.config;
      this.instance.editor.eyeProtectionMode(changeConfig.openEyeProtectMode);
      for (let key in changeConfig) {
        this.setLocalSystemConfig(key, changeConfig[key]);
      }
      // 单独保存一下护眼模式的配置
      if (changeConfig.openEyeProtectMode) {
        this.setLocalSystemConfig("page_color", "rgb(199,237,204)");
      } else {
        this.setLocalSystemConfig("page_color", "#ffffff");
      }
      //单独处理一下默认的字体字
      if (config.default_font_style) {
        config.default_font_style.family = changeConfig.family;
        config.default_font_style.height = changeConfig.height;
        this.instance.editor.change_font_style({
          family: changeConfig.family,
          height: changeConfig.height,
        });
      }

      for (let key in config) {
        if (key in changeConfig) {
          if (key !== "height" && key !== "family")
            config[key] = changeConfig[key];
        }
      }
      this.instance.editor.reInitConfig(config);
    },
  },
};
export default settingToolMixin;
