import Editor from "./Editor";
import EditorHelper from "./EditorHelper";

import handlerEvent from "./HandlerEvent";
import { getMaxScrollVal } from "./Helper";
import { Path } from "./Path";
import { allowEditing, getImageSrcWithWhiteBgc } from "./Utils";

export default class InputAdapter {
  target: HTMLTextAreaElement;
  timer: any = null;

  static onCompositionInputHelper(editor: Editor, ime_start: Path | null, text: string, isEnd: boolean) {
    // 设置选区然后删除选区，并不需要往历史堆栈里边记录，因为撤销的时候不需要展示拼音了
    const ime_end = [...editor.selection.end];
    if (ime_start) editor.selection.setSelectionByPath(ime_start, ime_end, "model_path"); // 设置选区是为了 insertText 的时候会把选区内容删掉，否则展示拼音的时候会重复展示字母
    if (!allowEditing(editor)) return false;
    const font = editor.contextState.getFontState();
    editor.contextState.setFontState(font);
    if (isEnd) {
      if (editor.config.useLetterPlaceholder) {
        return EditorHelper.insertText(editor, text, "ime_input");
      } else {
        editor.delSectionRecordStack(false);
        return editor.insertText(text, "ime_input");
      }
    } else {
      if (editor.config.useLetterPlaceholder) {
        return EditorHelper.insertText(editor, text, "init");
      } else {
        return editor.insertText(text, "init");
      }
    }
  }

  constructor(target: HTMLTextAreaElement, editor: Editor, size: any) {
    this.target = target;
    editor.inputDOM = target;
    this._editor = editor;
    this.size = size;
  }

  key_down(event: KeyboardEvent) {
    handlerEvent("onKeyDown", event, this._editor);
  }

  key_up(event: KeyboardEvent) {
    handlerEvent("onKeyUp", event, this._editor);

    this.target.focus();
    this.setTargetPosition();
  }

  dblclick(event: MouseEvent) {
    handlerEvent("dblClick", event, this._editor);
  }

  getImageFileListFromClipboard(event: ClipboardEvent): File[] {
    const items = event.clipboardData?.items;
    const types = event.clipboardData?.types;
    // 先这样判断 因为判断是否从 Excel 中来的图片 暂时不好判断 先这样处理 解决从 Excel 中复制图片到编辑器中有图片的问题
    if (types && types.length) {
      const allIsImage = types.every(type => type === "Files");
      if (!allIsImage)  return [];
    }
    const list: File[] = [];
    if (items && items.length) {
      // 检索剪切板 items
      for (let i = 0; i < items.length; i++) {
        if (items[i].type.startsWith("image")) {
          const imageFile = items[i].getAsFile();
          imageFile && list.push(imageFile);
        }
      }
    }
    return list;
  }

  /**
   * 监听的 input 上的粘贴事件
   * 该方法只处理 从 Excel 复制来的数据 还有图片 其他的全部都 return 掉
   * @param event ClipboardEvent
   */
  paste(event: ClipboardEvent) {
    const types = event.clipboardData?.types;
    if (types?.length === 2) return;
    // 先这样判断 因为判断是否从 Excel 中来的图片 暂时不好判断 先这样处理 解决从 Excel 中复制图片到编辑器中有图片的问题
    if (types && types.length) {
      const allIsImage = types.every(type => type === "Files");
      // @ts-ignore
      if (!allIsImage && types.includes("Files"))  {
        const data = event.clipboardData;
        const html = data.getData("text/html");
        const rawData = this._editor.event.emit("html2RawData", html, "Excel");
        if (rawData && rawData !== "origin") {
          const { isOnlyCell } = this._editor.selection;
          const rawDataObj = JSON.parse(JSON.stringify(rawData));
          if (isOnlyCell && rawDataObj.content.length === 1 && rawDataObj.content[0].type === "table") {
            const { selected_cells } = this._editor.selection;
            const baseRawData = JSON.parse(JSON.stringify(rawDataObj));
            selected_cells[0].cell.navigateToThisCell("start");
            const focus = [...this._editor.selection.focus];
            this._editor.makeHistoryStackAble(() => {
              for (let i = 0; i < selected_cells.length; i++) {
                if (rawDataObj.content[0].cells[i]?.children) {
                  baseRawData.content = rawDataObj.content[0].cells[i].children;
                  focus[1] += (i === 0 ? 0 : 1);
                  this._editor.selection.anchor = this._editor.selection.focus = focus;
                  EditorHelper.insertTemplateData(
                    this._editor,
                    baseRawData,
                    false,
                    false,
                    true,
                    {}
                  );
                } else {
                  break;
                }
              }
            })
            return;
          }
          return this._editor.insertTemplateData(rawData);
        }
        return
      }
    }
    
    const imageList = this.getImageFileListFromClipboard(event);
    if (imageList.length > 0) {
      for (const imageFile of imageList) {
        const url = window.URL.createObjectURL(imageFile);
        const image = new Image();
        image.src = url;
        if (this._editor.config.localImageMaxSize !== undefined && imageFile.size / 1024 / 1024 > this._editor.config.localImageMaxSize) {
          this._editor.compressImage(url, this._editor.page_size.width, this._editor.page_size.heighit).then(res => {
            if (typeof res === "string") {
              image.src = res;
              image.onload = () => {
                const src = getImageSrcWithWhiteBgc(image);
                this._editor.insertImage(src);
              };
            }
          })
        } else {
          image.onload = () => {
            const src = getImageSrcWithWhiteBgc(image);
            this._editor.insertImage(src);
          };
        }
      }
    }
  }

  // 处理手写输入
  async handleHandwritingInput() {
    const text = await navigator.clipboard.readText();
    if (text) {
      this._editor.insertText(text);
    }
  }

  wheel(event: WheelEvent) {
    event.preventDefault();
    requestAnimationFrame(() => {
      // 添加防抖 优化 设置目标位置 ↓
      if (this.timer) {
        clearTimeout(this.timer);
        this.timer = null;
      }
      this.timer = setTimeout(() => {
        this.setTargetPosition();
      }, 200);
      // 添加防抖 优化 设置目标位置 ↑
      this._editor.internal.scroll(event.deltaY);
      const maxScrollVal = getMaxScrollVal(this._editor);
      const hasScrollBar = ((this._editor.page_size.height + this._editor.config.page_margin_bottom) *
          this._editor.pages.length +
          this._editor.config.editor_padding_top) *
        this._editor.viewScale >=
        this._editor.init_canvas.height / this._editor.config.devicePixelRatio;
      // 编辑器滚动
      this._editor.event.emit("scroll", event, 
        { 
          overTop: this._editor.scroll_top <= 0, 
          overBottom: this._editor.scroll_top >= maxScrollVal, 
          maxScrollVal,
          hasScrollBar
        }
      );
    });
  }

  pointer_down(event: PointerEvent) {
    
    handlerEvent("onPointerDown", event, this._editor);
   
    this.setTargetPosition();

    const target = event.target as HTMLCanvasElement;

    target.setPointerCapture(event.pointerId);
  }

  pointer_up(event: PointerEvent) {

    if (!this._editor.isMobileTerminal()) {
      this.target.focus();
      handlerEvent("onPointerUp", event, this._editor);
    } else {
      // this._editor.event.emit("pointerUp", event);
      this._editor.internal.pointer_up_ev = event
    }
    const target = event.target as HTMLCanvasElement;

    target.releasePointerCapture(event.pointerId);
  }

  pointer_move(event: PointerEvent) {
    // todo 防抖性能优化
    if (!this._editor.isMobileTerminal()) {
      handlerEvent("onPointerMove", event, this._editor);
    } else {
      // this._editor.event.emit("pointerMove", event);
      this._editor.internal.pointer_move_ev = event
    }
  }

  input(event: InputEvent) {
    handlerEvent("onInput", event, this._editor);
  }

  composition_start(event: CompositionEvent) {
    handlerEvent("onCompositionStart", event, this._editor);
  }

  composition_update(event: CompositionEvent) {
    handlerEvent("onCompositionUpdate", event, this._editor);
  }

  composition_end(event: CompositionEvent) {
    handlerEvent("onCompositionEnd", event, this._editor);

    this.target.value = "";
  }

  private setTargetPosition() {
    const offset = this._editor.internal.view_scale_offset;
    this.target.style.left = this._editor.internal.client_left + (this._editor.caret.x + offset) * this._editor.viewScale + 12 + "px";
    let top = (this._editor.internal.client_top +
      this._editor.caret.y -
      this._editor.scroll_top +
      this._editor.caret.height -
      12) * this._editor.viewScale;
    if (top > this.size.height * this._editor.config.devicePixelRatio - this._editor.caret.height) {
      this.target.style.top =
        this.size.height * this._editor.config.devicePixelRatio - this._editor.caret.height + "px";
    }
    const max_top = this._editor.init_canvas.clientHeight - 25;
    top = Math.abs(top);
    if (top > max_top) {
      this.target.style.top = max_top + "px";
    } else {
      this.target.style.top = top + "px";
    }
  }

  readonly _editor: Editor;
  private size: any;
}
