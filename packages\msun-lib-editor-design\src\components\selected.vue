<template>
  <div class="select">
    <div class="show-selected" ref="select" @click="inputClick">
      <div :style="{ width: width + 'px' }">{{ inputOption }}</div>
      <div class="triangle" v-if="!show"></div>
      <div class="downTriangle" v-if="show"></div>
    </div>
    <div class="select-bottom" v-if="show" ref="selected">
      <div
        v-for="(item, i) in data"
        :key="i"
        :class="index === i ? 'selected' : 'unselected'"
        :ref="item.option"
        @mousedown.prevent="changeStyle(i)"
      >
        {{ item.option }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "selectItem",
  props: {
    data: {
      type: Array,
      default: () => [],
    },
    width: {
      type: Number,
      default: 80,
    },
    initData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      show: false,
      inputOption: "",
      index: 0,
      inputValue: null,
    };
  },
  watch: {
    initData(item) {
      if (item) {
        this.inputOption = item.option;
        this.inputValue = item.value;
      }
    },
    immediate: true,
  },
  mounted() {
    this.inputOption = this.initData.option;
    document.addEventListener(
      "mouseup",
      (event) => {
        var selected = this.$refs.selected;
        var select = this.$refs.select;
        if (selected) {
          if (
            !selected.contains(event.target) &&
            !select.contains(event.target)
          ) {
            this.show = false;
          }
        }
      },
      false
    );
  },
  methods: {
    changeStyle(i) {
      this.index = i;
      this.inputOption = this.data[i].option;
      this.inputValue = this.data[i].value;
      this.show = false;
      this.$emit("selected", this.inputValue, this.inputOption);
    },
    inputChange(e) {
      for (let i = 0; i < this.data.length; i++) {
        if (e === this.data[i]) {
          this.changeStyle(i);
        }
      }
    },
    inputClick() {
      this.show = !this.show;
      for (let i = 0; i < this.data.length; i++) {
        if (this.inputOption === this.data[i].option) {
          this.index = i;
          break;
        }
      }
    },
  },
};
</script>

<style lang="less" scoped>
.select {
  // position: relative;
  line-height: 34px;
  align-items: center;
  height: 30px;
  margin: 0px 5px 0 5px;
  cursor: pointer;
  padding: 0 3px 0 3px;
}

.select /deep/ .ant-input {
  border: none;
  border-radius: 0;
  padding: 0 0 0 5px;

  color: #000;
}
.select /deep/ .ant-input:hover {
  background-color: rgb(244, 244, 244);
}
.select /deep/ .ant-input[disabled] {
  cursor: default;
}
.select-bottom {
  position: absolute;
  max-height: 300px;
  width: 120px;
  border: 1px solid rgb(217, 217, 217);
  overflow-y: auto;
  overflow-x: hidden;
  background-color: rgb(255, 255, 255);
  box-shadow: 0 0px 5px rgb(200, 200, 200);
  z-index: 99;
}
.selected {
  padding: 2px 3px 3px 10px;
  background-color: rgb(235, 235, 235);
  z-index: 99;
  cursor: pointer;
}
.select :hover {
  background-color: rgb(231, 247, 255);
}
.show-selected {
  display: flex;
  font-size: 14px;
  line-height: 34px;
  align-items: center;
  height: 30px;
  z-index: 99;
}
.unselected {
  padding: 3px 3px 3px 10px;
  background-color: rgb(255, 255, 255);
  cursor: pointer;
}
.triangle {
  margin: 0 auto;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-top: 4px solid rgb(124, 124, 124);
  margin-left: 8px;
}
.downTriangle {
  margin: 0 auto;
  border-left: 4px solid transparent;
  border-right: 4px solid transparent;
  border-bottom: 4px solid rgb(124, 124, 124);
  margin-left: 8px;
}
</style>
