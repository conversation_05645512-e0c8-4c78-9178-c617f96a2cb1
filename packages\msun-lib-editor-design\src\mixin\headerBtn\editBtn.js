const editBtnMixIn = {
  data() {
    return {
      editBtn: [
        {
          type: "textIcon",
          icon: "icon-chexiao",
          title: "撤销",
          func: this.revoke,
        },
        {
          type: "textIcon",
          icon: "icon-huifu",
          title: "重做",
          func: this.redoes,
        },
        {
          type: "textIcon",
          icon: "icon-jianqie",
          title: "剪切",
          func: this.cut,
        },
        {
          type: "textIcon",
          icon: "icon-fuzhi",
          title: "复制",
          func: this.copy,
        },
        {
          type: "textIcon",
          icon: "icon-jurassic_paste",
          title: "粘贴",
          func: this.paste,
        },
        {
          type: "textIcon",
          icon: "icon-714bianjiqi_chunwenbenniantie",
          title: "纯文本粘贴",
          func: this.pasteText,
        },
      ],
    };
  },
  methods: {
    cut() {
      const { editor } = this.instance;
      editor.cut();
    },
    copy() {
      this.instance.copy();
    },
    paste() {
      this.instance.paste();
    },
    pasteText() {
      this.instance.paste(false, true);
    },
  },
};
export default editBtnMixIn;
