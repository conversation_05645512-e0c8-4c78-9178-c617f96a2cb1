<template>
  <div class="editor_contrast_tip">
    <div class="editor_tip_show_contrast">
      <div class="editor_tip_info_contrast">
        {{ tip_content.userName }}&nbsp;&nbsp;&nbsp; {{ tip_content.option }}
      </div>
    </div>
    <div class="editor_tip_show_contrast">
      <div class="tip_info">{{ tip_content.date }}</div>
    </div>
  </div>
</template>

<script>
export default {
  name: "contrast_tip",
  props: {
    tip_content: { type: Object, default: () => {} },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="less" scoped>
.editor_tip_show_contrast {
  display: flex;
}

.editor_contrast_tip {
  position: absolute;
  color: rgb(53, 53, 53);
  // background-color: rgb(255, 255, 255);
  border-radius: 4px;
  box-shadow: 0px 0px 10px rgb(180, 221, 231);
  border: 1px solid #bcd6eb;
  padding: 5px 5px 5px 15px;
  min-width: 150px;
  opacity: 1;
  .valid.tip_title {
    line-height: 1.5 !important;
    color: black;
    display: block;
  }
}
.editor_tip_info_contrast {
  font-weight: 400;
  margin-right: 8px;
}
</style>
