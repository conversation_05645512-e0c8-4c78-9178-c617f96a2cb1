/**
 * 文本域相关逻辑混入
 * @type {{data(): {}, method: {}}}
 */

const imageEditingMixIn = {
  data() {
    return {
      showImageEditing: false,
      formulaType: null,
      imageMeta: {
        params: [],
      },
    };
  },
  components: {},
  methods: {
    showImageEditingWindow() {
      if (
        !this.curClickInfo.element ||
        !this.curClickInfo.element.meta ||
        this.curClickInfo.element.meta.non_editable ||
        this.curClickInfo.element.meta.placeholder ||
        Number(this.curClickInfo.element.meta.formula_type) >= 0 ||
        Number(this.curClickInfo.element.meta.sign_type) >= 0 ||
        this.curClickInfo.element.meta.serialNum ||
        (!this.curClickInfo.field && this.editor.view_mode === "form") ||
        (this.curClickInfo.field && this.curClickInfo.field.isReadonly) ||
        this.editor.selection.getFocusGroup()?.lock
      ) {
        return;
      }
      if (
        this.curClickInfo.element.src &&
        this.curClickInfo.element.src.startsWith("http")
      ) {
        this.$editor.info("在线图片不支持编辑");
      } else {
        this.showImageEditing = true;
      }
    },
    confirmImageEditingModal(element) {
      this.showImageEditing = false;
      this.editor.insertImage(element.img, element.meta);
      this.editor.focus();
    },
    closeImageEditingModal() {
      this.showImageEditing = false;
      this.editor.focus();
    },
  },
};
export default imageEditingMixIn;
