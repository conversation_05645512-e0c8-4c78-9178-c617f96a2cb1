/**
 * Copyright FunASR (https://github.com/alibaba-damo-academy/FunASR). All Rights
 * Reserved. MIT License  (https://opensource.org/licenses/MIT)
 */
/* 2022-2023 by z<PERSON><PERSON>,mali aihealthx.com */

var isRec = true;

// 连接; 定义socket连接类对象与语音对象
// var Uri = 'wss://ai.msunhis.com/MSUNVOICE/serverVue?appId=msun-lis-app&nlpToken=HmSSFEpc6dKOIabzN6QxvtM4cqzuqQQmOwjKI%2FE13CK657GjPdQ%2BPy8oMB5BVZqE%2BXM9MlUkSGbeMZTah3TxGhmAi%2BBr6650yxbobIQpZFS4bfEnsCBfhVTWoBtvpz%2FvEVdzyEdSQ9lb6GegI%2ByG4UmY4Idof2dYLDvIVRe19DU%3D';

var sampleBuf = new Int16Array();
var clearMsg = null;
var isfilemode = false; // if it is in file mode

function record() {
  rec.open(function () {
    rec.start();
  });
}

// 识别启动、停止、清空操作
function connect() {
  // 清除显示
  clear();

  //启动连接
  var ret = wsconnecter.wsStart();
  // 1 is ok, 0 is error
  if (ret == 1) {
    isRec = true;
    return 1;
  } else {
    return 0;
  }
}

function stop() {
  var chunk_size = new Array(5, 10, 5);
  var request = {
    chunk_size: chunk_size,
    wav_name: "h5",
    is_speaking: false,
    chunk_interval: 10,
    mode: "2pass",
    itn: false,
    hotwords: '{"阿里巴巴":20,"hello world":40}',
  };
  if (sampleBuf.length > 0) {
    wsconnecter.wsSend(sampleBuf);
    sampleBuf = new Int16Array();
  }
  wsconnecter.wsSend(JSON.stringify(request));

  // 控件状态更新

  isRec = false;
  if (isfilemode == false) {
    //wait 3s for asr result
    setTimeout(function () {
      wsconnecter.wsStop();
    }, 3000);

    rec.stop(
      function (blob, duration) {
        console.log(blob);
      },
      function (errMsg) {
        console.log("errMsg: " + errMsg);
      }
    );
  }
  // 停止连接
  clear();
}

function clear() {
  clearMsg && clearMsg();

  rec_text = "";
  offline_text = "";
}

function init(clear, get) {}

export default {
  init,
  connect,
  record,
  stop,
};
