const createFieldsMixIn = {
  data() {
    return {
      showCreateFields: false,
    };
  },
  methods: {
    openCreateFields() {
      this.showCreateFields = true;
      this.$refs.createFields.instance = this.instance;
    },
    createFieldsSubmit(param, multiple) {
      if (param.formula) {
        if (!this.instance.editor.document_meta.customField) {
          this.instance.editor.document_meta.customField = [];
        }
        this.instance.editor.document_meta.customField.push(param);
      }
      if (!multiple) {
        this.showCreateFields = false;
      }
      this.updateCustomFieldDataSet();
      this.customFieldList = this.instance.editor.document_meta.customField
        ? this.instance.editor.document_meta.customField
        : [];
    },
    createFieldsCancel() {
      this.showCreateFields = false;
    },
  },
};
export default createFieldsMixIn;
