<template>
  <a-popover :title="popoverTitle" :overlayStyle="{ zIndex: 99999 }">
    <template slot="content">
      <div :style="{ width }">
        <div>
          <b>& &nbsp;&nbsp;&nbsp;</b>
          <span>字符串拼接，比如： [杜甫]&'拼接内容' => 杜甫拼接内容</span>
        </div>
        <div>
          <b>SUM &nbsp;&nbsp;&nbsp;</b>
          <span>求和计算，计算结果会跟随字段的扩展而扩展</span>
        </div>
        <div>
          <b>公式嵌套使用 &nbsp;&nbsp;</b>
          <span>比如：IF(POWER(2,2)>2, '大于2','小于2')=>大于2</span>
        </div>
      </div>
    </template>
    <div style="margin-bottom: 5px; margin-left: 10px" class="edit-btn">
      <icon-common icon="icon-jieshi" style="cursor: pointer"></icon-common>
    </div>
  </a-popover>
</template>
<script>
import iconCommon from "./iconCommon.vue";
export default {
  data() {
    return {};
  },
  components: {
    iconCommon,
  },
  props: {
    popoverTitle: {
      type: String,
      default: "",
    },
    width: {
      type: String,
      default: "400px",
    },
  },
};
</script>
<style></style>
