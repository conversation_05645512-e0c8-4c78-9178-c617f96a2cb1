import Rectangle from "./Rectangle";

export default class Element {
  readonly width: number;

  readonly height: number;

  bounding: Rectangle = new Rectangle();

  select: boolean = false;

  constructor (width: number, height: number) {
    this.width = width;

    this.height = height;
  }

  set x (value: number) {
    this._x = value;

    this.calculate_bounding();

    this._center = this._x + this.width * 0.5;
  }

  get x (): number {
    return this._x;
  }

  get center (): number {
    return this._center;
  }

  private calculate_bounding () {
    this.bounding.left = this._x;
    this.bounding.right = this._x + this.width;
  }

  private _x: number = 0;

  private _center: number = 0;
}
