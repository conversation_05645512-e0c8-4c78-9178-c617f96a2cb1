const qrCodeMixIn = {
  data() {
    return {
      qrCodeInfo: {},
      isShowQrCodeModal: false,
      isDblClickOpenQrCodeModal: false,
    };
  },
  methods: {
    showQrCodeModal(image, isDblClick) {
      this.qrCodeInfo = {
        type: image.meta.qrCodeType,
        value: image.meta.qrCodeInfo,
        displayValue: image.meta.displayValue,
        barcodeFormat: image.meta.barcodeFormat,
      };
      this.isShowQrCodeModal = true;
      this.isDblClickOpenQrCodeModal = !!isDblClick;
    },
    closeQrCodeModal() {
      this.isShowQrCodeModal = false;
    },
    insertQrCode(type, val, barcodeFormat, displayValue) {
      if (this.isDblClickOpenQrCodeModal) {
        const clickInfo = this.editor._curClickInfo;
        const image = clickInfo.image;
        let imageMeta = image.meta;
        if (
          imageMeta.qrCodeType === type &&
          imageMeta.qrCodeInfo === val &&
          imageMeta.barcodeFormat === barcodeFormat &&
          imageMeta.displayValue === displayValue
        ) {
          this.closeQrCodeModal();
          return;
        }
        const newMeta = Object.assign({}, imageMeta, {
          qrCodeType: type,
          qrCodeInfo: val,
          barcodeFormat: barcodeFormat,
          displayValue: displayValue,
        });
        if (val.indexOf("[") < 0) {
          const src = this.instance.utils.getBarcodeOrQrCodeSrc(val, {
            type,
            options: {
              displayValue: displayValue === "show" ? true : false,
              format: barcodeFormat,
            },
          });
          this.editor.insertImage(src, {
            width: image.width,
            height: image.height,
            meta: newMeta,
          });
        } else {
          if (type !== imageMeta.qrCodeType) {
            const src = this.instance.utils.createImagePlaceholder(
              type === "qrcode" ? "二维码" : "条形码"
            );
            this.editor.insertImage(src, {
              width: image.width,
              height: image.height,
              meta: newMeta,
            });
          } else {
            clickInfo.image.meta = newMeta;
          }
        }
      }
      this.closeQrCodeModal();
    },
  },
};
export default qrCodeMixIn;
